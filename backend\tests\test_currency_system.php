<?php
/**
 * FanBet247 Currency System Test Suite
 * 
 * This script tests all components of the multi-currency system:
 * - Database schema and data integrity
 * - Backend API endpoints
 * - Currency conversion logic
 * - Error handling and edge cases
 * 
 * Usage: php test_currency_system.php
 * Or access via browser: http://your-domain/backend/tests/test_currency_system.php
 */

header("Content-Type: application/json; charset=UTF-8");

// Include required files
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/currency_utils.php';

class CurrencySystemTester {
    private $conn;
    private $testResults = [];
    private $errors = [];
    private $testUserId = null;
    private $testAdminId = null;

    public function __construct() {
        $this->conn = getDBConnection();
        if (!$this->conn) {
            throw new Exception("Database connection failed");
        }
    }

    public function runAllTests() {
        echo json_encode([
            'status' => 'starting',
            'message' => 'Starting Currency System Test Suite...',
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT) . "\n\n";

        try {
            // Test database schema
            $this->testDatabaseSchema();
            
            // Test currency data
            $this->testCurrencyData();
            
            // Test exchange rates
            $this->testExchangeRates();
            
            // Test user currency preferences
            $this->testUserCurrencyPreferences();
            
            // Test currency conversion functions
            $this->testCurrencyConversions();
            
            // Test API endpoints
            $this->testAPIEndpoints();
            
            // Test error handling
            $this->testErrorHandling();
            
            // Generate final report
            $this->generateReport();
            
        } catch (Exception $e) {
            $this->errors[] = "Test suite failed: " . $e->getMessage();
            $this->generateReport();
        }
    }

    private function testDatabaseSchema() {
        echo "🔍 Testing Database Schema...\n";
        
        // Test currencies table
        $this->runTest("Currencies table exists", function() {
            $stmt = $this->conn->prepare("SHOW TABLES LIKE 'currencies'");
            $stmt->execute();
            return $stmt->rowCount() > 0;
        });

        $this->runTest("Currencies table structure", function() {
            $stmt = $this->conn->prepare("DESCRIBE currencies");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $requiredColumns = ['id', 'currency_code', 'currency_name', 'currency_symbol', 'is_active'];
            return count(array_intersect($requiredColumns, $columns)) === count($requiredColumns);
        });

        // Test exchange_rates table
        $this->runTest("Exchange rates table exists", function() {
            $stmt = $this->conn->prepare("SHOW TABLES LIKE 'exchange_rates'");
            $stmt->execute();
            return $stmt->rowCount() > 0;
        });

        $this->runTest("Exchange rates table structure", function() {
            $stmt = $this->conn->prepare("DESCRIBE exchange_rates");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $requiredColumns = ['id', 'currency_id', 'rate_to_fancoin', 'updated_by_admin_id'];
            return count(array_intersect($requiredColumns, $columns)) === count($requiredColumns);
        });

        // Test users table modification
        $this->runTest("Users table has preferred_currency_id", function() {
            $stmt = $this->conn->prepare("SHOW COLUMNS FROM users LIKE 'preferred_currency_id'");
            $stmt->execute();
            return $stmt->rowCount() > 0;
        });
    }

    private function testCurrencyData() {
        echo "💰 Testing Currency Data...\n";
        
        $this->runTest("Default currencies exist", function() {
            $stmt = $this->conn->prepare("SELECT COUNT(*) FROM currencies WHERE currency_code IN ('USD', 'ZAR', 'EUR', 'GBP')");
            $stmt->execute();
            return $stmt->fetchColumn() >= 4;
        });

        $this->runTest("USD currency is active", function() {
            $stmt = $this->conn->prepare("SELECT is_active FROM currencies WHERE currency_code = 'USD'");
            $stmt->execute();
            return $stmt->fetchColumn() == 1;
        });

        $this->runTest("Currency codes are unique", function() {
            $stmt = $this->conn->prepare("SELECT currency_code, COUNT(*) as count FROM currencies GROUP BY currency_code HAVING count > 1");
            $stmt->execute();
            return $stmt->rowCount() === 0;
        });
    }

    private function testExchangeRates() {
        echo "📊 Testing Exchange Rates...\n";
        
        $this->runTest("Exchange rates exist", function() {
            $stmt = $this->conn->prepare("SELECT COUNT(*) FROM exchange_rates");
            $stmt->execute();
            return $stmt->fetchColumn() > 0;
        });

        $this->runTest("USD base rate is 1.0", function() {
            $stmt = $this->conn->prepare("
                SELECT er.rate_to_fancoin 
                FROM exchange_rates er 
                JOIN currencies c ON er.currency_id = c.id 
                WHERE c.currency_code = 'USD'
                ORDER BY er.updated_at DESC 
                LIMIT 1
            ");
            $stmt->execute();
            $rate = $stmt->fetchColumn();
            return abs($rate - 1.0) < 0.0001; // Allow for floating point precision
        });

        $this->runTest("All active currencies have rates", function() {
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) 
                FROM currencies c 
                LEFT JOIN exchange_rates er ON c.id = er.currency_id 
                WHERE c.is_active = 1 AND er.rate_to_fancoin IS NULL
            ");
            $stmt->execute();
            return $stmt->fetchColumn() === 0;
        });
    }

    private function testUserCurrencyPreferences() {
        echo "👤 Testing User Currency Preferences...\n";
        
        // Get a test user
        $stmt = $this->conn->prepare("SELECT user_id FROM users LIMIT 1");
        $stmt->execute();
        $this->testUserId = $stmt->fetchColumn();

        if ($this->testUserId) {
            $this->runTest("User has currency preference", function() {
                $stmt = $this->conn->prepare("SELECT preferred_currency_id FROM users WHERE user_id = ?");
                $stmt->execute([$this->testUserId]);
                return $stmt->fetchColumn() !== null;
            });

            $this->runTest("User currency preference is valid", function() {
                $stmt = $this->conn->prepare("
                    SELECT c.is_active 
                    FROM users u 
                    JOIN currencies c ON u.preferred_currency_id = c.id 
                    WHERE u.user_id = ?
                ");
                $stmt->execute([$this->testUserId]);
                return $stmt->fetchColumn() == 1;
            });
        }
    }

    private function testCurrencyConversions() {
        echo "🔄 Testing Currency Conversions...\n";
        
        $this->runTest("Convert 100 FanCoin to USD", function() {
            $result = convertFanCoinToCurrency($this->conn, 100, 1); // USD currency_id = 1
            return $result['success'] && $result['converted_amount'] == 100;
        });

        $this->runTest("Convert 100 FanCoin to ZAR", function() {
            // Get ZAR currency ID
            $stmt = $this->conn->prepare("SELECT id FROM currencies WHERE currency_code = 'ZAR'");
            $stmt->execute();
            $zarId = $stmt->fetchColumn();
            
            if ($zarId) {
                $result = convertFanCoinToCurrency($this->conn, 100, $zarId);
                return $result['success'] && $result['converted_amount'] > 100; // ZAR should be > 1 FanCoin
            }
            return false;
        });

        if ($this->testUserId) {
            $this->runTest("Convert for specific user", function() {
                $result = convertFanCoinForUser($this->conn, 50, $this->testUserId);
                return $result['success'] && $result['original_amount'] == 50;
            });
        }
    }

    private function testAPIEndpoints() {
        echo "🌐 Testing API Endpoints...\n";
        
        $this->runTest("Get currencies endpoint", function() {
            $response = $this->makeAPIRequest('get_currencies.php');
            return $response && isset($response['success']) && $response['success'];
        });

        $this->runTest("Get exchange rates endpoint", function() {
            $response = $this->makeAPIRequest('get_exchange_rates.php');
            return $response && isset($response['success']) && $response['success'];
        });

        $this->runTest("Currency conversion endpoint", function() {
            $response = $this->makeAPIRequest('convert_currency.php?amount=100&currency_id=1');
            return $response && isset($response['success']) && $response['success'];
        });

        if ($this->testUserId) {
            $this->runTest("User currency preference endpoint", function() {
                $response = $this->makeAPIRequest("get_user_currency_preference.php?user_id={$this->testUserId}");
                return $response && isset($response['success']) && $response['success'];
            });
        }
    }

    private function testErrorHandling() {
        echo "⚠️ Testing Error Handling...\n";
        
        $this->runTest("Invalid currency ID conversion", function() {
            $result = convertFanCoinToCurrency($this->conn, 100, 99999);
            return !$result['success'];
        });

        $this->runTest("Negative amount conversion", function() {
            $result = convertFanCoinToCurrency($this->conn, -100, 1);
            return $result['success']; // Should handle negative amounts
        });

        $this->runTest("Invalid user ID preference", function() {
            $result = getUserPreferredCurrency($this->conn, 99999);
            return $result === false;
        });
    }

    private function runTest($testName, $testFunction) {
        try {
            $startTime = microtime(true);
            $result = $testFunction();
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            $this->testResults[] = [
                'test' => $testName,
                'status' => $result ? 'PASS' : 'FAIL',
                'duration' => $duration . 'ms'
            ];
            
            echo ($result ? "✅" : "❌") . " {$testName} ({$duration}ms)\n";
            
            if (!$result) {
                $this->errors[] = "Test failed: {$testName}";
            }
        } catch (Exception $e) {
            $this->testResults[] = [
                'test' => $testName,
                'status' => 'ERROR',
                'error' => $e->getMessage()
            ];
            $this->errors[] = "Test error in {$testName}: " . $e->getMessage();
            echo "💥 {$testName} - ERROR: " . $e->getMessage() . "\n";
        }
    }

    private function makeAPIRequest($endpoint) {
        $url = "http://localhost/backend/handlers/{$endpoint}";
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        return $response ? json_decode($response, true) : false;
    }

    private function generateReport() {
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($test) {
            return $test['status'] === 'PASS';
        }));
        $failedTests = count(array_filter($this->testResults, function($test) {
            return $test['status'] === 'FAIL';
        }));
        $errorTests = count(array_filter($this->testResults, function($test) {
            return $test['status'] === 'ERROR';
        }));

        $report = [
            'summary' => [
                'total_tests' => $totalTests,
                'passed' => $passedTests,
                'failed' => $failedTests,
                'errors' => $errorTests,
                'success_rate' => $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0
            ],
            'status' => $failedTests === 0 && $errorTests === 0 ? 'ALL_TESTS_PASSED' : 'SOME_TESTS_FAILED',
            'test_results' => $this->testResults,
            'errors' => $this->errors,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        echo "\n" . str_repeat("=", 50) . "\n";
        echo "📋 CURRENCY SYSTEM TEST REPORT\n";
        echo str_repeat("=", 50) . "\n";
        echo "Total Tests: {$totalTests}\n";
        echo "✅ Passed: {$passedTests}\n";
        echo "❌ Failed: {$failedTests}\n";
        echo "💥 Errors: {$errorTests}\n";
        echo "Success Rate: {$report['summary']['success_rate']}%\n";
        echo "Status: {$report['status']}\n";
        echo str_repeat("=", 50) . "\n\n";

        echo json_encode($report, JSON_PRETTY_PRINT);
    }
}

// Run the tests
try {
    $tester = new CurrencySystemTester();
    $tester->runAllTests();
} catch (Exception $e) {
    echo json_encode([
        'status' => 'FATAL_ERROR',
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}
?>
