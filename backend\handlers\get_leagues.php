<?php
header("Access-Control-Allow-Origin: *"); 
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';
$conn = getDBConnection();

try {
    // Get limit from query parameter, default to 7 for sidebar
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 7;
    $limit = max(1, min(20, $limit));

    // Enhanced query to get top leagues with activity data
    $query = "SELECT
        l.*,
        COALESCE(m.member_count, 0) as member_count,
        COALESCE(c.active_challenges, 0) as active_challenges,
        COALESCE(b.recent_bets, 0) as recent_bets,
        CASE
            WHEN c.active_challenges > 0 THEN 'live'
            WHEN l.status = 'active' THEN 'active'
            ELSE 'inactive'
        END as live_status
    FROM leagues l
    LEFT JOIN (
        SELECT league_id, COUNT(*) as member_count
        FROM league_memberships
        WHERE status = 'active'
        GROUP BY league_id
    ) m ON m.league_id = l.league_id
    LEFT JOIN (
        SELECT 'Premier League' as league_name, COUNT(*) as active_challenges
        FROM challenges
        WHERE status = 'Open' AND NOW() < end_time
    ) c ON c.league_name = l.name
    LEFT JOIN (
        SELECT 'Premier League' as league_name, COUNT(b.bet_id) as recent_bets
        FROM bets b
        JOIN challenges ch ON b.challenge_id = ch.challenge_id
        WHERE b.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ) b ON b.league_name = l.name
    WHERE l.status IN ('active', 'upcoming')
    ORDER BY
        CASE WHEN c.active_challenges > 0 THEN 0 ELSE 1 END,
        m.member_count DESC,
        l.created_at DESC
    LIMIT :limit";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    $leagues = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add full URLs for media files and format data
    foreach ($leagues as &$league) {
        if ($league['icon_path']) {
            $league['icon_url'] = '/backend/uploads/leagues/icons/' . $league['icon_path'];
        } else {
            // Provide default league icons
            $defaultIcons = [
                'Premier League' => 'https://media.api-sports.io/football/leagues/39.png',
                'La Liga' => 'https://media.api-sports.io/football/leagues/140.png',
                'Bundesliga' => 'https://media.api-sports.io/football/leagues/78.png',
                'Serie A' => 'https://media.api-sports.io/football/leagues/135.png',
                'Ligue 1' => 'https://media.api-sports.io/football/leagues/61.png',
                'Champions League' => 'https://media.api-sports.io/football/leagues/1.png',
                'Brasileirão' => 'https://media.api-sports.io/football/leagues/71.png'
            ];
            $league['icon_url'] = $defaultIcons[$league['name']] ?? '/images/default-league.png';
        }

        if ($league['banner_path']) {
            $league['banner_url'] = '/backend/uploads/leagues/banners/' . $league['banner_path'];
        }

        // Format numeric values
        $league['member_count'] = (int)$league['member_count'];
        $league['active_challenges'] = (int)$league['active_challenges'];
        $league['recent_bets'] = (int)$league['recent_bets'];
    }

    echo json_encode([
        'status' => 200,
        'success' => true,
        'data' => $leagues,
        'total_count' => count($leagues),
        'limit' => $limit,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} catch (PDOException $e) {
    error_log("Database error in get_leagues.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
} 