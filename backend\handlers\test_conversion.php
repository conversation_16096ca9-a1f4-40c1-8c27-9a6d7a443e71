<?php
// Test currency conversion functionality
require_once '../includes/db_connect.php';
require_once '../includes/currency_utils.php';

echo "Testing Currency Conversion...\n";

$conn = getDBConnection();

if (!$conn) {
    echo "❌ Database connection failed\n";
    exit(1);
}

// Test 1: Convert 100 FanCoin to USD
echo "\n1. Converting 100 FanCoin to USD:\n";
$result = convertFanCoinToCurrency($conn, 100, 1);
if ($result['success']) {
    echo "✅ Success: {$result['conversion_text']}\n";
} else {
    echo "❌ Failed: {$result['message']}\n";
}

// Test 2: Convert 100 FanCoin to ZAR
echo "\n2. Converting 100 FanCoin to ZAR:\n";
$result = convertFanCoinToCurrency($conn, 100, 2);
if ($result['success']) {
    echo "✅ Success: {$result['conversion_text']}\n";
} else {
    echo "❌ Failed: {$result['message']}\n";
}

// Test 3: Convert 50 FanCoin to EUR
echo "\n3. Converting 50 FanCoin to EUR:\n";
$result = convertFanCoinToCurrency($conn, 50, 3);
if ($result['success']) {
    echo "✅ Success: {$result['conversion_text']}\n";
} else {
    echo "❌ Failed: {$result['message']}\n";
}

// Test 4: Test with invalid currency
echo "\n4. Testing with invalid currency ID:\n";
$result = convertFanCoinToCurrency($conn, 100, 999);
if (!$result['success']) {
    echo "✅ Correctly handled invalid currency: {$result['message']}\n";
} else {
    echo "❌ Should have failed with invalid currency\n";
}

// Test 5: Test user currency preference
echo "\n5. Testing user currency preference:\n";
$stmt = $conn->prepare("SELECT user_id FROM users LIMIT 1");
$stmt->execute();
$userId = $stmt->fetchColumn();

if ($userId) {
    $userCurrency = getUserPreferredCurrency($conn, $userId);
    if ($userCurrency) {
        echo "✅ User {$userId} prefers: {$userCurrency['currency_code']} ({$userCurrency['currency_name']})\n";
        
        $conversion = convertFanCoinForUser($conn, 100, $userId);
        if ($conversion['success']) {
            echo "✅ 100 FanCoin for user = {$conversion['formatted_amount']}\n";
        }
    } else {
        echo "❌ Could not get user currency preference\n";
    }
} else {
    echo "⚠️ No users found in database\n";
}

echo "\n✅ Currency conversion tests completed!\n";
?>
