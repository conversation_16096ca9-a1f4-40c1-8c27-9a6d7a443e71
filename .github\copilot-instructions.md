
# Copilot Instructions for FanBet247

## Project Architecture

**Backend**
- `/backend/handlers/`: API endpoint handlers. Each handler starts with CORS headers, handles OPTIONS, includes `db_connect.php`, and uses `jsonResponse` for output.
- `/backend/includes/`: Shared utilities (e.g., `db_connect.php`, `cors_headers.php`, `error_logger.php`).
- `/backend/uploads/`: File uploads. Ensure directories exist and are writable.
- `/backend/sql/`: Database schema and migrations.

**Frontend**
- Uses `axios` for all API calls. Base URL: `/backend`.
- Example GET: `axios.get('/backend/handlers/example.php')`
- Example POST (file upload): Use `multipart/form-data` and post to `/backend/handlers/your_handler.php`.

## Developer Workflows

- **Backend tests**: Run with `setup-and-run-tests.ps1` or `.bat` in project root. Test files: `/backend/test_*.php`.
- **Frontend tests**: Use <PERSON>wright via `run-admin-tests.js` in `/tests`.
- **MySQL inspection**: Use PowerShell commands from `.cursor/rules/terminal-mysql.mdc` (e.g., `SHOW TABLES;`, `DESCRIBE users;`).

## API & Coding Conventions

- All API responses use:
  ```json
  { "status": 200, "message": "Success", "data": [] }
  ```
- Use the `jsonResponse` helper in handlers for all output.
- Always use prepared statements for SQL queries.
- Log errors with `error_log()` and `includes/error_logger.php`.
- File uploads: store in `/backend/uploads/`, check/`mkdir` as needed.

## Handler Example

```php
<?php
include_once '../includes/db_connect.php';
$conn = getDBConnection();
jsonResponse(200, 'Handler created successfully');
```

## Database & Integration

- DB connection: see `includes/db_connect.php` (`getDBConnection()` returns PDO).
- Composer manages backend libraries (e.g., `phpmailer`, `google2fa`).
- Frontend: use `axios` for all HTTP requests.

## Troubleshooting

- CORS: Always set headers and handle OPTIONS in handlers.
- File upload: Check permissions and Content-Type.
- DB: Use try-catch, log errors, check credentials in `db_connect.php`.

## References

- See `/docs/README.md` for backend patterns and troubleshooting.
- See `.cursor/rules/terminal-mysql.mdc` for MySQL command examples.

---
Update this file as the project evolves. For questions, check the `README.md` files in `/docs` and `/backend`.
