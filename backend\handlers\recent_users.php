<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get last 6 registered users with their team info
    $query = "
        SELECT 
            u.user_id,
            u.username,
            u.full_name,
            u.email,
            u.created_at,
            u.favorite_team,
            t.logo as team_logo
        FROM users u
        LEFT JOIN teams t ON u.favorite_team = t.name
        ORDER BY u.created_at DESC
        LIMIT 6
    ";
    
    $stmt = $conn->query($query);
    $recentUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format the response
    echo json_encode([
        'success' => true,
        'users' => array_map(function($user) {
            return [
                'id' => $user['user_id'],
                'username' => $user['username'],
                'fullName' => $user['full_name'],
                'email' => $user['email'],
                'createdAt' => $user['created_at'],
                'favoriteTeam' => $user['favorite_team'],
                'teamLogo' => $user['team_logo']
            ];
        }, $recentUsers)
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching recent users: ' . $e->getMessage()
    ]);
}
