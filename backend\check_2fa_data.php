<?php
require_once 'includes/db_connect.php';

echo "Checking 2FA database data...\n";

try {
    $conn = getDBConnection();
    
    // Check users table
    echo "\n=== USERS TABLE ===\n";
    $stmt = $conn->query("SELECT user_id, username, email, tfa_enabled, auth_method FROM users LIMIT 10");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "User {$row['user_id']}: {$row['username']} - TFA: {$row['tfa_enabled']} - Auth: {$row['auth_method']}\n";
    }
    
    // Check user_2fa table
    echo "\n=== USER_2FA TABLE ===\n";
    $stmt = $conn->query("SELECT * FROM user_2fa");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "User {$row['user_id']}: Secret: " . substr($row['secret_key'], 0, 8) . "... - Enabled: {$row['is_enabled']} - Setup: {$row['setup_completed']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
