[2025-07-18 15:06:26] 🧪 SMTP Email Testing Started at 2025-07-18 15:06:26
[2025-07-18 15:06:26] =============================================================
[2025-07-18 15:06:26] 🔧 FanBet247 SMTP Email Testing System
[2025-07-18 15:06:26] =============================================================
[2025-07-18 15:06:26] 
🔍 Test 1: Database Connection
[2025-07-18 15:06:26] ✅ Database connection successful
[2025-07-18 15:06:26] 📊 Connected to database: fanbet247
[2025-07-18 15:06:26] 
🔍 Test 2: SMTP Settings Retrieval
[2025-07-18 15:06:26] ✅ smtp_settings table exists
[2025-07-18 15:06:26] ✅ Active SMTP settings retrieved
[2025-07-18 15:06:26] 📊 SMTP Configuration:
[2025-07-18 15:06:26]    Host: smtp.hostinger.com
[2025-07-18 15:06:26]    Port: 465
[2025-07-18 15:06:26]    Username: <EMAIL>
[2025-07-18 15:06:26]    Password: ***************
[2025-07-18 15:06:26]    Encryption: ssl
[2025-07-18 15:06:26]    From Email: <EMAIL>
[2025-07-18 15:06:26]    From Name: FanBet247
[2025-07-18 15:06:26] 
🔍 Test 3: PHPMailer Configuration
[2025-07-18 15:06:26] ✅ PHPMailer instance created successfully
[2025-07-18 15:06:26] ✅ PHPMailer configured with SMTP settings
[2025-07-18 15:06:26] 
🔍 Test 4: SMTP Connection Test
[2025-07-18 15:06:26] 🔧 SMTP Debug: Connection: opening to ssl://smtp.hostinger.com:465, timeout=300, options=array()
[2025-07-18 15:06:27] 🔧 SMTP Debug: Connection: opened
[2025-07-18 15:06:27] 🔧 SMTP Debug: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com
[2025-07-18 15:06:27] 🔧 SMTP Debug: CLIENT -> SERVER: EHLO NAIJAGAMERX
[2025-07-18 15:06:27] 🔧 SMTP Debug: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING
[2025-07-18 15:06:27] 🔧 SMTP Debug: CLIENT -> SERVER: AUTH LOGIN
[2025-07-18 15:06:27] 🔧 SMTP Debug: SERVER -> CLIENT: 334 VXNlcm5hbWU6
[2025-07-18 15:06:27] 🔧 SMTP Debug: CLIENT -> SERVER: [credentials hidden]
[2025-07-18 15:06:27] 🔧 SMTP Debug: SERVER -> CLIENT: 334 UGFzc3dvcmQ6
[2025-07-18 15:06:27] 🔧 SMTP Debug: CLIENT -> SERVER: [credentials hidden]
[2025-07-18 15:06:28] 🔧 SMTP Debug: SERVER -> CLIENT: 235 2.7.0 Authentication successful
[2025-07-18 15:06:28] ✅ SMTP connection successful
[2025-07-18 15:06:28] 🔧 SMTP Debug: CLIENT -> SERVER: QUIT
[2025-07-18 15:06:28] 🔧 SMTP Debug: SERVER -> CLIENT: 221 2.0.0 Bye
[2025-07-18 15:06:28] 🔧 SMTP Debug: Connection: closed
[2025-07-18 15:06:28] 
🔍 Test 5: Send Test Email
[2025-07-18 15:06:28] 📧 Sending test email to: <EMAIL>
[2025-07-18 15:06:31] ✅ Test email sent successfully!
[2025-07-18 15:06:31] 📧 Email sent to: <EMAIL>
[2025-07-18 15:06:31] 📝 Subject: FanBet247 SMTP Test - 2025-07-18 15:06:28
[2025-07-18 15:06:31] 
🔍 Test 6: sendEmail() Function Test
[2025-07-18 15:06:31] ⚠️ sendEmail() function not found - will test direct PHPMailer only
[2025-07-18 15:06:31] 
🔍 Test 7: OTP Email Template Test
[2025-07-18 15:06:34] ✅ OTP email template test successful!
[2025-07-18 15:06:34] 📧 OTP email sent with code: 123456
[2025-07-18 15:06:34] =============================================================
[2025-07-18 15:06:34] ✅ SMTP Testing Completed
[2025-07-18 15:06:34] 📄 Log file saved as: smtp_test_2025-07-18_15-06-26.log
[2025-07-18 15:06:34] =============================================================
