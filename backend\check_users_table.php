<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Users table structure:\n";
    $stmt = $conn->query('DESCRIBE users');
    while($row = $stmt->fetch()) {
        echo "  {$row['Field']} - {$row['Type']}\n";
    }
    
    echo "\nSample user data:\n";
    $stmt = $conn->query('SELECT * FROM users WHERE user_id = 4');
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        foreach ($user as $key => $value) {
            echo "  $key: $value\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
