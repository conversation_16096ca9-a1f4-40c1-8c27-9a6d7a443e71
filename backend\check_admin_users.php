<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Checking for admin users...\n\n";
    
    $stmt = $conn->query('SELECT user_id, username, email, role FROM users WHERE role = "admin"');
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "No admin users found. Creating a test admin user...\n";
        
        // Create a test admin user
        $username = 'admin';
        $email = '<EMAIL>';
        $password = 'admin123';
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("
            INSERT INTO users (username, email, password_hash, role, full_name, status, auth_method) 
            VALUES (?, ?, ?, 'admin', 'System Administrator', 'active', 'password_only')
        ");
        
        $stmt->execute([$username, $email, $passwordHash]);
        $adminId = $conn->lastInsertId();
        
        echo "Created admin user:\n";
        echo "  Username: $username\n";
        echo "  Email: $email\n";
        echo "  Password: $password\n";
        echo "  User ID: $adminId\n";
        
    } else {
        echo "Found admin users:\n";
        foreach ($admins as $admin) {
            echo "  - {$admin['username']} ({$admin['email']}) - ID: {$admin['user_id']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
