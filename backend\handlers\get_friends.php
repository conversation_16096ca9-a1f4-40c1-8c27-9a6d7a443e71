<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    $userId = $_GET['user_id'] ?? null;
    $status = $_GET['status'] ?? 'accepted';

    if (!$userId) {
        throw new Exception('User ID is required');
    }

    // Get friends list with usernames and balances
    $query = "SELECT u.user_id, u.username, u.balance
             FROM users u
             INNER JOIN user_friends uf ON (
                 (uf.user_id = :user_id AND uf.friend_id = u.user_id) OR 
                 (uf.friend_id = :user_id AND uf.user_id = u.user_id)
             )
             WHERE uf.status = :status";
    
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':user_id', $userId);
    $stmt->bindParam(':status', $status);
    $stmt->execute();
    $friends = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'friends' => $friends
    ]);

} catch (Exception $e) {
    error_log("Error in get_friends.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 