-- Create SMTP Settings Table
CREATE TABLE IF NOT EXISTS smtp_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    host VARCHAR(255) NOT NULL DEFAULT 'smtp.gmail.com',
    port INT NOT NULL DEFAULT 587,
    username VARCHAR(255) NOT NULL DEFAULT '',
    password VARCHAR(255) NOT NULL DEFAULT '',
    encryption ENUM('none', 'ssl', 'tls') NOT NULL DEFAULT 'tls',
    from_email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>',
    from_name VARCHAR(255) NOT NULL DEFAULT 'FanBet247',
    is_active BOOLEAN NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default SMTP settings
INSERT INTO smtp_settings (host, port, username, password, encryption, from_email, from_name, is_active) VALUES
('smtp.gmail.com', 587, '', '', 'tls', '<EMAIL>', 'FanBet247', 0)
ON DUPLICATE KEY UPDATE 
    host = VALUES(host),
    port = VALUES(port),
    encryption = VALUES(encryption),
    from_email = VALUES(from_email),
    from_name = VALUES(from_name);
