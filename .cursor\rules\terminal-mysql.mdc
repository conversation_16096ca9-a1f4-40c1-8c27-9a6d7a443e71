---
description: 
globs: 
alwaysApply: true
---
# MySQL Terminal Commands for FanBet247 Database

## Basic Database Commands

### Show All Tables
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SHOW TABLES;'"
```

### Describe Table Structure
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'DESCRIBE users;'"
```

### Show Table Columns
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE TABLE_NAME = \"users\";'"
```

### Show Detailed Column Information
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = \"fanbet247\" AND TABLE_NAME = \"bets\";'"
```

## Query Data Commands

### Select Data from Table (with limit)
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SELECT * FROM users LIMIT 5;'"
```

### Count Records in Table
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SELECT COUNT(*) FROM users;'"
```

### Query with Conditions
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SELECT username, balance FROM users WHERE balance > 10000;'"
```

## Database Information Commands

### Show Database Version
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SELECT VERSION();'"
```

### Show Table Creation SQL
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SHOW CREATE TABLE users;'"
```

### Show Foreign Keys
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE REFERENCED_TABLE_SCHEMA = \"fanbet247\";'"
```

### Show Table Size and Statistics
```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH/1024/1024 as \"Data Size (MB)\", INDEX_LENGTH/1024/1024 as \"Index Size (MB)\" FROM information_schema.TABLES WHERE TABLE_SCHEMA = \"fanbet247\";'"
```

## Alternative Command Format (CMD)
You can also use these commands directly in CMD:

```
C:\MAMP\bin\mysql\bin\mysql.exe -u root -proot -h localhost fanbet247 -e "SHOW TABLES;"
C:\MAMP\bin\mysql\bin\mysql.exe -u root -proot -h localhost fanbet247 -e "DESCRIBE users;"
```

## Interactive MySQL Session
To start an interactive MySQL session:

```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247"
```

Once in the interactive session, you can run commands like:
```
SHOW TABLES;
DESCRIBE users;
SELECT * FROM users LIMIT 5;
```

## Notes
- The `-proot` parameter passes the password directly. For better security, use `-p` without the password and you'll be prompted to enter it.
- To save query results to a file, add `> filename.txt` at the end of the command.
- If you're having issues with the commands, try removing the `-h localhost` part.

## Tested Working Commands

The following command has been tested and confirmed working:

```
powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SHOW TABLES;'"
```

gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247
$ powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'DESCRIBE users;'"
mysql: [Warning] Using a password on the command line interface can be insecure.
+-------------------+----------------------+------+-----+-------------------+-----------------------------+
| Field             | Type                 | Null | Key | Default           | Extra                       |
+-------------------+----------------------+------+-----+-------------------+-----------------------------+
| user_id           | int(11)              | NO   | PRI | NULL              | auto_increment              |
| username          | varchar(50)          | NO   | UNI | NULL              |                             |
| full_name         | varchar(255)         | NO   |     | NULL              |                             |
| password_hash     | varchar(255)         | NO   |     | NULL              |                             |
| email             | varchar(100)         | NO   | UNI | NULL              |                             |
| favorite_team     | varchar(100)         | YES  |     | NULL              |                             |
| balance           | decimal(10,2)        | YES  |     | 0.00              |                             |
| created_at        | timestamp            | NO   |     | CURRENT_TIMESTAMP |                             |
| updated_at        | timestamp            | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| points            | int(11)              | YES  |     | 0                 |                             |
| current_league_id | int(11)              | YES  | MUL | NULL              |                             |
| total_points      | int(11)              | YES  |     | 0                 |                             |
| current_streak    | int(11)              | YES  |     | 0                 |                             |
| highest_streak    | int(11)              | YES  |     | 0                 |                             |
| current_season_id | int(11)              | YES  | MUL | NULL              |                             |
| last_active       | timestamp            | YES  | MUL | NULL              |                             |
| role              | enum('user','admin') | NO   |     | user              |                             |
| wins              | int(11)              | YES  |     | 0                 |                             |
| draws             | int(11)              | YES  |     | 0                 |                             |
| losses            | int(11)              | YES  |     | 0                 |                             |
| total_bets        | int(11)              | YES  |     | 0                 |                             |
| last_bet_date     | timestamp            | YES  |     | NULL              |                             |
+-------------------+----------------------+------+-----+-------------------+-----------------------------+

gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247
$ :


gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247
$ powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot fanbet247 -e 'DESCRIBE users;'"
mysql: [Warning] Using a password on the command line interface can be insecure.
+-------------------+----------------------+------+-----+-------------------+-----------------------------+
| Field             | Type                 | Null | Key | Default           | Extra                       |
+-------------------+----------------------+------+-----+-------------------+-----------------------------+
| user_id           | int(11)              | NO   | PRI | NULL              | auto_increment              |
| username          | varchar(50)          | NO   | UNI | NULL              |                             |
| full_name         | varchar(255)         | NO   |     | NULL              |                             |
| password_hash     | varchar(255)         | NO   |     | NULL              |                             |
| email             | varchar(100)         | NO   | UNI | NULL              |                             |
| favorite_team     | varchar(100)         | YES  |     | NULL              |                             |
| balance           | decimal(10,2)        | YES  |     | 0.00              |                             |
| created_at        | timestamp            | NO   |     | CURRENT_TIMESTAMP |                             |
| updated_at        | timestamp            | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| points            | int(11)              | YES  |     | 0                 |                             |
| current_league_id | int(11)              | YES  | MUL | NULL              |                             |
| total_points      | int(11)              | YES  |     | 0                 |                             |
| current_streak    | int(11)              | YES  |     | 0                 |                             |
| highest_streak    | int(11)              | YES  |     | 0                 |                             |
| current_season_id | int(11)              | YES  | MUL | NULL              |                             |
| last_active       | timestamp            | YES  | MUL | NULL              |                             |
| role              | enum('user','admin') | NO   |     | user              |                             |
| wins              | int(11)              | YES  |     | 0                 |                             |
| draws             | int(11)              | YES  |     | 0                 |                             |
| losses            | int(11)              | YES  |     | 0                 |                             |
| total_bets        | int(11)              | YES  |     | 0                 |                             |
| last_bet_date     | timestamp            | YES  |     | NULL              |                             |
+-------------------+----------------------+------+-----+-------------------+-----------------------------+

gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247
$ :


gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247
$ powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot fanbet247 -e 'DESCRIBE users;'"
mysql: [Warning] Using a password on the command line interface can be insecure.
+-------------------+----------------------+------+-----+-------------------+-----------------------------+
| Field             | Type                 | Null | Key | Default           | Extra                       |
+-------------------+----------------------+------+-----+-------------------+-----------------------------+
| user_id           | int(11)              | NO   | PRI | NULL              | auto_increment              |
| username          | varchar(50)          | NO   | UNI | NULL              |                             |
| full_name         | varchar(255)         | NO   |     | NULL              |                             |
| password_hash     | varchar(255)         | NO   |     | NULL              |                             |
| email             | varchar(100)         | NO   | UNI | NULL              |                             |
| favorite_team     | varchar(100)         | YES  |     | NULL              |                             |
| balance           | decimal(10,2)        | YES  |     | 0.00              |                             |
| created_at        | timestamp            | NO   |     | CURRENT_TIMESTAMP |                             |
| updated_at        | timestamp            | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| points            | int(11)              | YES  |     | 0                 |                             |
| current_league_id | int(11)              | YES  | MUL | NULL              |                             |
| total_points      | int(11)              | YES  |     | 0                 |                             |
| current_streak    | int(11)              | YES  |     | 0                 |                             |
| highest_streak    | int(11)              | YES  |     | 0                 |                             |
| current_season_id | int(11)              | YES  | MUL | NULL              |                             |
| last_active       | timestamp            | YES  | MUL | NULL              |                             |
| role              | enum('user','admin') | NO   |     | user              |                             |
| wins              | int(11)              | YES  |     | 0                 |                             |
| draws             | int(11)              | YES  |     | 0                 |                             |
| losses            | int(11)              | YES  |     | 0                 |                             |
| total_bets        | int(11)              | YES  |     | 0                 |                             |
| last_bet_date     | timestamp            | YES  |     | NULL              |                             |
+-------------------+----------------------+------+-----+-------------------+-----------------------------+

gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247
$ :


gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247
$ powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'SHOW TABLES;'"
mysql: [Warning] Using a password on the command line interface can be insecure.
+---------------------------+
| Tables_in_fanbet247       |
+---------------------------+
| achievements              |
| adminactions              |
| admins                    |
| bets                      |
| challenges                |
| credit_requests           |
| leaderboards              |
| league_audit_log          |
| league_history            |
| league_memberships        |
| league_memberships_backup |
| league_seasons            |
| leagues                   |
| messages                  |
| payment_methods           |
| paymentverifications      |
| season_history            |
| seasons                   |
| teams                     |
| transactions              |
| user_achievements         |
| user_activity_log         |
| user_friends              |
| user_league_stats         |
| user_league_stats_backup  |
| user_leagues              |
| user_leagues_backup       |
| user_sessions             |
| users                     |
+---------------------------+

gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247
$ :


gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247
$ powershell -Command "& 'C:\MAMP\bin\mysql\bin\mysql.exe' -u root -proot -h localhost fanbet247 -e 'DESCRIBE users;'"
mysql: [Warning] Using a password on the command line interface can be insecure.
+-------------------+----------------------+------+-----+-------------------+-----------------------------+
| Field             | Type                 | Null | Key | Default           | Extra                       |
+-------------------+----------------------+------+-----+-------------------+-----------------------------+
| user_id           | int(11)              | NO   | PRI | NULL              | auto_increment              |
| username          | varchar(50)          | NO   | UNI | NULL              |                             |
| full_name         | varchar(255)         | NO   |     | NULL              |                             |
| password_hash     | varchar(255)         | NO   |     | NULL              |                             |
| email             | varchar(100)         | NO   | UNI | NULL              |                             |
| favorite_team     | varchar(100)         | YES  |     | NULL              |                             |
| balance           | decimal(10,2)        | YES  |     | 0.00              |                             |
| created_at        | timestamp            | NO   |     | CURRENT_TIMESTAMP |                             |
| updated_at        | timestamp            | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| points            | int(11)              | YES  |     | 0                 |                             |
| current_league_id | int(11)              | YES  | MUL | NULL              |                             |
| total_points      | int(11)              | YES  |     | 0                 |                             |
| current_streak    | int(11)              | YES  |     | 0                 |                             |
| highest_streak    | int(11)              | YES  |     | 0                 |                             |
| current_season_id | int(11)              | YES  | MUL | NULL              |                             |
| last_active       | timestamp            | YES  | MUL | NULL              |                             |
| role              | enum('user','admin') | NO   |     | user              |                             |
| wins              | int(11)              | YES  |     | 0                 |                             |
| draws             | int(11)              | YES  |     | 0                 |                             |
| losses            | int(11)              | YES  |     | 0                 |                             |
| total_bets        | int(11)              | YES  |     | 0                 |                             |
| last_bet_date     | timestamp            | YES  |     | NULL              |                             |
+-------------------+----------------------+------+-----+-------------------+-----------------------------+


gcoll@NAIJAGAMERX MINGW64 /c/MAMP/htdocs/FanBet247