{"ast": null, "code": "/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */import React,{useState,useEffect}from'react';import axios from'../utils/axiosConfig';import{API_BASE_URL}from'../config';import{FaCoins,FaEdit,FaToggleOn,FaToggleOff,FaPlus,FaSave,FaTimes,FaExchangeAlt,FaTrash}from'react-icons/fa';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CurrencyManagement(){const[currencies,setCurrencies]=useState([]);const[exchangeRates,setExchangeRates]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');// Stats\nconst[stats,setStats]=useState({totalCurrencies:0,activeCurrencies:0,inactiveCurrencies:0,lastUpdated:null});// Modal states\nconst[showAddCurrency,setShowAddCurrency]=useState(false);const[showEditRateModal,setShowEditRateModal]=useState(false);const[editingCurrency,setEditingCurrency]=useState(null);// Form states\nconst[newCurrency,setNewCurrency]=useState({currency_code:'',currency_name:'',currency_symbol:'',is_active:true});const[newRate,setNewRate]=useState('');const[notes,setNotes]=useState('');useEffect(()=>{loadCurrencies();loadExchangeRates();},[]);const loadCurrencies=async()=>{setLoading(true);try{const response=await axios.get('get_currencies.php');if(response.data.success){const currencyData=response.data.data.currencies||[];setCurrencies(currencyData);// Calculate stats\nconst totalCurrencies=currencyData.length;const activeCurrencies=currencyData.filter(c=>c.is_active).length;const inactiveCurrencies=totalCurrencies-activeCurrencies;setStats({totalCurrencies,activeCurrencies,inactiveCurrencies,lastUpdated:new Date().toISOString()});}else{setError(response.data.message||'Failed to load currencies');}}catch(err){console.error('Error loading currencies:',err);setError('Failed to load currencies. Please check your network connection.');}finally{setLoading(false);}};const loadExchangeRates=async()=>{try{const response=await axios.get('get_exchange_rates.php');if(response.data.success){setExchangeRates(response.data.data.exchange_rates||[]);}}catch(err){console.error('Error loading exchange rates:',err);}};const handleUpdateRate=async()=>{if(!editingCurrency)return;if(!newRate||isNaN(parseFloat(newRate))){setError('Please enter a valid exchange rate');return;}setLoading(true);try{const response=await axios.post(`${API_BASE_URL}/handlers/update_exchange_rate.php`,{currency_id:editingCurrency.id,rate_to_fancoin:parseFloat(newRate),notes:notes});if(response.data.success){setSuccess('Exchange rate updated successfully!');loadExchangeRates();setShowEditRateModal(false);setEditingCurrency(null);setNewRate('');setNotes('');setTimeout(()=>setSuccess(''),3000);}else{setError(response.data.message||'Failed to update exchange rate');setTimeout(()=>setError(''),3000);}}catch(err){console.error('Error updating exchange rate:',err);setError('Failed to update exchange rate. Please try again.');setTimeout(()=>setError(''),3000);}finally{setLoading(false);}};const handleToggleCurrency=async(currencyId,currentStatus)=>{setLoading(true);try{const response=await axios.post('manage_currencies.php',{action:'toggle',currency_id:currencyId});if(response.data.success){setSuccess(`Currency ${currentStatus?'deactivated':'activated'} successfully!`);loadCurrencies();setTimeout(()=>setSuccess(''),3000);}else{setError(response.data.message||'Failed to toggle currency status');setTimeout(()=>setError(''),3000);}}catch(err){console.error('Error toggling currency:',err);setError('Failed to toggle currency status. Please try again.');setTimeout(()=>setError(''),3000);}finally{setLoading(false);}};const handleDeleteCurrency=async(currencyId,currencyCode)=>{// Confirm deletion\nif(!window.confirm(`Are you sure you want to delete the currency \"${currencyCode}\"? This action cannot be undone and will remove all associated exchange rates.`)){return;}setLoading(true);try{const response=await axios.post('manage_currencies.php',{action:'delete',currency_id:currencyId});if(response.data.success){setSuccess(`Currency \"${currencyCode}\" deleted successfully!`);loadCurrencies();loadExchangeRates();setTimeout(()=>setSuccess(''),3000);}else{setError(response.data.message||'Failed to delete currency');setTimeout(()=>setError(''),3000);}}catch(err){console.error('Error deleting currency:',err);setError('Failed to delete currency. Please try again.');setTimeout(()=>setError(''),3000);}finally{setLoading(false);}};const handleAddCurrency=async()=>{if(!newCurrency.currency_code||!newCurrency.currency_name||!newCurrency.currency_symbol){setError('Please fill in all required fields');return;}setLoading(true);try{const response=await axios.post('manage_currencies.php',{action:'create',...newCurrency});if(response.data.success){setSuccess('Currency added successfully!');loadCurrencies();setShowAddCurrency(false);setNewCurrency({currency_code:'',currency_name:'',currency_symbol:'',is_active:true});setTimeout(()=>setSuccess(''),3000);}else{setError(response.data.message||'Failed to add currency');setTimeout(()=>setError(''),3000);}}catch(err){console.error('Error adding currency:',err);setError('Failed to add currency. Please try again.');setTimeout(()=>setError(''),3000);}finally{setLoading(false);}};const getExchangeRate=currencyId=>{return exchangeRates.find(rate=>rate.currency_id===currencyId);};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString('en-US',{year:'numeric',month:'short',day:'numeric',hour:'2-digit',minute:'2-digit'});};const handleInputChange=e=>{const{name,value}=e.target;setNewCurrency(prev=>({...prev,[name]:value}));};return/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 bg-gray-50 min-h-screen\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-800\",children:\"Currency Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Manage supported currencies and exchange rates\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\",onClick:()=>setShowAddCurrency(true),disabled:loading,children:[/*#__PURE__*/_jsx(FaPlus,{}),\"Add New Currency\"]})]})}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",role:\"alert\",children:/*#__PURE__*/_jsx(\"span\",{className:\"block sm:inline\",children:error})}),success&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\",role:\"alert\",children:/*#__PURE__*/_jsx(\"span\",{className:\"block sm:inline\",children:success})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-blue-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaCoins,{className:\"text-blue-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Total Currencies\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:stats.totalCurrencies})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-green-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaToggleOn,{className:\"text-green-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Active Currencies\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:stats.activeCurrencies})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-red-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaToggleOff,{className:\"text-red-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Inactive Currencies\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:stats.inactiveCurrencies})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-yellow-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaExchangeAlt,{className:\"text-yellow-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Exchange Rates\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:exchangeRates.length})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-gray-800\",children:\"Currencies & Exchange Rates\"})}),loading&&currencies.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"p-8 text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-600\",children:\"Loading currencies...\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Currency\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Exchange Rate\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Last Updated\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:currencies.map(currency=>{const rate=getExchangeRate(currency.id);return/*#__PURE__*/_jsxs(\"tr\",{className:`${!currency.is_active?'opacity-60':''}`,children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0 h-10 w-10\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-blue-600 font-semibold\",children:currency.currency_symbol})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:currency.currency_code}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:currency.currency_name})]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:rate?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm font-medium text-gray-900\",children:[\"1 FC = \",currency.currency_symbol,rate.rate_to_fancoin]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setEditingCurrency(currency);setNewRate(rate.rate_to_fancoin?rate.rate_to_fancoin.toString():'');setShowEditRateModal(true);},className:\"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\",children:[/*#__PURE__*/_jsx(FaEdit,{}),\"Update\"]})]}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-500\",children:\"No rate set\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setEditingCurrency(currency);setNewRate('');setShowEditRateModal(true);},className:\"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\",children:[/*#__PURE__*/_jsx(FaPlus,{}),\"Set Rate\"]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${currency.is_active?'bg-green-100 text-green-800':'bg-red-100 text-red-800'}`,children:currency.is_active?'Active':'Inactive'})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:rate?formatDate(rate.updated_at):'Never'}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleToggleCurrency(currency.id,currency.is_active),className:`inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium transition-colors ${currency.is_active?'bg-red-500 text-white hover:bg-red-600 shadow-sm':'bg-green-500 text-white hover:bg-green-600 shadow-sm'}`,disabled:loading,children:[currency.is_active?/*#__PURE__*/_jsx(FaToggleOff,{}):/*#__PURE__*/_jsx(FaToggleOn,{}),currency.is_active?'Deactivate':'Activate']}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleDeleteCurrency(currency.id,currency.currency_code),className:\"inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium bg-gray-600 text-white hover:bg-gray-700 shadow-sm transition-colors\",disabled:loading,title:`Delete ${currency.currency_code} currency`,children:[/*#__PURE__*/_jsx(FaTrash,{}),\"Delete\"]})]})})]},currency.id);})})]})})]}),showAddCurrency&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Add New Currency\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Currency Code (e.g., EUR, GBP)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"currency_code\",value:newCurrency.currency_code,onChange:handleInputChange,placeholder:\"USD\",maxLength:\"3\",className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Currency Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"currency_name\",value:newCurrency.currency_name,onChange:handleInputChange,placeholder:\"US Dollar\",className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Currency Symbol\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"currency_symbol\",value:newCurrency.currency_symbol,onChange:handleInputChange,placeholder:\"$\",maxLength:\"5\",className:\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"is_active\",checked:newCurrency.is_active,onChange:e=>setNewCurrency({...newCurrency,is_active:e.target.checked}),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Active\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end gap-3 mt-6\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowAddCurrency(false),className:\"bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg\",children:\"Cancel\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleAddCurrency,className:\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\",disabled:loading,children:[/*#__PURE__*/_jsx(FaPlus,{}),\"Add Currency\"]})]})]})})}),showEditRateModal&&editingCurrency&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Update Exchange Rate\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowEditRateModal(false);setEditingCurrency(null);setNewRate('');setNotes('');},className:\"text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(FaTimes,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl mr-2\",children:editingCurrency.currency_symbol}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium\",children:editingCurrency.currency_code}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:editingCurrency.currency_name})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:[\"Exchange Rate (1 FC = \",editingCurrency.currency_symbol,\"?)\"]}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.0001\",value:newRate,onChange:e=>setNewRate(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\",placeholder:\"Enter exchange rate\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Update Notes (Optional)\"}),/*#__PURE__*/_jsx(\"textarea\",{value:notes,onChange:e=>setNotes(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\",rows:\"3\",placeholder:\"Add notes about this rate update...\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowEditRateModal(false);setEditingCurrency(null);setNewRate('');setNotes('');},className:\"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",children:\"Cancel\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleUpdateRate,disabled:loading||!newRate,className:\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\",children:[/*#__PURE__*/_jsx(FaSave,{}),loading?'Saving...':'Save Rate']})]})]})})})]});}export default CurrencyManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "API_BASE_URL", "FaCoins", "FaEdit", "FaToggleOn", "FaToggleOff", "FaPlus", "FaSave", "FaTimes", "FaExchangeAlt", "FaTrash", "jsx", "_jsx", "jsxs", "_jsxs", "CurrencyManagement", "currencies", "setCurrencies", "exchangeRates", "setExchangeRates", "loading", "setLoading", "error", "setError", "success", "setSuccess", "stats", "setStats", "totalCurrencies", "activeCurrencies", "inactiveCurrencies", "lastUpdated", "showAddCurrency", "setShowAddCurrency", "showEditRateModal", "setShowEditRateModal", "editing<PERSON><PERSON><PERSON>cy", "setEditingCurrency", "newCurrency", "setNewCurrency", "currency_code", "currency_name", "currency_symbol", "is_active", "newRate", "setNewRate", "notes", "setNotes", "loadCurrencies", "loadExchangeRates", "response", "get", "data", "currencyData", "length", "filter", "c", "Date", "toISOString", "message", "err", "console", "exchange_rates", "handleUpdateRate", "isNaN", "parseFloat", "post", "currency_id", "id", "rate_to_fancoin", "setTimeout", "handleToggleCurrency", "currencyId", "currentStatus", "action", "handleDeleteCurrency", "currencyCode", "window", "confirm", "handleAddCurrency", "getExchangeRate", "find", "rate", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "hour", "minute", "handleInputChange", "e", "name", "value", "target", "prev", "className", "children", "onClick", "disabled", "role", "map", "currency", "toString", "updated_at", "title", "type", "onChange", "placeholder", "max<PERSON><PERSON><PERSON>", "checked", "step", "rows"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CurrencyManagement.js"], "sourcesContent": ["/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt, FaTrash } from 'react-icons/fa';\n\nfunction CurrencyManagement() {\n    const [currencies, setCurrencies] = useState([]);\n    const [exchangeRates, setExchangeRates] = useState([]);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    // Stats\n    const [stats, setStats] = useState({\n        totalCurrencies: 0,\n        activeCurrencies: 0,\n        inactiveCurrencies: 0,\n        lastUpdated: null\n    });\n\n    // Modal states\n    const [showAddCurrency, setShowAddCurrency] = useState(false);\n    const [showEditRateModal, setShowEditRateModal] = useState(false);\n    const [editingCurrency, setEditingCurrency] = useState(null);\n\n    // Form states\n    const [newCurrency, setNewCurrency] = useState({\n        currency_code: '',\n        currency_name: '',\n        currency_symbol: '',\n        is_active: true\n    });\n    const [newRate, setNewRate] = useState('');\n    const [notes, setNotes] = useState('');\n\n    useEffect(() => {\n        loadCurrencies();\n        loadExchangeRates();\n    }, []);\n\n    const loadCurrencies = async () => {\n        setLoading(true);\n        try {\n            const response = await axios.get('get_currencies.php');\n            if (response.data.success) {\n                const currencyData = response.data.data.currencies || [];\n                setCurrencies(currencyData);\n\n                // Calculate stats\n                const totalCurrencies = currencyData.length;\n                const activeCurrencies = currencyData.filter(c => c.is_active).length;\n                const inactiveCurrencies = totalCurrencies - activeCurrencies;\n\n                setStats({\n                    totalCurrencies,\n                    activeCurrencies,\n                    inactiveCurrencies,\n                    lastUpdated: new Date().toISOString()\n                });\n            } else {\n                setError(response.data.message || 'Failed to load currencies');\n            }\n        } catch (err) {\n            console.error('Error loading currencies:', err);\n            setError('Failed to load currencies. Please check your network connection.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const loadExchangeRates = async () => {\n        try {\n            const response = await axios.get('get_exchange_rates.php');\n            if (response.data.success) {\n                setExchangeRates(response.data.data.exchange_rates || []);\n            }\n        } catch (err) {\n            console.error('Error loading exchange rates:', err);\n        }\n    };\n\n    const handleUpdateRate = async () => {\n        if (!editingCurrency) return;\n\n        if (!newRate || isNaN(parseFloat(newRate))) {\n            setError('Please enter a valid exchange rate');\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/update_exchange_rate.php`, {\n                currency_id: editingCurrency.id,\n                rate_to_fancoin: parseFloat(newRate),\n                notes: notes\n            });\n\n            if (response.data.success) {\n                setSuccess('Exchange rate updated successfully!');\n                loadExchangeRates();\n                setShowEditRateModal(false);\n                setEditingCurrency(null);\n                setNewRate('');\n                setNotes('');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update exchange rate');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error updating exchange rate:', err);\n            setError('Failed to update exchange rate. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleToggleCurrency = async (currencyId, currentStatus) => {\n        setLoading(true);\n        try {\n            const response = await axios.post('manage_currencies.php', {\n                action: 'toggle',\n                currency_id: currencyId\n            });\n\n            if (response.data.success) {\n                setSuccess(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n                loadCurrencies();\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to toggle currency status');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error toggling currency:', err);\n            setError('Failed to toggle currency status. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleDeleteCurrency = async (currencyId, currencyCode) => {\n        // Confirm deletion\n        if (!window.confirm(`Are you sure you want to delete the currency \"${currencyCode}\"? This action cannot be undone and will remove all associated exchange rates.`)) {\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post('manage_currencies.php', {\n                action: 'delete',\n                currency_id: currencyId\n            });\n\n            if (response.data.success) {\n                setSuccess(`Currency \"${currencyCode}\" deleted successfully!`);\n                loadCurrencies();\n                loadExchangeRates();\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to delete currency');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error deleting currency:', err);\n            setError('Failed to delete currency. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleAddCurrency = async () => {\n        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n            setError('Please fill in all required fields');\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post('manage_currencies.php', {\n                action: 'create',\n                ...newCurrency\n            });\n\n            if (response.data.success) {\n                setSuccess('Currency added successfully!');\n                loadCurrencies();\n                setShowAddCurrency(false);\n                setNewCurrency({\n                    currency_code: '',\n                    currency_name: '',\n                    currency_symbol: '',\n                    is_active: true\n                });\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to add currency');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error adding currency:', err);\n            setError('Failed to add currency. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const getExchangeRate = (currencyId) => {\n        return exchangeRates.find(rate => rate.currency_id === currencyId);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setNewCurrency(prev => ({ ...prev, [name]: value }));\n    };\n\n    return (\n        <div className=\"p-6 bg-gray-50 min-h-screen\">\n            {/* Page Header */}\n            <div className=\"mb-8\">\n                <div className=\"flex justify-between items-center\">\n                    <div>\n                        <h1 className=\"text-2xl font-bold text-gray-800\">Currency Management</h1>\n                        <p className=\"text-gray-600\">Manage supported currencies and exchange rates</p>\n                    </div>\n                    <button\n                        className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\"\n                        onClick={() => setShowAddCurrency(true)}\n                        disabled={loading}\n                    >\n                        <FaPlus />\n                        Add New Currency\n                    </button>\n                </div>\n            </div>\n\n            {/* Notification Messages */}\n            {error && (\n                <div className=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\n                    <span className=\"block sm:inline\">{error}</span>\n                </div>\n            )}\n            {success && (\n                <div className=\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\" role=\"alert\">\n                    <span className=\"block sm:inline\">{success}</span>\n                </div>\n            )}\n\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n                {/* Total Currencies */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-blue-100 p-3 mr-4\">\n                        <FaCoins className=\"text-blue-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Total Currencies</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.totalCurrencies}</h3>\n                    </div>\n                </div>\n\n                {/* Active Currencies */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-green-100 p-3 mr-4\">\n                        <FaToggleOn className=\"text-green-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Active Currencies</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.activeCurrencies}</h3>\n                    </div>\n                </div>\n\n                {/* Inactive Currencies */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-red-100 p-3 mr-4\">\n                        <FaToggleOff className=\"text-red-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Inactive Currencies</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.inactiveCurrencies}</h3>\n                    </div>\n                </div>\n\n                {/* Exchange Rates */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-yellow-100 p-3 mr-4\">\n                        <FaExchangeAlt className=\"text-yellow-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Exchange Rates</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{exchangeRates.length}</h3>\n                    </div>\n                </div>\n            </div>\n\n            {/* Currencies Table */}\n            <div className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                    <h2 className=\"text-lg font-semibold text-gray-800\">Currencies & Exchange Rates</h2>\n                </div>\n\n                {loading && currencies.length === 0 ? (\n                    <div className=\"p-8 text-center\">\n                        <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n                        <p className=\"mt-2 text-gray-600\">Loading currencies...</p>\n                    </div>\n                ) : (\n                    <div className=\"overflow-x-auto\">\n                        <table className=\"min-w-full divide-y divide-gray-200\">\n                            <thead className=\"bg-gray-50\">\n                                <tr>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Currency</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Exchange Rate</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Status</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Last Updated</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                                </tr>\n                            </thead>\n                            <tbody className=\"bg-white divide-y divide-gray-200\">\n                                {currencies.map((currency) => {\n                                    const rate = getExchangeRate(currency.id);\n\n                                    return (\n                                        <tr key={currency.id} className={`${!currency.is_active ? 'opacity-60' : ''}`}>\n                                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                                                <div className=\"flex items-center\">\n                                                    <div className=\"flex-shrink-0 h-10 w-10\">\n                                                        <div className=\"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\">\n                                                            <span className=\"text-blue-600 font-semibold\">{currency.currency_symbol}</span>\n                                                        </div>\n                                                    </div>\n                                                    <div className=\"ml-4\">\n                                                        <div className=\"text-sm font-medium text-gray-900\">{currency.currency_code}</div>\n                                                        <div className=\"text-sm text-gray-500\">{currency.currency_name}</div>\n                                                    </div>\n                                                </div>\n                                            </td>\n\n                                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                                                {rate ? (\n                                                    <div>\n                                                        <div className=\"text-sm font-medium text-gray-900\">\n                                                            1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}\n                                                        </div>\n                                                        <button\n                                                            onClick={() => {\n                                                                setEditingCurrency(currency);\n                                                                setNewRate(rate.rate_to_fancoin ? rate.rate_to_fancoin.toString() : '');\n                                                                setShowEditRateModal(true);\n                                                            }}\n                                                            className=\"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\"\n                                                        >\n                                                            <FaEdit />\n                                                            Update\n                                                        </button>\n                                                    </div>\n                                                ) : (\n                                                    <div>\n                                                        <span className=\"text-sm text-gray-500\">No rate set</span>\n                                                        <button\n                                                            onClick={() => {\n                                                                setEditingCurrency(currency);\n                                                                setNewRate('');\n                                                                setShowEditRateModal(true);\n                                                            }}\n                                                            className=\"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\"\n                                                        >\n                                                            <FaPlus />\n                                                            Set Rate\n                                                        </button>\n                                                    </div>\n                                                )}\n                                            </td>\n                                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                                                    currency.is_active\n                                                        ? 'bg-green-100 text-green-800'\n                                                        : 'bg-red-100 text-red-800'\n                                                }`}>\n                                                    {currency.is_active ? 'Active' : 'Inactive'}\n                                                </span>\n                                            </td>\n                                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                                {rate ? formatDate(rate.updated_at) : 'Never'}\n                                            </td>\n                                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                                <div className=\"flex items-center gap-2\">\n                                                    <button\n                                                        onClick={() => handleToggleCurrency(currency.id, currency.is_active)}\n                                                        className={`inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium transition-colors ${\n                                                            currency.is_active\n                                                                ? 'bg-red-500 text-white hover:bg-red-600 shadow-sm'\n                                                                : 'bg-green-500 text-white hover:bg-green-600 shadow-sm'\n                                                        }`}\n                                                        disabled={loading}\n                                                    >\n                                                        {currency.is_active ? <FaToggleOff /> : <FaToggleOn />}\n                                                        {currency.is_active ? 'Deactivate' : 'Activate'}\n                                                    </button>\n                                                    <button\n                                                        onClick={() => handleDeleteCurrency(currency.id, currency.currency_code)}\n                                                        className=\"inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium bg-gray-600 text-white hover:bg-gray-700 shadow-sm transition-colors\"\n                                                        disabled={loading}\n                                                        title={`Delete ${currency.currency_code} currency`}\n                                                    >\n                                                        <FaTrash />\n                                                        Delete\n                                                    </button>\n                                                </div>\n                                            </td>\n                                        </tr>\n                                    );\n                                })}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n            </div>\n\n            {/* Add Currency Modal */}\n            {showAddCurrency && (\n                <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n                    <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n                        <div className=\"mt-3\">\n                            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add New Currency</h3>\n\n                            <div className=\"space-y-4\">\n                                <div>\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                                        Currency Code (e.g., EUR, GBP)\n                                    </label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"currency_code\"\n                                        value={newCurrency.currency_code}\n                                        onChange={handleInputChange}\n                                        placeholder=\"USD\"\n                                        maxLength=\"3\"\n                                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                    />\n                                </div>\n\n                                <div>\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                                        Currency Name\n                                    </label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"currency_name\"\n                                        value={newCurrency.currency_name}\n                                        onChange={handleInputChange}\n                                        placeholder=\"US Dollar\"\n                                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                    />\n                                </div>\n\n                                <div>\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                                        Currency Symbol\n                                    </label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"currency_symbol\"\n                                        value={newCurrency.currency_symbol}\n                                        onChange={handleInputChange}\n                                        placeholder=\"$\"\n                                        maxLength=\"5\"\n                                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                    />\n                                </div>\n\n                                <div className=\"flex items-center\">\n                                    <input\n                                        type=\"checkbox\"\n                                        name=\"is_active\"\n                                        checked={newCurrency.is_active}\n                                        onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}\n                                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                    />\n                                    <label className=\"ml-2 block text-sm text-gray-900\">\n                                        Active\n                                    </label>\n                                </div>\n                            </div>\n\n                            <div className=\"flex justify-end gap-3 mt-6\">\n                                <button\n                                    onClick={() => setShowAddCurrency(false)}\n                                    className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg\"\n                                >\n                                    Cancel\n                                </button>\n                                <button\n                                    onClick={handleAddCurrency}\n                                    className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\"\n                                    disabled={loading}\n                                >\n                                    <FaPlus />\n                                    Add Currency\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* Edit Rate Modal */}\n            {showEditRateModal && editingCurrency && (\n                <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n                    <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n                        <div className=\"mt-3\">\n                            <div className=\"flex items-center justify-between mb-4\">\n                                <h3 className=\"text-lg font-medium text-gray-900\">\n                                    Update Exchange Rate\n                                </h3>\n                                <button\n                                    onClick={() => {\n                                        setShowEditRateModal(false);\n                                        setEditingCurrency(null);\n                                        setNewRate('');\n                                        setNotes('');\n                                    }}\n                                    className=\"text-gray-400 hover:text-gray-600\"\n                                >\n                                    <FaTimes />\n                                </button>\n                            </div>\n\n                            <div className=\"mb-4\">\n                                <div className=\"flex items-center mb-2\">\n                                    <span className=\"text-2xl mr-2\">{editingCurrency.currency_symbol}</span>\n                                    <div>\n                                        <div className=\"font-medium\">{editingCurrency.currency_code}</div>\n                                        <div className=\"text-sm text-gray-500\">{editingCurrency.currency_name}</div>\n                                    </div>\n                                </div>\n                            </div>\n\n                            <div className=\"mb-4\">\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Exchange Rate (1 FC = {editingCurrency.currency_symbol}?)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    step=\"0.0001\"\n                                    value={newRate}\n                                    onChange={(e) => setNewRate(e.target.value)}\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\"\n                                    placeholder=\"Enter exchange rate\"\n                                />\n                            </div>\n\n                            <div className=\"mb-6\">\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Update Notes (Optional)\n                                </label>\n                                <textarea\n                                    value={notes}\n                                    onChange={(e) => setNotes(e.target.value)}\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\"\n                                    rows=\"3\"\n                                    placeholder=\"Add notes about this rate update...\"\n                                />\n                            </div>\n\n                            <div className=\"flex justify-end space-x-3\">\n                                <button\n                                    onClick={() => {\n                                        setShowEditRateModal(false);\n                                        setEditingCurrency(null);\n                                        setNewRate('');\n                                        setNotes('');\n                                    }}\n                                    className=\"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n                                >\n                                    Cancel\n                                </button>\n                                <button\n                                    onClick={handleUpdateRate}\n                                    disabled={loading || !newRate}\n                                    className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\n                                >\n                                    <FaSave />\n                                    {loading ? 'Saving...' : 'Save Rate'}\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n}\n\nexport default CurrencyManagement;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,GAEA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,sBAAsB,CACxC,OAASC,YAAY,KAAQ,WAAW,CACxC,OAASC,OAAO,CAAEC,MAAM,CAAEC,UAAU,CAAEC,WAAW,CAAEC,MAAM,CAAEC,MAAM,CAAEC,OAAO,CAAEC,aAAa,CAAEC,OAAO,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3H,QAAS,CAAAC,kBAAkBA,CAAA,CAAG,CAC1B,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACoB,aAAa,CAAEC,gBAAgB,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACwB,KAAK,CAAEC,QAAQ,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CAE1C;AACA,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAG7B,QAAQ,CAAC,CAC/B8B,eAAe,CAAE,CAAC,CAClBC,gBAAgB,CAAE,CAAC,CACnBC,kBAAkB,CAAE,CAAC,CACrBC,WAAW,CAAE,IACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACoC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACsC,eAAe,CAAEC,kBAAkB,CAAC,CAAGvC,QAAQ,CAAC,IAAI,CAAC,CAE5D;AACA,KAAM,CAACwC,WAAW,CAAEC,cAAc,CAAC,CAAGzC,QAAQ,CAAC,CAC3C0C,aAAa,CAAE,EAAE,CACjBC,aAAa,CAAE,EAAE,CACjBC,eAAe,CAAE,EAAE,CACnBC,SAAS,CAAE,IACf,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACgD,KAAK,CAAEC,QAAQ,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAEtCC,SAAS,CAAC,IAAM,CACZiD,cAAc,CAAC,CAAC,CAChBC,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,cAAc,CAAG,KAAAA,CAAA,GAAY,CAC/B3B,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACA,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACmD,GAAG,CAAC,oBAAoB,CAAC,CACtD,GAAID,QAAQ,CAACE,IAAI,CAAC5B,OAAO,CAAE,CACvB,KAAM,CAAA6B,YAAY,CAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACpC,UAAU,EAAI,EAAE,CACxDC,aAAa,CAACoC,YAAY,CAAC,CAE3B;AACA,KAAM,CAAAzB,eAAe,CAAGyB,YAAY,CAACC,MAAM,CAC3C,KAAM,CAAAzB,gBAAgB,CAAGwB,YAAY,CAACE,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACb,SAAS,CAAC,CAACW,MAAM,CACrE,KAAM,CAAAxB,kBAAkB,CAAGF,eAAe,CAAGC,gBAAgB,CAE7DF,QAAQ,CAAC,CACLC,eAAe,CACfC,gBAAgB,CAChBC,kBAAkB,CAClBC,WAAW,CAAE,GAAI,CAAA0B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACxC,CAAC,CAAC,CACN,CAAC,IAAM,CACHnC,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,EAAI,2BAA2B,CAAC,CAClE,CACJ,CAAE,MAAOC,GAAG,CAAE,CACVC,OAAO,CAACvC,KAAK,CAAC,2BAA2B,CAAEsC,GAAG,CAAC,CAC/CrC,QAAQ,CAAC,kEAAkE,CAAC,CAChF,CAAC,OAAS,CACNF,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAED,KAAM,CAAA4B,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACmD,GAAG,CAAC,wBAAwB,CAAC,CAC1D,GAAID,QAAQ,CAACE,IAAI,CAAC5B,OAAO,CAAE,CACvBL,gBAAgB,CAAC+B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACU,cAAc,EAAI,EAAE,CAAC,CAC7D,CACJ,CAAE,MAAOF,GAAG,CAAE,CACVC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,CAAEsC,GAAG,CAAC,CACvD,CACJ,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CAAC3B,eAAe,CAAE,OAEtB,GAAI,CAACQ,OAAO,EAAIoB,KAAK,CAACC,UAAU,CAACrB,OAAO,CAAC,CAAC,CAAE,CACxCrB,QAAQ,CAAC,oCAAoC,CAAC,CAC9C,OACJ,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACA,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACkE,IAAI,CAAC,GAAGjE,YAAY,oCAAoC,CAAE,CACnFkE,WAAW,CAAE/B,eAAe,CAACgC,EAAE,CAC/BC,eAAe,CAAEJ,UAAU,CAACrB,OAAO,CAAC,CACpCE,KAAK,CAAEA,KACX,CAAC,CAAC,CAEF,GAAII,QAAQ,CAACE,IAAI,CAAC5B,OAAO,CAAE,CACvBC,UAAU,CAAC,qCAAqC,CAAC,CACjDwB,iBAAiB,CAAC,CAAC,CACnBd,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,kBAAkB,CAAC,IAAI,CAAC,CACxBQ,UAAU,CAAC,EAAE,CAAC,CACdE,QAAQ,CAAC,EAAE,CAAC,CACZuB,UAAU,CAAC,IAAM7C,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAC,IAAM,CACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,EAAI,gCAAgC,CAAC,CACnEW,UAAU,CAAC,IAAM/C,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAE,MAAOqC,GAAG,CAAE,CACVC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,CAAEsC,GAAG,CAAC,CACnDrC,QAAQ,CAAC,mDAAmD,CAAC,CAC7D+C,UAAU,CAAC,IAAM/C,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CAAC,OAAS,CACNF,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAED,KAAM,CAAAkD,oBAAoB,CAAG,KAAAA,CAAOC,UAAU,CAAEC,aAAa,GAAK,CAC9DpD,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACA,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACkE,IAAI,CAAC,uBAAuB,CAAE,CACvDQ,MAAM,CAAE,QAAQ,CAChBP,WAAW,CAAEK,UACjB,CAAC,CAAC,CAEF,GAAItB,QAAQ,CAACE,IAAI,CAAC5B,OAAO,CAAE,CACvBC,UAAU,CAAC,YAAYgD,aAAa,CAAG,aAAa,CAAG,WAAW,gBAAgB,CAAC,CACnFzB,cAAc,CAAC,CAAC,CAChBsB,UAAU,CAAC,IAAM7C,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAC,IAAM,CACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,EAAI,kCAAkC,CAAC,CACrEW,UAAU,CAAC,IAAM/C,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAE,MAAOqC,GAAG,CAAE,CACVC,OAAO,CAACvC,KAAK,CAAC,0BAA0B,CAAEsC,GAAG,CAAC,CAC9CrC,QAAQ,CAAC,qDAAqD,CAAC,CAC/D+C,UAAU,CAAC,IAAM/C,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CAAC,OAAS,CACNF,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAED,KAAM,CAAAsD,oBAAoB,CAAG,KAAAA,CAAOH,UAAU,CAAEI,YAAY,GAAK,CAC7D;AACA,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,iDAAiDF,YAAY,gFAAgF,CAAC,CAAE,CAChK,OACJ,CAEAvD,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACA,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACkE,IAAI,CAAC,uBAAuB,CAAE,CACvDQ,MAAM,CAAE,QAAQ,CAChBP,WAAW,CAAEK,UACjB,CAAC,CAAC,CAEF,GAAItB,QAAQ,CAACE,IAAI,CAAC5B,OAAO,CAAE,CACvBC,UAAU,CAAC,aAAamD,YAAY,yBAAyB,CAAC,CAC9D5B,cAAc,CAAC,CAAC,CAChBC,iBAAiB,CAAC,CAAC,CACnBqB,UAAU,CAAC,IAAM7C,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAC,IAAM,CACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,EAAI,2BAA2B,CAAC,CAC9DW,UAAU,CAAC,IAAM/C,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAE,MAAOqC,GAAG,CAAE,CACVC,OAAO,CAACvC,KAAK,CAAC,0BAA0B,CAAEsC,GAAG,CAAC,CAC9CrC,QAAQ,CAAC,8CAA8C,CAAC,CACxD+C,UAAU,CAAC,IAAM/C,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CAAC,OAAS,CACNF,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAED,KAAM,CAAA0D,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAACzC,WAAW,CAACE,aAAa,EAAI,CAACF,WAAW,CAACG,aAAa,EAAI,CAACH,WAAW,CAACI,eAAe,CAAE,CAC1FnB,QAAQ,CAAC,oCAAoC,CAAC,CAC9C,OACJ,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACA,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACkE,IAAI,CAAC,uBAAuB,CAAE,CACvDQ,MAAM,CAAE,QAAQ,CAChB,GAAGpC,WACP,CAAC,CAAC,CAEF,GAAIY,QAAQ,CAACE,IAAI,CAAC5B,OAAO,CAAE,CACvBC,UAAU,CAAC,8BAA8B,CAAC,CAC1CuB,cAAc,CAAC,CAAC,CAChBf,kBAAkB,CAAC,KAAK,CAAC,CACzBM,cAAc,CAAC,CACXC,aAAa,CAAE,EAAE,CACjBC,aAAa,CAAE,EAAE,CACjBC,eAAe,CAAE,EAAE,CACnBC,SAAS,CAAE,IACf,CAAC,CAAC,CACF2B,UAAU,CAAC,IAAM7C,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAC,IAAM,CACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,EAAI,wBAAwB,CAAC,CAC3DW,UAAU,CAAC,IAAM/C,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAE,MAAOqC,GAAG,CAAE,CACVC,OAAO,CAACvC,KAAK,CAAC,wBAAwB,CAAEsC,GAAG,CAAC,CAC5CrC,QAAQ,CAAC,2CAA2C,CAAC,CACrD+C,UAAU,CAAC,IAAM/C,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CAAC,OAAS,CACNF,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAED,KAAM,CAAA2D,eAAe,CAAIR,UAAU,EAAK,CACpC,MAAO,CAAAtD,aAAa,CAAC+D,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACf,WAAW,GAAKK,UAAU,CAAC,CACtE,CAAC,CAED,KAAM,CAAAW,UAAU,CAAIC,UAAU,EAAK,CAC/B,MAAO,IAAI,CAAA3B,IAAI,CAAC2B,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAE,CACpDC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACZ,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIC,CAAC,EAAK,CAC7B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChCxD,cAAc,CAACyD,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACH,IAAI,EAAGC,KAAM,CAAC,CAAC,CAAC,CACxD,CAAC,CAED,mBACIhF,KAAA,QAAKmF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAExCtF,IAAA,QAAKqF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBpF,KAAA,QAAKmF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC9CpF,KAAA,QAAAoF,QAAA,eACItF,IAAA,OAAIqF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cACzEtF,IAAA,MAAGqF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gDAA8C,CAAG,CAAC,EAC9E,CAAC,cACNpF,KAAA,WACImF,SAAS,CAAC,uFAAuF,CACjGE,OAAO,CAAEA,CAAA,GAAMlE,kBAAkB,CAAC,IAAI,CAAE,CACxCmE,QAAQ,CAAEhF,OAAQ,CAAA8E,QAAA,eAElBtF,IAAA,CAACN,MAAM,GAAE,CAAC,mBAEd,EAAQ,CAAC,EACR,CAAC,CACL,CAAC,CAGLgB,KAAK,eACFV,IAAA,QAAKqF,SAAS,CAAC,+EAA+E,CAACI,IAAI,CAAC,OAAO,CAAAH,QAAA,cACvGtF,IAAA,SAAMqF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAE5E,KAAK,CAAO,CAAC,CAC/C,CACR,CACAE,OAAO,eACJZ,IAAA,QAAKqF,SAAS,CAAC,qFAAqF,CAACI,IAAI,CAAC,OAAO,CAAAH,QAAA,cAC7GtF,IAAA,SAAMqF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAE1E,OAAO,CAAO,CAAC,CACjD,CACR,cAGDV,KAAA,QAAKmF,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eAEtEpF,KAAA,QAAKmF,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAChEtF,IAAA,QAAKqF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAC9CtF,IAAA,CAACV,OAAO,EAAC+F,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC5C,CAAC,cACNnF,KAAA,QAAAoF,QAAA,eACItF,IAAA,MAAGqF,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAAC,cAClFtF,IAAA,OAAIqF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAExE,KAAK,CAACE,eAAe,CAAK,CAAC,EAC5E,CAAC,EACL,CAAC,cAGNd,KAAA,QAAKmF,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAChEtF,IAAA,QAAKqF,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cAC/CtF,IAAA,CAACR,UAAU,EAAC6F,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAChD,CAAC,cACNnF,KAAA,QAAAoF,QAAA,eACItF,IAAA,MAAGqF,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,mBAAiB,CAAG,CAAC,cACnFtF,IAAA,OAAIqF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAExE,KAAK,CAACG,gBAAgB,CAAK,CAAC,EAC7E,CAAC,EACL,CAAC,cAGNf,KAAA,QAAKmF,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAChEtF,IAAA,QAAKqF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAC7CtF,IAAA,CAACP,WAAW,EAAC4F,SAAS,CAAC,sBAAsB,CAAE,CAAC,CAC/C,CAAC,cACNnF,KAAA,QAAAoF,QAAA,eACItF,IAAA,MAAGqF,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,qBAAmB,CAAG,CAAC,cACrFtF,IAAA,OAAIqF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAExE,KAAK,CAACI,kBAAkB,CAAK,CAAC,EAC/E,CAAC,EACL,CAAC,cAGNhB,KAAA,QAAKmF,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAChEtF,IAAA,QAAKqF,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAChDtF,IAAA,CAACH,aAAa,EAACwF,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACpD,CAAC,cACNnF,KAAA,QAAAoF,QAAA,eACItF,IAAA,MAAGqF,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,cAChFtF,IAAA,OAAIqF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEhF,aAAa,CAACoC,MAAM,CAAK,CAAC,EAC3E,CAAC,EACL,CAAC,EACL,CAAC,cAGNxC,KAAA,QAAKmF,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC1DtF,IAAA,QAAKqF,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cAC/CtF,IAAA,OAAIqF,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,6BAA2B,CAAI,CAAC,CACnF,CAAC,CAEL9E,OAAO,EAAIJ,UAAU,CAACsC,MAAM,GAAK,CAAC,cAC/BxC,KAAA,QAAKmF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BtF,IAAA,QAAKqF,SAAS,CAAC,2EAA2E,CAAM,CAAC,cACjGrF,IAAA,MAAGqF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,uBAAqB,CAAG,CAAC,EAC1D,CAAC,cAENtF,IAAA,QAAKqF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC5BpF,KAAA,UAAOmF,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDtF,IAAA,UAAOqF,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBpF,KAAA,OAAAoF,QAAA,eACItF,IAAA,OAAIqF,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC5GtF,IAAA,OAAIqF,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACjHtF,IAAA,OAAIqF,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,cAC1GtF,IAAA,OAAIqF,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAChHtF,IAAA,OAAIqF,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,EAC3G,CAAC,CACF,CAAC,cACRtF,IAAA,UAAOqF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/ClF,UAAU,CAACsF,GAAG,CAAEC,QAAQ,EAAK,CAC1B,KAAM,CAAArB,IAAI,CAAGF,eAAe,CAACuB,QAAQ,CAACnC,EAAE,CAAC,CAEzC,mBACItD,KAAA,OAAsBmF,SAAS,CAAE,GAAG,CAACM,QAAQ,CAAC5D,SAAS,CAAG,YAAY,CAAG,EAAE,EAAG,CAAAuD,QAAA,eAC1EtF,IAAA,OAAIqF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvCpF,KAAA,QAAKmF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC9BtF,IAAA,QAAKqF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACpCtF,IAAA,QAAKqF,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAChFtF,IAAA,SAAMqF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAEK,QAAQ,CAAC7D,eAAe,CAAO,CAAC,CAC9E,CAAC,CACL,CAAC,cACN5B,KAAA,QAAKmF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBtF,IAAA,QAAKqF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEK,QAAQ,CAAC/D,aAAa,CAAM,CAAC,cACjF5B,IAAA,QAAKqF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEK,QAAQ,CAAC9D,aAAa,CAAM,CAAC,EACpE,CAAC,EACL,CAAC,CACN,CAAC,cAEL7B,IAAA,OAAIqF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CACtChB,IAAI,cACDpE,KAAA,QAAAoF,QAAA,eACIpF,KAAA,QAAKmF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,SACxC,CAACK,QAAQ,CAAC7D,eAAe,CAAEwC,IAAI,CAACb,eAAe,EACrD,CAAC,cACNvD,KAAA,WACIqF,OAAO,CAAEA,CAAA,GAAM,CACX9D,kBAAkB,CAACkE,QAAQ,CAAC,CAC5B1D,UAAU,CAACqC,IAAI,CAACb,eAAe,CAAGa,IAAI,CAACb,eAAe,CAACmC,QAAQ,CAAC,CAAC,CAAG,EAAE,CAAC,CACvErE,oBAAoB,CAAC,IAAI,CAAC,CAC9B,CAAE,CACF8D,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFtF,IAAA,CAACT,MAAM,GAAE,CAAC,SAEd,EAAQ,CAAC,EACR,CAAC,cAENW,KAAA,QAAAoF,QAAA,eACItF,IAAA,SAAMqF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,cAC1DpF,KAAA,WACIqF,OAAO,CAAEA,CAAA,GAAM,CACX9D,kBAAkB,CAACkE,QAAQ,CAAC,CAC5B1D,UAAU,CAAC,EAAE,CAAC,CACdV,oBAAoB,CAAC,IAAI,CAAC,CAC9B,CAAE,CACF8D,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFtF,IAAA,CAACN,MAAM,GAAE,CAAC,WAEd,EAAQ,CAAC,EACR,CACR,CACD,CAAC,cACLM,IAAA,OAAIqF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvCtF,IAAA,SAAMqF,SAAS,CAAE,4DACbM,QAAQ,CAAC5D,SAAS,CACZ,6BAA6B,CAC7B,yBAAyB,EAChC,CAAAuD,QAAA,CACEK,QAAQ,CAAC5D,SAAS,CAAG,QAAQ,CAAG,UAAU,CACzC,CAAC,CACP,CAAC,cACL/B,IAAA,OAAIqF,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC5DhB,IAAI,CAAGC,UAAU,CAACD,IAAI,CAACuB,UAAU,CAAC,CAAG,OAAO,CAC7C,CAAC,cACL7F,IAAA,OAAIqF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC3DpF,KAAA,QAAKmF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACpCpF,KAAA,WACIqF,OAAO,CAAEA,CAAA,GAAM5B,oBAAoB,CAACgC,QAAQ,CAACnC,EAAE,CAAEmC,QAAQ,CAAC5D,SAAS,CAAE,CACrEsD,SAAS,CAAE,0FACPM,QAAQ,CAAC5D,SAAS,CACZ,kDAAkD,CAClD,sDAAsD,EAC7D,CACHyD,QAAQ,CAAEhF,OAAQ,CAAA8E,QAAA,EAEjBK,QAAQ,CAAC5D,SAAS,cAAG/B,IAAA,CAACP,WAAW,GAAE,CAAC,cAAGO,IAAA,CAACR,UAAU,GAAE,CAAC,CACrDmG,QAAQ,CAAC5D,SAAS,CAAG,YAAY,CAAG,UAAU,EAC3C,CAAC,cACT7B,KAAA,WACIqF,OAAO,CAAEA,CAAA,GAAMxB,oBAAoB,CAAC4B,QAAQ,CAACnC,EAAE,CAAEmC,QAAQ,CAAC/D,aAAa,CAAE,CACzEyD,SAAS,CAAC,2IAA2I,CACrJG,QAAQ,CAAEhF,OAAQ,CAClBsF,KAAK,CAAE,UAAUH,QAAQ,CAAC/D,aAAa,WAAY,CAAA0D,QAAA,eAEnDtF,IAAA,CAACF,OAAO,GAAE,CAAC,SAEf,EAAQ,CAAC,EACR,CAAC,CACN,CAAC,GAtFA6F,QAAQ,CAACnC,EAuFd,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,EACL,CAAC,CACP,CACR,EACA,CAAC,CAGLpC,eAAe,eACZpB,IAAA,QAAKqF,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACvFtF,IAAA,QAAKqF,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cAClFpF,KAAA,QAAKmF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBtF,IAAA,OAAIqF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAE5EpF,KAAA,QAAKmF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBpF,KAAA,QAAAoF,QAAA,eACItF,IAAA,UAAOqF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,gCAEhE,CAAO,CAAC,cACRtF,IAAA,UACI+F,IAAI,CAAC,MAAM,CACXd,IAAI,CAAC,eAAe,CACpBC,KAAK,CAAExD,WAAW,CAACE,aAAc,CACjCoE,QAAQ,CAAEjB,iBAAkB,CAC5BkB,WAAW,CAAC,KAAK,CACjBC,SAAS,CAAC,GAAG,CACbb,SAAS,CAAC,wGAAwG,CACrH,CAAC,EACD,CAAC,cAENnF,KAAA,QAAAoF,QAAA,eACItF,IAAA,UAAOqF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAEhE,CAAO,CAAC,cACRtF,IAAA,UACI+F,IAAI,CAAC,MAAM,CACXd,IAAI,CAAC,eAAe,CACpBC,KAAK,CAAExD,WAAW,CAACG,aAAc,CACjCmE,QAAQ,CAAEjB,iBAAkB,CAC5BkB,WAAW,CAAC,WAAW,CACvBZ,SAAS,CAAC,wGAAwG,CACrH,CAAC,EACD,CAAC,cAENnF,KAAA,QAAAoF,QAAA,eACItF,IAAA,UAAOqF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,iBAEhE,CAAO,CAAC,cACRtF,IAAA,UACI+F,IAAI,CAAC,MAAM,CACXd,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAExD,WAAW,CAACI,eAAgB,CACnCkE,QAAQ,CAAEjB,iBAAkB,CAC5BkB,WAAW,CAAC,GAAG,CACfC,SAAS,CAAC,GAAG,CACbb,SAAS,CAAC,wGAAwG,CACrH,CAAC,EACD,CAAC,cAENnF,KAAA,QAAKmF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC9BtF,IAAA,UACI+F,IAAI,CAAC,UAAU,CACfd,IAAI,CAAC,WAAW,CAChBkB,OAAO,CAAEzE,WAAW,CAACK,SAAU,CAC/BiE,QAAQ,CAAGhB,CAAC,EAAKrD,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEK,SAAS,CAAEiD,CAAC,CAACG,MAAM,CAACgB,OAAO,CAAC,CAAE,CAC/Ed,SAAS,CAAC,mEAAmE,CAChF,CAAC,cACFrF,IAAA,UAAOqF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,QAEpD,CAAO,CAAC,EACP,CAAC,EACL,CAAC,cAENpF,KAAA,QAAKmF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACxCtF,IAAA,WACIuF,OAAO,CAAEA,CAAA,GAAMlE,kBAAkB,CAAC,KAAK,CAAE,CACzCgE,SAAS,CAAC,kEAAkE,CAAAC,QAAA,CAC/E,QAED,CAAQ,CAAC,cACTpF,KAAA,WACIqF,OAAO,CAAEpB,iBAAkB,CAC3BkB,SAAS,CAAC,uFAAuF,CACjGG,QAAQ,CAAEhF,OAAQ,CAAA8E,QAAA,eAElBtF,IAAA,CAACN,MAAM,GAAE,CAAC,eAEd,EAAQ,CAAC,EACR,CAAC,EACL,CAAC,CACL,CAAC,CACL,CACR,CAGA4B,iBAAiB,EAAIE,eAAe,eACjCxB,IAAA,QAAKqF,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACvFtF,IAAA,QAAKqF,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cAClFpF,KAAA,QAAKmF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBpF,KAAA,QAAKmF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACnDtF,IAAA,OAAIqF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,sBAElD,CAAI,CAAC,cACLtF,IAAA,WACIuF,OAAO,CAAEA,CAAA,GAAM,CACXhE,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,kBAAkB,CAAC,IAAI,CAAC,CACxBQ,UAAU,CAAC,EAAE,CAAC,CACdE,QAAQ,CAAC,EAAE,CAAC,CAChB,CAAE,CACFkD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7CtF,IAAA,CAACJ,OAAO,GAAE,CAAC,CACP,CAAC,EACR,CAAC,cAENI,IAAA,QAAKqF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBpF,KAAA,QAAKmF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACnCtF,IAAA,SAAMqF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE9D,eAAe,CAACM,eAAe,CAAO,CAAC,cACxE5B,KAAA,QAAAoF,QAAA,eACItF,IAAA,QAAKqF,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE9D,eAAe,CAACI,aAAa,CAAM,CAAC,cAClE5B,IAAA,QAAKqF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE9D,eAAe,CAACK,aAAa,CAAM,CAAC,EAC3E,CAAC,EACL,CAAC,CACL,CAAC,cAEN3B,KAAA,QAAKmF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBpF,KAAA,UAAOmF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,EAAC,wBACtC,CAAC9D,eAAe,CAACM,eAAe,CAAC,IAC3D,EAAO,CAAC,cACR9B,IAAA,UACI+F,IAAI,CAAC,QAAQ,CACbK,IAAI,CAAC,QAAQ,CACblB,KAAK,CAAElD,OAAQ,CACfgE,QAAQ,CAAGhB,CAAC,EAAK/C,UAAU,CAAC+C,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE,CAC5CG,SAAS,CAAC,6HAA6H,CACvIY,WAAW,CAAC,qBAAqB,CACpC,CAAC,EACD,CAAC,cAEN/F,KAAA,QAAKmF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBtF,IAAA,UAAOqF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,yBAEhE,CAAO,CAAC,cACRtF,IAAA,aACIkF,KAAK,CAAEhD,KAAM,CACb8D,QAAQ,CAAGhB,CAAC,EAAK7C,QAAQ,CAAC6C,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE,CAC1CG,SAAS,CAAC,6HAA6H,CACvIgB,IAAI,CAAC,GAAG,CACRJ,WAAW,CAAC,qCAAqC,CACpD,CAAC,EACD,CAAC,cAEN/F,KAAA,QAAKmF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACvCtF,IAAA,WACIuF,OAAO,CAAEA,CAAA,GAAM,CACXhE,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,kBAAkB,CAAC,IAAI,CAAC,CACxBQ,UAAU,CAAC,EAAE,CAAC,CACdE,QAAQ,CAAC,EAAE,CAAC,CAChB,CAAE,CACFkD,SAAS,CAAC,uIAAuI,CAAAC,QAAA,CACpJ,QAED,CAAQ,CAAC,cACTpF,KAAA,WACIqF,OAAO,CAAEpC,gBAAiB,CAC1BqC,QAAQ,CAAEhF,OAAO,EAAI,CAACwB,OAAQ,CAC9BqD,SAAS,CAAC,kNAAkN,CAAAC,QAAA,eAE5NtF,IAAA,CAACL,MAAM,GAAE,CAAC,CACTa,OAAO,CAAG,WAAW,CAAG,WAAW,EAChC,CAAC,EACR,CAAC,EACL,CAAC,CACL,CAAC,CACL,CACR,EACA,CAAC,CAEd,CAEA,cAAe,CAAAL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}