<?php
/**
 * Setup Admin Authentication Tables
 * This script creates all necessary tables for the enhanced admin authentication system
 */

// Include database connection
require_once '../includes/db_connect.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Read and execute the SQL schema file
    $sqlFile = __DIR__ . '/admin_authentication_schema.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL schema file not found: " . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    
    if ($sql === false) {
        throw new Exception("Failed to read SQL schema file");
    }
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $results = [];
    $conn->beginTransaction();
    
    foreach ($statements as $index => $statement) {
        try {
            if (trim($statement)) {
                $conn->exec($statement);
                $results[] = [
                    'statement' => $index + 1,
                    'status' => 'success',
                    'message' => 'Executed successfully'
                ];
            }
        } catch (PDOException $e) {
            // Log the error but continue with other statements
            $results[] = [
                'statement' => $index + 1,
                'status' => 'error',
                'message' => $e->getMessage(),
                'sql' => substr($statement, 0, 100) . '...'
            ];
        }
    }
    
    $conn->commit();
    
    // Verify tables were created
    $tables = ['admin_auth_settings', 'admin_2fa', 'admin_otp', 'admin_auth_logs', 'admin_login_attempts'];
    $verification = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->rowCount() > 0;
            $verification[$table] = $exists ? 'exists' : 'missing';
        } catch (PDOException $e) {
            $verification[$table] = 'error: ' . $e->getMessage();
        }
    }
    
    // Check if default settings were inserted
    $stmt = $conn->query("SELECT COUNT(*) as count FROM admin_auth_settings");
    $settingsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo json_encode([
        'success' => true,
        'message' => 'Admin authentication tables setup completed',
        'results' => $results,
        'verification' => $verification,
        'settings_count' => $settingsCount,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to setup admin authentication tables',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
