<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

$conn = getDBConnection();
$conn->beginTransaction();

try {
    $user_id = $_POST['user_id'];
    $amount = $_POST['amount'];
    $admin_id = $_POST['admin_id'] ?? 1; // Get from admin session

    // Validate inputs
    if (!$user_id || !$amount || $amount <= 0) {
        throw new Exception('Invalid input parameters');
    }

    // Update user balance
    $updateBalance = $conn->prepare("
        UPDATE users 
        SET balance = balance + :amount 
        WHERE user_id = :user_id
    ");
    $updateBalance->execute([
        ':amount' => $amount,
        ':user_id' => $user_id
    ]);

    // Record transaction
    $recordTransaction = $conn->prepare("
        INSERT INTO transactions 
        (user_id, amount, type, status, admin_id) 
        VALUES 
        (:user_id, :amount, 'admin_credit', 'completed', :admin_id)
    ");
    $recordTransaction->execute([
        ':user_id' => $user_id,
        ':amount' => $amount,
        ':admin_id' => $admin_id
    ]);

    // Record admin action
    $recordAction = $conn->prepare("
        INSERT INTO adminactions 
        (admin_id, action_type, details) 
        VALUES 
        (:admin_id, 'points_awarded', :details)
    ");
    $recordAction->execute([
        ':admin_id' => $admin_id,
        ':details' => "Credited user ID: $user_id with amount: $amount FC"
    ]);

    $conn->commit();
    echo json_encode(['success' => true, 'message' => 'User credited successfully']);

} catch (Exception $e) {
    $conn->rollBack();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
