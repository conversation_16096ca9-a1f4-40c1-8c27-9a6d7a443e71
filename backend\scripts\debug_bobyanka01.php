<?php
require_once __DIR__ . '/../includes/db_connect.php';

$conn = getDBConnection();
if (!$conn) {
    die("DB connection failed\n");
}

$username = '<PERSON>yanka<PERSON>';

$stmt = $conn->prepare("SELECT user_id, username, email, status, otp_enabled, tfa_enabled, auth_method FROM users WHERE username = ?");
$stmt->execute([$username]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    echo "User not found\n";
    exit(1);
}

print_r($user);

// Check user_2fa
$stmt = $conn->prepare("SELECT * FROM user_2fa WHERE user_id = ?");
$stmt->execute([$user['user_id']]);
$user2fa = $stmt->fetch(PDO::FETCH_ASSOC);

echo "\nuser_2fa: ";
print_r($user2fa);

// Check user_otp
$stmt = $conn->prepare("SELECT * FROM user_otp WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
$stmt->execute([$user['user_id']]);
$userotp = $stmt->fetch(PDO::FETCH_ASSOC);

echo "\nuser_otp: ";
print_r($userotp);
