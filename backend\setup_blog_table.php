<?php
/**
 * Setup script for blog_posts table
 * Run this script to create the blog_posts table and insert sample data
 */

include_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    echo "<h1>Blog Posts Table Setup</h1>\n";
    echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; }</style>\n";
    
    // Read and execute the SQL file
    $sqlFile = 'sql/create_blog_posts_table.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    if ($sql === false) {
        throw new Exception("Failed to read SQL file");
    }
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $conn->exec($statement);
            $successCount++;
            echo "<p class='success'>✅ Executed: " . substr($statement, 0, 50) . "...</p>\n";
        } catch (PDOException $e) {
            $errorCount++;
            echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>\n";
            echo "<p>Statement: " . substr($statement, 0, 100) . "...</p>\n";
        }
    }
    
    echo "<h2>Setup Complete!</h2>\n";
    echo "<p>Successful statements: $successCount</p>\n";
    echo "<p>Failed statements: $errorCount</p>\n";
    
    // Verify the table was created
    $stmt = $conn->query("SHOW TABLES LIKE 'blog_posts'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ blog_posts table created successfully!</p>\n";
        
        // Check if data was inserted
        $stmt = $conn->query("SELECT COUNT(*) as count FROM blog_posts");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p class='success'>✅ $count blog posts inserted!</p>\n";
        
        // Show sample data
        $stmt = $conn->query("SELECT title, author_name, category, published_at FROM blog_posts ORDER BY published_at DESC LIMIT 3");
        $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Sample Blog Posts:</h3>\n";
        echo "<ul>\n";
        foreach ($posts as $post) {
            echo "<li><strong>" . htmlspecialchars($post['title']) . "</strong> by " . htmlspecialchars($post['author_name']) . " (" . $post['category'] . ")</li>\n";
        }
        echo "</ul>\n";
        
    } else {
        echo "<p class='error'>❌ blog_posts table was not created!</p>\n";
    }
    
    echo "<h3>Next Steps:</h3>\n";
    echo "<ul>\n";
    echo "<li>Test the homepage API: <a href='test_homepage_api.php'>test_homepage_api.php</a></li>\n";
    echo "<li>The blog section will now show real data instead of sample data</li>\n";
    echo "<li>You can add more blog posts through the admin panel (when implemented)</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Setup failed: " . $e->getMessage() . "</p>\n";
}
?>