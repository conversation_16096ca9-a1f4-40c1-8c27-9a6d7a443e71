<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$data = json_decode(file_get_contents("php://input"));

if (!isset($data->bet_id) || !isset($data->status)) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

try {
    $sql = "UPDATE bets SET bet_status = :status WHERE bet_id = :bet_id";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':status', $data->status);
    $stmt->bindParam(':bet_id', $data->bet_id);
    $stmt->execute();

    echo json_encode(['success' => true, 'message' => 'Bet status updated successfully']);
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
