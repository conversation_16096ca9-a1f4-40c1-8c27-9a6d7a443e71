-- =====================================================
-- FanBet247 User Authentication Enhancement Schema
-- =====================================================
-- This script creates the necessary tables for user dual authentication (OTP + 2FA)

-- Create User 2FA Table (mirror of admin_2fa)
CREATE TABLE IF NOT EXISTS user_2fa (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    secret_key VARCHAR(255),
    is_enabled BOOLEAN NOT NULL DEFAULT 0,
    auth_type ENUM('email_otp', 'google_auth') NOT NULL DEFAULT 'email_otp',
    backup_codes TEXT,
    setup_completed BOOLEAN NOT NULL DEFAULT 0,
    last_used TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_2fa (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create User OTP Table (mirror of admin_otp)
CREATE TABLE IF NOT EXISTS user_otp (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    otp VARCHAR(10) NOT NULL,
    expires_at DATETIME NOT NULL,
    attempts INT NOT NULL DEFAULT 0,
    locked_until DATETIME NULL,
    used BOOLEAN NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_otp_user_id (user_id),
    INDEX idx_user_otp_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create User Login Attempts Table (mirror of admin_login_attempts)
CREATE TABLE IF NOT EXISTS user_login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_type ENUM('login', 'otp', '2fa') NOT NULL DEFAULT 'login',
    attempts INT NOT NULL DEFAULT 1,
    locked_until DATETIME NULL,
    last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_attempt (user_id, attempt_type),
    INDEX idx_user_attempts_ip (ip_address),
    INDEX idx_user_attempts_locked (locked_until)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create User Auth Logs Table (mirror of admin_auth_logs)
CREATE TABLE IF NOT EXISTS user_auth_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    auth_type ENUM('login', 'otp', '2fa', 'logout') NOT NULL,
    action VARCHAR(50) NOT NULL,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_auth_logs_user_id (user_id),
    INDEX idx_user_auth_logs_type (auth_type),
    INDEX idx_user_auth_logs_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create User Auth Settings Table (for user-specific auth preferences)
CREATE TABLE IF NOT EXISTS user_auth_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    setting_name VARCHAR(100) NOT NULL,
    setting_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_setting (user_id, setting_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default user auth settings (these will be applied per user when they first access settings)
-- Note: These are template settings that will be copied per user when needed

-- Add indexes for better performance
ALTER TABLE users ADD INDEX idx_users_email (email);
ALTER TABLE users ADD INDEX idx_users_username (username);

-- Add 2FA preference columns to users table if they don't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS otp_enabled BOOLEAN DEFAULT 0,
ADD COLUMN IF NOT EXISTS tfa_enabled BOOLEAN DEFAULT 0,
ADD COLUMN IF NOT EXISTS auth_method ENUM('password_only', 'password_otp', 'password_2fa', 'password_otp_2fa') DEFAULT 'password_only';

COMMIT;
