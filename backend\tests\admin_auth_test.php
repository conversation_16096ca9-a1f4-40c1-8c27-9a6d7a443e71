<?php
/**
 * Admin Authentication System Test Suite
 * Comprehensive testing for the enhanced admin authentication system
 */

require_once '../includes/db_connect.php';
require_once '../includes/rate_limiter.php';
require_once '../includes/audit_logger.php';
require_once '../includes/error_handler.php';

class AdminAuthTestSuite {
    private $conn;
    private $testResults = [];
    private $testAdmin = null;

    public function __construct() {
        $this->conn = getDBConnection();
        if (!$this->conn) {
            throw new Exception("Database connection failed");
        }
    }

    /**
     * Run all tests
     */
    public function runAllTests() {
        echo "🚀 Starting Admin Authentication Test Suite\n";
        echo "==========================================\n\n";

        try {
            $this->setupTestEnvironment();
            
            // Database Tests
            $this->testDatabaseSchema();
            
            // Authentication Settings Tests
            $this->testAuthenticationSettings();
            
            // Rate Limiting Tests
            $this->testRateLimiting();
            
            // Audit Logging Tests
            $this->testAuditLogging();
            
            // OTP Tests
            $this->testOTPFunctionality();
            
            // 2FA Tests
            $this->test2FAFunctionality();
            
            // Security Tests
            $this->testSecurityFeatures();
            
            // Integration Tests
            $this->testIntegration();
            
            $this->cleanupTestEnvironment();
            
        } catch (Exception $e) {
            echo "❌ Test suite failed: " . $e->getMessage() . "\n";
            $this->cleanupTestEnvironment();
            return false;
        }

        $this->printTestResults();
        return $this->allTestsPassed();
    }

    /**
     * Test database schema
     */
    private function testDatabaseSchema() {
        echo "📊 Testing Database Schema...\n";

        $requiredTables = [
            'admin_auth_settings',
            'admin_2fa',
            'admin_otp',
            'admin_auth_logs',
            'admin_login_attempts',
            'admin_recovery_codes',
            'admin_recovery_tokens'
        ];

        foreach ($requiredTables as $table) {
            try {
                $stmt = $this->conn->query("SHOW TABLES LIKE '$table'");
                $exists = $stmt->rowCount() > 0;
                
                if ($exists) {
                    // Test table structure
                    $stmt = $this->conn->query("DESCRIBE $table");
                    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    
                    $this->addTestResult("Database", "Table $table exists", true);
                    $this->addTestResult("Database", "Table $table has columns", count($columns) > 0);
                } else {
                    $this->addTestResult("Database", "Table $table exists", false);
                }
            } catch (PDOException $e) {
                $this->addTestResult("Database", "Table $table check", false, $e->getMessage());
            }
        }

        // Test admin table enhancements
        try {
            $stmt = $this->conn->query("SHOW COLUMNS FROM admins LIKE 'auth_method'");
            $hasAuthMethod = $stmt->rowCount() > 0;
            $this->addTestResult("Database", "Admins table has auth_method column", $hasAuthMethod);
        } catch (PDOException $e) {
            $this->addTestResult("Database", "Admins table enhancement check", false, $e->getMessage());
        }
    }

    /**
     * Test authentication settings
     */
    private function testAuthenticationSettings() {
        echo "⚙️ Testing Authentication Settings...\n";

        try {
            // Test getting settings
            $stmt = $this->conn->query("SELECT COUNT(*) as count FROM admin_auth_settings");
            $settingsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            $this->addTestResult("Settings", "Default settings exist", $settingsCount > 0);

            // Test setting update
            $stmt = $this->conn->prepare("
                UPDATE admin_auth_settings 
                SET setting_value = 'true' 
                WHERE setting_name = 'admin_otp_enabled'
            ");
            $updated = $stmt->execute();
            
            $this->addTestResult("Settings", "Settings can be updated", $updated);

        } catch (PDOException $e) {
            $this->addTestResult("Settings", "Settings functionality", false, $e->getMessage());
        }
    }

    /**
     * Test rate limiting
     */
    private function testRateLimiting() {
        echo "🛡️ Testing Rate Limiting...\n";

        try {
            $rateLimiter = new AdminRateLimiter($this->conn);
            
            // Test rate limit check (should not be limited initially)
            $isLimited = $rateLimiter->isRateLimited('127.0.0.1', 'login', 'ip');
            $this->addTestResult("Rate Limiting", "Initial rate limit check", !$isLimited);
            
            // Test recording attempts
            for ($i = 0; $i < 3; $i++) {
                $rateLimiter->recordAttempt('127.0.0.1', 'login', 'ip');
            }
            
            // Test remaining attempts
            $remaining = $rateLimiter->getRemainingAttempts('127.0.0.1', 'login', 'ip');
            $this->addTestResult("Rate Limiting", "Remaining attempts calculation", $remaining >= 0);
            
            // Test clearing attempts
            $rateLimiter->clearAttempts('127.0.0.1', 'login', 'ip');
            $remainingAfterClear = $rateLimiter->getRemainingAttempts('127.0.0.1', 'login', 'ip');
            $this->addTestResult("Rate Limiting", "Clear attempts functionality", $remainingAfterClear > $remaining);

        } catch (Exception $e) {
            $this->addTestResult("Rate Limiting", "Rate limiter functionality", false, $e->getMessage());
        }
    }

    /**
     * Test audit logging
     */
    private function testAuditLogging() {
        echo "📝 Testing Audit Logging...\n";

        try {
            $auditLogger = new AdminAuditLogger($this->conn);
            
            // Test logging an event
            $auditLogger->logAuthEvent(
                $this->testAdmin['admin_id'],
                'test',
                'test_action',
                ['test' => true],
                'INFO'
            );
            
            // Verify log was created
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) as count 
                FROM admin_auth_logs 
                WHERE admin_id = ? AND action = 'test_action'
            ");
            $stmt->execute([$this->testAdmin['admin_id']]);
            $logCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            $this->addTestResult("Audit Logging", "Log event creation", $logCount > 0);
            
            // Test getting logs
            $logs = $auditLogger->getAuthLogs(['admin_id' => $this->testAdmin['admin_id']], 10);
            $this->addTestResult("Audit Logging", "Log retrieval", count($logs) > 0);
            
            // Test security stats
            $stats = $auditLogger->getSecurityStats(1);
            $this->addTestResult("Audit Logging", "Security statistics", !empty($stats));

        } catch (Exception $e) {
            $this->addTestResult("Audit Logging", "Audit logger functionality", false, $e->getMessage());
        }
    }

    /**
     * Test OTP functionality
     */
    private function testOTPFunctionality() {
        echo "📧 Testing OTP Functionality...\n";

        try {
            // Test OTP generation
            $otp = sprintf('%06d', random_int(100000, 999999));
            $expiresAt = date('Y-m-d H:i:s', time() + 300);
            
            $stmt = $this->conn->prepare("
                INSERT INTO admin_otp (admin_id, otp, expires_at, attempts, used) 
                VALUES (?, ?, ?, 0, 0)
            ");
            $otpCreated = $stmt->execute([$this->testAdmin['admin_id'], $otp, $expiresAt]);
            
            $this->addTestResult("OTP", "OTP generation and storage", $otpCreated);
            
            // Test OTP verification
            $stmt = $this->conn->prepare("
                SELECT id, otp, expires_at 
                FROM admin_otp 
                WHERE admin_id = ? AND used = 0 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$this->testAdmin['admin_id']]);
            $otpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->addTestResult("OTP", "OTP retrieval", !empty($otpRecord));
            
            // Test OTP validation
            $isValid = ($otpRecord && $otpRecord['otp'] === $otp);
            $this->addTestResult("OTP", "OTP validation", $isValid);

        } catch (Exception $e) {
            $this->addTestResult("OTP", "OTP functionality", false, $e->getMessage());
        }
    }

    /**
     * Test 2FA functionality
     */
    private function test2FAFunctionality() {
        echo "🔐 Testing 2FA Functionality...\n";

        try {
            // Test 2FA setup
            $secretKey = 'TESTSECRETKEY123456789012345';
            $backupCodes = ['CODE1234', 'CODE5678', 'CODE9012'];
            
            $stmt = $this->conn->prepare("
                INSERT INTO admin_2fa (admin_id, secret_key, auth_type, backup_codes, is_enabled, setup_completed) 
                VALUES (?, ?, 'google_auth', ?, 1, 1)
                ON DUPLICATE KEY UPDATE 
                secret_key = VALUES(secret_key),
                backup_codes = VALUES(backup_codes),
                is_enabled = VALUES(is_enabled),
                setup_completed = VALUES(setup_completed)
            ");
            $tfaSetup = $stmt->execute([
                $this->testAdmin['admin_id'],
                $secretKey,
                json_encode($backupCodes)
            ]);
            
            $this->addTestResult("2FA", "2FA setup and storage", $tfaSetup);
            
            // Test 2FA retrieval
            $stmt = $this->conn->prepare("
                SELECT secret_key, backup_codes, is_enabled 
                FROM admin_2fa 
                WHERE admin_id = ?
            ");
            $stmt->execute([$this->testAdmin['admin_id']]);
            $tfaRecord = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->addTestResult("2FA", "2FA data retrieval", !empty($tfaRecord));
            
            // Test backup codes
            $retrievedCodes = json_decode($tfaRecord['backup_codes'], true);
            $this->addTestResult("2FA", "Backup codes storage", count($retrievedCodes) === count($backupCodes));

        } catch (Exception $e) {
            $this->addTestResult("2FA", "2FA functionality", false, $e->getMessage());
        }
    }

    /**
     * Test security features
     */
    private function testSecurityFeatures() {
        echo "🔒 Testing Security Features...\n";

        try {
            $errorHandler = new AdminErrorHandler($this->conn);
            
            // Test system health check
            $health = $errorHandler->checkSystemHealth();
            $this->addTestResult("Security", "System health check", isset($health['status']));
            
            // Test input validation
            $validation = $errorHandler->validateInput(
                ['email' => '<EMAIL>', 'code' => '123456'],
                [
                    'email' => ['required' => true, 'type' => 'email'],
                    'code' => ['required' => true, 'pattern' => '/^\d{6}$/']
                ]
            );
            
            $this->addTestResult("Security", "Input validation", $validation['valid']);
            
            // Test error classification
            $testError = new Exception("Invalid OTP code");
            $response = $errorHandler->handleAuthError($testError, [
                'admin_id' => $this->testAdmin['admin_id'],
                'action' => 'otp_verify'
            ]);
            
            $this->addTestResult("Security", "Error handling", isset($response['error_type']));

        } catch (Exception $e) {
            $this->addTestResult("Security", "Security features", false, $e->getMessage());
        }
    }

    /**
     * Test integration
     */
    private function testIntegration() {
        echo "🔗 Testing Integration...\n";

        try {
            // Test SMTP settings check
            $stmt = $this->conn->query("SHOW TABLES LIKE 'smtp_settings'");
            $smtpTableExists = $stmt->rowCount() > 0;
            $this->addTestResult("Integration", "SMTP table exists", $smtpTableExists);
            
            // Test admin authentication flow
            $stmt = $this->conn->prepare("
                SELECT auth_method, two_factor_enabled 
                FROM admins 
                WHERE admin_id = ?
            ");
            $stmt->execute([$this->testAdmin['admin_id']]);
            $adminAuth = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->addTestResult("Integration", "Admin auth method retrieval", !empty($adminAuth));
            
            // Test view creation
            $stmt = $this->conn->query("SELECT * FROM admin_auth_status LIMIT 1");
            $viewWorks = $stmt !== false;
            $this->addTestResult("Integration", "Admin auth status view", $viewWorks);

        } catch (Exception $e) {
            $this->addTestResult("Integration", "Integration tests", false, $e->getMessage());
        }
    }

    /**
     * Setup test environment
     */
    private function setupTestEnvironment() {
        echo "🔧 Setting up test environment...\n";

        // Create or get test admin
        $stmt = $this->conn->prepare("
            SELECT admin_id, username, email 
            FROM admins 
            WHERE username = 'test_admin_auth' 
            LIMIT 1
        ");
        $stmt->execute();
        $this->testAdmin = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$this->testAdmin) {
            // Create test admin
            $stmt = $this->conn->prepare("
                INSERT INTO admins (username, email, password_hash, role, auth_method) 
                VALUES ('test_admin_auth', '<EMAIL>', ?, 'admin', 'password_only')
            ");
            $stmt->execute([password_hash('test123', PASSWORD_DEFAULT)]);
            
            $this->testAdmin = [
                'admin_id' => $this->conn->lastInsertId(),
                'username' => 'test_admin_auth',
                'email' => '<EMAIL>'
            ];
        }

        echo "✅ Test environment ready\n\n";
    }

    /**
     * Cleanup test environment
     */
    private function cleanupTestEnvironment() {
        echo "\n🧹 Cleaning up test environment...\n";

        if ($this->testAdmin) {
            // Clean up test data
            $tables = ['admin_auth_logs', 'admin_otp', 'admin_2fa', 'admin_login_attempts'];
            
            foreach ($tables as $table) {
                try {
                    $stmt = $this->conn->prepare("DELETE FROM $table WHERE admin_id = ?");
                    $stmt->execute([$this->testAdmin['admin_id']]);
                } catch (PDOException $e) {
                    // Table might not exist, continue
                }
            }
            
            // Remove test admin
            $stmt = $this->conn->prepare("DELETE FROM admins WHERE admin_id = ?");
            $stmt->execute([$this->testAdmin['admin_id']]);
        }

        echo "✅ Cleanup completed\n";
    }

    /**
     * Add test result
     */
    private function addTestResult($category, $test, $passed, $error = null) {
        $this->testResults[] = [
            'category' => $category,
            'test' => $test,
            'passed' => $passed,
            'error' => $error
        ];

        $status = $passed ? '✅' : '❌';
        echo "  $status $test\n";
        
        if (!$passed && $error) {
            echo "     Error: $error\n";
        }
    }

    /**
     * Print test results summary
     */
    private function printTestResults() {
        echo "\n📊 Test Results Summary\n";
        echo "======================\n";

        $categories = [];
        foreach ($this->testResults as $result) {
            $category = $result['category'];
            if (!isset($categories[$category])) {
                $categories[$category] = ['passed' => 0, 'failed' => 0];
            }
            
            if ($result['passed']) {
                $categories[$category]['passed']++;
            } else {
                $categories[$category]['failed']++;
            }
        }

        foreach ($categories as $category => $stats) {
            $total = $stats['passed'] + $stats['failed'];
            $percentage = round(($stats['passed'] / $total) * 100, 1);
            echo "$category: {$stats['passed']}/$total passed ($percentage%)\n";
        }

        $totalPassed = array_sum(array_column($this->testResults, 'passed'));
        $totalTests = count($this->testResults);
        $overallPercentage = round(($totalPassed / $totalTests) * 100, 1);
        
        echo "\nOverall: $totalPassed/$totalTests passed ($overallPercentage%)\n";
    }

    /**
     * Check if all tests passed
     */
    private function allTestsPassed() {
        foreach ($this->testResults as $result) {
            if (!$result['passed']) {
                return false;
            }
        }
        return true;
    }
}

// Run tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $testSuite = new AdminAuthTestSuite();
        $success = $testSuite->runAllTests();
        
        if ($success) {
            echo "\n🎉 All tests passed! The admin authentication system is ready.\n";
            exit(0);
        } else {
            echo "\n💥 Some tests failed. Please review the issues above.\n";
            exit(1);
        }
    } catch (Exception $e) {
        echo "\n💥 Test suite failed to run: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
