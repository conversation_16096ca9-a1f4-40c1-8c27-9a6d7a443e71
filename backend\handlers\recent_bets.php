<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->connect();

    // Get recent bets with team and user information
    $query = "SELECT 
        b.bet_id,
        b.amount,
        b.amount_user1,
        b.amount_user2,
        COALESCE(b.bet_status, 'pending') as bet_status,
        b.created_at as bet_date,
        COALESCE(b.odds_user1, 1.00) as odds_user1,
        COALESCE(b.odds_user2, 1.00) as odds_user2,
        b.bet_choice_user1,
        b.bet_choice_user2,
        b.unique_code,
        COALESCE(b.potential_return_win_user1, b.amount_user1) as potential_return_win_user1,
        COALESCE(b.potential_return_draw_user1, b.amount_user1) as potential_return_draw_user1,
        COALESCE(b.potential_return_loss_user1, 0) as potential_return_loss_user1,
        COALESCE(t1.name, 'Team A') as team_a,
        COALESCE(t1.logo, 'default-team-logo.png') as team_a_logo,
        COALESCE(t2.name, 'Team B') as team_b,
        COALESCE(t2.logo, 'default-team-logo.png') as team_b_logo,
        COALESCE(u1.username, 'User 1') as user1_name,
        COALESCE(u2.username, 'User 2') as user2_name
    FROM bets b
    LEFT JOIN teams t1 ON b.team1_id = t1.team_id
    LEFT JOIN teams t2 ON b.team2_id = t2.team_id
    LEFT JOIN users u1 ON b.user1_id = u1.user_id
    LEFT JOIN users u2 ON b.user2_id = u2.user_id
    WHERE b.bet_status IS NOT NULL
    ORDER BY b.created_at DESC
    LIMIT 10";

    $stmt = $db->prepare($query);
    $stmt->execute();

    $bets = array();
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // Format the bet data
        $bet = array(
            'bet_id' => $row['bet_id'],
            'amount' => $row['amount'],
            'amount_user1' => $row['amount_user1'],
            'amount_user2' => $row['amount_user2'],
            'bet_status' => $row['bet_status'],
            'bet_date' => $row['bet_date'],
            'team_a' => $row['team_a'],
            'team_a_logo' => $row['team_a_logo'],
            'team_b' => $row['team_b'],
            'team_b_logo' => $row['team_b_logo'],
            'user1_name' => $row['user1_name'],
            'user2_name' => $row['user2_name'],
            'selected_odds' => $row['odds_user1'],
            'bet_choice_user1' => $row['bet_choice_user1'],
            'bet_choice_user2' => $row['bet_choice_user2'],
            'unique_code' => $row['unique_code'],
            'potential_return_win_user1' => $row['potential_return_win_user1'],
            'potential_return_draw_user1' => $row['potential_return_draw_user1'],
            'potential_return_loss_user1' => $row['potential_return_loss_user1']
        );
        array_push($bets, $bet);
    }

    // Log the number of bets found
    error_log("Found " . count($bets) . " recent bets");

    echo json_encode(array(
        'success' => true,
        'bets' => $bets
    ));

} catch(PDOException $e) {
    error_log("Database Error in recent_bets.php: " . $e->getMessage());
    echo json_encode(array(
        'success' => false,
        'message' => 'Database Error: ' . $e->getMessage()
    ));
}
