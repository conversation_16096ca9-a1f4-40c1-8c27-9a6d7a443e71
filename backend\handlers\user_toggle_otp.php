<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed");
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $userId = $input['userId'] ?? null;
    $enabled = $input['enabled'] ?? false;
    
    if (!$userId) {
        throw new Exception("User ID is required");
    }
    
    // Verify user exists
    $stmt = $conn->prepare("SELECT user_id, username, email, otp_enabled, tfa_enabled, auth_method FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    $conn->beginTransaction();
    
    // Update OTP setting
    $stmt = $conn->prepare("UPDATE users SET otp_enabled = ? WHERE user_id = ?");
    $stmt->execute([$enabled ? 1 : 0, $userId]);
    
    // Update auth method based on current settings
    if ($enabled) {
        $newAuthMethod = $user['tfa_enabled'] ? 'password_otp_2fa' : 'password_otp';
    } else {
        $newAuthMethod = $user['tfa_enabled'] ? 'password_2fa' : 'password_only';
    }
    
    $stmt = $conn->prepare("UPDATE users SET auth_method = ? WHERE user_id = ?");
    $stmt->execute([$newAuthMethod, $userId]);
    
    // Log the change
    $stmt = $conn->prepare("
        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, 'otp', ?, ?, ?, ?)
    ");
    $stmt->execute([
        $userId,
        $enabled ? 'otp_enabled' : 'otp_disabled',
        json_encode([
            'otp_enabled' => $enabled,
            'previous_auth_method' => $user['auth_method'],
            'new_auth_method' => $newAuthMethod
        ]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => $enabled ? 'OTP enabled successfully' : 'OTP disabled successfully',
        'otp_enabled' => $enabled,
        'auth_method' => $newAuthMethod
    ]);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
