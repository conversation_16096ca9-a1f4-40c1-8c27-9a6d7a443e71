<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get total active users (users who have placed bets in the last 24 hours)
    $activeUsersQuery = "SELECT COUNT(DISTINCT user1_id) as active_users 
                        FROM bets 
                        WHERE created_at >= NOW() - INTERVAL 24 HOUR";
    $activeUsersStmt = $db->query($activeUsersQuery);
    $activeUsers = $activeUsersStmt->fetch(PDO::FETCH_ASSOC)['active_users'];

    // Get total bets
    $totalBetsQuery = "SELECT COUNT(*) as total_bets FROM bets";
    $totalBetsStmt = $db->query($totalBetsQuery);
    $totalBets = $totalBetsStmt->fetch(PDO::FETCH_ASSOC)['total_bets'];

    // Get total open challenges
    $openChallengesQuery = "SELECT COUNT(*) as total_challenges 
                           FROM challenges 
                           WHERE status = 'Open' 
                           AND end_time > NOW()";
    $openChallengesStmt = $db->query($openChallengesQuery);
    $openChallenges = $openChallengesStmt->fetch(PDO::FETCH_ASSOC)['total_challenges'];

    $response = [
        'success' => true,
        'data' => [
            'active_users' => (int)$activeUsers,
            'total_bets' => (int)$totalBets,
            'total_challenges' => (int)$openChallenges
        ]
    ];

    echo json_encode($response);

} catch(PDOException $e) {
    $response = [
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ];
    http_response_code(500);
    echo json_encode($response);
}
?>
