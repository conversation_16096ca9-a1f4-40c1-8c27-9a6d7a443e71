<?php
/**
 * Test Database Connection
 */

echo "🔧 Testing Database Connection...\n\n";

// Test basic PHP functionality
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Current Directory: " . __DIR__ . "\n\n";

// Test database connection
try {
    $host = 'localhost';
    $dbname = 'fanbet247';
    $username = 'root';
    $password = 'root';
    
    echo "Attempting to connect to database...\n";
    echo "Host: $host\n";
    echo "Database: $dbname\n";
    echo "Username: $username\n\n";
    
    $conn = new PDO(
        "mysql:host=$host;dbname=$dbname",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ Database connection successful!\n\n";
    
    // Test users table
    echo "📋 Testing users table...\n";
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "Users table has {$result['count']} records\n\n";
    
    // Check users table structure
    echo "📋 Users table structure:\n";
    $stmt = $conn->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']}\n";
    }
    
    echo "\n✅ Database test completed successfully!\n";
    
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    echo "\nPossible solutions:\n";
    echo "1. Make sure MAMP is running\n";
    echo "2. Check database credentials in db_connect.php\n";
    echo "3. Verify the 'fanbet247' database exists\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
