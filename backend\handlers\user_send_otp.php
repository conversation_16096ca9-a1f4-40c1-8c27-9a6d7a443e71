<?php
/**
 * User OTP Send Handler - Fresh & Simple Implementation
 * 
 * This handler sends OTP codes to users via email using the working SMTP configuration.
 * Built from scratch for clarity and reliability.
 */

// Headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Include dependencies
require_once '../includes/db_connect.php';
require_once '../vendor/autoload.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use P<PERSON>Mailer\PHPMailer\Exception;

try {
    // Get database connection
    $conn = getDBConnection();
    
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    $userId = $input['userId'] ?? null;
    $email = $input['email'] ?? null;
    
    if (!$userId || !$email) {
        throw new Exception("User ID and email are required");
    }
    
    // Verify user exists and has OTP enabled
    $stmt = $conn->prepare("SELECT user_id, username, email, otp_enabled FROM users WHERE user_id = ? AND email = ?");
    $stmt->execute([$userId, $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("User not found or email mismatch");
    }
    
    if (!$user['otp_enabled']) {
        throw new Exception("OTP is not enabled for this user");
    }
    
    // Generate 6-digit OTP
    $otp = sprintf('%06d', mt_rand(0, 999999));
    $expiresAt = date('Y-m-d H:i:s', time() + 300); // 5 minutes
    
    // Start transaction
    $conn->beginTransaction();
    
    // Clean up old OTPs for this user
    $stmt = $conn->prepare("DELETE FROM user_otp WHERE user_id = ? AND used = 0");
    $stmt->execute([$userId]);
    
    // Insert new OTP
    $stmt = $conn->prepare("INSERT INTO user_otp (user_id, otp, expiry) VALUES (?, ?, ?)");
    $stmt->execute([$userId, $otp, $expiresAt]);
    
    // Get SMTP settings
    $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $smtp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$smtp) {
        throw new Exception("SMTP settings not configured");
    }
    
    // Send email using PHPMailer
    $mail = new PHPMailer(true);
    
    // SMTP Configuration (using working settings from test)
    $mail->isSMTP();
    $mail->Host = $smtp['host'];
    $mail->SMTPAuth = true;
    $mail->Username = $smtp['username'];
    $mail->Password = $smtp['password'];
    $mail->SMTPSecure = $smtp['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = $smtp['port'];
    
    // Email content
    $mail->setFrom($smtp['from_email'], $smtp['from_name']);
    $mail->addAddress($user['email'], $user['username']);
    $mail->isHTML(true);
    // Create unique subject line to prevent email grouping
    $uniqueId = substr(md5(uniqid(mt_rand(), true)), 0, 8);
    $timestamp = date('H:i');
    $mail->Subject = "Your FanBet247 Verification Code - {$timestamp} #{$uniqueId}";
    
    // Simple, clean email template
    $mail->Body = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>FanBet247 Verification Code</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
            .otp-box { background: white; border: 2px solid #2563eb; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0; }
            .otp-code { font-size: 32px; font-weight: bold; color: #2563eb; letter-spacing: 5px; }
            .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>FanBet247</h1>
                <p>Your Verification Code</p>
            </div>
            <div class='content'>
                <p>Hello {$user['username']},</p>
                <p>Use the following code to complete your login:</p>
                
                <div class='otp-box'>
                    <div class='otp-code'>{$otp}</div>
                </div>
                
                <p><strong>Important:</strong></p>
                <ul>
                    <li>This code expires in 5 minutes</li>
                    <li>Do not share this code with anyone</li>
                    <li>If you did not request this code, please ignore this email</li>
                </ul>
                
                <div class='footer'>
                    <p>This is an automated email from FanBet247.<br>Please do not reply to this email.</p>
                </div>
            </div>
        </div>
    </body>
    </html>";
    
    // Plain text version
    $mail->AltBody = "FanBet247 Verification Code\n\n" .
                     "Hello {$user['username']},\n\n" .
                     "Your verification code is: {$otp}\n\n" .
                     "This code expires in 5 minutes.\n" .
                     "Do not share this code with anyone.\n\n" .
                     "This is an automated email from FanBet247.";
    
    // Send the email
    $mail->send();
    
    // Log the OTP send event
    $stmt = $conn->prepare("
        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, 'otp', 'otp_sent', ?, ?, ?)
    ");
    $stmt->execute([
        $userId,
        json_encode(['email' => $user['email'], 'expiry' => $expiresAt]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    // Commit transaction
    $conn->commit();
    
    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'OTP sent successfully to your email address',
        'expires_in' => 300, // 5 minutes in seconds
        'email_masked' => substr($user['email'], 0, 3) . '***@' . substr(strrchr($user['email'], '@'), 1)
    ]);
    
} catch (Exception $e) {
    // Rollback transaction if active
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    // Log error
    error_log("OTP Send Error: " . $e->getMessage());
    
    // Error response
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to send OTP: ' . $e->getMessage()
    ]);
}
?>
