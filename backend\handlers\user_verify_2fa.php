<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';
require_once '../vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

try {
    // COMPREHENSIVE DEBUGGING - Log all request details
    $debugLog = [
        'timestamp' => date('Y-m-d H:i:s'),
        'method' => $_SERVER['REQUEST_METHOD'],
        'uri' => $_SERVER['REQUEST_URI'],
        'raw_input' => file_get_contents('php://input'),
        'server_time' => time()
    ];

    error_log("🔍 2FA VERIFICATION REQUEST: " . json_encode($debugLog));

    $conn = getDBConnection();

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed");
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $userId = $input['userId'] ?? null;
    $verificationCode = $input['verification_code'] ?? '';
    $isBackupCode = $input['is_backup_code'] ?? false;
    
    if (!$userId) {
        throw new Exception("User ID is required");
    }
    
    if (empty($verificationCode)) {
        throw new Exception("Verification code is required");
    }
    
    // Verify user exists
    $stmt = $conn->prepare("SELECT user_id, username, email, role, tfa_enabled FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    if (!$user['tfa_enabled']) {
        throw new Exception("2FA is not enabled for this user");
    }
    
    // Get user's 2FA setup
    $stmt = $conn->prepare("
        SELECT secret_key, backup_codes, is_enabled, setup_completed 
        FROM user_2fa 
        WHERE user_id = ? AND is_enabled = 1 AND setup_completed = 1
    ");
    $stmt->execute([$userId]);
    $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tfaSetup) {
        throw new Exception("2FA is not set up for this user account");
    }
    
    // Check for rate limiting (max 5 attempts per 30 minutes)
    $stmt = $conn->prepare("
        SELECT attempts, locked_until, last_attempt 
        FROM user_login_attempts 
        WHERE user_id = ? AND attempt_type = '2fa' AND locked_until > NOW()
        ORDER BY last_attempt DESC 
        LIMIT 1
    ");
    $stmt->execute([$userId]);
    $rateLimitCheck = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($rateLimitCheck) {
        throw new Exception("Account is temporarily locked due to too many failed 2FA attempts. Please try again later.");
    }
    
    $isValid = false;
    
    if ($isBackupCode) {
        // Verify backup code
        $backupCodes = json_decode($tfaSetup['backup_codes'], true);
        if (in_array(strtoupper($verificationCode), $backupCodes)) {
            $isValid = true;
            
            // Remove used backup code
            $backupCodes = array_diff($backupCodes, [strtoupper($verificationCode)]);
            $stmt = $conn->prepare("UPDATE user_2fa SET backup_codes = ? WHERE user_id = ?");
            $stmt->execute([json_encode(array_values($backupCodes)), $userId]);
        }
    } else {
        // Verify Google Authenticator code
        if (strlen($verificationCode) !== 6 || !ctype_digit($verificationCode)) {
            throw new Exception("Please provide a valid 6-digit verification code");
        }
        
        $google2fa = new Google2FA();
        // Use window of 2 to allow for a 1-minute time drift (2 * 30 seconds = 60 seconds)
        $window = 2;

        // COMPREHENSIVE DEBUG LOGGING
        $currentCode = $google2fa->getCurrentOtp($tfaSetup['secret_key']);
        $debugInfo = [
            'user_id' => $userId,
            'input_code' => $verificationCode,
            'secret_key' => $tfaSetup['secret_key'],
            'current_valid_code' => $currentCode,
            'window' => $window,
            'server_time' => date('Y-m-d H:i:s'),
            'timestamp' => time(),
            'code_length' => strlen($verificationCode),
            'is_numeric' => ctype_digit($verificationCode)
        ];

        error_log("🔍 2FA VERIFICATION ATTEMPT: " . json_encode($debugInfo));

        // Test verification with detailed logging
        $isValid = $google2fa->verifyKey($tfaSetup['secret_key'], $verificationCode, $window);

        error_log("🎯 2FA VERIFICATION RESULT: " . ($isValid ? 'SUCCESS' : 'FAILED'));

        // If failed, test with different windows for debugging
        if (!$isValid) {
            error_log("🔧 TESTING DIFFERENT WINDOWS:");
            for ($testWindow = 0; $testWindow <= 8; $testWindow++) {
                $testResult = $google2fa->verifyKey($tfaSetup['secret_key'], $verificationCode, $testWindow);
                error_log("   Window $testWindow: " . ($testResult ? 'SUCCESS' : 'FAILED'));
            }
        }
    }
    
    $conn->beginTransaction();
    
    if (!$isValid) {
        // Handle failed attempt
        $stmt = $conn->prepare("
            INSERT INTO user_login_attempts (user_id, ip_address, attempt_type, attempts) 
            VALUES (?, ?, '2fa', 1)
            ON DUPLICATE KEY UPDATE 
            attempts = attempts + 1, 
            last_attempt = NOW()
        ");
        $stmt->execute([$userId, $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
        
        // Check if we need to lock the account
        $stmt = $conn->prepare("
            SELECT attempts FROM user_login_attempts 
            WHERE user_id = ? AND attempt_type = '2fa' 
            ORDER BY last_attempt DESC 
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        $attempts = $stmt->fetch(PDO::FETCH_ASSOC)['attempts'] ?? 0;
        
        if ($attempts >= 5) {
            $lockedUntil = date('Y-m-d H:i:s', time() + 1800); // 30 minutes
            $stmt = $conn->prepare("
                UPDATE user_login_attempts 
                SET locked_until = ? 
                WHERE user_id = ? AND attempt_type = '2fa'
            ");
            $stmt->execute([$lockedUntil, $userId]);
            
            // Log lockout
            $stmt = $conn->prepare("
                INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
                VALUES (?, '2fa', 'account_locked', ?, ?, ?)
            ");
            $stmt->execute([
                $userId,
                json_encode(['reason' => 'max_2fa_attempts_exceeded', 'locked_until' => $lockedUntil]),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            $conn->commit();
            throw new Exception("Too many failed 2FA attempts. Account locked for 30 minutes.");
        }
        
        // Log failed attempt
        $stmt = $conn->prepare("
            INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, '2fa', '2fa_failed', ?, ?, ?)
        ");
        $stmt->execute([
            $userId,
            json_encode(['reason' => 'invalid_code', 'is_backup_code' => $isBackupCode]),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        throw new Exception("Invalid verification code. Please check your authenticator app and try again.");
    }
    
    // 2FA verification successful
    
    // Clear failed attempts
    $stmt = $conn->prepare("DELETE FROM user_login_attempts WHERE user_id = ? AND attempt_type = '2fa'");
    $stmt->execute([$userId]);
    
    // Update last login and 2FA usage
    $stmt = $conn->prepare("UPDATE users SET last_active = NOW() WHERE user_id = ?");
    $stmt->execute([$userId]);
    
    $stmt = $conn->prepare("UPDATE user_2fa SET last_used = NOW() WHERE user_id = ?");
    $stmt->execute([$userId]);
    
    // Log successful 2FA verification
    $stmt = $conn->prepare("
        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, '2fa', '2fa_success', ?, ?, ?)
    ");
    $stmt->execute([
        $userId,
        json_encode(['is_backup_code' => $isBackupCode]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    // Generate session token
    $token = bin2hex(random_bytes(32));
    try {
        $stmt = $conn->prepare("
            INSERT INTO user_sessions (user_id, token, expires_at)
            VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR))
            ON DUPLICATE KEY UPDATE
            token = VALUES(token),
            expires_at = VALUES(expires_at)
        ");
        $stmt->execute([$userId, $token]);
    } catch (PDOException $e) {
        error_log("Session storage failed (table may not exist): " . $e->getMessage());
    }

    // Get user balance and points
    $stmt = $conn->prepare("
        SELECT balance,
               COALESCE((SELECT SUM(points) FROM user_achievements WHERE user_id = ?), 0) as points
        FROM users WHERE user_id = ?
    ");
    $stmt->execute([$userId, $userId]);
    $userStats = $stmt->fetch(PDO::FETCH_ASSOC);

    // Check if this is part of multi-step authentication
    $partialLogin = $_SESSION['partial_login'] ?? null;
    $requiresAdditionalAuth = false;
    $nextSteps = [];

    if ($partialLogin && $partialLogin['user_id'] == $userId) {
        // Update completed steps
        $completedSteps = $partialLogin['completed_steps'];
        $completedSteps[] = '2fa';

        $remainingSteps = array_diff($partialLogin['remaining_steps'], ['2fa']);

        if (!empty($remainingSteps)) {
            // Still have more authentication steps
            $requiresAdditionalAuth = true;
            $nextSteps = $remainingSteps;

            // Update partial login state
            $_SESSION['partial_login'] = array_merge($partialLogin, [
                'completed_steps' => $completedSteps,
                'remaining_steps' => $remainingSteps
            ]);
        } else {
            // All authentication steps completed
            $_SESSION['user_id'] = $userId;
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['login_time'] = time();

            // Clear partial login state
            unset($_SESSION['partial_login']);
        }
    } else {
        // Direct 2FA verification (not part of multi-step login)
        $_SESSION['user_id'] = $userId;
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['login_time'] = time();
    }

    $conn->commit();

    $response = [
        'success' => true,
        'message' => '2FA verification successful',
        'user_id' => $userId,
        'username' => $user['username'],
        'email' => $user['email'],
        'role' => $user['role'] ?? null,
        'balance' => floatval($userStats['balance'] ?? 0),
        'points' => intval($userStats['points'] ?? 0),
        'session_token' => $token,
        'remaining_backup_codes' => count(json_decode($tfaSetup['backup_codes'], true))
    ];

    if ($requiresAdditionalAuth) {
        $response['requires_additional_auth'] = true;
        $response['next_steps'] = $nextSteps;
        $response['message'] = '2FA verified. Additional authentication required.';
    }

    echo json_encode($response);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
