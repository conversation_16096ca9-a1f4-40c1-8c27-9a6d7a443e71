<?php
function checkAndCreateUploadDirectory() {
    $uploadDir = dirname(dirname(__DIR__)) . '/uploads/';
    
    // Check if directory exists
    if (!file_exists($uploadDir)) {
        // Try to create directory
        if (!@mkdir($uploadDir, 0777, true)) {
            error_log("Failed to create uploads directory at: " . $uploadDir);
            return false;
        }
    }
    
    // Check if directory is writable
    if (!is_writable($uploadDir)) {
        error_log("Uploads directory is not writable at: " . $uploadDir);
        return false;
    }
    
    // Try to write a test file
    $testFile = $uploadDir . 'test.txt';
    if (!@file_put_contents($testFile, 'test')) {
        error_log("Failed to write test file to uploads directory");
        return false;
    }
    
    // Clean up test file
    @unlink($testFile);
    
    return true;
}

// Check permissions when this file is included
$permissionsOk = checkAndCreateUploadDirectory();
if (!$permissionsOk) {
    error_log("WARNING: Upload directory permissions are not properly set. Please ensure the web server has write permissions to the uploads directory.");
} 