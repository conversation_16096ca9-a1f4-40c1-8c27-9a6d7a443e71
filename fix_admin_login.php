<?php
/**
 * Fix Admin Login Script
 * This script will diagnose and fix the admin login issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 FanBet247 Admin Login Fix Tool</h1>";
echo "<p><strong>Started:</strong> " . date('Y-m-d H:i:s') . "</p>";

$action = $_GET['action'] ?? 'diagnose';

switch ($action) {
    case 'diagnose':
        echo "<h2>🔍 Diagnosing Issues</h2>";
        
        // Check 1: File existence
        echo "<h3>1. File Structure Check</h3>";
        $files = [
            'backend/handlers/admin_login_handler.php' => 'Admin Login Handler',
            'backend/includes/db_connect.php' => 'Database Connection',
            'backend/handlers/login.php' => 'User Login Handler'
        ];
        
        foreach ($files as $file => $name) {
            $exists = file_exists($file);
            $readable = $exists && is_readable($file);
            $size = $exists ? filesize($file) : 0;
            
            echo "<p><strong>$name:</strong> ";
            if ($exists) {
                echo "✅ Found ($size bytes)";
                if (!$readable) echo " ⚠️ Not readable";
            } else {
                echo "❌ Missing";
            }
            echo "</p>";
        }
        
        // Check 2: Database connection
        echo "<h3>2. Database Connection Test</h3>";
        try {
            include_once 'backend/includes/db_connect.php';
            $conn = getDBConnection();
            if ($conn) {
                echo "<p>✅ Database connection successful</p>";
                
                // Test admin count
                $stmt = $conn->query("SELECT COUNT(*) as count FROM admins");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                echo "<p>📊 Admin users found: " . $result['count'] . "</p>";
                
                // Test admin credentials
                $stmt = $conn->query("SELECT username, email FROM admins LIMIT 3");
                $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo "<p>👤 Sample admin users:</p><ul>";
                foreach ($admins as $admin) {
                    echo "<li>" . htmlspecialchars($admin['username']) . " (" . htmlspecialchars($admin['email']) . ")</li>";
                }
                echo "</ul>";
                
            } else {
                echo "<p>❌ Database connection failed</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Check 3: PHP syntax check
        echo "<h3>3. PHP Syntax Check</h3>";
        $adminLoginFile = 'backend/handlers/admin_login_handler.php';
        if (file_exists($adminLoginFile)) {
            $output = [];
            $returnCode = 0;
            exec("php -l $adminLoginFile 2>&1", $output, $returnCode);
            
            if ($returnCode === 0) {
                echo "<p>✅ PHP syntax is valid</p>";
            } else {
                echo "<p>❌ PHP syntax errors found:</p>";
                echo "<pre>" . htmlspecialchars(implode("\n", $output)) . "</pre>";
            }
        }
        
        // Check 4: Test admin login directly
        echo "<h3>4. Direct Admin Login Test</h3>";
        try {
            include_once 'backend/includes/db_connect.php';
            $conn = getDBConnection();
            
            $testIdentifier = 'admin';
            $testPassword = 'loving12';
            
            $stmt = $conn->prepare("SELECT admin_id, username, password_hash, role FROM admins WHERE username = :identifier OR email = :identifier");
            $stmt->bindParam(':identifier', $testIdentifier);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $row = $stmt->fetch(PDO::FETCH_ASSOC);
                echo "<p>✅ Admin user '$testIdentifier' found</p>";
                
                $passwordMatch = password_verify($testPassword, $row['password_hash']);
                if ($passwordMatch) {
                    echo "<p>✅ Password verification successful</p>";
                    echo "<p>🎉 Admin login should work!</p>";
                } else {
                    echo "<p>❌ Password verification failed</p>";
                    echo "<p>💡 Try these common passwords: admin, password, fanbet247, loving12</p>";
                }
            } else {
                echo "<p>❌ Admin user '$testIdentifier' not found</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Direct test error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "<h3>🛠️ Actions Available</h3>";
        echo "<p><a href='?action=fix_syntax' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Fix Syntax Issues</a></p>";
        echo "<p><a href='?action=test_login' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🧪 Test Login API</a></p>";
        echo "<p><a href='?action=reset_password' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔑 Reset Admin Password</a></p>";
        
        break;
        
    case 'fix_syntax':
        echo "<h2>🔧 Fixing Syntax Issues</h2>";
        
        $cleanContent = '<?php
/**
 * Clean Admin Login Handler - Auto-generated fix
 */

error_reporting(E_ALL);
ini_set(\'display_errors\', 1);

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Credentials: true");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, X-Admin-ID");

if ($_SERVER[\'REQUEST_METHOD\'] === \'OPTIONS\') {
    http_response_code(200);
    exit;
}

include_once \'../includes/db_connect.php\';

function validateAdminCredentials($identifier, $password) {
    try {
        $conn = getDBConnection();
        if (!$conn) {
            throw new PDOException("Database connection failed");
        }

        $stmt = $conn->prepare("SELECT admin_id, username, password_hash, role, email FROM admins WHERE username = :identifier OR email = :identifier");
        $stmt->bindParam(\':identifier\', $identifier);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (password_verify($password, $row[\'password_hash\'])) {
                $updateStmt = $conn->prepare("UPDATE admins SET last_login = CURRENT_TIMESTAMP WHERE admin_id = :admin_id");
                $updateStmt->bindParam(\':admin_id\', $row[\'admin_id\']);
                $updateStmt->execute();

                return [
                    \'success\' => true,
                    \'admin_id\' => $row[\'admin_id\'],
                    \'username\' => $row[\'username\'],
                    \'email\' => $row[\'email\'],
                    \'role\' => $row[\'role\']
                ];
            }
        }
        
        return [\'success\' => false, \'message\' => \'Invalid credentials\'];
        
    } catch (PDOException $e) {
        error_log("Admin Login Error: " . $e->getMessage());
        return [\'success\' => false, \'error\' => $e->getMessage()];
    }
}

try {
    $conn = getDBConnection();
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    $rawInput = file_get_contents("php://input");
    $data = json_decode($rawInput, true);
    
    if (!$data && !empty($_POST)) {
        $data = $_POST;
    }

    if (!$data || !isset($data[\'identifier\']) || !isset($data[\'password\'])) {
        throw new Exception("Missing identifier or password");
    }

    $identifier = trim($data[\'identifier\']);
    $password = $data[\'password\'];
    
    $result = validateAdminCredentials($identifier, $password);
    
    if ($result[\'success\']) {
        echo json_encode([
            "success" => true,
            "message" => "Login successful",
            "admin_id" => $result[\'admin_id\'],
            "username" => $result[\'username\'],
            "email" => $result[\'email\'],
            "role" => $result[\'role\'],
            "timestamp" => date(\'Y-m-d H:i:s\')
        ]);
    } else {
        echo json_encode([
            "success" => false,
            "message" => "Invalid username/email or password"
        ]);
    }

} catch (Exception $e) {
    error_log("Admin Login Exception: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "An error occurred during login",
        "debug_info" => [
            "error" => $e->getMessage(),
            "timestamp" => date(\'Y-m-d H:i:s\')
        ]
    ]);
}
?>';

        // Backup original file
        $originalFile = 'backend/handlers/admin_login_handler.php';
        $backupFile = 'backend/handlers/admin_login_handler.php.backup.' . date('Y-m-d-H-i-s');
        
        if (file_exists($originalFile)) {
            copy($originalFile, $backupFile);
            echo "<p>✅ Original file backed up to: $backupFile</p>";
        }
        
        // Write clean file
        if (file_put_contents($originalFile, $cleanContent)) {
            echo "<p>✅ Clean admin login handler written successfully</p>";
            echo "<p>🧪 <a href='?action=test_login'>Test the fixed login</a></p>";
        } else {
            echo "<p>❌ Failed to write clean file</p>";
        }
        
        break;
        
    case 'test_login':
        echo "<h2>🧪 Testing Admin Login</h2>";
        
        $testData = [
            'identifier' => 'admin',
            'password' => 'loving12'
        ];
        
        echo "<p>Testing with: " . htmlspecialchars(json_encode($testData)) . "</p>";
        
        // Simulate the login request
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://fanbet247.xyz/backend/handlers/admin_login_handler.php');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
        if ($error) {
            echo "<p><strong>cURL Error:</strong> " . htmlspecialchars($error) . "</p>";
        }
        echo "<p><strong>Response:</strong></p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        break;
        
    case 'reset_password':
        echo "<h2>🔑 Reset Admin Password</h2>";
        
        $newPassword = 'loving12';
        $newHash = password_hash($newPassword, PASSWORD_DEFAULT);
        
        try {
            include_once 'backend/includes/db_connect.php';
            $conn = getDBConnection();
            
            $stmt = $conn->prepare("UPDATE admins SET password_hash = ? WHERE username = ?");
            $result = $stmt->execute([$newHash, 'admin']);
            
            if ($result) {
                echo "<p>✅ Password reset successful for user 'admin'</p>";
                echo "<p>🔑 New password: <strong>$newPassword</strong></p>";
                echo "<p>🧪 <a href='?action=test_login'>Test the login now</a></p>";
            } else {
                echo "<p>❌ Password reset failed</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Password reset error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        break;
        
    default:
        echo "<p>Unknown action</p>";
        break;
}

echo "<hr>";
echo "<p><a href='?action=diagnose'>🔍 Run Diagnosis</a> | ";
echo "<a href='?action=fix_syntax'>🔧 Fix Syntax</a> | ";
echo "<a href='?action=test_login'>🧪 Test Login</a> | ";
echo "<a href='?action=reset_password'>🔑 Reset Password</a></p>";
?>
