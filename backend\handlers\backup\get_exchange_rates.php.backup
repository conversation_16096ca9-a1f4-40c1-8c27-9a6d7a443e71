<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }
    
    // Check if we want rates for a specific currency
    $currencyId = isset($_GET['currency_id']) ? (int)$_GET['currency_id'] : null;
    $currencyCode = isset($_GET['currency_code']) ? strtoupper(trim($_GET['currency_code'])) : null;
    
    // Build the main query to get exchange rates with currency details
    $query = "SELECT 
                c.id as currency_id,
                c.currency_code,
                c.currency_name,
                c.currency_symbol,
                c.is_active,
                er.id as rate_id,
                er.rate_to_fancoin,
                er.notes,
                er.created_at as rate_created_at,
                er.updated_at as rate_updated_at,
                er.updated_by_admin_id,
                a.username as updated_by_admin_username
              FROM currencies c
              LEFT JOIN exchange_rates er ON c.id = er.currency_id
              LEFT JOIN admins a ON er.updated_by_admin_id = a.admin_id
              WHERE c.is_active = 1";
    
    $params = [];
    
    // Add specific currency filter if provided
    if ($currencyId) {
        $query .= " AND c.id = :currency_id";
        $params['currency_id'] = $currencyId;
    } elseif ($currencyCode) {
        $query .= " AND c.currency_code = :currency_code";
        $params['currency_code'] = $currencyCode;
    }
    
    $query .= " ORDER BY c.currency_code ASC, er.updated_at DESC";
    
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process results to get the latest rate for each currency
    $exchangeRates = [];
    $processedCurrencies = [];
    
    foreach ($results as $row) {
        $currencyId = $row['currency_id'];
        
        // Skip if we've already processed this currency (we want the latest rate)
        if (in_array($currencyId, $processedCurrencies)) {
            continue;
        }
        
        $exchangeRates[] = [
            'currency_id' => (int)$row['currency_id'],
            'currency_code' => $row['currency_code'],
            'currency_name' => $row['currency_name'],
            'currency_symbol' => $row['currency_symbol'],
            'is_active' => (bool)$row['is_active'],
            'rate_id' => $row['rate_id'] ? (int)$row['rate_id'] : null,
            'rate_to_fancoin' => $row['rate_to_fancoin'] ? (float)$row['rate_to_fancoin'] : null,
            'formatted_rate' => $row['rate_to_fancoin'] ? 
                $row['currency_symbol'] . ' ' . number_format($row['rate_to_fancoin'], 4) : 
                'No rate set',
            'conversion_example' => $row['rate_to_fancoin'] ? 
                '1 FanCoin = ' . $row['currency_symbol'] . number_format($row['rate_to_fancoin'], 2) : 
                'No conversion available',
            'notes' => $row['notes'],
            'rate_updated_at' => $row['rate_updated_at'],
            'updated_by_admin_id' => $row['updated_by_admin_id'] ? (int)$row['updated_by_admin_id'] : null,
            'updated_by_admin_username' => $row['updated_by_admin_username'],
            'has_rate' => !is_null($row['rate_to_fancoin'])
        ];
        
        $processedCurrencies[] = $currencyId;
    }
    
    // If specific currency requested, return single object instead of array
    if (($currencyId || $currencyCode) && count($exchangeRates) === 1) {
        jsonResponse(200, "Exchange rate fetched successfully", $exchangeRates[0]);
    } else {
        // Get summary statistics
        $totalCurrencies = count($exchangeRates);
        $currenciesWithRates = count(array_filter($exchangeRates, function($rate) {
            return $rate['has_rate'];
        }));
        $currenciesWithoutRates = $totalCurrencies - $currenciesWithRates;
        
        jsonResponse(200, "Exchange rates fetched successfully", [
            'exchange_rates' => $exchangeRates,
            'summary' => [
                'total_currencies' => $totalCurrencies,
                'currencies_with_rates' => $currenciesWithRates,
                'currencies_without_rates' => $currenciesWithoutRates,
                'last_updated' => !empty($exchangeRates) ? max(array_column($exchangeRates, 'rate_updated_at')) : null
            ]
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Database error in get_exchange_rates.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    error_log("General error in get_exchange_rates.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred while fetching exchange rates");
}
?>
