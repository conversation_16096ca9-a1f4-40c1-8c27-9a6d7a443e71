<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once __DIR__ . '/../includes/db_connect.php';
$conn = getDBConnection();

function sendResponse($status, $message, $data = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status === 200,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

// Simple admin check (matching existing pattern)
function isAdmin() {
    // For now, return admin ID 1. In production, implement proper admin authentication
    return 1;
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendResponse(405, "Only POST method is allowed");
    }

    // Verify admin (using same pattern as other admin endpoints)
    $adminId = isAdmin();
    if (!$adminId) {
        sendResponse(403, "Admin privileges required");
    }

    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    $action = $input['action'] ?? null;
    $targetUserId = $input['userId'] ?? null;
    $actionType = $input['actionType'] ?? null;
    
    if (!$action || !$targetUserId) {
        throw new Exception("Missing required parameters");
    }

    // Verify target user exists (handle missing columns gracefully)
    // First check what columns exist
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $hasOtpEnabled = false;
    $hasTfaEnabled = false;
    $hasAuthMethod = false;

    foreach ($columns as $column) {
        if ($column['Field'] === 'otp_enabled') $hasOtpEnabled = true;
        if ($column['Field'] === 'tfa_enabled') $hasTfaEnabled = true;
        if ($column['Field'] === 'auth_method') $hasAuthMethod = true;
    }

    // Build query based on available columns
    $selectColumns = "user_id, username, email, role";
    if ($hasOtpEnabled) $selectColumns .= ", otp_enabled";
    if ($hasTfaEnabled) $selectColumns .= ", tfa_enabled";
    if ($hasAuthMethod) $selectColumns .= ", auth_method";

    $stmt = $conn->prepare("SELECT $selectColumns FROM users WHERE user_id = ?");
    $stmt->execute([$targetUserId]);
    $targetUser = $stmt->fetch(PDO::FETCH_ASSOC);

    // Set default values for missing columns
    if (!$hasOtpEnabled) $targetUser['otp_enabled'] = 0;
    if (!$hasTfaEnabled) $targetUser['tfa_enabled'] = 0;
    if (!$hasAuthMethod) $targetUser['auth_method'] = 'password_only';
    
    if (!$targetUser) {
        throw new Exception("Target user not found");
    }

    // Prevent admin from modifying other admin accounts (security measure)
    if ($targetUser['role'] === 'admin' && $targetUserId != $adminUserId) {
        throw new Exception("Cannot modify other admin accounts");
    }

    $conn->beginTransaction();
    
    try {
        $message = '';
        $logDetails = '';
        
        switch ($action) {
            case 'disable_otp':
                $message = disableOTP($conn, $targetUserId, $targetUser);
                $logDetails = "Disabled OTP for user {$targetUser['username']} ({$targetUser['email']})";
                break;
                
            case 'disable_2fa':
                $message = disable2FA($conn, $targetUserId, $targetUser);
                $logDetails = "Disabled 2FA for user {$targetUser['username']} ({$targetUser['email']})";
                break;
                
            case 'reset_auth':
                $message = resetAuthentication($conn, $targetUserId, $targetUser);
                $logDetails = "Reset authentication to password-only for user {$targetUser['username']} ({$targetUser['email']})";
                break;
                
            default:
                throw new Exception("Invalid action specified");
        }
        
        // Log the admin action (simplified)
        error_log("Admin $adminId performed action '$action' on user $targetUserId: $logDetails");

        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => $message
        ]);
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to perform security action: ' . $e->getMessage()
    ]);
}

function disableOTP($conn, $userId, $user) {
    // Check if OTP is enabled (handle missing column gracefully)
    $otpEnabled = isset($user['otp_enabled']) ? $user['otp_enabled'] : 0;
    if (!$otpEnabled) {
        throw new Exception("OTP is not enabled for this user");
    }

    // Disable OTP in users table
    $stmt = $conn->prepare("UPDATE users SET otp_enabled = 0 WHERE user_id = ?");
    $stmt->execute([$userId]);

    // Disable OTP setup (check if table exists first)
    $stmt = $conn->query("SHOW TABLES LIKE 'user_otp'");
    if ($stmt->rowCount() > 0) {
        // Check if is_enabled column exists
        $stmt = $conn->query("DESCRIBE user_otp");
        $otpColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasIsEnabled = false;
        foreach ($otpColumns as $column) {
            if ($column['Field'] === 'is_enabled') {
                $hasIsEnabled = true;
                break;
            }
        }

        if ($hasIsEnabled) {
            $stmt = $conn->prepare("UPDATE user_otp SET is_enabled = 0 WHERE user_id = ?");
            $stmt->execute([$userId]);
        } else {
            // Just delete OTP records if is_enabled column doesn't exist
            $stmt = $conn->prepare("DELETE FROM user_otp WHERE user_id = ?");
            $stmt->execute([$userId]);
        }
    }

    // Update auth method
    $newAuthMethod = 'password_only';
    $tfaEnabled = isset($user['tfa_enabled']) ? $user['tfa_enabled'] : 0;
    if ($tfaEnabled) {
        $newAuthMethod = 'password_2fa';
    }

    $stmt = $conn->prepare("UPDATE users SET auth_method = ? WHERE user_id = ?");
    $stmt->execute([$newAuthMethod, $userId]);

    return "OTP has been successfully disabled for user {$user['username']}";
}

function disable2FA($conn, $userId, $user) {
    // Check if 2FA is enabled (handle missing column gracefully)
    $tfaEnabled = isset($user['tfa_enabled']) ? $user['tfa_enabled'] : 0;
    if (!$tfaEnabled) {
        throw new Exception("2FA is not enabled for this user");
    }

    // Disable 2FA in users table
    $stmt = $conn->prepare("UPDATE users SET tfa_enabled = 0 WHERE user_id = ?");
    $stmt->execute([$userId]);

    // Disable 2FA setup (check if table exists first)
    $stmt = $conn->query("SHOW TABLES LIKE 'user_2fa'");
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("UPDATE user_2fa SET is_enabled = 0 WHERE user_id = ?");
        $stmt->execute([$userId]);
    }

    // Update auth method
    $newAuthMethod = 'password_only';
    $otpEnabled = isset($user['otp_enabled']) ? $user['otp_enabled'] : 0;
    if ($otpEnabled) {
        $newAuthMethod = 'password_otp';
    }

    $stmt = $conn->prepare("UPDATE users SET auth_method = ? WHERE user_id = ?");
    $stmt->execute([$newAuthMethod, $userId]);

    return "2FA has been successfully disabled for user {$user['username']}";
}

function resetAuthentication($conn, $userId, $user) {
    // Check if required columns exist in users table
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $hasOtpEnabled = false;
    $hasTfaEnabled = false;
    $hasAuthMethod = false;

    foreach ($columns as $column) {
        if ($column['Field'] === 'otp_enabled') $hasOtpEnabled = true;
        if ($column['Field'] === 'tfa_enabled') $hasTfaEnabled = true;
        if ($column['Field'] === 'auth_method') $hasAuthMethod = true;
    }

    // Add missing columns if they don't exist
    if (!$hasOtpEnabled) {
        $conn->exec("ALTER TABLE users ADD COLUMN otp_enabled BOOLEAN DEFAULT 0");
    }
    if (!$hasTfaEnabled) {
        $conn->exec("ALTER TABLE users ADD COLUMN tfa_enabled BOOLEAN DEFAULT 0");
    }
    if (!$hasAuthMethod) {
        $conn->exec("ALTER TABLE users ADD COLUMN auth_method ENUM('password_only', 'password_otp', 'password_2fa', 'password_otp_2fa') DEFAULT 'password_only'");
    }

    // Disable both OTP and 2FA
    $stmt = $conn->prepare("UPDATE users SET otp_enabled = 0, tfa_enabled = 0, auth_method = 'password_only' WHERE user_id = ?");
    $stmt->execute([$userId]);

    // Disable OTP setup (check if table exists first)
    $stmt = $conn->query("SHOW TABLES LIKE 'user_otp'");
    if ($stmt->rowCount() > 0) {
        // Check if is_enabled column exists
        $stmt = $conn->query("DESCRIBE user_otp");
        $otpColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasIsEnabled = false;
        foreach ($otpColumns as $column) {
            if ($column['Field'] === 'is_enabled') {
                $hasIsEnabled = true;
                break;
            }
        }

        if ($hasIsEnabled) {
            $stmt = $conn->prepare("UPDATE user_otp SET is_enabled = 0 WHERE user_id = ?");
            $stmt->execute([$userId]);
        } else {
            // Just delete OTP records if is_enabled column doesn't exist
            $stmt = $conn->prepare("DELETE FROM user_otp WHERE user_id = ?");
            $stmt->execute([$userId]);
        }
    }

    // Disable 2FA setup (check if table exists first)
    $stmt = $conn->query("SHOW TABLES LIKE 'user_2fa'");
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("UPDATE user_2fa SET is_enabled = 0 WHERE user_id = ?");
        $stmt->execute([$userId]);
    }

    return "Authentication has been reset to password-only for user {$user['username']}";
}

// Simplified logging - just use error_log for now
function logAdminSecurityAction($adminId, $targetUserId, $action, $details) {
    error_log("Admin Security Action: Admin $adminId performed '$action' on user $targetUserId - $details");
}
?>
