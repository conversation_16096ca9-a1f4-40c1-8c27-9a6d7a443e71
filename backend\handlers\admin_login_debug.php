<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Return basic server information for debugging
$response = [
    'success' => true,
    'message' => 'Admin login debug endpoint is working',
    'server_info' => [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'],
        'document_root' => $_SERVER['DOCUMENT_ROOT'],
        'script_filename' => $_SERVER['SCRIPT_FILENAME'],
        'request_uri' => $_SERVER['REQUEST_URI'],
        'request_method' => $_SERVER['REQUEST_METHOD'],
        'remote_addr' => $_SERVER['REMOTE_ADDR'],
        'http_host' => $_SERVER['HTTP_HOST'],
        'time' => date('Y-m-d H:i:s')
    ],
    'request_headers' => getallheaders()
];

// Check if the database connection file exists
$dbConnectPath = '../includes/db_connect.php';
$response['db_connect_exists'] = file_exists($dbConnectPath);

// Try to include the database connection file
if ($response['db_connect_exists']) {
    try {
        include_once $dbConnectPath;
        $conn = getDBConnection();
        $response['db_connection'] = ($conn !== null);
        
        // Check if the admins table exists and has data
        if ($conn !== null) {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM admins");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $response['admins_count'] = $result['count'];
        }
    } catch (Exception $e) {
        $response['db_error'] = $e->getMessage();
    }
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
