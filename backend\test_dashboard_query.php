<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Testing dashboard queries...\n\n";
    
    // Test the enhanced recent activity query
    echo "1. Testing recent activity query...\n";
    $stmt = $conn->query("
        (SELECT 
            'bet' as activity_type,
            u.user_id,
            u.username,
            u.balance,
            b.amount_user1 as amount,
            b.created_at as activity_time,
            c.team_a,
            c.team_b,
            'placed a bet' as action_description,
            b.potential_return_win_user1 as potential_return
        FROM bets b
        JOIN users u ON b.user1_id = u.user_id
        JOIN challenges c ON b.challenge_id = c.challenge_id
        ORDER BY b.created_at DESC
        LIMIT 10)
        
        UNION ALL
        
        (SELECT 
            'registration' as activity_type,
            u.user_id,
            u.username,
            u.balance,
            0 as amount,
            u.created_at as activity_time,
            NULL as team_a,
            NULL as team_b,
            'registered' as action_description,
            0 as potential_return
        FROM users u
        WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY u.created_at DESC
        LIMIT 5)
        
        UNION ALL
        
        (SELECT 
            'transaction' as activity_type,
            u.user_id,
            u.username,
            u.balance,
            t.amount,
            t.created_at as activity_time,
            NULL as team_a,
            NULL as team_b,
            CASE 
                WHEN t.type = 'admin_credit' THEN 'received credit'
                WHEN t.type = 'withdrawal' THEN 'made withdrawal'
                WHEN t.type = 'win' THEN 'won a bet'
                WHEN t.type = 'bet' THEN 'placed a bet'
                WHEN t.type = 'deposit' THEN 'made deposit'
                WHEN t.type = 'transfer_received' THEN 'received transfer'
                WHEN t.type = 'transfer_sent' THEN 'sent transfer'
                ELSE t.type
            END as action_description,
            t.amount as potential_return
        FROM transactions t
        JOIN users u ON t.user_id = u.user_id
        WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY t.created_at DESC
        LIMIT 10)
        
        ORDER BY activity_time DESC
        LIMIT 8
    ");
    
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ Query successful! Found " . count($results) . " activities\n";
    
    foreach ($results as $activity) {
        echo "- {$activity['username']}: {$activity['action_description']} ({$activity['activity_time']})\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "SQL Error: " . $e->getCode() . "\n";
}
?>