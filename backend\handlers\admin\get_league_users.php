<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once '../../includes/db_connect.php';
$conn = getDBConnection();

// Check if the request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['status' => 405, 'message' => 'Method not allowed']);
    exit;
}

// Get league ID from query parameters
if (!isset($_GET['league_id'])) {
    http_response_code(400);
    echo json_encode(['status' => 400, 'message' => 'League ID is required']);
    exit;
}

$leagueId = $_GET['league_id'];

try {
    // Fetch users and their stats for the specified league
    $query = "SELECT 
        u.user_id,
        u.username,
        u.balance,
        lm.status as membership_status,
        lm.join_date,
        lm.deposit_amount,
        COALESCE(uls.points, 0) as points,
        COALESCE(uls.wins, 0) as wins,
        COALESCE(uls.draws, 0) as draws,
        COALESCE(uls.losses, 0) as losses,
        ul.status as league_status
    FROM league_memberships lm
    JOIN users u ON lm.user_id = u.user_id
    JOIN user_leagues ul ON ul.user_id = u.user_id AND ul.league_id = lm.league_id
    LEFT JOIN user_league_stats uls ON uls.user_id = u.user_id AND uls.league_id = lm.league_id
    WHERE lm.league_id = ? AND lm.status = 'active'
    ORDER BY COALESCE(uls.points, 0) DESC, COALESCE(uls.wins, 0) DESC";

    $stmt = $conn->prepare($query);
    $stmt->execute([$leagueId]);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format the response
    foreach ($users as &$user) {
        // Convert numeric values
        $user['balance'] = (float)$user['balance'];
        $user['deposit_amount'] = (float)$user['deposit_amount'];
        $user['points'] = (int)$user['points'];
        $user['wins'] = (int)$user['wins'];
        $user['draws'] = (int)$user['draws'];
        $user['losses'] = (int)$user['losses'];
        // Format join date
        $user['join_date'] = date('Y-m-d H:i:s', strtotime($user['join_date']));
    }

    echo json_encode([
        'status' => 200,
        'message' => 'League users retrieved successfully',
        'data' => $users
    ]);

} catch (Exception $e) {
    error_log("Error in get_league_users.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'status' => 500,
        'message' => 'Failed to fetch league users: ' . $e->getMessage()
    ]);
}
?> 