-- FanBet247 Multi-Currency System Database Schema
-- This script creates the necessary tables for the multi-currency system
-- Execute this script after backing up your database

-- Create currencies table
CREATE TABLE IF NOT EXISTS currencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    currency_code VARCHAR(3) NOT NULL UNIQUE COMMENT 'ISO currency code (USD, ZAR, etc.)',
    currency_name VARCHAR(50) NOT NULL COMMENT 'Full currency name',
    currency_symbol VARCHAR(10) NOT NULL COMMENT 'Currency symbol ($, R, etc.)',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether currency is available for selection',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_currency_code (currency_code),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Available currencies for user selection';

-- Create exchange_rates table
CREATE TABLE IF NOT EXISTS exchange_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    currency_id INT NOT NULL,
    rate_to_fancoin DECIMAL(10,4) NOT NULL COMMENT 'How many units of this currency = 1 FanCoin',
    updated_by_admin_id INT NOT NULL COMMENT 'Admin who last updated this rate',
    notes TEXT COMMENT 'Optional notes about the rate update',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by_admin_id) REFERENCES admins(admin_id) ON DELETE RESTRICT,
    INDEX idx_currency_id (currency_id),
    INDEX idx_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Exchange rates for converting FanCoin to other currencies';

-- Add preferred_currency to users table
ALTER TABLE users 
ADD COLUMN preferred_currency_id INT DEFAULT 1 COMMENT 'User preferred currency for display',
ADD FOREIGN KEY (preferred_currency_id) REFERENCES currencies(id) ON DELETE SET NULL;

-- Create index for better performance
ALTER TABLE users ADD INDEX idx_preferred_currency (preferred_currency_id);

-- Insert default currencies
INSERT INTO currencies (currency_code, currency_name, currency_symbol, is_active) VALUES
('USD', 'US Dollar', '$', TRUE),
('ZAR', 'South African Rand', 'R', TRUE),
('EUR', 'Euro', '€', TRUE),
('GBP', 'British Pound', '£', TRUE),
('CAD', 'Canadian Dollar', 'C$', TRUE),
('AUD', 'Australian Dollar', 'A$', TRUE);

-- Insert default exchange rates (1 FanCoin = 1 USD base)
-- Note: You'll need to replace admin_id with an actual admin ID from your system
INSERT INTO exchange_rates (currency_id, rate_to_fancoin, updated_by_admin_id, notes) VALUES
(1, 1.0000, 1, 'Base rate - 1 FanCoin = 1 USD'),
(2, 18.0000, 1, 'Initial rate - 1 FanCoin = 18 ZAR'),
(3, 0.9200, 1, 'Initial rate - 1 FanCoin = 0.92 EUR'),
(4, 0.8000, 1, 'Initial rate - 1 FanCoin = 0.80 GBP'),
(5, 1.3500, 1, 'Initial rate - 1 FanCoin = 1.35 CAD'),
(6, 1.5000, 1, 'Initial rate - 1 FanCoin = 1.50 AUD');

-- Create a view for easy currency rate lookups
CREATE OR REPLACE VIEW v_currency_rates AS
SELECT 
    c.id as currency_id,
    c.currency_code,
    c.currency_name,
    c.currency_symbol,
    c.is_active,
    er.rate_to_fancoin,
    er.updated_at as rate_updated_at,
    CONCAT(c.currency_symbol, ' ', FORMAT(er.rate_to_fancoin, 4)) as formatted_rate
FROM currencies c
LEFT JOIN exchange_rates er ON c.id = er.currency_id
WHERE c.is_active = TRUE
ORDER BY c.currency_code;

-- Create a function to convert FanCoin to user currency
DELIMITER //
CREATE FUNCTION ConvertFanCoinToCurrency(fancoin_amount DECIMAL(10,2), currency_id INT)
RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE exchange_rate DECIMAL(10,4);
    DECLARE converted_amount DECIMAL(10,2);
    
    -- Get the exchange rate
    SELECT rate_to_fancoin INTO exchange_rate
    FROM exchange_rates 
    WHERE currency_id = currency_id
    ORDER BY updated_at DESC 
    LIMIT 1;
    
    -- If no rate found, return original amount
    IF exchange_rate IS NULL THEN
        RETURN fancoin_amount;
    END IF;
    
    -- Calculate converted amount
    SET converted_amount = fancoin_amount * exchange_rate;
    
    RETURN converted_amount;
END //
DELIMITER ;

-- Update existing users to have USD as default currency (currency_id = 1)
UPDATE users SET preferred_currency_id = 1 WHERE preferred_currency_id IS NULL;
