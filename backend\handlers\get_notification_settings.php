<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Simple authentication check - in production, implement proper token validation
    // For now, we'll trust the frontend authentication since admin routes are protected
    // This is acceptable for development but should be enhanced for production
    
    $stmt = $conn->prepare("SELECT setting_name, setting_value, description FROM notification_settings");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $settings[$row['setting_name']] = [
                'value' => $row['setting_value'],
                'description' => $row['description']
            ];
        }
        
        http_response_code(200);
        echo json_encode([
            "success" => true,
            "settings" => $settings
        ]);
    } else {
        // Return default settings if none exist
        $defaultSettings = [
            'email_notifications_enabled' => [
                'value' => 'true',
                'description' => 'Enable email notifications'
            ],
            'bet_notifications' => [
                'value' => 'true',
                'description' => 'Send notifications for bet activities'
            ],
            'challenge_notifications' => [
                'value' => 'true',
                'description' => 'Send notifications for challenge activities'
            ],
            'league_notifications' => [
                'value' => 'true',
                'description' => 'Send notifications for league activities'
            ],
            'admin_notifications' => [
                'value' => 'true',
                'description' => 'Send notifications for admin activities'
            ],
            'notification_frequency' => [
                'value' => 'immediate',
                'description' => 'Frequency of notifications (immediate, hourly, daily)'
            ]
        ];
        
        http_response_code(200);
        echo json_encode([
            "success" => true,
            "settings" => $defaultSettings,
            "message" => "Using default notification settings"
        ]);
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
}
?>
