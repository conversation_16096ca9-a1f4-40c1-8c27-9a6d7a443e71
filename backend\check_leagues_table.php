<?php
require_once 'includes/db_connect.php';

try {
    $db = getDBConnection();
    
    echo "=== Leagues Table Structure ===\n";
    $result = $db->query('DESCRIBE leagues');
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo $row['Field'] . " - " . $row['Type'] . "\n";
    }
    
    echo "\n=== Sample Leagues Data ===\n";
    $result = $db->query('SELECT * FROM leagues LIMIT 5');
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "ID: " . $row['league_id'] . " - Name: " . $row['name'] . "\n";
    }
    
    echo "\n=== Total Leagues Count ===\n";
    $result = $db->query('SELECT COUNT(*) as total FROM leagues');
    $count = $result->fetch(PDO::FETCH_ASSOC);
    echo "Total leagues: " . $count['total'] . "\n";
    
    echo "\n=== Bets Table Structure ===\n";
    $result = $db->query('DESCRIBE bets');
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo $row['Field'] . " - " . $row['Type'] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>