<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

if (isset($_GET['betId'])) {
    $betId = $_GET['betId'];

    $query = "SELECT b.*, c.team_a, c.team_b, u1.username as user1_name 
              FROM bets b
              JOIN challenges c ON b.challenge_id = c.challenge_id
              JOIN users u1 ON b.user1_id = u1.user_id
              WHERE b.bet_id = ?";

    $stmt = $conn->prepare($query);
    $stmt->execute([$betId]);
    $bet = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($bet) {
        echo json_encode(["success" => true, "bet" => $bet]);
    } else {
        echo json_encode(["success" => false, "message" => "Bet not found."]);
    }
} else {
    echo json_encode(["success" => false, "message" => "Bet ID not provided."]);
}
