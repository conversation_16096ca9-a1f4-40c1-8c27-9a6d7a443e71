<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed");
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $userId = $input['userId'] ?? null;
    
    if (!$userId) {
        throw new Exception("User ID is required");
    }
    
    // Verify user exists and has 2FA enabled
    $stmt = $conn->prepare("SELECT user_id, username, email, otp_enabled, tfa_enabled, auth_method FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    if (!$user['tfa_enabled']) {
        throw new Exception("2FA is not currently enabled for this user");
    }
    
    $conn->beginTransaction();
    
    // Disable 2FA in user_2fa table
    $stmt = $conn->prepare("UPDATE user_2fa SET is_enabled = 0 WHERE user_id = ?");
    $stmt->execute([$userId]);
    
    // Update user's 2FA status
    $stmt = $conn->prepare("UPDATE users SET tfa_enabled = 0 WHERE user_id = ?");
    $stmt->execute([$userId]);
    
    // Update auth method based on OTP setting
    $newAuthMethod = $user['otp_enabled'] ? 'password_otp' : 'password_only';
    $stmt = $conn->prepare("UPDATE users SET auth_method = ? WHERE user_id = ?");
    $stmt->execute([$newAuthMethod, $userId]);
    
    // Log the change
    $stmt = $conn->prepare("
        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, '2fa', 'tfa_disabled', ?, ?, ?)
    ");
    $stmt->execute([
        $userId,
        json_encode([
            'tfa_enabled' => false,
            'previous_auth_method' => $user['auth_method'],
            'new_auth_method' => $newAuthMethod,
            'disabled_by' => 'user'
        ]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => '2FA has been disabled successfully. Your account is now protected by password' . ($user['otp_enabled'] ? ' and OTP' : ' only') . '.',
        'tfa_enabled' => false,
        'auth_method' => $newAuthMethod
    ]);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
