<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get input data
    $data = json_decode(file_get_contents("php://input"), true);
    
    if (!isset($data['request_id']) || !isset($data['status'])) {
        throw new Exception('Missing required fields');
    }

    $requestId = $data['request_id'];
    $newStatus = $data['status'];

    // Start transaction
    $conn->beginTransaction();

    // Get credit request details
    $stmt = $conn->prepare("
        SELECT cr.*, u.balance, u.user_id 
        FROM credit_requests cr
        JOIN users u ON cr.user_id = u.user_id
        WHERE cr.request_id = :request_id
    ");
    $stmt->bindParam(':request_id', $requestId);
    $stmt->execute();
    $request = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$request) {
        throw new Exception('Credit request not found');
    }

    if ($request['status'] !== 'pending') {
        throw new Exception('Can only update pending requests');
    }

    // Update credit request status
    $updateRequest = $conn->prepare("
        UPDATE credit_requests 
        SET status = :status
        WHERE request_id = :request_id
    ");
    $updateRequest->bindParam(':status', $newStatus);
    $updateRequest->bindParam(':request_id', $requestId);
    $updateRequest->execute();

    // If approved, update user balance
    if ($newStatus === 'approved') {
        $newBalance = $request['balance'] + $request['amount'];
        
        $updateBalance = $conn->prepare("
            UPDATE users 
            SET balance = :balance 
            WHERE user_id = :user_id
        ");
        $updateBalance->bindParam(':balance', $newBalance);
        $updateBalance->bindParam(':user_id', $request['user_id']);
        $updateBalance->execute();

        // Record transaction (updated to match table structure)
        $recordTransaction = $conn->prepare("
            INSERT INTO transactions 
            (user_id, amount, type, status, admin_id) 
            VALUES 
            (:user_id, :amount, 'admin_credit', 'completed', 1)
        ");
        $recordTransaction->bindParam(':user_id', $request['user_id']);
        $recordTransaction->bindParam(':amount', $request['amount']);
        $recordTransaction->execute();
    }

    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Credit request updated successfully'
    ]);

} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    error_log("Error in admin/update_credit_request.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 