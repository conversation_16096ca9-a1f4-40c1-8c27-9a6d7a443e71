<?php
/**
 * Database Schema Analysis Script for FanBet247
 * 
 * This script analyzes and documents the complete database structure
 * with focus on authentication-related tables and their relationships.
 * 
 * Purpose: Comprehensive database documentation for OTP/2FA system
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'includes/db_connect.php';

class DatabaseAnalyzer {
    private $conn;
    private $reportFile;
    private $authTables = [
        'users',
        'user_otp', 
        'user_auth_logs',
        'user_2fa',
        'user_sessions',
        'smtp_settings',
        'admins',
        'admin_2fa',
        'admin_otp',
        'admin_auth_logs'
    ];
    
    public function __construct() {
        $this->conn = getDBConnection();
        $this->reportFile = 'database_analysis_' . date('Y-m-d_H-i-s') . '.md';
        $this->log("# FanBet247 Database Analysis Report");
        $this->log("Generated on: " . date('Y-m-d H:i:s') . "\n");
    }
    
    private function log($message) {
        file_put_contents($this->reportFile, $message . "\n", FILE_APPEND);
        echo $message . "\n";
    }
    
    public function runAnalysis() {
        $this->log("## 🔍 Database Analysis Overview");
        $this->log("This report provides comprehensive analysis of the FanBet247 database structure.");
        $this->log("Focus: Authentication system tables and relationships\n");
        
        // Analysis sections
        $this->analyzeDatabaseInfo();
        $this->analyzeAllTables();
        $this->analyzeAuthenticationTables();
        $this->analyzeSMTPConfiguration();
        $this->analyzeUserData();
        $this->analyzeTableRelationships();
        $this->generateRecommendations();
        
        $this->log("\n## ✅ Analysis Complete");
        $this->log("Report saved as: " . $this->reportFile);
        
        return true;
    }
    
    private function analyzeDatabaseInfo() {
        $this->log("## 📊 Database Information");
        
        try {
            // Get database name
            $stmt = $this->conn->prepare("SELECT DATABASE() as db_name");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->log("**Database Name:** " . $result['db_name']);
            
            // Get MySQL version
            $stmt = $this->conn->prepare("SELECT VERSION() as version");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->log("**MySQL Version:** " . $result['version']);
            
            // Get character set
            $stmt = $this->conn->prepare("SELECT @@character_set_database as charset");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->log("**Character Set:** " . $result['charset']);
            
            // Count total tables
            $stmt = $this->conn->prepare("SHOW TABLES");
            $stmt->execute();
            $tableCount = $stmt->rowCount();
            $this->log("**Total Tables:** " . $tableCount . "\n");
            
        } catch (Exception $e) {
            $this->log("❌ Error getting database info: " . $e->getMessage() . "\n");
        }
    }
    
    private function analyzeAllTables() {
        $this->log("## 📋 All Database Tables");
        
        try {
            $stmt = $this->conn->prepare("SHOW TABLES");
            $stmt->execute();
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $this->log("| Table Name | Type | Rows | Size |");
            $this->log("|------------|------|------|------|");
            
            foreach ($tables as $table) {
                $info = $this->getTableInfo($table);
                $type = in_array($table, $this->authTables) ? "🔐 Auth" : "📊 Data";
                $this->log("| `$table` | $type | {$info['rows']} | {$info['size']} |");
            }
            
            $this->log("");
            
        } catch (Exception $e) {
            $this->log("❌ Error analyzing tables: " . $e->getMessage() . "\n");
        }
    }
    
    private function getTableInfo($tableName) {
        try {
            // Get row count
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM `$tableName`");
            $stmt->execute();
            $rowCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Get table size
            $stmt = $this->conn->prepare("
                SELECT 
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                FROM information_schema.TABLES 
                WHERE table_schema = DATABASE() AND table_name = ?
            ");
            $stmt->execute([$tableName]);
            $sizeInfo = $stmt->fetch(PDO::FETCH_ASSOC);
            $size = $sizeInfo['size_mb'] . ' MB';
            
            return [
                'rows' => number_format($rowCount),
                'size' => $size
            ];
            
        } catch (Exception $e) {
            return [
                'rows' => 'Error',
                'size' => 'Error'
            ];
        }
    }
    
    private function analyzeAuthenticationTables() {
        $this->log("## 🔐 Authentication Tables Analysis");
        
        foreach ($this->authTables as $table) {
            if ($this->tableExists($table)) {
                $this->analyzeTable($table);
            } else {
                $this->log("### ❌ Table `$table` - NOT FOUND\n");
            }
        }
    }
    
    private function tableExists($tableName) {
        try {
            $stmt = $this->conn->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$tableName]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function analyzeTable($tableName) {
        $this->log("### 📊 Table: `$tableName`");
        
        try {
            // Get table structure
            $stmt = $this->conn->prepare("DESCRIBE `$tableName`");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->log("**Structure:**");
            $this->log("| Column | Type | Null | Key | Default | Extra |");
            $this->log("|--------|------|------|-----|---------|-------|");
            
            foreach ($columns as $column) {
                $this->log("| `{$column['Field']}` | {$column['Type']} | {$column['Null']} | {$column['Key']} | {$column['Default']} | {$column['Extra']} |");
            }
            
            // Get row count
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM `$tableName`");
            $stmt->execute();
            $rowCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            $this->log("\n**Row Count:** " . number_format($rowCount));
            
            // Get sample data (anonymized)
            if ($rowCount > 0) {
                $this->getSampleData($tableName, $columns);
            }
            
            // Get foreign keys
            $this->getForeignKeys($tableName);
            
            $this->log("");
            
        } catch (Exception $e) {
            $this->log("❌ Error analyzing table $tableName: " . $e->getMessage() . "\n");
        }
    }
    
    private function getSampleData($tableName, $columns) {
        try {
            $this->log("\n**Sample Data (Top 3 rows, anonymized):**");
            
            $stmt = $this->conn->prepare("SELECT * FROM `$tableName` LIMIT 3");
            $stmt->execute();
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($rows)) {
                $this->log("*No data found*");
                return;
            }
            
            // Create header
            $headers = array_keys($rows[0]);
            $this->log("| " . implode(" | ", $headers) . " |");
            $this->log("|" . str_repeat("---|", count($headers)) . "");
            
            // Display rows with anonymization
            foreach ($rows as $row) {
                $anonymizedRow = [];
                foreach ($row as $key => $value) {
                    $anonymizedRow[] = $this->anonymizeValue($key, $value);
                }
                $this->log("| " . implode(" | ", $anonymizedRow) . " |");
            }
            
        } catch (Exception $e) {
            $this->log("❌ Error getting sample data: " . $e->getMessage());
        }
    }
    
    private function anonymizeValue($columnName, $value) {
        // Anonymize sensitive data
        $sensitiveColumns = ['password', 'email', 'username', 'secret_key', 'backup_codes', 'otp'];
        
        if (is_null($value)) {
            return 'NULL';
        }
        
        $columnLower = strtolower($columnName);
        
        foreach ($sensitiveColumns as $sensitive) {
            if (strpos($columnLower, $sensitive) !== false) {
                if ($sensitive === 'email') {
                    return 'user***@example.com';
                } elseif ($sensitive === 'username') {
                    return 'user***';
                } elseif ($sensitive === 'password') {
                    return '***hashed***';
                } elseif ($sensitive === 'otp') {
                    return '123***';
                } else {
                    return '***hidden***';
                }
            }
        }
        
        // Truncate long values
        if (strlen($value) > 50) {
            return substr($value, 0, 47) . '...';
        }
        
        return $value;
    }
    
    private function getForeignKeys($tableName) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = ? 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            $stmt->execute([$tableName]);
            $foreignKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($foreignKeys)) {
                $this->log("\n**Foreign Keys:**");
                foreach ($foreignKeys as $fk) {
                    $this->log("- `{$fk['COLUMN_NAME']}` → `{$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}`");
                }
            }
            
        } catch (Exception $e) {
            $this->log("❌ Error getting foreign keys: " . $e->getMessage());
        }
    }
    
    private function analyzeSMTPConfiguration() {
        $this->log("## 📧 SMTP Configuration Analysis");
        
        if (!$this->tableExists('smtp_settings')) {
            $this->log("❌ smtp_settings table not found\n");
            return;
        }
        
        try {
            $stmt = $this->conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
            $stmt->execute();
            $smtp = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($smtp) {
                $this->log("**Active SMTP Configuration:**");
                $this->log("- Host: " . ($smtp['host'] ?? 'Not set'));
                $this->log("- Port: " . ($smtp['port'] ?? 'Not set'));
                $this->log("- Username: " . ($smtp['username'] ?? 'Not set'));
                $this->log("- Password: " . (isset($smtp['password']) && !empty($smtp['password']) ? '***configured***' : 'Not set'));
                $this->log("- Encryption: " . ($smtp['encryption'] ?? 'Not set'));
                $this->log("- From Email: " . ($smtp['from_email'] ?? 'Not set'));
                $this->log("- From Name: " . ($smtp['from_name'] ?? 'Not set'));
                $this->log("- Status: " . ($smtp['is_active'] ? 'Active' : 'Inactive'));
                
                // Check configuration completeness
                $required = ['host', 'port', 'username', 'password', 'from_email'];
                $missing = [];
                foreach ($required as $field) {
                    if (empty($smtp[$field])) {
                        $missing[] = $field;
                    }
                }
                
                if (empty($missing)) {
                    $this->log("- **Configuration Status:** ✅ Complete");
                } else {
                    $this->log("- **Configuration Status:** ❌ Missing: " . implode(', ', $missing));
                }
            } else {
                $this->log("❌ No active SMTP configuration found");
            }
            
            $this->log("");
            
        } catch (Exception $e) {
            $this->log("❌ Error analyzing SMTP configuration: " . $e->getMessage() . "\n");
        }
    }
    
    private function analyzeUserData() {
        $this->log("## 👥 User Authentication Data Analysis");
        
        if (!$this->tableExists('users')) {
            $this->log("❌ users table not found\n");
            return;
        }
        
        try {
            // Total users
            $stmt = $this->conn->prepare("SELECT COUNT(*) as total FROM users");
            $stmt->execute();
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            $this->log("**Total Users:** " . number_format($total));
            
            // Active users
            $stmt = $this->conn->prepare("SELECT COUNT(*) as active FROM users WHERE status = 'active'");
            $stmt->execute();
            $active = $stmt->fetch(PDO::FETCH_ASSOC)['active'];
            $this->log("**Active Users:** " . number_format($active));
            
            // Authentication methods
            $stmt = $this->conn->prepare("
                SELECT 
                    COALESCE(auth_method, 'password_only') as method,
                    COUNT(*) as count 
                FROM users 
                GROUP BY COALESCE(auth_method, 'password_only')
                ORDER BY count DESC
            ");
            $stmt->execute();
            $authMethods = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->log("\n**Authentication Methods:**");
            foreach ($authMethods as $method) {
                $percentage = round(($method['count'] / $total) * 100, 1);
                $this->log("- {$method['method']}: {$method['count']} users ({$percentage}%)");
            }
            
            // OTP enabled users
            if ($this->columnExists('users', 'otp_enabled')) {
                $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM users WHERE otp_enabled = 1");
                $stmt->execute();
                $otpEnabled = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                $otpPercentage = $total > 0 ? round(($otpEnabled / $total) * 100, 1) : 0;
                $this->log("- **OTP Enabled:** $otpEnabled users ($otpPercentage%)");
            }
            
            // 2FA enabled users
            if ($this->columnExists('users', 'tfa_enabled')) {
                $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM users WHERE tfa_enabled = 1");
                $stmt->execute();
                $tfaEnabled = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                $tfaPercentage = $total > 0 ? round(($tfaEnabled / $total) * 100, 1) : 0;
                $this->log("- **2FA Enabled:** $tfaEnabled users ($tfaPercentage%)");
            }
            
            $this->log("");
            
        } catch (Exception $e) {
            $this->log("❌ Error analyzing user data: " . $e->getMessage() . "\n");
        }
    }
    
    private function columnExists($tableName, $columnName) {
        try {
            $stmt = $this->conn->prepare("SHOW COLUMNS FROM `$tableName` LIKE ?");
            $stmt->execute([$columnName]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function analyzeTableRelationships() {
        $this->log("## 🔗 Table Relationships");
        
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
                ORDER BY TABLE_NAME, COLUMN_NAME
            ");
            $stmt->execute();
            $relationships = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($relationships)) {
                $this->log("**Foreign Key Relationships:**");
                $this->log("| From Table | From Column | To Table | To Column |");
                $this->log("|------------|-------------|----------|-----------|");
                
                foreach ($relationships as $rel) {
                    $this->log("| `{$rel['TABLE_NAME']}` | `{$rel['COLUMN_NAME']}` | `{$rel['REFERENCED_TABLE_NAME']}` | `{$rel['REFERENCED_COLUMN_NAME']}` |");
                }
            } else {
                $this->log("No foreign key relationships found.");
            }
            
            $this->log("");
            
        } catch (Exception $e) {
            $this->log("❌ Error analyzing relationships: " . $e->getMessage() . "\n");
        }
    }
    
    private function generateRecommendations() {
        $this->log("## 💡 Recommendations");
        
        $recommendations = [];
        
        // Check for missing authentication tables
        $missingTables = [];
        foreach ($this->authTables as $table) {
            if (!$this->tableExists($table)) {
                $missingTables[] = $table;
            }
        }
        
        if (!empty($missingTables)) {
            $recommendations[] = "**Missing Tables:** Create the following authentication tables: " . implode(', ', $missingTables);
        }
        
        // Check SMTP configuration
        if ($this->tableExists('smtp_settings')) {
            try {
                $stmt = $this->conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1");
                $stmt->execute();
                $smtp = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$smtp) {
                    $recommendations[] = "**SMTP Configuration:** No active SMTP settings found. Configure SMTP for email functionality.";
                } else {
                    $required = ['host', 'port', 'username', 'password', 'from_email'];
                    $missing = [];
                    foreach ($required as $field) {
                        if (empty($smtp[$field])) {
                            $missing[] = $field;
                        }
                    }
                    
                    if (!empty($missing)) {
                        $recommendations[] = "**SMTP Configuration:** Missing required fields: " . implode(', ', $missing);
                    }
                }
            } catch (Exception $e) {
                $recommendations[] = "**SMTP Configuration:** Error checking SMTP settings.";
            }
        }
        
        // Check for users with OTP/2FA
        if ($this->tableExists('users')) {
            try {
                $stmt = $this->conn->prepare("SELECT COUNT(*) as total FROM users");
                $stmt->execute();
                $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
                
                if ($totalUsers > 0) {
                    if ($this->columnExists('users', 'otp_enabled')) {
                        $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM users WHERE otp_enabled = 1");
                        $stmt->execute();
                        $otpUsers = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        
                        if ($otpUsers == 0) {
                            $recommendations[] = "**Security Enhancement:** Consider encouraging users to enable OTP for enhanced security.";
                        }
                    }
                }
            } catch (Exception $e) {
                // Ignore error
            }
        }
        
        if (empty($recommendations)) {
            $this->log("✅ **All systems appear to be properly configured!**");
        } else {
            foreach ($recommendations as $i => $recommendation) {
                $this->log(($i + 1) . ". " . $recommendation);
            }
        }
        
        $this->log("");
    }
}

// Run the analysis
echo "🚀 Starting Database Analysis...\n\n";

$analyzer = new DatabaseAnalyzer();
$success = $analyzer->runAnalysis();

if ($success) {
    echo "\n🎉 Database analysis completed! Check the markdown report for detailed results.\n";
} else {
    echo "\n⚠️ Analysis encountered some issues. Check the report for details.\n";
}
?>
