<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

function validateRequest($data) {
    $required = ['betId', 'userId', 'amount'];
    foreach ($required as $field) {
        if (!isset($data->$field)) {
            return ["valid" => false, "message" => "$field is required"];
        }
    }
    return ["valid" => true];
}

try {
    $conn = getDBConnection();
    $data = json_decode(file_get_contents("php://input"));
    
    // Validate request
    $validation = validateRequest($data);
    if (!$validation["valid"]) {
        throw new Exception($validation["message"]);
    }

    $conn->beginTransaction();

    // Check user balance
    $stmt = $conn->prepare("SELECT balance FROM users WHERE user_id = ? FOR UPDATE");
    $stmt->execute([$data->userId]);
    $user = $stmt->fetch();

    if (!$user) {
        throw new Exception("User not found");
    }

    if ($user['balance'] < $data->amount) {
        throw new Exception("Insufficient balance. Required: " . $data->amount . " FanCoins");
    }

    // Check bet status
    $stmt = $conn->prepare("SELECT * FROM bets WHERE bet_id = ? AND bet_status = 'open' FOR UPDATE");
    $stmt->execute([$data->betId]);
    $bet = $stmt->fetch();

    if (!$bet) {
        throw new Exception("Bet is no longer available");
    }

    // Update bet with user2 details
    $stmt = $conn->prepare("
        UPDATE bets 
        SET bet_status = 'joined',
            user2_id = ?,
            amount_user2 = ?,
            bet_choice_user2 = ?,
            odds_user2 = ?,
            potential_return_user2 = ?,
            potential_return_win_user2 = ?,
            potential_return_draw_user2 = ?,
            potential_return_loss_user2 = ?
        WHERE bet_id = ?
    ");

    $betChoiceUser2 = $bet['bet_choice_user1'] === 'team_a_win' ? 'team_b_win' : 'team_a_win';
    
    $stmt->execute([
        $data->userId,
        $data->amount,
        $betChoiceUser2,
        $data->odds_user2,
        $data->potential_return_user2,
        $data->potential_return_win_user2,
        $data->potential_return_draw_user2,
        $data->potential_return_loss_user2,
        $data->betId
    ]);

    // Deduct amount from user balance
    $stmt = $conn->prepare("UPDATE users SET balance = balance - ? WHERE user_id = ?");
    $stmt->execute([$data->amount, $data->userId]);

    // Create transaction record
    $stmt = $conn->prepare("
        INSERT INTO transactions (user_id, amount, type, status, related_bet_id)
        VALUES (?, ?, 'bet', 'completed', ?)
    ");
    $stmt->execute([$data->userId, $data->amount, $data->betId]);

    $conn->commit();

    echo json_encode([
        "success" => true,
        "message" => "Bet accepted successfully"
    ]);

} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollBack();
    }
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}