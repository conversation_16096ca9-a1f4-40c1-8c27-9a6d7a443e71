<?php
header("Access-Control-Allow-Origin: *"); 
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';
$conn = getDBConnection();

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,  // Add success flag based on status code
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

// GET: Retrieve all payment methods
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
      $stmt = $conn->query("SELECT * FROM payment_methods"); 
      $paymentMethods = $stmt->fetchAll(PDO::FETCH_ASSOC);
      
      // Convert fields from JSON to array for each payment method
      foreach ($paymentMethods as &$method) {
        if (isset($method['fields'])) {
          $method['fields'] = json_decode($method['fields'], true) ?? [];
        } else {
          $method['fields'] = [];
        }
      }
      
      jsonResponse(200, 'Payment methods fetched successfully', $paymentMethods);
    } catch (PDOException $e) {
      jsonResponse(500, 'Database error: ' . $e->getMessage());
    }
}

// POST: Create a new payment method
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
  
    if (!$data) {
      jsonResponse(400, 'Invalid input data');
    }

    if (!isset($data['name']) || !isset($data['type']) || !isset($data['fields'])) {
      jsonResponse(400, 'Missing required fields');
    }

    try {
      $conn->beginTransaction();

      // Insert the payment method
      $stmt = $conn->prepare("INSERT INTO payment_methods (name, type) VALUES (:name, :type)");
      $stmt->bindParam(':name', $data['name']);
      $stmt->bindParam(':type', $data['type']);
      
      if ($stmt->execute()) {
        $paymentMethodId = $conn->lastInsertId();
        
        // Convert fields array to JSON and store in fields column
        $fieldsJson = json_encode($data['fields']);
        $updateFields = $conn->prepare("UPDATE payment_methods SET fields = :fields WHERE id = :id");
        $updateFields->bindParam(':fields', $fieldsJson);
        $updateFields->bindParam(':id', $paymentMethodId);
        $updateFields->execute();

        $conn->commit();
        jsonResponse(201, 'Payment method created successfully', ['id' => $paymentMethodId]);
      } else {
        $conn->rollBack();
        jsonResponse(500, 'Failed to create payment method');
      }
    } catch (PDOException $e) {
      if ($conn->inTransaction()) {
        $conn->rollBack();
      }
      jsonResponse(500, 'Database error: ' . $e->getMessage());
    }
}

// DELETE: Delete a payment method
if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $id = isset($_GET['id']) ? $_GET['id'] : null;
    if (!$id) {
      jsonResponse(400, 'Payment method ID is required');
    }

    try {
      // First check if the payment method is being used
      $checkStmt = $conn->prepare("SELECT COUNT(*) as count FROM credit_requests WHERE payment_method_id = :id");
      $checkStmt->bindParam(':id', $id);
      $checkStmt->execute();
      $result = $checkStmt->fetch(PDO::FETCH_ASSOC);
      
      if ($result['count'] > 0) {
        jsonResponse(400, 'Cannot delete this payment method as it is being used by existing credit requests. Please archive it instead.');
      }

      // If not being used, proceed with deletion
      $stmt = $conn->prepare("DELETE FROM payment_methods WHERE id = :id");
      $stmt->bindParam(':id', $id);
      if ($stmt->execute()) {
        jsonResponse(200, 'Payment method deleted successfully');
      } else {
        jsonResponse(500, 'Failed to delete payment method');
      }
    } catch (PDOException $e) {
      jsonResponse(500, 'Database error: ' . $e->getMessage());
    }
}

// PUT: Update a payment method
if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    $id = isset($_GET['id']) ? $_GET['id'] : null;
    if (!$id) {
      jsonResponse(400, 'Payment method ID is required');
    }

    $data = json_decode(file_get_contents("php://input"), true);
    if (!$data) {
      jsonResponse(400, 'Invalid input data');
    }

    try {
      $conn->beginTransaction();

      $stmt = $conn->prepare("UPDATE payment_methods SET name = :name, type = :type WHERE id = :id");
      $stmt->bindParam(':id', $id);
      $stmt->bindParam(':name', $data['name']);
      $stmt->bindParam(':type', $data['type']);
      
      if ($stmt->execute()) {
        // Update fields
        if (isset($data['fields'])) {
          $fieldsJson = json_encode($data['fields']);
          $updateFields = $conn->prepare("UPDATE payment_methods SET fields = :fields WHERE id = :id");
          $updateFields->bindParam(':fields', $fieldsJson);
          $updateFields->bindParam(':id', $id);
          $updateFields->execute();
        }
        
        $conn->commit();
        jsonResponse(200, 'Payment method updated successfully');
      } else {
        $conn->rollBack();
        jsonResponse(500, 'Failed to update payment method');
      }
    } catch (PDOException $e) {
      if ($conn->inTransaction()) {
        $conn->rollBack();
      }
      jsonResponse(500, 'Database error: ' . $e->getMessage());
    }
}
