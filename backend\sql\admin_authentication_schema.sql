-- =====================================================
-- FanBet247 Admin Authentication Enhancement Schema
-- =====================================================
-- This script creates the necessary tables for admin dual authentication (OTP + 2FA)

-- Create Admin Authentication Settings Table
CREATE TABLE IF NOT EXISTS admin_auth_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_name VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create Admin 2FA Table
CREATE TABLE IF NOT EXISTS admin_2fa (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    secret_key VARCHAR(255),
    is_enabled BOOLEAN NOT NULL DEFAULT 0,
    auth_type ENUM('email_otp', 'google_auth') NOT NULL DEFAULT 'email_otp',
    backup_codes TEXT,
    setup_completed BOOLEAN NOT NULL DEFAULT 0,
    last_used TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
    UNIQUE KEY unique_admin_2fa (admin_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create Admin OTP Table
CREATE TABLE IF NOT EXISTS admin_otp (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    otp VARCHAR(10) NOT NULL,
    expires_at DATETIME NOT NULL,
    attempts INT NOT NULL DEFAULT 0,
    locked_until DATETIME NULL,
    used BOOLEAN NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
    INDEX idx_admin_otp_admin_id (admin_id),
    INDEX idx_admin_otp_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create Admin Authentication Logs Table
CREATE TABLE IF NOT EXISTS admin_auth_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    auth_type ENUM('password', 'otp', '2fa', 'backup_code') NOT NULL,
    action ENUM('login_attempt', 'login_success', 'login_failed', 'otp_sent', 'otp_verified', '2fa_setup', '2fa_verified', 'account_locked', 'account_unlocked') NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
    INDEX idx_admin_auth_logs_admin_id (admin_id),
    INDEX idx_admin_auth_logs_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create Admin Login Attempts Table (for rate limiting)
CREATE TABLE IF NOT EXISTS admin_login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT,
    ip_address VARCHAR(45) NOT NULL,
    attempt_type ENUM('password', 'otp', '2fa') NOT NULL,
    attempts INT NOT NULL DEFAULT 1,
    locked_until DATETIME NULL,
    last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
    INDEX idx_admin_login_attempts_ip (ip_address),
    INDEX idx_admin_login_attempts_admin_id (admin_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default admin authentication settings
INSERT INTO admin_auth_settings (setting_name, setting_value, description) VALUES
('admin_auth_method', 'password_only', 'Admin authentication method: password_only, otp, 2fa'),
('admin_otp_enabled', 'false', 'Enable OTP authentication for admins'),
('admin_2fa_enabled', 'false', 'Enable 2FA authentication for admins'),
('admin_otp_expiry_time', '300', 'Admin OTP expiry time in seconds (default: 5 minutes)'),
('admin_max_otp_attempts', '3', 'Maximum OTP verification attempts before lockout'),
('admin_max_login_attempts', '5', 'Maximum login attempts before account lockout'),
('admin_lockout_time', '1800', 'Admin account lockout time in seconds (default: 30 minutes)'),
('admin_require_2fa_for', 'login,password_change', 'Actions requiring 2FA verification (comma-separated)'),
('admin_backup_codes_count', '10', 'Number of backup codes to generate for 2FA'),
('admin_session_timeout', '3600', 'Admin session timeout in seconds (default: 1 hour)')
ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admin_auth_settings_name ON admin_auth_settings(setting_name);

-- Add additional columns to existing admins table if they don't exist
ALTER TABLE admins 
ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS auth_method ENUM('password_only', 'otp', '2fa') NOT NULL DEFAULT 'password_only',
ADD COLUMN IF NOT EXISTS last_2fa_setup TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS account_locked_until DATETIME NULL,
ADD COLUMN IF NOT EXISTS failed_login_attempts INT NOT NULL DEFAULT 0;

-- Create Admin Recovery Codes Table
CREATE TABLE IF NOT EXISTS admin_recovery_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    recovery_code VARCHAR(20) NOT NULL,
    expires_at DATETIME NOT NULL,
    used BOOLEAN NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
    INDEX idx_admin_recovery_admin_id (admin_id),
    INDEX idx_admin_recovery_code (recovery_code),
    INDEX idx_admin_recovery_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create Admin Recovery Tokens Table
CREATE TABLE IF NOT EXISTS admin_recovery_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    access_token VARCHAR(255) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
    UNIQUE KEY unique_admin_token (admin_id),
    INDEX idx_admin_recovery_token (access_token),
    INDEX idx_admin_recovery_token_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add log_level column to admin_auth_logs if it doesn't exist
ALTER TABLE admin_auth_logs
ADD COLUMN IF NOT EXISTS log_level ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL DEFAULT 'INFO';

-- Create a view for admin authentication status
CREATE OR REPLACE VIEW admin_auth_status AS
SELECT
    a.admin_id,
    a.username,
    a.email,
    a.role,
    a.auth_method,
    a.two_factor_enabled,
    a.account_locked_until,
    a.failed_login_attempts,
    a2fa.is_enabled as has_2fa_setup,
    a2fa.auth_type as preferred_2fa_type,
    a2fa.setup_completed as is_2fa_setup_complete,
    CASE
        WHEN a.account_locked_until IS NOT NULL AND a.account_locked_until > NOW() THEN 'locked'
        WHEN a.two_factor_enabled = 1 AND a2fa.setup_completed = 1 THEN 'secure'
        WHEN a.auth_method = 'password_only' THEN 'basic'
        ELSE 'setup_required'
    END as security_status
FROM admins a
LEFT JOIN admin_2fa a2fa ON a.admin_id = a2fa.admin_id;
