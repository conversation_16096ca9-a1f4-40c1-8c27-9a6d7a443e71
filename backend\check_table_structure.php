<?php
header('Content-Type: text/plain');
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "📋 DATABASE TABLE STRUCTURE CHECK\n";
    echo "=================================\n\n";
    
    // Check if user_otp table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'user_otp'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "✅ user_otp table exists\n\n";
        
        // Show table structure
        $stmt = $conn->prepare("DESCRIBE user_otp");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "📋 user_otp table structure:\n";
        foreach ($columns as $column) {
            echo "   {$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Key']}\n";
        }
    } else {
        echo "❌ user_otp table does not exist\n";
        echo "Creating user_otp table...\n";
        
        $createTable = "
        CREATE TABLE user_otp (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            otp_code VARCHAR(6) NOT NULL,
            expires_at DATETIME NOT NULL,
            is_used BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_otp (user_id, otp_code),
            INDEX idx_expires (expires_at)
        )";
        
        $conn->exec($createTable);
        echo "✅ user_otp table created\n";
    }
    
    echo "\n📋 Checking other required tables:\n";
    
    $requiredTables = [
        'user_2fa' => "
        CREATE TABLE user_2fa (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            secret_key VARCHAR(32) NOT NULL,
            auth_type VARCHAR(20) DEFAULT 'google_auth',
            backup_codes JSON,
            is_enabled BOOLEAN DEFAULT FALSE,
            setup_completed BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_2fa (user_id)
        )",
        'user_auth_logs' => "
        CREATE TABLE user_auth_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            auth_type VARCHAR(50),
            action VARCHAR(50) NOT NULL,
            details JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_auth_logs (user_id, created_at),
            INDEX idx_auth_type (auth_type)
        )",
        'user_login_attempts' => "
        CREATE TABLE user_login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            email VARCHAR(255),
            ip_address VARCHAR(45) NOT NULL,
            attempt_type VARCHAR(20) NOT NULL,
            success BOOLEAN DEFAULT FALSE,
            failure_reason VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_login_attempts (ip_address, created_at),
            INDEX idx_user_attempts (user_id, created_at)
        )",
        'user_auth_settings' => "
        CREATE TABLE user_auth_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            email_notifications BOOLEAN DEFAULT TRUE,
            security_alerts BOOLEAN DEFAULT TRUE,
            login_notifications BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_auth_settings (user_id)
        )"
    ];
    
    foreach ($requiredTables as $tableName => $createSQL) {
        $stmt = $conn->prepare("SHOW TABLES LIKE '$tableName'");
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            echo "✅ $tableName table exists\n";
        } else {
            echo "❌ $tableName table missing - creating...\n";
            $conn->exec($createSQL);
            echo "✅ $tableName table created\n";
        }
    }
    
    echo "\n📋 Checking users table columns:\n";
    
    $stmt = $conn->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = [
        'otp_enabled' => "ALTER TABLE users ADD COLUMN otp_enabled BOOLEAN DEFAULT FALSE",
        'tfa_enabled' => "ALTER TABLE users ADD COLUMN tfa_enabled BOOLEAN DEFAULT FALSE", 
        'auth_method' => "ALTER TABLE users ADD COLUMN auth_method VARCHAR(50) DEFAULT 'password_only'"
    ];
    
    foreach ($requiredColumns as $column => $alterSQL) {
        if (in_array($column, $columns)) {
            echo "✅ users.$column exists\n";
        } else {
            echo "❌ users.$column missing - adding...\n";
            $conn->exec($alterSQL);
            echo "✅ users.$column added\n";
        }
    }
    
    echo "\n🎉 DATABASE STRUCTURE CHECK COMPLETE!\n";
    echo "All required tables and columns are now available.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
