# FanBet247 Project Structure

## Root Directory Organization
```
├── frontend/          # React application
├── backend/           # PHP API backend
├── docs/             # Project documentation
├── tests/            # End-to-end testing
├── sql/              # Database backups and schemas
├── uploads/          # User-uploaded files
├── plan/             # Feature planning documents
└── .kiro/            # Kiro AI assistant configuration
```

## Frontend Structure (`frontend/`)
```
├── src/
│   ├── components/    # Reusable React components
│   │   ├── Admin/     # Admin-specific components
│   │   ├── User/      # User-specific components
│   │   ├── Layout/    # Layout components (Header, Sidebar, Footer)
│   │   ├── Messages/  # Messaging system components
│   │   └── Currency/  # Currency-related components
│   ├── pages/         # Page-level components (routes)
│   ├── services/      # API service layers
│   ├── context/       # React Context providers
│   ├── hooks/         # Custom React hooks
│   ├── utils/         # Utility functions and helpers
│   ├── styles/        # CSS files and styling
│   └── tests/         # Component tests
├── public/            # Static assets
├── build/             # Production build output
└── package.json       # Dependencies and scripts
```

## Backend Structure (`backend/`)
```
├── handlers/          # API endpoint handlers
│   ├── admin/         # Admin-specific endpoints
│   └── backup/        # Backup handlers
├── includes/          # Shared PHP utilities
│   ├── db_connect.php      # Database connection
│   ├── cors_headers.php    # CORS configuration
│   ├── error_logger.php    # Error logging
│   ├── auth_check.php      # Authentication utilities
│   └── rate_limiter.php    # Rate limiting
├── sql/               # Database schemas and migrations
│   └── updates/       # Schema update scripts
├── uploads/           # File upload directories
│   ├── team_logos/    # Team logo uploads
│   ├── favicon/       # Site favicon uploads
│   └── leagues/       # League-related uploads
├── logs/              # Application logs
├── tests/             # Backend integration tests
├── vendor/            # Composer dependencies
└── composer.json      # PHP dependencies
```

## Key File Patterns

### Frontend Component Structure
- **Layout Components**: `*Layout.js` with corresponding `.css` files
- **Page Components**: Located in `src/pages/` with descriptive names
- **Service Files**: API interaction logic in `src/services/`
- **Context Providers**: Global state management in `src/context/`

### Backend Handler Structure
- **Naming Convention**: Descriptive names like `user_registration.php`, `admin_login_handler.php`
- **Organization**: Related handlers grouped in subdirectories
- **Response Format**: Consistent JSON structure with status, message, and data fields

### Database Schema Files
- **Main Schema**: `sql/fanbet247.sql` (complete database structure)
- **Feature Schemas**: Specific schemas like `admin_authentication_schema.sql`
- **Setup Scripts**: Automated setup in `sql/setup_*.php` files

## Configuration Files
- **Frontend Config**: `.env` files for environment variables
- **Backend Config**: Database credentials in `includes/db_connect.php`
- **Build Config**: `tailwind.config.js`, `postcss.config.js`, `jsconfig.json`
- **Package Management**: `package.json` (frontend), `composer.json` (backend)

## Development Workflow
1. **Frontend Development**: Work in `frontend/src/`, use `npm start` for hot reload
2. **Backend Development**: Create handlers in `backend/handlers/`, test with direct PHP execution
3. **Database Changes**: Update schemas in `backend/sql/`, run setup scripts
4. **Testing**: Use `tests/` directory for integration tests
5. **Documentation**: Update relevant files in `docs/` directory

## File Naming Conventions
- **React Components**: PascalCase (e.g., `UserDashboard.js`)
- **PHP Handlers**: snake_case (e.g., `user_registration.php`)
- **CSS Files**: Match component names (e.g., `UserDashboard.css`)
- **Utility Files**: Descriptive names (e.g., `axiosConfig.js`, `errorHandler.js`)

## Import/Include Patterns
- **Frontend**: Relative imports from `src/` directory
- **Backend**: Include shared files from `includes/` directory
- **Database**: Use `getDBConnection()` function from `db_connect.php`