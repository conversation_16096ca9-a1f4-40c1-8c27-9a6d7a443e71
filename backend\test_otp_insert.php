<?php
// Test OTP insertion to find the exact error
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "🧪 Testing OTP insertion...\n";
    
    // Test the exact query from user_send_otp.php
    $userId = 13; // testuser123
    $otp = '123456';
    $expiresAt = date('Y-m-d H:i:s', time() + 300);
    
    echo "Parameters:\n";
    echo "   User ID: $userId\n";
    echo "   OTP: $otp\n";
    echo "   Expires: $expiresAt\n\n";
    
    // First, delete old OTPs
    echo "🗑️ Deleting old OTPs...\n";
    $stmt = $conn->prepare("DELETE FROM user_otp WHERE user_id = ? AND used = 0");
    $stmt->execute([$userId]);
    echo "✅ Deleted " . $stmt->rowCount() . " old OTP records\n";
    
    // Now try to insert new OTP
    echo "\n📝 Inserting new OTP...\n";
    $stmt = $conn->prepare("
        INSERT INTO user_otp (user_id, otp, expiry)
        VALUES (?, ?, ?)
    ");
    $stmt->execute([$userId, $otp, $expiresAt]);
    echo "✅ Successfully inserted OTP record\n";
    
    // Verify the insertion
    $stmt = $conn->prepare("SELECT * FROM user_otp WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$userId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "\n📊 Inserted record:\n";
        foreach ($result as $key => $value) {
            echo "   $key: $value\n";
        }
    }
    
    echo "\n✅ OTP insertion test completed successfully!\n";
    echo "The issue is not with the user_otp table.\n";
    
} catch (Exception $e) {
    echo "❌ Error during OTP insertion test: " . $e->getMessage() . "\n";
    echo "Full error details:\n";
    echo $e->getTraceAsString() . "\n";
}
?>
