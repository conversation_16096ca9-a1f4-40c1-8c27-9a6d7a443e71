<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once '../db_connect.php';
require_once '../auth_middleware.php';

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 405, 'message' => 'Method not allowed']);
    exit;
}

// Get the request body
$data = json_decode(file_get_contents('php://input'), true);

// Validate required fields
if (!isset($data['user_id']) || !isset($data['league_id'])) {
    http_response_code(400);
    echo json_encode(['status' => 400, 'message' => 'Missing required fields']);
    exit;
}

$userId = $data['user_id'];
$leagueId = $data['league_id'];

try {
    // Start transaction
    $pdo->beginTransaction();

    // Get the league details and user's registration fee
    $stmt = $pdo->prepare("
        SELECT l.min_bet_amount, u.balance, u.current_league_id
        FROM leagues l
        JOIN users u ON u.current_league_id = l.league_id
        WHERE u.user_id = ? AND l.league_id = ?
    ");
    $stmt->execute([$userId, $leagueId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$result) {
        throw new Exception('User is not a member of this league');
    }

    $registrationFee = $result['min_bet_amount'];
    $currentBalance = $result['balance'];

    // Update user's balance to refund the registration fee
    $stmt = $pdo->prepare("
        UPDATE users 
        SET balance = balance + ?, current_league_id = NULL 
        WHERE user_id = ?
    ");
    $stmt->execute([$registrationFee, $userId]);

    // Remove user's league stats
    $stmt = $pdo->prepare("
        DELETE FROM league_user_stats 
        WHERE user_id = ? AND league_id = ?
    ");
    $stmt->execute([$userId, $leagueId]);

    // Record the refund transaction
    $stmt = $pdo->prepare("
        INSERT INTO transactions (
            user_id, 
            amount, 
            type, 
            status, 
            description
        ) VALUES (
            ?, 
            ?, 
            'refund', 
            'completed',
            'League registration fee refund'
        )
    ");
    $stmt->execute([$userId, $registrationFee]);

    // Commit transaction
    $pdo->commit();

    echo json_encode([
        'status' => 200,
        'message' => 'User successfully removed from league and registration fee refunded',
        'data' => [
            'refunded_amount' => $registrationFee,
            'new_balance' => $currentBalance + $registrationFee
        ]
    ]);

} catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    http_response_code(500);
    echo json_encode([
        'status' => 500,
        'message' => 'Failed to remove user from league: ' . $e->getMessage()
    ]);
}
?> 