<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    $newPassword = 'loving12';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    echo "Updating admin passwords to 'loving12'...\n\n";
    
    // Update both admin accounts
    $stmt = $conn->prepare('UPDATE admins SET password_hash = ? WHERE admin_id IN (1, 2)');
    $stmt->execute([$hashedPassword]);
    
    echo "Updated {$stmt->rowCount()} admin accounts.\n\n";
    
    // Verify the updates
    echo "Verifying password updates:\n";
    $stmt = $conn->query('SELECT admin_id, username, email FROM admins WHERE admin_id IN (1, 2)');
    while ($row = $stmt->fetch()) {
        echo "- Admin {$row['admin_id']} ({$row['username']}) - {$row['email']}\n";
        
        // Test password verification
        $stmt2 = $conn->prepare('SELECT password_hash FROM admins WHERE admin_id = ?');
        $stmt2->execute([$row['admin_id']]);
        $hash = $stmt2->fetchColumn();
        
        if (password_verify($newPassword, $hash)) {
            echo "  ✅ Password verification successful\n";
        } else {
            echo "  ❌ Password verification failed\n";
        }
    }
    
    echo "\n✅ Admin passwords updated successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>