<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    echo "✅ Database connection successful\n\n";
    
    // Check users table structure
    echo "📋 Checking users table structure:\n";
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasOtpEnabled = false;
    $hasTfaEnabled = false;
    $hasAuthMethod = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'otp_enabled') $hasOtpEnabled = true;
        if ($column['Field'] === 'tfa_enabled') $hasTfaEnabled = true;
        if ($column['Field'] === 'auth_method') $hasAuthMethod = true;
        echo "  - {$column['Field']} ({$column['Type']})\n";
    }
    
    echo "\n📊 Authentication columns status:\n";
    echo "  - otp_enabled: " . ($hasOtpEnabled ? "✅ EXISTS" : "❌ MISSING") . "\n";
    echo "  - tfa_enabled: " . ($hasTfaEnabled ? "✅ EXISTS" : "❌ MISSING") . "\n";
    echo "  - auth_method: " . ($hasAuthMethod ? "✅ EXISTS" : "❌ MISSING") . "\n";
    
    // Check if user_otp table exists
    echo "\n📋 Checking user_otp table:\n";
    $stmt = $conn->query("SHOW TABLES LIKE 'user_otp'");
    if ($stmt->rowCount() > 0) {
        echo "✅ user_otp table exists\n";
        $stmt = $conn->query("DESCRIBE user_otp");
        $otpColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($otpColumns as $column) {
            echo "  - {$column['Field']} ({$column['Type']})\n";
        }
    } else {
        echo "❌ user_otp table does not exist\n";
    }
    
    // Check if user_2fa table exists
    echo "\n📋 Checking user_2fa table:\n";
    $stmt = $conn->query("SHOW TABLES LIKE 'user_2fa'");
    if ($stmt->rowCount() > 0) {
        echo "✅ user_2fa table exists\n";
        $stmt = $conn->query("DESCRIBE user_2fa");
        $tfaColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($tfaColumns as $column) {
            echo "  - {$column['Field']} ({$column['Type']})\n";
        }
    } else {
        echo "❌ user_2fa table does not exist\n";
    }
    
    // Test a specific user
    echo "\n👤 Testing user ID 14 (dynamicuser456):\n";
    $stmt = $conn->prepare("SELECT user_id, username, email" . 
                          ($hasOtpEnabled ? ", otp_enabled" : "") .
                          ($hasTfaEnabled ? ", tfa_enabled" : "") .
                          ($hasAuthMethod ? ", auth_method" : "") .
                          " FROM users WHERE user_id = ?");
    $stmt->execute([14]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✅ User found:\n";
        foreach ($user as $key => $value) {
            echo "  - $key: $value\n";
        }
    } else {
        echo "❌ User not found\n";
    }
    
    // Test the reset authentication function logic
    echo "\n🔧 Testing reset authentication logic:\n";
    
    if (!$hasOtpEnabled || !$hasTfaEnabled || !$hasAuthMethod) {
        echo "❌ Missing required columns in users table. Need to add them.\n";
        
        echo "\n🛠️ Adding missing columns...\n";
        
        if (!$hasOtpEnabled) {
            $conn->exec("ALTER TABLE users ADD COLUMN otp_enabled BOOLEAN DEFAULT 0");
            echo "✅ Added otp_enabled column\n";
        }
        
        if (!$hasTfaEnabled) {
            $conn->exec("ALTER TABLE users ADD COLUMN tfa_enabled BOOLEAN DEFAULT 0");
            echo "✅ Added tfa_enabled column\n";
        }
        
        if (!$hasAuthMethod) {
            $conn->exec("ALTER TABLE users ADD COLUMN auth_method ENUM('password_only', 'password_otp', 'password_2fa', 'password_otp_2fa') DEFAULT 'password_only'");
            echo "✅ Added auth_method column\n";
        }
    }
    
    // Create missing tables
    $stmt = $conn->query("SHOW TABLES LIKE 'user_otp'");
    if ($stmt->rowCount() === 0) {
        echo "\n🛠️ Creating user_otp table...\n";
        $conn->exec("CREATE TABLE user_otp (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            otp VARCHAR(10) NOT NULL,
            expires_at DATETIME NOT NULL,
            attempts INT NOT NULL DEFAULT 0,
            locked_until DATETIME NULL,
            used BOOLEAN NOT NULL DEFAULT 0,
            is_enabled BOOLEAN NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_otp_user_id (user_id),
            INDEX idx_user_otp_expires (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        echo "✅ Created user_otp table\n";
    }
    
    $stmt = $conn->query("SHOW TABLES LIKE 'user_2fa'");
    if ($stmt->rowCount() === 0) {
        echo "\n🛠️ Creating user_2fa table...\n";
        $conn->exec("CREATE TABLE user_2fa (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            secret_key VARCHAR(255),
            is_enabled BOOLEAN NOT NULL DEFAULT 0,
            auth_type ENUM('email_otp', 'google_auth') NOT NULL DEFAULT 'email_otp',
            backup_codes TEXT,
            setup_completed BOOLEAN NOT NULL DEFAULT 0,
            last_used TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_2fa (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        echo "✅ Created user_2fa table\n";
    }
    
    echo "\n✅ Database setup complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
