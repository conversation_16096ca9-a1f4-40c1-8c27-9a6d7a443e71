<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Update open challenges that have passed their end time
    $stmt = $conn->prepare("
        UPDATE challenges 
        SET status = 'Closed'
        WHERE status = 'Open' 
        AND end_time <= NOW()
    ");
    
    $stmt->execute();
    
    $affectedRows = $stmt->rowCount();
    
    // Get remaining open challenges that should be closed
    $checkStmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM challenges 
        WHERE status = 'Open' 
        AND end_time <= NOW()
    ");
    $checkStmt->execute();
    $remainingExpired = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo json_encode([
        'success' => true,
        'message' => "Updated $affectedRows expired challenges to Closed status",
        'affected_rows' => $affectedRows,
        'remaining_expired' => $remainingExpired
    ]);
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to update challenge statuses',
        'error' => $e->getMessage()
    ]);
}
