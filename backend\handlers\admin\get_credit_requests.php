<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get all credit requests with user and payment method details
    $stmt = $conn->prepare("
        SELECT 
            cr.*,
            u.username,
            pm.name as payment_method_name,
            pm.type as payment_method_type,
            pm.fields as payment_method_fields
        FROM credit_requests cr
        JOIN users u ON cr.user_id = u.user_id
        JOIN payment_methods pm ON cr.payment_method_id = pm.id
        ORDER BY cr.created_at DESC
    ");
    
    $stmt->execute();
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Process the payment method fields
    foreach ($requests as &$request) {
        $request['payment_method'] = [
            'id' => $request['payment_method_id'],
            'name' => $request['payment_method_name'],
            'type' => $request['payment_method_type'],
            'fields' => json_decode($request['payment_method_fields'], true)
        ];

        // Remove the raw fields from the response
        unset($request['payment_method_name']);
        unset($request['payment_method_type']);
        unset($request['payment_method_fields']);
    }

    echo json_encode([
        'success' => true,
        'requests' => $requests
    ]);

} catch (Exception $e) {
    error_log("Error in admin/get_credit_requests.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 