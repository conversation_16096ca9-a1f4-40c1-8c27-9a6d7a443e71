#!/bin/bash

# FanBet247 VPS Deployment Script (Bash version)
# Usage: ./deploy_to_vps.sh [test|deploy|check]

VPS_HOST="**************"
VPS_USER="root"
VPS_PATH="/var/www/fanbet247.xyz"
VPS_PASSWORD="Money2025@Demo#"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🚀 FanBet247 VPS Deployment Script${NC}"
echo -e "${CYAN}===================================${NC}"

# Function to execute SSH commands
ssh_exec() {
    sshpass -p "$VPS_PASSWORD" ssh -o StrictHostKeyChecking=no "$VPS_USER@$VPS_HOST" "$1"
}

# Function to copy files via SCP
scp_copy() {
    echo -e "${GREEN}📤 Copying $1 to $2${NC}"
    sshpass -p "$VPS_PASSWORD" scp -o StrictHostKeyChecking=no -r "$1" "$VPS_USER@$VPS_HOST:$2"
}

# Check if sshpass is available
if ! command -v sshpass &> /dev/null; then
    echo -e "${RED}❌ sshpass is required but not installed.${NC}"
    echo -e "${YELLOW}Install it with: sudo apt-get install sshpass (Ubuntu/Debian) or brew install sshpass (macOS)${NC}"
    exit 1
fi

# Test VPS connection
echo -e "${BLUE}🔍 Testing VPS connection...${NC}"
if ssh_exec "echo 'Connection successful'" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ VPS connection successful${NC}"
else
    echo -e "${RED}❌ Cannot connect to VPS. Please check credentials.${NC}"
    exit 1
fi

case "${1:-deploy}" in
    "test")
        echo -e "${YELLOW}🧪 Testing VPS structure...${NC}"
        echo -e "${BLUE}Current VPS structure:${NC}"
        ssh_exec "ls -la $VPS_PATH"
        echo -e "${BLUE}Backend handlers:${NC}"
        ssh_exec "ls -la $VPS_PATH/backend/handlers/ 2>/dev/null || echo 'Backend handlers directory not found'"
        ;;
    
    "check")
        echo -e "${YELLOW}🔍 Checking VPS status...${NC}"
        echo -e "${BLUE}Disk usage:${NC}"
        ssh_exec "df -h"
        echo -e "${BLUE}Apache status:${NC}"
        ssh_exec "systemctl status apache2 --no-pager"
        echo -e "${BLUE}PHP version:${NC}"
        ssh_exec "php -v"
        echo -e "${BLUE}MySQL status:${NC}"
        ssh_exec "systemctl status mysql --no-pager"
        ;;
    
    "deploy")
        echo -e "${BLUE}💾 Creating backup...${NC}"
        BACKUP_NAME="fanbet247_backup_$(date +%Y-%m-%d_%H-%M-%S)"
        ssh_exec "cd /var/www && tar -czf ${BACKUP_NAME}.tar.gz fanbet247.xyz 2>/dev/null || echo 'No existing files to backup'"
        
        echo -e "${BLUE}📁 Ensuring directory structure...${NC}"
        ssh_exec "mkdir -p $VPS_PATH/{backend/{handlers,includes},uploads,static}"
        
        echo -e "${BLUE}🚀 Deploying files...${NC}"
        
        # Deploy frontend files
        if [ -f "index.html" ]; then
            scp_copy "index.html" "$VPS_PATH/"
            scp_copy "static" "$VPS_PATH/" 2>/dev/null || echo "Static directory not found"
            scp_copy "manifest.json" "$VPS_PATH/" 2>/dev/null || echo "Manifest not found"
            scp_copy "favicon.ico" "$VPS_PATH/" 2>/dev/null || echo "Favicon not found"
            [ -f "logo192.png" ] && scp_copy "logo192.png" "$VPS_PATH/"
            [ -f "logo512.png" ] && scp_copy "logo512.png" "$VPS_PATH/"
            echo -e "${GREEN}✅ Frontend files deployed${NC}"
        else
            echo -e "${YELLOW}⚠️ Warning: index.html not found. Run 'npm run build' first.${NC}"
        fi
        
        # Deploy backend files
        if [ -d "backend" ]; then
            scp_copy "backend" "$VPS_PATH/"
            echo -e "${GREEN}✅ Backend files deployed${NC}"
        else
            echo -e "${RED}❌ Error: Backend directory not found${NC}"
        fi
        
        # Deploy configuration files
        [ -f ".htaccess" ] && scp_copy ".htaccess" "$VPS_PATH/"
        [ -f "debug.php" ] && scp_copy "debug.php" "$VPS_PATH/"
        [ -f "vps_structure_checker.php" ] && scp_copy "vps_structure_checker.php" "$VPS_PATH/"
        
        echo -e "${BLUE}🔐 Setting permissions...${NC}"
        ssh_exec "chown -R www-data:www-data $VPS_PATH"
        ssh_exec "find $VPS_PATH -type d -exec chmod 755 {} \;"
        ssh_exec "find $VPS_PATH -type f -exec chmod 644 {} \;"
        ssh_exec "chmod 755 $VPS_PATH/backend/handlers/*.php 2>/dev/null || true"
        ssh_exec "chmod 777 $VPS_PATH/uploads"
        
        echo -e "${BLUE}🔧 Testing Apache configuration...${NC}"
        ssh_exec "apache2ctl configtest"
        
        echo -e "${BLUE}🔄 Restarting Apache...${NC}"
        ssh_exec "systemctl restart apache2"
        
        echo -e "${BLUE}🔍 Final verification...${NC}"
        ssh_exec "ls -la $VPS_PATH/ | head -10"
        
        echo ""
        echo -e "${GREEN}🎉 Deployment completed!${NC}"
        echo -e "${CYAN}📋 Next steps:${NC}"
        echo -e "${NC}1. Visit https://fanbet247.xyz/vps_structure_checker.php to verify deployment${NC}"
        echo -e "${NC}2. Visit https://fanbet247.xyz/debug.php to check system status${NC}"
        echo -e "${NC}3. Test your main site at https://fanbet247.xyz${NC}"
        ;;
    
    *)
        echo -e "${YELLOW}Usage: $0 [test|deploy|check]${NC}"
        echo -e "${NC}  test   - Check VPS structure only${NC}"
        echo -e "${NC}  deploy - Full deployment (default)${NC}"
        echo -e "${NC}  check  - Check VPS system status${NC}"
        ;;
esac
