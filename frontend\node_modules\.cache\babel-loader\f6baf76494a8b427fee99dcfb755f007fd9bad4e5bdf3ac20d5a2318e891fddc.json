{"ast": null, "code": "import React,{useState}from'react';import axiosInstance from'../utils/axiosConfig';import{useNavigate}from'react-router-dom';import{AdminOTPVerification,Admin2FAVerification,Admin2FASetup}from'../components/Admin';import'./AdminLoginPage.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminLoginPage=()=>{const navigate=useNavigate();// Authentication flow state\nconst[authStep,setAuthStep]=useState('login');// 'login', 'otp', '2fa', '2fa_setup'\nconst[adminData,setAdminData]=useState(null);// Login form state\nconst[identifier,setIdentifier]=useState('');const[password,setPassword]=useState('');const[error,setError]=useState('');const[isLoading,setIsLoading]=useState(false);const[rememberMe,setRememberMe]=useState(false);const handleSubmit=async e=>{e.preventDefault();setError('');setIsLoading(true);try{console.log('🔐 Attempting admin login with:',{identifier,remember_me:rememberMe});console.log('🌐 Making request to:','/backend/handlers/admin_login_handler.php');const response=await axiosInstance.post('admin_login_handler.php',{identifier,password,remember_me:rememberMe});console.log('✅ Login response received:',response.data);if(response.data.success){// Store admin data for potential next steps\nsetAdminData({admin_id:response.data.admin_id,username:response.data.username,role:response.data.role});// Check if additional authentication is required\nif(response.data.requires_additional_auth){const nextStep=response.data.next_step;if(nextStep==='otp'){setAuthStep('otp');}else if(nextStep==='2fa'){// Check if 2FA is set up\nsetAuthStep('2fa');}}else{// Complete login - no additional auth required\ncompleteLogin({admin_id:response.data.admin_id,username:response.data.username,role:response.data.role,auth_method:response.data.auth_method||'password_only'});}}else{setError(response.data.message||'Login failed');}}catch(error){console.error('❌ Login error details:',{message:error.message,response:error.response,request:error.request,config:error.config});if(error.response){var _error$response$data;console.error('Response status:',error.response.status);console.error('Response data:',error.response.data);console.error('Response headers:',error.response.headers);setError(((_error$response$data=error.response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||`Server error: ${error.response.status}`);}else if(error.request){console.error('Request made but no response:',error.request);setError('Network error. Please check your connection and server status.');}else{console.error('Error setting up request:',error.message);setError('An error occurred. Please try again.');}}finally{setIsLoading(false);}};const completeLogin=loginData=>{// Store authentication data\nlocalStorage.setItem('adminId',loginData.admin_id);localStorage.setItem('adminUsername',loginData.username);localStorage.setItem('adminRole',loginData.role);localStorage.setItem('adminAuthMethod',loginData.auth_method);if(loginData.session_token){localStorage.setItem('adminSessionToken',loginData.session_token);}// Navigate to dashboard\nnavigate('/admin/dashboard');};const handleAuthSuccess=authData=>{completeLogin(authData);};const handleBackToLogin=()=>{setAuthStep('login');setAdminData(null);setError('');setPassword('');// Clear password for security\n};// Render different authentication steps\nif(authStep==='otp'&&adminData){return/*#__PURE__*/_jsx(AdminOTPVerification,{adminId:adminData.admin_id,username:adminData.username,onSuccess:handleAuthSuccess,onBack:handleBackToLogin});}if(authStep==='2fa'&&adminData){return/*#__PURE__*/_jsx(Admin2FAVerification,{adminId:adminData.admin_id,username:adminData.username,onSuccess:handleAuthSuccess,onBack:handleBackToLogin});}if(authStep==='2fa_setup'&&adminData){return/*#__PURE__*/_jsx(Admin2FASetup,{adminId:adminData.admin_id,username:adminData.username,onSuccess:handleAuthSuccess,onBack:handleBackToLogin});}// Default login form\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-left-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-logo\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"2\",y:\"3\",width:\"20\",height:\"14\",rx:\"2\",ry:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"8\",y1:\"21\",x2:\"16\",y2:\"21\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"12\",y1:\"17\",x2:\"12\",y2:\"21\"})]})}),/*#__PURE__*/_jsx(\"h1\",{children:\"FanBet247\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"login-form-container\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Admin Login\"}),/*#__PURE__*/_jsx(\"p\",{className:\"login-subtitle\",children:\"Enter your credentials to access the admin dashboard\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"security-notice\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"security-icon\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"})})}),/*#__PURE__*/_jsx(\"span\",{children:\"Enhanced security enabled - Additional verification may be required\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"identifier\",children:\"Username or Email\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"identifier\",value:identifier,onChange:e=>setIdentifier(e.target.value),disabled:isLoading,placeholder:\"Enter your username or email\",required:true}),/*#__PURE__*/_jsx(\"div\",{className:\"input-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"12\",cy:\"7\",r:\"4\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",children:\"Password\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"password\",value:password,onChange:e=>setPassword(e.target.value),disabled:isLoading,placeholder:\"Enter your password\",required:true}),/*#__PURE__*/_jsx(\"div\",{className:\"input-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"3\",y:\"11\",width:\"18\",height:\"11\",rx:\"2\",ry:\"2\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M7 11V7a5 5 0 0 1 10 0v4\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-options\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"remember-me\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"rememberMe\",checked:rememberMe,onChange:e=>setRememberMe(e.target.checked)}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"rememberMe\",children:\"Remember me\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"forgot-password\",onClick:()=>{// Show temporary message instead of alert\nconst button=document.activeElement;const originalText=button.textContent;button.textContent='Coming soon!';button.style.color='#3b82f6';setTimeout(()=>{button.textContent=originalText;button.style.color='';},3000);},children:\"Forgot password?\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"login-button\",disabled:isLoading,children:isLoading?'Logging in...':'Login'})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"login-right-panel\"})]});};export default AdminLoginPage;", "map": {"version": 3, "names": ["React", "useState", "axiosInstance", "useNavigate", "AdminOTPVerification", "Admin2FAVerification", "Admin2FASetup", "jsx", "_jsx", "jsxs", "_jsxs", "AdminLoginPage", "navigate", "authStep", "setAuthStep", "adminData", "setAdminData", "identifier", "setIdentifier", "password", "setPassword", "error", "setError", "isLoading", "setIsLoading", "rememberMe", "setRememberMe", "handleSubmit", "e", "preventDefault", "console", "log", "remember_me", "response", "post", "data", "success", "admin_id", "username", "role", "requires_additional_auth", "nextStep", "next_step", "completeLogin", "auth_method", "message", "request", "config", "_error$response$data", "status", "headers", "loginData", "localStorage", "setItem", "session_token", "handleAuthSuccess", "authData", "handleBackToLogin", "adminId", "onSuccess", "onBack", "className", "children", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x", "y", "width", "height", "rx", "ry", "x1", "y1", "x2", "y2", "d", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "disabled", "placeholder", "required", "cx", "cy", "r", "checked", "onClick", "button", "document", "activeElement", "originalText", "textContent", "style", "color", "setTimeout"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/AdminLoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axiosInstance from '../utils/axiosConfig';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { AdminOTPVerification, Admin2FAVerification, Admin2FASetup } from '../components/Admin';\r\nimport './AdminLoginPage.css';\r\n\r\n\r\nconst AdminLoginPage = () => {\r\n    const navigate = useNavigate();\r\n\r\n    // Authentication flow state\r\n    const [authStep, setAuthStep] = useState('login'); // 'login', 'otp', '2fa', '2fa_setup'\r\n    const [adminData, setAdminData] = useState(null);\r\n\r\n    // Login form state\r\n    const [identifier, setIdentifier] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const [rememberMe, setRememberMe] = useState(false);\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n        setError('');\r\n        setIsLoading(true);\r\n\r\n        try {\r\n            console.log('🔐 Attempting admin login with:', { identifier, remember_me: rememberMe });\r\n            console.log('🌐 Making request to:', '/backend/handlers/admin_login_handler.php');\r\n            \r\n            const response = await axiosInstance.post('admin_login_handler.php', {\r\n                identifier,\r\n                password,\r\n                remember_me: rememberMe\r\n            });\r\n            \r\n            console.log('✅ Login response received:', response.data);\r\n\r\n            if (response.data.success) {\r\n                // Store admin data for potential next steps\r\n                setAdminData({\r\n                    admin_id: response.data.admin_id,\r\n                    username: response.data.username,\r\n                    role: response.data.role\r\n                });\r\n\r\n                // Check if additional authentication is required\r\n                if (response.data.requires_additional_auth) {\r\n                    const nextStep = response.data.next_step;\r\n\r\n                    if (nextStep === 'otp') {\r\n                        setAuthStep('otp');\r\n                    } else if (nextStep === '2fa') {\r\n                        // Check if 2FA is set up\r\n                        setAuthStep('2fa');\r\n                    }\r\n                } else {\r\n                    // Complete login - no additional auth required\r\n                    completeLogin({\r\n                        admin_id: response.data.admin_id,\r\n                        username: response.data.username,\r\n                        role: response.data.role,\r\n                        auth_method: response.data.auth_method || 'password_only'\r\n                    });\r\n                }\r\n            } else {\r\n                setError(response.data.message || 'Login failed');\r\n            }\r\n        } catch (error) {\r\n            console.error('❌ Login error details:', {\r\n                message: error.message,\r\n                response: error.response,\r\n                request: error.request,\r\n                config: error.config\r\n            });\r\n            \r\n            if (error.response) {\r\n                console.error('Response status:', error.response.status);\r\n                console.error('Response data:', error.response.data);\r\n                console.error('Response headers:', error.response.headers);\r\n                setError(error.response.data?.message || `Server error: ${error.response.status}`);\r\n            } else if (error.request) {\r\n                console.error('Request made but no response:', error.request);\r\n                setError('Network error. Please check your connection and server status.');\r\n            } else {\r\n                console.error('Error setting up request:', error.message);\r\n                setError('An error occurred. Please try again.');\r\n            }\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    const completeLogin = (loginData) => {\r\n        // Store authentication data\r\n        localStorage.setItem('adminId', loginData.admin_id);\r\n        localStorage.setItem('adminUsername', loginData.username);\r\n        localStorage.setItem('adminRole', loginData.role);\r\n        localStorage.setItem('adminAuthMethod', loginData.auth_method);\r\n\r\n        if (loginData.session_token) {\r\n            localStorage.setItem('adminSessionToken', loginData.session_token);\r\n        }\r\n\r\n        // Navigate to dashboard\r\n        navigate('/admin/dashboard');\r\n    };\r\n\r\n    const handleAuthSuccess = (authData) => {\r\n        completeLogin(authData);\r\n    };\r\n\r\n    const handleBackToLogin = () => {\r\n        setAuthStep('login');\r\n        setAdminData(null);\r\n        setError('');\r\n        setPassword(''); // Clear password for security\r\n    };\r\n\r\n    // Render different authentication steps\r\n    if (authStep === 'otp' && adminData) {\r\n        return (\r\n            <AdminOTPVerification\r\n                adminId={adminData.admin_id}\r\n                username={adminData.username}\r\n                onSuccess={handleAuthSuccess}\r\n                onBack={handleBackToLogin}\r\n            />\r\n        );\r\n    }\r\n\r\n    if (authStep === '2fa' && adminData) {\r\n        return (\r\n            <Admin2FAVerification\r\n                adminId={adminData.admin_id}\r\n                username={adminData.username}\r\n                onSuccess={handleAuthSuccess}\r\n                onBack={handleBackToLogin}\r\n            />\r\n        );\r\n    }\r\n\r\n    if (authStep === '2fa_setup' && adminData) {\r\n        return (\r\n            <Admin2FASetup\r\n                adminId={adminData.admin_id}\r\n                username={adminData.username}\r\n                onSuccess={handleAuthSuccess}\r\n                onBack={handleBackToLogin}\r\n            />\r\n        );\r\n    }\r\n\r\n    // Default login form\r\n    return (\r\n        <div className=\"admin-login-container\">\r\n            <div className=\"login-left-panel\">\r\n                <div className=\"login-logo\">\r\n                    <div className=\"logo-icon\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                            <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\r\n                            <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\r\n                            <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\r\n                        </svg>\r\n                    </div>\r\n                    <h1>FanBet247</h1>\r\n                </div>\r\n\r\n                <div className=\"login-form-container\">\r\n                    <h2>Admin Login</h2>\r\n                    <p className=\"login-subtitle\">Enter your credentials to access the admin dashboard</p>\r\n\r\n                    {/* Security Notice */}\r\n                    <div className=\"security-notice\">\r\n                        <div className=\"security-icon\">\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"></path>\r\n                            </svg>\r\n                        </div>\r\n                        <span>Enhanced security enabled - Additional verification may be required</span>\r\n                    </div>\r\n\r\n                    {error && <div className=\"error-message\">{error}</div>}\r\n\r\n                    <form onSubmit={handleSubmit}>\r\n                        <div className=\"form-group\">\r\n                            <label htmlFor=\"identifier\">Username or Email</label>\r\n                            <div className=\"input-container\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    id=\"identifier\"\r\n                                    value={identifier}\r\n                                    onChange={(e) => setIdentifier(e.target.value)}\r\n                                    disabled={isLoading}\r\n                                    placeholder=\"Enter your username or email\"\r\n                                    required\r\n                                />\r\n                                <div className=\"input-icon\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\r\n                                        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"form-group\">\r\n                            <label htmlFor=\"password\">Password</label>\r\n                            <div className=\"input-container\">\r\n                                <input\r\n                                    type=\"password\"\r\n                                    id=\"password\"\r\n                                    value={password}\r\n                                    onChange={(e) => setPassword(e.target.value)}\r\n                                    disabled={isLoading}\r\n                                    placeholder=\"Enter your password\"\r\n                                    required\r\n                                />\r\n                                <div className=\"input-icon\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                        <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"></rect>\r\n                                        <path d=\"M7 11V7a5 5 0 0 1 10 0v4\"></path>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"form-options\">\r\n                            <div className=\"remember-me\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    id=\"rememberMe\"\r\n                                    checked={rememberMe}\r\n                                    onChange={(e) => setRememberMe(e.target.checked)}\r\n                                />\r\n                                <label htmlFor=\"rememberMe\">Remember me</label>\r\n                            </div>\r\n                            <button type=\"button\" className=\"forgot-password\" onClick={() => {\r\n                                // Show temporary message instead of alert\r\n                                const button = document.activeElement;\r\n                                const originalText = button.textContent;\r\n                                button.textContent = 'Coming soon!';\r\n                                button.style.color = '#3b82f6';\r\n                                setTimeout(() => {\r\n                                    button.textContent = originalText;\r\n                                    button.style.color = '';\r\n                                }, 3000);\r\n                            }}>Forgot password?</button>\r\n                        </div>\r\n\r\n                        <button type=\"submit\" className=\"login-button\" disabled={isLoading}>\r\n                            {isLoading ? 'Logging in...' : 'Login'}\r\n                        </button>\r\n                    </form>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"login-right-panel\"></div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AdminLoginPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,aAAa,KAAM,sBAAsB,CAChD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,oBAAoB,CAAEC,oBAAoB,CAAEC,aAAa,KAAQ,qBAAqB,CAC/F,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG9B,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACU,QAAQ,CAAEC,WAAW,CAAC,CAAGb,QAAQ,CAAC,OAAO,CAAC,CAAE;AACnD,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAEhD;AACA,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACkB,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACoB,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACsB,SAAS,CAAEC,YAAY,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAEnD,KAAM,CAAA0B,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBP,QAAQ,CAAC,EAAE,CAAC,CACZE,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACAM,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAE,CAAEd,UAAU,CAAEe,WAAW,CAAEP,UAAW,CAAC,CAAC,CACvFK,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAE,2CAA2C,CAAC,CAEjF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAA/B,aAAa,CAACgC,IAAI,CAAC,yBAAyB,CAAE,CACjEjB,UAAU,CACVE,QAAQ,CACRa,WAAW,CAAEP,UACjB,CAAC,CAAC,CAEFK,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEE,QAAQ,CAACE,IAAI,CAAC,CAExD,GAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CACvB;AACApB,YAAY,CAAC,CACTqB,QAAQ,CAAEJ,QAAQ,CAACE,IAAI,CAACE,QAAQ,CAChCC,QAAQ,CAAEL,QAAQ,CAACE,IAAI,CAACG,QAAQ,CAChCC,IAAI,CAAEN,QAAQ,CAACE,IAAI,CAACI,IACxB,CAAC,CAAC,CAEF;AACA,GAAIN,QAAQ,CAACE,IAAI,CAACK,wBAAwB,CAAE,CACxC,KAAM,CAAAC,QAAQ,CAAGR,QAAQ,CAACE,IAAI,CAACO,SAAS,CAExC,GAAID,QAAQ,GAAK,KAAK,CAAE,CACpB3B,WAAW,CAAC,KAAK,CAAC,CACtB,CAAC,IAAM,IAAI2B,QAAQ,GAAK,KAAK,CAAE,CAC3B;AACA3B,WAAW,CAAC,KAAK,CAAC,CACtB,CACJ,CAAC,IAAM,CACH;AACA6B,aAAa,CAAC,CACVN,QAAQ,CAAEJ,QAAQ,CAACE,IAAI,CAACE,QAAQ,CAChCC,QAAQ,CAAEL,QAAQ,CAACE,IAAI,CAACG,QAAQ,CAChCC,IAAI,CAAEN,QAAQ,CAACE,IAAI,CAACI,IAAI,CACxBK,WAAW,CAAEX,QAAQ,CAACE,IAAI,CAACS,WAAW,EAAI,eAC9C,CAAC,CAAC,CACN,CACJ,CAAC,IAAM,CACHtB,QAAQ,CAACW,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAI,cAAc,CAAC,CACrD,CACJ,CAAE,MAAOxB,KAAK,CAAE,CACZS,OAAO,CAACT,KAAK,CAAC,wBAAwB,CAAE,CACpCwB,OAAO,CAAExB,KAAK,CAACwB,OAAO,CACtBZ,QAAQ,CAAEZ,KAAK,CAACY,QAAQ,CACxBa,OAAO,CAAEzB,KAAK,CAACyB,OAAO,CACtBC,MAAM,CAAE1B,KAAK,CAAC0B,MAClB,CAAC,CAAC,CAEF,GAAI1B,KAAK,CAACY,QAAQ,CAAE,KAAAe,oBAAA,CAChBlB,OAAO,CAACT,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAACY,QAAQ,CAACgB,MAAM,CAAC,CACxDnB,OAAO,CAACT,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAACY,QAAQ,CAACE,IAAI,CAAC,CACpDL,OAAO,CAACT,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAACY,QAAQ,CAACiB,OAAO,CAAC,CAC1D5B,QAAQ,CAAC,EAAA0B,oBAAA,CAAA3B,KAAK,CAACY,QAAQ,CAACE,IAAI,UAAAa,oBAAA,iBAAnBA,oBAAA,CAAqBH,OAAO,GAAI,iBAAiBxB,KAAK,CAACY,QAAQ,CAACgB,MAAM,EAAE,CAAC,CACtF,CAAC,IAAM,IAAI5B,KAAK,CAACyB,OAAO,CAAE,CACtBhB,OAAO,CAACT,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAACyB,OAAO,CAAC,CAC7DxB,QAAQ,CAAC,gEAAgE,CAAC,CAC9E,CAAC,IAAM,CACHQ,OAAO,CAACT,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAACwB,OAAO,CAAC,CACzDvB,QAAQ,CAAC,sCAAsC,CAAC,CACpD,CACJ,CAAC,OAAS,CACNE,YAAY,CAAC,KAAK,CAAC,CACvB,CACJ,CAAC,CAED,KAAM,CAAAmB,aAAa,CAAIQ,SAAS,EAAK,CACjC;AACAC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAEF,SAAS,CAACd,QAAQ,CAAC,CACnDe,YAAY,CAACC,OAAO,CAAC,eAAe,CAAEF,SAAS,CAACb,QAAQ,CAAC,CACzDc,YAAY,CAACC,OAAO,CAAC,WAAW,CAAEF,SAAS,CAACZ,IAAI,CAAC,CACjDa,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAEF,SAAS,CAACP,WAAW,CAAC,CAE9D,GAAIO,SAAS,CAACG,aAAa,CAAE,CACzBF,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAEF,SAAS,CAACG,aAAa,CAAC,CACtE,CAEA;AACA1C,QAAQ,CAAC,kBAAkB,CAAC,CAChC,CAAC,CAED,KAAM,CAAA2C,iBAAiB,CAAIC,QAAQ,EAAK,CACpCb,aAAa,CAACa,QAAQ,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC5B3C,WAAW,CAAC,OAAO,CAAC,CACpBE,YAAY,CAAC,IAAI,CAAC,CAClBM,QAAQ,CAAC,EAAE,CAAC,CACZF,WAAW,CAAC,EAAE,CAAC,CAAE;AACrB,CAAC,CAED;AACA,GAAIP,QAAQ,GAAK,KAAK,EAAIE,SAAS,CAAE,CACjC,mBACIP,IAAA,CAACJ,oBAAoB,EACjBsD,OAAO,CAAE3C,SAAS,CAACsB,QAAS,CAC5BC,QAAQ,CAAEvB,SAAS,CAACuB,QAAS,CAC7BqB,SAAS,CAAEJ,iBAAkB,CAC7BK,MAAM,CAAEH,iBAAkB,CAC7B,CAAC,CAEV,CAEA,GAAI5C,QAAQ,GAAK,KAAK,EAAIE,SAAS,CAAE,CACjC,mBACIP,IAAA,CAACH,oBAAoB,EACjBqD,OAAO,CAAE3C,SAAS,CAACsB,QAAS,CAC5BC,QAAQ,CAAEvB,SAAS,CAACuB,QAAS,CAC7BqB,SAAS,CAAEJ,iBAAkB,CAC7BK,MAAM,CAAEH,iBAAkB,CAC7B,CAAC,CAEV,CAEA,GAAI5C,QAAQ,GAAK,WAAW,EAAIE,SAAS,CAAE,CACvC,mBACIP,IAAA,CAACF,aAAa,EACVoD,OAAO,CAAE3C,SAAS,CAACsB,QAAS,CAC5BC,QAAQ,CAAEvB,SAAS,CAACuB,QAAS,CAC7BqB,SAAS,CAAEJ,iBAAkB,CAC7BK,MAAM,CAAEH,iBAAkB,CAC7B,CAAC,CAEV,CAEA;AACA,mBACI/C,KAAA,QAAKmD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAClCpD,KAAA,QAAKmD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC7BpD,KAAA,QAAKmD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBtD,IAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAC,QAAA,cACtBpD,KAAA,QAAKqD,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAAP,QAAA,eACtJtD,IAAA,SAAM8D,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAACC,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAAO,CAAC,cAC9DnE,IAAA,SAAMoE,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAO,CAAC,cAC5CvE,IAAA,SAAMoE,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAO,CAAC,EAC5C,CAAC,CACL,CAAC,cACNvE,IAAA,OAAAsD,QAAA,CAAI,WAAS,CAAI,CAAC,EACjB,CAAC,cAENpD,KAAA,QAAKmD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCtD,IAAA,OAAAsD,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBtD,IAAA,MAAGqD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,sDAAoD,CAAG,CAAC,cAGtFpD,KAAA,QAAKmD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BtD,IAAA,QAAKqD,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC1BtD,IAAA,QAAKuD,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAAP,QAAA,cACtJtD,IAAA,SAAMwE,CAAC,CAAC,6CAA6C,CAAO,CAAC,CAC5D,CAAC,CACL,CAAC,cACNxE,IAAA,SAAAsD,QAAA,CAAM,qEAAmE,CAAM,CAAC,EAC/E,CAAC,CAELzC,KAAK,eAAIb,IAAA,QAAKqD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEzC,KAAK,CAAM,CAAC,cAEtDX,KAAA,SAAMuE,QAAQ,CAAEtD,YAAa,CAAAmC,QAAA,eACzBpD,KAAA,QAAKmD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBtD,IAAA,UAAO0E,OAAO,CAAC,YAAY,CAAApB,QAAA,CAAC,mBAAiB,CAAO,CAAC,cACrDpD,KAAA,QAAKmD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BtD,IAAA,UACI2E,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,YAAY,CACfC,KAAK,CAAEpE,UAAW,CAClBqE,QAAQ,CAAG1D,CAAC,EAAKV,aAAa,CAACU,CAAC,CAAC2D,MAAM,CAACF,KAAK,CAAE,CAC/CG,QAAQ,CAAEjE,SAAU,CACpBkE,WAAW,CAAC,8BAA8B,CAC1CC,QAAQ,MACX,CAAC,cACFlF,IAAA,QAAKqD,SAAS,CAAC,YAAY,CAAAC,QAAA,cACvBpD,KAAA,QAAKqD,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAAP,QAAA,eACtJtD,IAAA,SAAMwE,CAAC,CAAC,2CAA2C,CAAO,CAAC,cAC3DxE,IAAA,WAAQmF,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAS,CAAC,EACrC,CAAC,CACL,CAAC,EACL,CAAC,EACL,CAAC,cAENnF,KAAA,QAAKmD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBtD,IAAA,UAAO0E,OAAO,CAAC,UAAU,CAAApB,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC1CpD,KAAA,QAAKmD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BtD,IAAA,UACI2E,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,UAAU,CACbC,KAAK,CAAElE,QAAS,CAChBmE,QAAQ,CAAG1D,CAAC,EAAKR,WAAW,CAACQ,CAAC,CAAC2D,MAAM,CAACF,KAAK,CAAE,CAC7CG,QAAQ,CAAEjE,SAAU,CACpBkE,WAAW,CAAC,qBAAqB,CACjCC,QAAQ,MACX,CAAC,cACFlF,IAAA,QAAKqD,SAAS,CAAC,YAAY,CAAAC,QAAA,cACvBpD,KAAA,QAAKqD,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAAP,QAAA,eACtJtD,IAAA,SAAM8D,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAAO,CAAC,cAC/DnE,IAAA,SAAMwE,CAAC,CAAC,0BAA0B,CAAO,CAAC,EACzC,CAAC,CACL,CAAC,EACL,CAAC,EACL,CAAC,cAENtE,KAAA,QAAKmD,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzBpD,KAAA,QAAKmD,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxBtD,IAAA,UACI2E,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,YAAY,CACfU,OAAO,CAAErE,UAAW,CACpB6D,QAAQ,CAAG1D,CAAC,EAAKF,aAAa,CAACE,CAAC,CAAC2D,MAAM,CAACO,OAAO,CAAE,CACpD,CAAC,cACFtF,IAAA,UAAO0E,OAAO,CAAC,YAAY,CAAApB,QAAA,CAAC,aAAW,CAAO,CAAC,EAC9C,CAAC,cACNtD,IAAA,WAAQ2E,IAAI,CAAC,QAAQ,CAACtB,SAAS,CAAC,iBAAiB,CAACkC,OAAO,CAAEA,CAAA,GAAM,CAC7D;AACA,KAAM,CAAAC,MAAM,CAAGC,QAAQ,CAACC,aAAa,CACrC,KAAM,CAAAC,YAAY,CAAGH,MAAM,CAACI,WAAW,CACvCJ,MAAM,CAACI,WAAW,CAAG,cAAc,CACnCJ,MAAM,CAACK,KAAK,CAACC,KAAK,CAAG,SAAS,CAC9BC,UAAU,CAAC,IAAM,CACbP,MAAM,CAACI,WAAW,CAAGD,YAAY,CACjCH,MAAM,CAACK,KAAK,CAACC,KAAK,CAAG,EAAE,CAC3B,CAAC,CAAE,IAAI,CAAC,CACZ,CAAE,CAAAxC,QAAA,CAAC,kBAAgB,CAAQ,CAAC,EAC3B,CAAC,cAENtD,IAAA,WAAQ2E,IAAI,CAAC,QAAQ,CAACtB,SAAS,CAAC,cAAc,CAAC2B,QAAQ,CAAEjE,SAAU,CAAAuC,QAAA,CAC9DvC,SAAS,CAAG,eAAe,CAAG,OAAO,CAClC,CAAC,EACP,CAAC,EACN,CAAC,EACL,CAAC,cAENf,IAAA,QAAKqD,SAAS,CAAC,mBAAmB,CAAM,CAAC,EACxC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAlD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}