{"name": "endroid/qr-code", "description": "Endroid QR Code", "keywords": ["endroid", "qrcode", "qr", "code", "php"], "homepage": "https://github.com/endroid/qr-code", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "bacon/bacon-qr-code": "^3.0"}, "require-dev": {"ext-gd": "*", "endroid/quality": "dev-main", "khanamiryan/qrcode-detector-decoder": "^2.0.2", "setasign/fpdf": "^1.8.2"}, "suggest": {"ext-gd": "Enables you to write PNG images", "khanamiryan/qrcode-detector-decoder": "Enables you to use the image validator", "roave/security-advisories": "Makes sure package versions with known security issues are not installed", "setasign/fpdf": "Enables you to use the PDF writer"}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "autoload-dev": {"psr-4": {"Endroid\\QrCode\\Tests\\": "tests/"}}, "config": {"sort-packages": true, "preferred-install": {"endroid/*": "source"}, "allow-plugins": {"endroid/installer": true}}, "extra": {"branch-alias": {"dev-main": "6.x-dev"}}}