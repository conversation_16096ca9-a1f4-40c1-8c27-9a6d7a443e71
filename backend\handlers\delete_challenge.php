<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

$conn = getDBConnection();

if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $id = isset($_GET['id']) ? $_GET['id'] : null;

    if ($id) {
        try {
            $stmt = $conn->prepare("DELETE FROM Challenges WHERE challenge_id = :id");
            $stmt->bindParam(':id', $id);
            
            if ($stmt->execute()) {
                echo json_encode(array("success" => true, "message" => "Challenge deleted successfully."));
            } else {
                echo json_encode(array("success" => false, "message" => "Failed to delete challenge."));
            }
        } catch(PDOException $e) {
            echo json_encode(array("success" => false, "message" => "Error: " . $e->getMessage()));
        }
    } else {
        echo json_encode(array("success" => false, "message" => "Challenge ID is required."));
    }
}
?>
