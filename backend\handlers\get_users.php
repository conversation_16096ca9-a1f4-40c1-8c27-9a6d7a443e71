<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    $query = "SELECT user_id, username, balance FROM users ORDER BY username";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'users' => $users
    ]);

} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => "Error fetching users"
    ]);
}
