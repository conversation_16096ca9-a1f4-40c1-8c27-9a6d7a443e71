<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get active users (users who placed bets in last 24 hours)
    $activeUsersQuery = "SELECT COUNT(DISTINCT user1_id) as active_users 
                        FROM bets 
                        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    $stmt = $conn->query($activeUsersQuery);
    $activeUsers = $stmt->fetch(PDO::FETCH_ASSOC)['active_users'];

    // Get total bets
    $totalBetsQuery = "SELECT COUNT(*) as total_bets FROM bets";
    $stmt = $conn->query($totalBetsQuery);
    $totalBets = $stmt->fetch(PDO::FETCH_ASSOC)['total_bets'];

    // Get recent users
    $recentUsersQuery = "SELECT user_id, username, favorite_team, created_at 
                        FROM users 
                        ORDER BY created_at DESC 
                        LIMIT 5";
    $stmt = $conn->query($recentUsersQuery);
    $recentUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'activeUsers' => $activeUsers,
        'totalBets' => $totalBets,
        'recentUsers' => $recentUsers
    ]);

} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
