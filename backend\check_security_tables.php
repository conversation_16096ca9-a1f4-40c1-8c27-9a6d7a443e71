<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Checking security-related tables...\n\n";
    
    // Check user_otp table
    try {
        $stmt = $conn->query('DESCRIBE user_otp');
        echo "✅ user_otp table exists\n";
        while($row = $stmt->fetch()) {
            echo "   {$row['Field']} - {$row['Type']}\n";
        }
    } catch(Exception $e) {
        echo "❌ user_otp table does not exist: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
    
    // Check user_2fa table
    try {
        $stmt = $conn->query('DESCRIBE user_2fa');
        echo "✅ user_2fa table exists\n";
        while($row = $stmt->fetch()) {
            echo "   {$row['Field']} - {$row['Type']}\n";
        }
    } catch(Exception $e) {
        echo "❌ user_2fa table does not exist: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
