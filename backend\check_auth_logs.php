<?php
// Check user_auth_logs table structure
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "📊 Checking user_auth_logs table structure:\n";
    
    $stmt = $conn->prepare("DESCRIBE user_auth_logs");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📋 Current columns:\n";
    foreach ($columns as $column) {
        echo "   - " . $column['Field'] . " (" . $column['Type'] . ")\n";
    }
    
    // Check what columns the handler expects
    $expectedColumns = ['user_id', 'auth_type', 'action', 'details', 'ip_address', 'user_agent'];
    $actualColumns = array_column($columns, 'Field');
    
    echo "\n🔍 Column check:\n";
    foreach ($expectedColumns as $expected) {
        if (in_array($expected, $actualColumns)) {
            echo "   ✅ $expected - exists\n";
        } else {
            echo "   ❌ $expected - MISSING\n";
        }
    }
    
    // Test the exact INSERT query
    echo "\n🧪 Testing INSERT query:\n";
    $testUserId = 13;
    $testDetails = json_encode(['email' => '<EMAIL>', 'expiry' => date('Y-m-d H:i:s')]);
    $testIP = '127.0.0.1';
    $testUserAgent = 'Test Agent';
    
    $stmt = $conn->prepare("
        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, 'otp', 'otp_sent', ?, ?, ?)
    ");
    
    $stmt->execute([$testUserId, $testDetails, $testIP, $testUserAgent]);
    echo "✅ INSERT query executed successfully\n";
    
    // Verify the insertion
    $stmt = $conn->prepare("SELECT * FROM user_auth_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$testUserId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "\n📊 Inserted record:\n";
        foreach ($result as $key => $value) {
            echo "   $key: $value\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
?>
