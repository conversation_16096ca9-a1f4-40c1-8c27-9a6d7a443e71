# FanBet247 Backend Documentation

## Backend Architecture Overview

### 1. Directory Structure
```
/backend
├── includes/              # Shared utilities and configurations
│   ├── db_connect.php    # Database connection handling
│   ├── cors_headers.php  # CORS configuration
│   └── error_logger.php  # Error logging utilities
├── handlers/             # API endpoints handlers
├── uploads/             # File upload directories
└── sql/                # Database schema and migrations
```

### 2. Backend Connection Pattern

#### 2.1 Standard PHP Handler Structure
Every PHP handler should follow this structure:

```php
<?php
// 1. CORS Headers
header("Access-Control-Allow-Origin: *"); 
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// 2. Handle OPTIONS preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 3. Include required files
include_once '../includes/db_connect.php';

// 4. Initialize database connection
$conn = getDBConnection();

// 5. Helper function for consistent JSON responses
function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
    exit();
}

// 6. Handle different HTTP methods
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Handle GET request
        $query = "SELECT * FROM your_table";
        $stmt = $conn->query($query);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        jsonResponse(200, 'Data fetched successfully', $data);
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        jsonResponse(500, 'Database error occurred');
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Handle POST request
        jsonResponse(201, 'Resource created successfully', ['id' => $newId]);
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        jsonResponse(500, 'Failed to create resource');
    }
}
```

#### 2.2 Frontend Connection Pattern
In React components, use this pattern:

```javascript
const API_BASE_URL = '/backend';

// GET Request
const fetchData = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/handlers/your_handler.php`);
        if (response.data.status === 200) {
            setData(response.data.data);
        } else {
            setError(response.data.message);
        }
    } catch (err) {
        console.error('Error:', err);
        setError('Failed to fetch data');
    }
};

// POST Request with File Upload
const handleSubmit = async (formData) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/handlers/your_handler.php`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        if (response.data.status === 200 || response.data.status === 201) {
            setSuccess('Operation successful');
        } else {
            setError(response.data.message);
        }
    } catch (err) {
        console.error('Error:', err);
        setError('Operation failed');
    }
};
```

### 3. Response Format

All API responses should follow this format:

```json
{
    "status": 200,          // HTTP status code
    "message": "Success",   // Human-readable message
    "data": []             // Response data (optional)
}
```

Common status codes:
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 404: Not Found
- 500: Server Error

### 4. File Upload Handling

```php
// Handle file uploads
if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
    $targetDir = '../uploads/your_directory/';
    if (!file_exists($targetDir)) {
        mkdir($targetDir, 0777, true);
    }
    $filePath = 'uploads/your_directory/' . basename($_FILES['file']['name']);
    move_uploaded_file($_FILES['file']['tmp_name'], "../" . $filePath);
}
```

### 5. Error Handling

```php
try {
    // Your code here
} catch (PDOException $e) {
    error_log("Database error in handler_name.php: " . $e->getMessage());
    jsonResponse(500, 'A database error occurred');
} catch (Exception $e) {
    error_log("General error in handler_name.php: " . $e->getMessage());
    jsonResponse(500, 'An unexpected error occurred');
}
```

### 6. Database Connection

The `db_connect.php` file handles database connections:

```php
function getDatabaseConfig() {
    return [
        'host' => 'localhost',
        'db_name' => 'fanbet247',
        'username' => 'root',
        'password' => 'root'
    ];
}

function getDBConnection() {
    $config = getDatabaseConfig();
    try {
        $conn = new PDO(
            "mysql:host={$config['host']};dbname={$config['db_name']}",
            $config['username'],
            $config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        );
        return $conn;
    } catch (PDOException $e) {
        error_log("Database connection error: " . $e->getMessage());
        throw $e;
    }
}
```

### 7. Common Issues and Solutions

1. CORS Issues:
   - Always include proper CORS headers
   - Handle OPTIONS requests
   - Use relative paths in frontend API calls

2. File Upload Issues:
   - Check directory permissions
   - Use proper Content-Type header
   - Handle file size limits

3. Database Connection Issues:
   - Use try-catch blocks
   - Log specific error messages
   - Check database credentials

4. Response Format Issues:
   - Always use jsonResponse function
   - Include status codes
   - Provide meaningful messages

### 8. Best Practices

1. Always use prepared statements for SQL queries
2. Log errors appropriately
3. Use consistent response format
4. Handle file uploads securely
5. Implement proper error handling
6. Use relative paths for API endpoints
7. Keep handler files focused and single-purpose
8. Follow the established pattern for new handlers

### 9. Error Handling and Database Connections

#### 9.1 Database Connection
The application uses PDO for database connections. Connection settings are managed in `includes/db_connect.php`:

```php
// Get database connection
$conn = getDBConnection();

// Use in transactions
try {
    $conn->beginTransaction();
    // ... your code ...
    $conn->commit();
} catch (Exception $e) {
    $conn->rollBack();
    throw $e;
}
```

#### 9.2 Error Handling
Error handling is managed through `includes/error_logger.php`. The system supports:

1. **Custom Error Handler**
   - Logs all PHP errors
   - Configurable debug mode
   - Stack traces in development

2. **Exception Handling**
   ```php
   try {
       // Your code
   } catch (Exception $e) {
       $statusCode = $e->getCode() ?: 500;
       echo json_encode([
           'status' => $statusCode,
           'message' => $e->getMessage(),
           'error' => DEBUG_MODE ? [
               'file' => $e->getFile(),
               'line' => $e->getLine(),
               'trace' => $e->getTraceAsString()
           ] : null
       ]);
   }
   ```

3. **HTTP Status Codes**
   - 200: Success
   - 400: Bad Request
   - 401: Unauthorized
   - 403: Forbidden
   - 404: Not Found
   - 500: Internal Server Error

This documentation should help maintain consistency across the backend codebase and prevent common issues.
