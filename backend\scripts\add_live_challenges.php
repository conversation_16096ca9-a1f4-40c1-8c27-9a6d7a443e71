<?php
require_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get current time and add future dates
    $now = new DateTime();
    $start_time = clone $now;
    $start_time->add(new DateInterval('PT1H')); // 1 hour from now
    
    $end_time = clone $now;
    $end_time->add(new DateInterval('PT6H')); // 6 hours from now
    
    $match_time = clone $now;
    $match_time->add(new DateInterval('PT8H')); // 8 hours from now
    
    // Challenge 1: Arsenal vs Liverpool
    $stmt = $conn->prepare("
        INSERT INTO challenges (
            team_a, team_b, 
            logo1, logo2,
            odds_team_a, odds_team_b, odds_draw,
            team_a_goal_advantage, team_b_goal_advantage,
            start_time, end_time, match_date,
            status, challenge_date
        ) VALUES (
            :team_a, :team_b,
            :logo1, :logo2,
            :odds_team_a, :odds_team_b, :odds_draw,
            :goal_adv_a, :goal_adv_b,
            :start_time, :end_time, :match_time,
            'Open', NOW()
        )
    ");
    
    $stmt->execute([
        ':team_a' => 'Arsenal',
        ':team_b' => 'Liverpool Fc',
        ':logo1' => '/backend/uploads/Arsenal-FC-logo.png',
        ':logo2' => '/backend/uploads/pngwing.com.png',
        ':odds_team_a' => 2.10,
        ':odds_team_b' => 1.85,
        ':odds_draw' => 3.20,
        ':goal_adv_a' => 0,
        ':goal_adv_b' => 0,
        ':start_time' => $start_time->format('Y-m-d H:i:s'),
        ':end_time' => $end_time->format('Y-m-d H:i:s'),
        ':match_time' => $match_time->format('Y-m-d H:i:s')
    ]);
    
    echo "Challenge 1 created: Arsenal vs Liverpool\n";
    
    // Challenge 2: Manchester City vs Chelsea
    $end_time2 = clone $now;
    $end_time2->add(new DateInterval('PT4H')); // 4 hours from now
    
    $match_time2 = clone $now;
    $match_time2->add(new DateInterval('PT6H')); // 6 hours from now
    
    $stmt->execute([
        ':team_a' => 'Manchester City',
        ':team_b' => 'Chelsea Fc',
        ':logo1' => '/backend/uploads/Manchester City.png',
        ':logo2' => '/backend/uploads/image.png',
        ':odds_team_a' => 1.75,
        ':odds_team_b' => 2.30,
        ':odds_draw' => 3.50,
        ':goal_adv_a' => 0,
        ':goal_adv_b' => 0,
        ':start_time' => $start_time->format('Y-m-d H:i:s'),
        ':end_time' => $end_time2->format('Y-m-d H:i:s'),
        ':match_time' => $match_time2->format('Y-m-d H:i:s')
    ]);
    
    echo "Challenge 2 created: Manchester City vs Chelsea\n";
    
    // Challenge 3: Manchester United vs Everton
    $end_time3 = clone $now;
    $end_time3->add(new DateInterval('PT2H')); // 2 hours from now (urgent)
    
    $match_time3 = clone $now;
    $match_time3->add(new DateInterval('PT3H')); // 3 hours from now
    
    $stmt->execute([
        ':team_a' => 'Manchester United',
        ':team_b' => 'Everton',
        ':logo1' => '/backend/uploads/Manchester United Logo.png',
        ':logo2' => '/backend/uploads/Everton Logo.png',
        ':odds_team_a' => 1.95,
        ':odds_team_b' => 2.05,
        ':odds_draw' => 3.10,
        ':goal_adv_a' => 0,
        ':goal_adv_b' => 0,
        ':start_time' => $start_time->format('Y-m-d H:i:s'),
        ':end_time' => $end_time3->format('Y-m-d H:i:s'),
        ':match_time' => $match_time3->format('Y-m-d H:i:s')
    ]);
    
    echo "Challenge 3 created: Manchester United vs Everton (Urgent)\n";
    
    echo "All live challenges created successfully!\n";
    
} catch(Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
