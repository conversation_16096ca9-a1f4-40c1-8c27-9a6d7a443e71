<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';
include_once '../includes/currency_utils.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }
    
    // Get user ID from query parameter
    $userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : null;
    
    if (!$userId) {
        jsonResponse(400, "User ID is required");
    }
    
    // Get user's preferred currency with exchange rate information
    $stmt = $conn->prepare("
        SELECT 
            u.user_id,
            u.username,
            u.preferred_currency_id,
            c.currency_code,
            c.currency_name,
            c.currency_symbol,
            c.is_active,
            er.rate_to_fancoin,
            er.updated_at as rate_updated_at
        FROM users u
        JOIN currencies c ON u.preferred_currency_id = c.id
        LEFT JOIN exchange_rates er ON c.id = er.currency_id
        WHERE u.user_id = :user_id
        ORDER BY er.updated_at DESC
        LIMIT 1
    ");
    
    $stmt->execute(['user_id' => $userId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$result) {
        jsonResponse(404, "User not found");
    }
    
    // Check if currency is still active
    if (!$result['is_active']) {
        // User's preferred currency has been deactivated, suggest USD as fallback
        $usdCurrency = getCurrencyByCode($conn, 'USD');
        
        jsonResponse(200, "User currency preference retrieved (currency inactive)", [
            'user_id' => (int)$result['user_id'],
            'username' => $result['username'],
            'preferred_currency' => [
                'id' => (int)$result['preferred_currency_id'],
                'currency_code' => $result['currency_code'],
                'currency_name' => $result['currency_name'],
                'currency_symbol' => $result['currency_symbol'],
                'is_active' => false,
                'rate_to_fancoin' => $result['rate_to_fancoin'] ? (float)$result['rate_to_fancoin'] : null,
                'rate_updated_at' => $result['rate_updated_at']
            ],
            'suggested_fallback' => $usdCurrency,
            'warning' => 'User\'s preferred currency is inactive. Consider updating to an active currency.'
        ]);
    }
    
    // Format response data
    $responseData = [
        'user_id' => (int)$result['user_id'],
        'username' => $result['username'],
        'preferred_currency' => [
            'id' => (int)$result['preferred_currency_id'],
            'currency_code' => $result['currency_code'],
            'currency_name' => $result['currency_name'],
            'currency_symbol' => $result['currency_symbol'],
            'is_active' => (bool)$result['is_active'],
            'rate_to_fancoin' => $result['rate_to_fancoin'] ? (float)$result['rate_to_fancoin'] : null,
            'formatted_rate' => $result['rate_to_fancoin'] ? 
                $result['currency_symbol'] . number_format($result['rate_to_fancoin'], 4) : 
                'No rate available',
            'conversion_example' => $result['rate_to_fancoin'] ? 
                '1 FanCoin = ' . $result['currency_symbol'] . number_format($result['rate_to_fancoin'], 2) . ' ' . $result['currency_code'] :
                'No conversion available',
            'rate_updated_at' => $result['rate_updated_at'],
            'display_name' => $result['currency_symbol'] . ' ' . $result['currency_code'] . ' - ' . $result['currency_name']
        ]
    ];
    
    // Add sample conversion for demonstration
    if ($result['rate_to_fancoin']) {
        $sampleAmounts = [10, 50, 100, 500];
        $sampleConversions = [];
        
        foreach ($sampleAmounts as $amount) {
            $converted = $amount * $result['rate_to_fancoin'];
            $sampleConversions[] = [
                'fancoin_amount' => $amount,
                'converted_amount' => (float)$converted,
                'formatted' => $result['currency_symbol'] . number_format($converted, 2)
            ];
        }
        
        $responseData['sample_conversions'] = $sampleConversions;
    }
    
    jsonResponse(200, "User currency preference retrieved successfully", $responseData);
    
} catch (PDOException $e) {
    error_log("Database error in get_user_currency_preference.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    error_log("General error in get_user_currency_preference.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred while retrieving user currency preference");
}
?>
