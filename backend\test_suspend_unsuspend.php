<?php
// Test script to verify suspend/unsuspend functionality
require_once 'includes/db_connect.php';

try {
    $db = getDBConnection();
    
    if (!$db) {
        echo "Database connection failed\n";
        exit;
    }
    
    // Get a test user (first active user)
    $query = "SELECT user_id, username, status FROM users WHERE status = 'active' LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "No active users found for testing\n";
        exit;
    }
    
    echo "Testing with user: {$user['username']} (ID: {$user['user_id']})\n";
    echo "Current status: {$user['status']}\n\n";
    
    // Test suspend
    echo "Testing SUSPEND functionality...\n";
    $suspend_data = json_encode([
        'user_id' => $user['user_id'],
        'action' => 'suspend'
    ]);
    
    // Simulate POST request to suspend_user.php
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $suspend_data
        ]
    ]);
    
    // Check status after suspend
    $query = "SELECT status FROM users WHERE user_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Manually test suspend
    $update_query = "UPDATE users SET status = 'suspended' WHERE user_id = :user_id";
    $stmt = $db->prepare($update_query);
    $stmt->bindParam(':user_id', $user['user_id']);
    if ($stmt->execute()) {
        echo "✓ User suspended successfully\n";
    } else {
        echo "✗ Failed to suspend user\n";
    }
    
    // Verify suspend
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Status after suspend: {$result['status']}\n\n";
    
    // Test unsuspend
    echo "Testing UNSUSPEND functionality...\n";
    $update_query = "UPDATE users SET status = 'active' WHERE user_id = :user_id";
    $stmt = $db->prepare($update_query);
    $stmt->bindParam(':user_id', $user['user_id']);
    if ($stmt->execute()) {
        echo "✓ User unsuspended successfully\n";
    } else {
        echo "✗ Failed to unsuspend user\n";
    }
    
    // Verify unsuspend
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Status after unsuspend: {$result['status']}\n\n";
    
    echo "Test completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>