Write-Host "Testing Deployment Tools" -ForegroundColor Cyan

# Test SSH
Write-Host "Testing SSH..." -ForegroundColor Blue
try {
    $ssh = Get-Command ssh -ErrorAction Stop
    Write-Host "SSH found at: $($ssh.Source)" -ForegroundColor Green
} catch {
    Write-Host "SSH not found" -ForegroundColor Red
}

# Test SCP
Write-Host "Testing SCP..." -ForegroundColor Blue
try {
    $scp = Get-Command scp -ErrorAction Stop
    Write-Host "SCP found at: $($scp.Source)" -ForegroundColor Green
} catch {
    Write-Host "SCP not found" -ForegroundColor Red
}

# Check files
Write-Host "Checking files..." -ForegroundColor Blue
$files = @("index.html", ".htaccess", "debug.php", "backend")
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Found: $file" -ForegroundColor Green
    } else {
        Write-Host "Missing: $file" -ForegroundColor Red
    }
}

Write-Host "Test complete" -ForegroundColor Cyan
