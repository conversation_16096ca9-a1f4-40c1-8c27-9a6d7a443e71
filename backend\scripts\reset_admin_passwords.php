<?php
require_once '../includes/db_connect.php';

function resetAllAdminPasswords($newPassword) {
    $conn = getDBConnection();
    if (!$conn) {
        die("Database connection failed.");
    }

    $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);

    try {
        $stmt = $conn->prepare("UPDATE admins SET password_hash = :password_hash");
        $stmt->bindParam(':password_hash', $passwordHash);
        $stmt->execute();

        $rowCount = $stmt->rowCount();
        echo "Successfully updated the password for " . $rowCount . " admin(s).";

    } catch (PDOException $e) {
        die("Error updating passwords: " . $e->getMessage());
    }
}

// Set the new password here
$newPassword = 'loving12';
resetAllAdminPasswords($newPassword);

?>