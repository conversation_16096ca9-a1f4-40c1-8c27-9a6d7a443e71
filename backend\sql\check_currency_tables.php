<?php
// Check if currency tables exist
try {
    $conn = new PDO(
        "mysql:host=localhost;dbname=fanbet247",
        'root',
        'root',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    echo "Checking currency system tables...\n";
    
    // Check currencies table
    $stmt = $conn->query("SHOW TABLES LIKE 'currencies'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Currencies table exists\n";
        
        // Check if it has data
        $stmt = $conn->query("SELECT COUNT(*) as count FROM currencies");
        $count = $stmt->fetch()['count'];
        echo "  - Contains $count currencies\n";
        
        if ($count > 0) {
            $stmt = $conn->query("SELECT currency_code, currency_name FROM currencies LIMIT 3");
            while ($row = $stmt->fetch()) {
                echo "    - {$row['currency_code']}: {$row['currency_name']}\n";
            }
        }
    } else {
        echo "✗ Currencies table does not exist\n";
    }
    
    // Check exchange_rates table
    $stmt = $conn->query("SHOW TABLES LIKE 'exchange_rates'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Exchange_rates table exists\n";
        
        // Check if it has data
        $stmt = $conn->query("SELECT COUNT(*) as count FROM exchange_rates");
        $count = $stmt->fetch()['count'];
        echo "  - Contains $count exchange rates\n";
    } else {
        echo "✗ Exchange_rates table does not exist\n";
    }
    
    // Check if users table has preferred_currency_id
    $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'preferred_currency_id'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Users table has preferred_currency_id column\n";
    } else {
        echo "✗ Users table missing preferred_currency_id column\n";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
