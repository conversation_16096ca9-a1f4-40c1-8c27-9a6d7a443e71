<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Transactions table structure:\n";
    $stmt = $conn->query('DESCRIBE transactions');
    while ($row = $stmt->fetch()) {
        echo "- {$row['Field']}: {$row['Type']}\n";
    }
    
    echo "\nSample transactions:\n";
    $stmt = $conn->query('SELECT * FROM transactions LIMIT 5');
    while ($row = $stmt->fetch()) {
        print_r($row);
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>