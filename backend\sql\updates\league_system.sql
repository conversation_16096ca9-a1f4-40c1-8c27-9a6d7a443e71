 -- Create seasons table if not exists
CREATE TABLE IF NOT EXISTS seasons (
    season_id INT NOT NULL AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('upcoming', 'active', 'completed') DEFAULT 'upcoming',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (season_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create achievements table if not exists
CREATE TABLE IF NOT EXISTS achievements (
    achievement_id INT NOT NULL AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    points_required INT DEFAULT 0,
    streak_required INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY <PERSON>EY (achievement_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create user_achievements table if not exists
CREATE TABLE IF NOT EXISTS user_achievements (
    user_id INT NOT NULL,
    achievement_id INT NOT NULL,
    earned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, achievement_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (achievement_id) REFERENCES achievements(achievement_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create season_history table if not exists
CREATE TABLE IF NOT EXISTS season_history (
    history_id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    season_id INT NOT NULL,
    final_points INT DEFAULT 0,
    final_rank INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (history_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (season_id) REFERENCES seasons(season_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add new columns to users table
ALTER TABLE users 
ADD COLUMN current_league_id INT NULL,
ADD COLUMN total_points INT DEFAULT 0,
ADD COLUMN current_streak INT DEFAULT 0,
ADD COLUMN highest_streak INT DEFAULT 0,
ADD COLUMN current_season_id INT NULL;

-- Add foreign key constraints
ALTER TABLE users
ADD CONSTRAINT fk_user_current_league FOREIGN KEY (current_league_id) REFERENCES leagues(league_id) ON DELETE SET NULL,
ADD CONSTRAINT fk_user_current_season FOREIGN KEY (current_season_id) REFERENCES seasons(season_id) ON DELETE SET NULL;

-- Insert default achievements
INSERT INTO achievements (name, description, icon, points_required, streak_required) VALUES
('Rookie Bettor', 'Earn your first 10 points', '', 10, 0),
('Rising Star', 'Accumulate 50 points', '', 50, 0),
('Betting Pro', 'Reach 100 points', '', 100, 0),
('Master Predictor', 'Achieve 500 points', '', 500, 0),
('Hot Streak', 'Win 5 bets in a row', '', 0, 5),
('Unstoppable', 'Win 10 bets in a row', '', 0, 10),
('Perfect Season', 'Finish a season at rank #1', '', 0, 0);

-- Insert initial season
INSERT INTO seasons (name, start_date, end_date, status) VALUES
('Season 1 2024', CURRENT_DATE, DATE_ADD(CURRENT_DATE, INTERVAL 90 DAY), 'active');

-- Create trigger to update user achievements
DELIMITER //

DROP TRIGGER IF EXISTS check_achievements //

CREATE TRIGGER check_achievements 
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    -- Check points-based achievements
    INSERT IGNORE INTO user_achievements (user_id, achievement_id)
    SELECT NEW.user_id, achievement_id
    FROM achievements
    WHERE points_required > 0 
    AND points_required <= NEW.total_points
    AND achievement_id NOT IN (
        SELECT achievement_id 
        FROM user_achievements 
        WHERE user_id = NEW.user_id
    );

    -- Check streak-based achievements
    IF NEW.current_streak > OLD.current_streak THEN
        INSERT IGNORE INTO user_achievements (user_id, achievement_id)
        SELECT NEW.user_id, achievement_id
        FROM achievements
        WHERE streak_required > 0 
        AND streak_required <= NEW.current_streak
        AND achievement_id NOT IN (
            SELECT achievement_id 
            FROM user_achievements 
            WHERE user_id = NEW.user_id
        );
    END IF;
END //

DELIMITER ;
