<?php
require_once 'includes/db_connect.php';
require_once 'vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

echo "🔍 2FA Debugging Script\n";
echo "========================\n\n";

try {
    $conn = getDBConnection();
    echo "✅ Database connection successful\n\n";
    
    // Check for users with 2FA enabled
    $stmt = $conn->prepare("
        SELECT u.user_id, u.username, u.email, u.tfa_enabled, u.auth_method,
               t.secret_key, t.is_enabled, t.setup_completed, t.created_at
        FROM users u 
        LEFT JOIN user_2fa t ON u.user_id = t.user_id 
        WHERE u.tfa_enabled = 1 OR t.is_enabled = 1
    ");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "❌ No users with 2FA enabled found\n";
        echo "Let's create a test setup...\n\n";
        
        // Create a test 2FA setup
        $google2fa = new Google2FA();
        $secretKey = $google2fa->generateSecretKey();
        
        echo "🔑 Generated test secret key: $secretKey\n";
        echo "📱 QR Code URL: " . $google2fa->getQRCodeUrl('FanBet247', '<EMAIL>', $secretKey) . "\n\n";
        
        // Test current timestamp
        $timestamp = $google2fa->getCurrentOtp($secretKey);
        echo "🕐 Current TOTP code for this secret: $timestamp\n";
        echo "⏰ Current server time: " . date('Y-m-d H:i:s') . "\n";
        echo "⏰ Current Unix timestamp: " . time() . "\n\n";
        
        // Test verification with different windows
        echo "🧪 Testing verification with different windows:\n";
        for ($window = 0; $window <= 4; $window++) {
            $google2fa->setWindow($window);
            $isValid = $google2fa->verifyKey($secretKey, $timestamp);
            echo "   Window $window: " . ($isValid ? "✅ VALID" : "❌ INVALID") . "\n";
        }
        
    } else {
        echo "👥 Found " . count($users) . " user(s) with 2FA:\n\n";
        
        foreach ($users as $user) {
            echo "👤 User: {$user['username']} (ID: {$user['user_id']})\n";
            echo "   Email: {$user['email']}\n";
            echo "   TFA Enabled: " . ($user['tfa_enabled'] ? 'Yes' : 'No') . "\n";
            echo "   Auth Method: {$user['auth_method']}\n";
            echo "   Secret Key: " . ($user['secret_key'] ? substr($user['secret_key'], 0, 8) . '...' : 'None') . "\n";
            echo "   Setup Completed: " . ($user['setup_completed'] ? 'Yes' : 'No') . "\n";
            echo "   Created: {$user['created_at']}\n";
            
            if ($user['secret_key']) {
                $google2fa = new Google2FA();
                
                // Test current code generation
                $currentCode = $google2fa->getCurrentOtp($user['secret_key']);
                echo "   🔢 Current TOTP code: $currentCode\n";
                
                // Test verification with different windows
                echo "   🧪 Verification test (windows 0-4):\n";
                for ($window = 0; $window <= 4; $window++) {
                    $google2fa->setWindow($window);
                    $isValid = $google2fa->verifyKey($user['secret_key'], $currentCode);
                    echo "      Window $window: " . ($isValid ? "✅" : "❌") . "\n";
                }
            }
            echo "\n";
        }
    }
    
    // Test time synchronization
    echo "⏰ Time Synchronization Check:\n";
    echo "   Server time: " . date('Y-m-d H:i:s T') . "\n";
    echo "   Unix timestamp: " . time() . "\n";
    echo "   30-second window: " . floor(time() / 30) . "\n\n";
    
    // Test Google2FA library directly
    echo "📚 Google2FA Library Test:\n";
    $google2fa = new Google2FA();
    $testSecret = 'JBSWY3DPEHPK3PXP'; // Standard test secret
    $testCode = $google2fa->getCurrentOtp($testSecret);
    
    echo "   Test secret: $testSecret\n";
    echo "   Generated code: $testCode\n";
    
    // Test verification
    $google2fa->setWindow(2);
    $isValid = $google2fa->verifyKey($testSecret, $testCode);
    echo "   Verification result: " . ($isValid ? "✅ SUCCESS" : "❌ FAILED") . "\n\n";
    
    // Check for common issues
    echo "🔍 Common Issue Checks:\n";
    
    // Check if Google2FA class exists
    if (class_exists('PragmaRX\Google2FA\Google2FA')) {
        echo "   ✅ Google2FA class loaded\n";
    } else {
        echo "   ❌ Google2FA class not found\n";
    }
    
    // Check if secret key format is correct
    if (strlen($testSecret) >= 16) {
        echo "   ✅ Secret key length is valid\n";
    } else {
        echo "   ❌ Secret key too short\n";
    }
    
    // Check if base32 encoding works
    try {
        $decoded = $google2fa->base32Decode($testSecret);
        echo "   ✅ Base32 decoding works\n";
    } catch (Exception $e) {
        echo "   ❌ Base32 decoding failed: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
