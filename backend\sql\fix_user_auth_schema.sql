-- =====================================================
-- Fix User Authentication Schema for FanBet247
-- =====================================================
-- This script adds missing authentication columns to the users table

-- Add missing authentication columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS otp_enabled BOOLEAN DEFAULT 0,
ADD COLUMN IF NOT EXISTS tfa_enabled BOOLEAN DEFAULT 0,
ADD COLUMN IF NOT EXISTS auth_method ENUM('password_only', 'password_otp', 'password_2fa', 'password_otp_2fa') DEFAULT 'password_only';

-- Create user_otp table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_otp (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    otp VARCHAR(10) NOT NULL,
    expiry DATETIME NOT NULL,
    attempts INT NOT NULL DEFAULT 0,
    locked_until DATETIME NULL,
    used BOOLEAN NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_otp_user_id (user_id),
    INDEX idx_user_otp_expires (expiry)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create user_auth_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_auth_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    auth_type VARCHAR(50),
    action VARCHAR(50) NOT NULL,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_auth_logs (user_id, created_at),
    INDEX idx_auth_type (auth_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create user_login_attempts table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    email VARCHAR(255),
    ip_address VARCHAR(45) NOT NULL,
    attempt_type VARCHAR(20) NOT NULL,
    success BOOLEAN DEFAULT FALSE,
    failure_reason VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_login_attempts (ip_address, created_at),
    INDEX idx_user_attempts (user_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Update existing test users to have proper password hashes
-- Note: These are for testing only - remove in production
UPDATE users SET 
    password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    auth_method = 'password_only'
WHERE username IN ('testuser', 'demohomexx', 'lilwayne', 'jameslink01', 'Bobyanka01');

-- Add indexes for better performance
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_users_email (email);
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_users_username (username);
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_users_auth_method (auth_method);

COMMIT;
