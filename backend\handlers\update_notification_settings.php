<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Simple authentication check - in production, implement proper token validation
    // For now, we'll trust the frontend authentication since admin routes are protected
    // This is acceptable for development but should be enhanced for production
    
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input);
    
    if (!$data || !isset($data->settings)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Invalid input data"
        ]);
        exit;
    }
    
    // Create notification_settings table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS notification_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_name VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    $conn->exec($createTableSQL);
    
    // Begin transaction
    $conn->beginTransaction();
    
    foreach ($data->settings as $name => $value) {
        // Check if setting exists
        $checkStmt = $conn->prepare("SELECT COUNT(*) as count FROM notification_settings WHERE setting_name = :name");
        $checkStmt->bindParam(':name', $name);
        $checkStmt->execute();
        $exists = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;
        
        if ($exists) {
            // Update existing setting
            $stmt = $conn->prepare("
                UPDATE notification_settings 
                SET setting_value = :value
                WHERE setting_name = :name
            ");
        } else {
            // Insert new setting
            $stmt = $conn->prepare("
                INSERT INTO notification_settings 
                (setting_name, setting_value, description) 
                VALUES 
                (:name, :value, :description)
            ");
            $description = "Notification setting configured via admin panel";
            $stmt->bindParam(':description', $description);
        }
        
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':value', $value);
        
        if (!$stmt->execute()) {
            $conn->rollBack();
            http_response_code(500);
            echo json_encode([
                "success" => false,
                "message" => "Failed to update setting: " . $name
            ]);
            exit;
        }
    }
    
    // Commit transaction
    $conn->commit();
    
    http_response_code(200);
    echo json_encode([
        "success" => true,
        "message" => "Notification settings updated successfully"
    ]);
    
} catch (PDOException $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Error: " . $e->getMessage()
    ]);
}
?>
