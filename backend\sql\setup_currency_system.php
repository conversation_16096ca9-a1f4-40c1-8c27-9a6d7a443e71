<?php
/**
 * FanBet247 Currency System Setup Script
 * This script sets up the multi-currency system for FanBet247
 * 
 * Usage: php setup_currency_system.php
 * Or access via browser: http://your-domain/backend/sql/setup_currency_system.php
 */

header("Content-Type: application/json; charset=UTF-8");

// Include database connection
require_once __DIR__ . '/../includes/db_connect.php';

try {
    $conn = getDBConnection();

    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    $conn->beginTransaction();
    
    $results = [];
    $errors = [];
    
    // Step 1: Create currencies table
    $sql = "CREATE TABLE IF NOT EXISTS currencies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        currency_code VARCHAR(3) NOT NULL UNIQUE COMMENT 'ISO currency code (USD, ZAR, etc.)',
        currency_name VARCHAR(50) NOT NULL COMMENT 'Full currency name',
        currency_symbol VARCHAR(10) NOT NULL COMMENT 'Currency symbol (\$, R, etc.)',
        is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether currency is available for selection',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_currency_code (currency_code),
        INDEX idx_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Available currencies for user selection'";
    
    if ($conn->exec($sql) !== false) {
        $results[] = "✓ Created currencies table";
    } else {
        $errors[] = "✗ Failed to create currencies table";
    }
    
    // Step 2: Create exchange_rates table
    $sql = "CREATE TABLE IF NOT EXISTS exchange_rates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        currency_id INT NOT NULL,
        rate_to_fancoin DECIMAL(10,4) NOT NULL COMMENT 'How many units of this currency = 1 FanCoin',
        updated_by_admin_id INT NOT NULL COMMENT 'Admin who last updated this rate',
        notes TEXT COMMENT 'Optional notes about the rate update',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (currency_id) REFERENCES currencies(id) ON DELETE CASCADE,
        FOREIGN KEY (updated_by_admin_id) REFERENCES admins(admin_id) ON DELETE RESTRICT,
        INDEX idx_currency_id (currency_id),
        INDEX idx_updated_at (updated_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Exchange rates for converting FanCoin to other currencies'";
    
    if ($conn->exec($sql) !== false) {
        $results[] = "✓ Created exchange_rates table";
    } else {
        $errors[] = "✗ Failed to create exchange_rates table";
    }
    
    // Step 3: Add preferred_currency_id to users table (check if column exists first)
    $stmt = $conn->prepare("SHOW COLUMNS FROM users LIKE 'preferred_currency_id'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        $sql = "ALTER TABLE users 
                ADD COLUMN preferred_currency_id INT DEFAULT 1 COMMENT 'User preferred currency for display',
                ADD INDEX idx_preferred_currency (preferred_currency_id)";
        
        if ($conn->exec($sql) !== false) {
            $results[] = "✓ Added preferred_currency_id to users table";
        } else {
            $errors[] = "✗ Failed to add preferred_currency_id to users table";
        }
    } else {
        $results[] = "✓ preferred_currency_id column already exists in users table";
    }
    
    // Step 4: Insert default currencies (check if they exist first)
    $stmt = $conn->prepare("SELECT COUNT(*) FROM currencies");
    $stmt->execute();
    $currencyCount = $stmt->fetchColumn();
    
    if ($currencyCount == 0) {
        $currencies = [
            ['USD', 'US Dollar', '$'],
            ['ZAR', 'South African Rand', 'R'],
            ['EUR', 'Euro', '€'],
            ['GBP', 'British Pound', '£'],
            ['CAD', 'Canadian Dollar', 'C$'],
            ['AUD', 'Australian Dollar', 'A$']
        ];
        
        $stmt = $conn->prepare("INSERT INTO currencies (currency_code, currency_name, currency_symbol, is_active) VALUES (?, ?, ?, TRUE)");
        
        foreach ($currencies as $currency) {
            if ($stmt->execute($currency)) {
                $results[] = "✓ Added currency: {$currency[0]} - {$currency[1]}";
            } else {
                $errors[] = "✗ Failed to add currency: {$currency[0]}";
            }
        }
    } else {
        $results[] = "✓ Currencies already exist in database";
    }
    
    // Step 5: Insert default exchange rates (check if they exist first)
    $stmt = $conn->prepare("SELECT COUNT(*) FROM exchange_rates");
    $stmt->execute();
    $rateCount = $stmt->fetchColumn();
    
    if ($rateCount == 0) {
        // Get first admin ID for the rates
        $stmt = $conn->prepare("SELECT admin_id FROM admins ORDER BY admin_id LIMIT 1");
        $stmt->execute();
        $adminId = $stmt->fetchColumn();
        
        if ($adminId) {
            $rates = [
                [1, 1.0000, 'Base rate - 1 FanCoin = 1 USD'],
                [2, 18.0000, 'Initial rate - 1 FanCoin = 18 ZAR'],
                [3, 0.9200, 'Initial rate - 1 FanCoin = 0.92 EUR'],
                [4, 0.8000, 'Initial rate - 1 FanCoin = 0.80 GBP'],
                [5, 1.3500, 'Initial rate - 1 FanCoin = 1.35 CAD'],
                [6, 1.5000, 'Initial rate - 1 FanCoin = 1.50 AUD']
            ];
            
            $stmt = $conn->prepare("INSERT INTO exchange_rates (currency_id, rate_to_fancoin, updated_by_admin_id, notes) VALUES (?, ?, ?, ?)");
            
            foreach ($rates as $rate) {
                if ($stmt->execute([$rate[0], $rate[1], $adminId, $rate[2]])) {
                    $results[] = "✓ Added exchange rate: {$rate[2]}";
                } else {
                    $errors[] = "✗ Failed to add exchange rate for currency ID: {$rate[0]}";
                }
            }
        } else {
            $errors[] = "✗ No admin found to assign exchange rates";
        }
    } else {
        $results[] = "✓ Exchange rates already exist in database";
    }
    
    // Step 6: Add foreign key constraint for users.preferred_currency_id (if not exists)
    try {
        $sql = "ALTER TABLE users ADD FOREIGN KEY (preferred_currency_id) REFERENCES currencies(id) ON DELETE SET NULL";
        $conn->exec($sql);
        $results[] = "✓ Added foreign key constraint for preferred_currency_id";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            $results[] = "✓ Foreign key constraint already exists for preferred_currency_id";
        } else {
            $errors[] = "✗ Failed to add foreign key constraint: " . $e->getMessage();
        }
    }
    
    // Step 7: Update existing users to have USD as default currency
    $stmt = $conn->prepare("UPDATE users SET preferred_currency_id = 1 WHERE preferred_currency_id IS NULL");
    if ($stmt->execute()) {
        $affectedRows = $stmt->rowCount();
        $results[] = "✓ Updated {$affectedRows} users to have USD as default currency";
    } else {
        $errors[] = "✗ Failed to update existing users with default currency";
    }
    
    // Commit transaction if no errors
    if (empty($errors)) {
        $conn->commit();
        $status = "success";
        $message = "Currency system setup completed successfully!";
    } else {
        $conn->rollback();
        $status = "error";
        $message = "Currency system setup failed. Transaction rolled back.";
    }
    
    // Return results
    echo json_encode([
        'success' => $status === 'success',
        'status' => $status,
        'message' => $message,
        'results' => $results,
        'errors' => $errors,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    if ($conn && $conn->inTransaction()) {
        $conn->rollback();
    }

    echo json_encode([
        'success' => false,
        'status' => 'error',
        'message' => 'Setup failed: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}
?>
