import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamationTriangle, FaArrowLeft, FaRedo } from 'react-icons/fa';
import '../../pages/AdminLoginPage.css';

const API_BASE_URL = '/backend';

const AdminOTPVerification = ({ adminId, username, onSuccess, onBack }) => {
    const [otp, setOtp] = useState(['', '', '', '', '', '']);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [timeLeft, setTimeLeft] = useState(0);
    const [canResend, setCanResend] = useState(false); // Start disabled - OTP will be sent automatically
    const [resendLoading, setResendLoading] = useState(false);
    const [otpSent, setOtpSent] = useState(false);
    const [initialOtpSent, setInitialOtpSent] = useState(false); // Flag to prevent duplicate initial sends
    const [initialOtpFailed, setInitialOtpFailed] = useState(false); // Track if initial OTP failed

    const inputRefs = useRef([]);

    useEffect(() => {
        // Send initial OTP when component mounts (only once)
        if (!initialOtpSent) {
            setInitialOtpSent(true);
            sendInitialOTP();
        }
    }, [initialOtpSent]);

    useEffect(() => {
        // Timer for OTP expiry and resend availability
        if (timeLeft > 0) {
            const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
            return () => clearTimeout(timer);
        } else if (otpSent && timeLeft === 0) {
            // Only enable resend when timer expires
            setCanResend(true);
        }
    }, [timeLeft, otpSent]);

    // Function specifically for initial OTP sending on component mount
    const sendInitialOTP = async () => {
        try {
            setResendLoading(true);
            setError('');
            setSuccess('');

            console.log('Sending initial OTP for admin:', adminId);

            const response = await axios.post(`${API_BASE_URL}/handlers/admin_send_otp.php`, {
                admin_id: adminId
            });

            console.log('Initial OTP send response:', response.data);

            if (response.data.success) {
                setSuccess(`OTP sent to ${response.data.email_masked}`);
                setTimeLeft(response.data.expires_in || 300);
                setCanResend(false); // Keep resend disabled until timer expires
                setOtpSent(true);
                setInitialOtpFailed(false);
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to send OTP');
                setCanResend(true); // Enable resend if initial OTP failed
                setInitialOtpFailed(true);
            }
        } catch (err) {
            console.error('Initial OTP send error:', err);

            if (err.response) {
                setError(err.response.data?.message || 'Server error occurred while sending OTP');
            } else if (err.request) {
                setError('Network error: Unable to send OTP. Please check your connection.');
            } else {
                setError('Failed to send OTP. Please try again.');
            }

            setCanResend(true); // Enable resend if initial OTP failed
            setInitialOtpFailed(true);
        } finally {
            setResendLoading(false);
        }
    };

    // Function for manual resend requests
    const sendOTP = async () => {
        try {
            setResendLoading(true);
            setError('');
            setSuccess('');

            console.log('Resending OTP for admin:', adminId);

            const response = await axios.post(`${API_BASE_URL}/handlers/admin_send_otp.php`, {
                admin_id: adminId
            });

            console.log('OTP resend response:', response.data);

            if (response.data.success) {
                setSuccess(`New OTP sent to ${response.data.email_masked}`);
                setTimeLeft(response.data.expires_in || 300);
                setCanResend(false); // Disable resend until new timer expires
                setOtpSent(true);
                setInitialOtpFailed(false);
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to resend OTP');
                setCanResend(true); // Allow retry on failure
            }
        } catch (err) {
            console.error('OTP resend error:', err);

            if (err.response) {
                setError(err.response.data?.message || 'Server error occurred while resending OTP');
            } else if (err.request) {
                setError('Network error: Unable to resend OTP. Please check your connection.');
            } else {
                setError('Failed to resend OTP. Please try again.');
            }

            setCanResend(true); // Allow retry on error
        } finally {
            setResendLoading(false);
        }
    };

    const handleOtpChange = (index, value) => {
        if (value.length > 1) return; // Prevent multiple characters
        
        const newOtp = [...otp];
        newOtp[index] = value;
        setOtp(newOtp);

        // Auto-focus next input
        if (value && index < 5) {
            inputRefs.current[index + 1]?.focus();
        }

        // Auto-submit when all fields are filled
        if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
            verifyOTP(newOtp.join(''));
        }
    };

    const handleKeyDown = (index, e) => {
        if (e.key === 'Backspace' && !otp[index] && index > 0) {
            inputRefs.current[index - 1]?.focus();
        }
    };

    const verifyOTP = async (otpCode = null) => {
        const codeToVerify = otpCode || otp.join('');

        if (codeToVerify.length !== 6) {
            setError('Please enter the complete 6-digit OTP code');
            return;
        }

        // Validate that OTP contains only digits
        if (!/^\d{6}$/.test(codeToVerify)) {
            setError('OTP must contain only numbers');
            setOtp(['', '', '', '', '', '']);
            inputRefs.current[0]?.focus();
            return;
        }

        try {
            setLoading(true);
            setError('');
            setSuccess('');

            console.log('Verifying OTP:', codeToVerify, 'for admin:', adminId);

            const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_otp.php`, {
                admin_id: adminId,
                otp: codeToVerify
            });

            console.log('OTP verification response:', response.data);

            if (response.data.success) {
                setSuccess('OTP verified successfully!');
                setTimeout(() => {
                    onSuccess({
                        admin_id: response.data.admin_id,
                        username: response.data.username,
                        role: response.data.role,
                        session_token: response.data.session_token,
                        auth_method: response.data.auth_method
                    });
                }, 1000);
            } else {
                setError(response.data.message || 'Invalid OTP code');
                // Clear OTP inputs on error
                setOtp(['', '', '', '', '', '']);
                inputRefs.current[0]?.focus();
            }
        } catch (err) {
            console.error('OTP verification error:', err);

            // Enhanced error handling
            if (err.response) {
                // Server responded with error status
                const errorMessage = err.response.data?.message || `Server error: ${err.response.status}`;
                setError(errorMessage);
                console.error('Server error response:', err.response.data);
            } else if (err.request) {
                // Request was made but no response received
                setError('Network error: Unable to reach server. Please check your connection.');
                console.error('Network error:', err.request);
            } else {
                // Something else happened
                setError('An unexpected error occurred. Please try again.');
                console.error('Unexpected error:', err.message);
            }

            // Clear OTP inputs on error
            setOtp(['', '', '', '', '', '']);
            inputRefs.current[0]?.focus();
        } finally {
            setLoading(false);
        }
    };

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <div className="admin-login-container">
            <div className="login-left-panel">
                {/* Logo Section */}
                <div className="login-logo">
                    <div className="logo-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                        </svg>
                    </div>
                    <h1>FanBet247</h1>
                </div>

                <div className="login-form-container">
                    <h2>Enter OTP Code</h2>
                    <p className="login-subtitle">We've automatically sent a 6-digit code to your registered email address</p>

                    {/* Initial OTP Info */}
                    {otpSent && !initialOtpFailed && (
                        <div style={{
                            padding: '0.75rem',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            color: '#1e40af',
                            borderRadius: '0.5rem',
                            marginBottom: '1rem',
                            textAlign: 'center',
                            fontSize: '0.875rem',
                            border: '1px solid rgba(59, 130, 246, 0.2)'
                        }}>
                            📧 OTP sent automatically - Check your email inbox
                        </div>
                    )}

                    {/* Security Notice */}
                    <div className="security-notice">
                        <div className="security-icon">
                            <FaKey />
                        </div>
                        <span>Admin: {username} - OTP Authentication Required</span>
                    </div>

                    {/* Error Message */}
                    {error && <div className="error-message">{error}</div>}

                    {/* Success Message */}
                    {success && (
                        <div style={{
                            padding: '0.75rem',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            color: '#15803d',
                            borderRadius: '0.5rem',
                            marginBottom: '1.5rem',
                            textAlign: 'center',
                            fontSize: '0.875rem',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '0.5rem'
                        }}>
                            <FaEnvelope />
                            {success}
                        </div>
                    )}

                    {/* OTP Input */}
                    <div className="form-group">
                        <label>Enter the 6-digit code</label>
                        <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            gap: '0.75rem',
                            marginTop: '0.5rem'
                        }}>
                            {otp.map((digit, index) => (
                                <input
                                    key={index}
                                    ref={el => inputRefs.current[index] = el}
                                    type="text"
                                    maxLength="1"
                                    value={digit}
                                    onChange={(e) => handleOtpChange(index, e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(index, e)}
                                    style={{
                                        width: '3rem',
                                        height: '3rem',
                                        textAlign: 'center',
                                        fontSize: '1.25rem',
                                        fontWeight: 'bold',
                                        border: '1px solid #d1d5db',
                                        borderRadius: '0.5rem',
                                        backgroundColor: '#ffffff'
                                    }}
                                    className="otp-input"
                                    disabled={loading}
                                />
                            ))}
                        </div>
                    </div>

                    {/* Timer */}
                    {timeLeft > 0 && (
                        <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
                            <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                                Code expires in: <span style={{ fontWeight: '500', color: '#2C5F2D' }}>{formatTime(timeLeft)}</span>
                            </p>
                        </div>
                    )}

                    {/* Verify Button */}
                    <button
                        onClick={() => verifyOTP()}
                        disabled={loading || otp.join('').length !== 6}
                        className="login-button"
                        style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '0.5rem' }}
                    >
                        {loading && <FaSpinner className="animate-spin" />}
                        {loading ? 'Verifying...' : 'Verify OTP'}
                    </button>

                    {/* Action Buttons */}
                    <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '1rem',
                        marginTop: '1.5rem'
                    }}>
                        {/* Resend OTP Button */}
                        <button
                            onClick={sendOTP}
                            disabled={!canResend || resendLoading}
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '0.5rem',
                                padding: '0.75rem 1.5rem',
                                backgroundColor: (!canResend || resendLoading) ? '#9ca3af' : '#3b82f6',
                                color: 'white',
                                border: 'none',
                                borderRadius: '0.5rem',
                                fontSize: '0.875rem',
                                fontWeight: '600',
                                cursor: (!canResend || resendLoading) ? 'not-allowed' : 'pointer',
                                transition: 'all 0.2s ease',
                                opacity: (!canResend || resendLoading) ? '0.6' : '1',
                                boxShadow: (!canResend || resendLoading) ? 'none' : '0 2px 4px rgba(59, 130, 246, 0.3)'
                            }}
                            onMouseEnter={(e) => {
                                if (canResend && !resendLoading) {
                                    e.target.style.backgroundColor = '#2563eb';
                                    e.target.style.transform = 'translateY(-1px)';
                                    e.target.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.4)';
                                }
                            }}
                            onMouseLeave={(e) => {
                                if (canResend && !resendLoading) {
                                    e.target.style.backgroundColor = '#3b82f6';
                                    e.target.style.transform = 'translateY(0)';
                                    e.target.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.3)';
                                }
                            }}
                        >
                            {resendLoading ? (
                                <FaSpinner className="animate-spin" />
                            ) : (
                                <FaRedo />
                            )}
                            {resendLoading ? 'Sending...' :
                             !canResend && timeLeft > 0 ? `Resend available in ${formatTime(timeLeft)}` :
                             !canResend && initialOtpFailed ? 'Retry Send OTP' :
                             canResend ? 'Resend OTP' :
                             'OTP Sent - Check Email'}
                        </button>

                        {/* Back to Login Button */}
                        <button
                            onClick={onBack}
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '0.5rem',
                                padding: '0.75rem 1.5rem',
                                backgroundColor: '#dc2626',
                                color: 'white',
                                border: '1px solid #dc2626',
                                borderRadius: '0.5rem',
                                fontSize: '0.875rem',
                                fontWeight: '500',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease',
                                boxShadow: '0 2px 4px rgba(220, 38, 38, 0.3)'
                            }}
                            onMouseEnter={(e) => {
                                e.target.style.backgroundColor = '#b91c1c';
                                e.target.style.transform = 'translateY(-1px)';
                                e.target.style.boxShadow = '0 4px 8px rgba(220, 38, 38, 0.4)';
                            }}
                            onMouseLeave={(e) => {
                                e.target.style.backgroundColor = '#dc2626';
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = '0 2px 4px rgba(220, 38, 38, 0.3)';
                            }}
                        >
                            <FaArrowLeft />
                            Back to Login
                        </button>
                    </div>
                </div>
            </div>

            <div className="login-right-panel"></div>
        </div>
    );
};

export default AdminOTPVerification;
