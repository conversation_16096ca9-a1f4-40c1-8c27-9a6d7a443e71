<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

$conn = getDBConnection();

try {
    if (!isset($_GET['challenge_id'])) {
        throw new Exception('Challenge ID is required');
    }

    $challenge_id = $_GET['challenge_id'];

    // Get all bets with user details and potential returns
    $stmt = $conn->prepare("
        SELECT 
            b.bet_id,
            b.challenge_id,
            b.user1_id,
            u1.username as user1_name,
            b.bet_choice_user1,
            b.amount_user1,
            b.odds_user1,
            b.potential_return_win_user1,
            b.potential_return_draw_user1,
            b.potential_return_loss_user1,
            b.user2_id,
            u2.username as user2_name,
            b.bet_choice_user2,
            b.amount_user2,
            b.odds_user2,
            b.potential_return_win_user2,
            b.potential_return_draw_user2,
            b.potential_return_loss_user2
        FROM bets b
        JOIN users u1 ON b.user1_id = u1.user_id
        JOIN users u2 ON b.user2_id = u2.user_id
        WHERE b.challenge_id = :challenge_id
        AND b.bet_status = 'joined'
    ");

    $stmt->execute([':challenge_id' => $challenge_id]);
    $bets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format bets for display
    $formattedBets = array_map(function($bet) {
        return [
            'bet_id' => $bet['bet_id'],
            'user1_name' => $bet['user1_name'],
            'bet_choice_user1' => $bet['bet_choice_user1'],
            'amount_user1' => $bet['amount_user1'],
            'potential_returns_user1' => [
                'win' => $bet['potential_return_win_user1'],
                'draw' => $bet['potential_return_draw_user1'],
                'loss' => $bet['potential_return_loss_user1']
            ],
            'user2_name' => $bet['user2_name'],
            'bet_choice_user2' => $bet['bet_choice_user2'],
            'amount_user2' => $bet['amount_user2'],
            'potential_returns_user2' => [
                'win' => $bet['potential_return_win_user2'],
                'draw' => $bet['potential_return_draw_user2'],
                'loss' => $bet['potential_return_loss_user2']
            ]
        ];
    }, $bets);

    echo json_encode([
        'success' => true,
        'bets' => $formattedBets
    ]);

} catch (Exception $e) {
    error_log("Error in get_challenge_bets.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}