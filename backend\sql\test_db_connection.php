<?php
require_once __DIR__ . '/../includes/db_connect.php';

echo "Testing database connection...\n";

$conn = getDBConnection();

if ($conn) {
    echo "✅ Database connection successful!\n";
    
    // Test a simple query
    try {
        $stmt = $conn->query("SELECT 1 as test");
        $result = $stmt->fetch();
        if ($result['test'] == 1) {
            echo "✅ Database query test successful!\n";
        }
    } catch (Exception $e) {
        echo "❌ Database query failed: " . $e->getMessage() . "\n";
    }
    
    // Check if currencies table exists
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'currencies'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Currencies table already exists\n";
        } else {
            echo "ℹ️ Currencies table does not exist yet\n";
        }
    } catch (Exception $e) {
        echo "❌ Error checking currencies table: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "❌ Database connection failed!\n";
    echo "Please check your database configuration in backend/includes/db_connect.php\n";
}
?>
