# FanBet247 VPS Deployment Guide

## Quick Fix for Current Issues

### 1. The Main Problem: Missing .htaccess and Incorrect Deployment

Your 404 errors are caused by:
- Missing `.htaccess` file on the VPS
- Frontend not properly deployed to root directory
- Backend API base URL configuration issues

### 2. Immediate Steps to Fix

#### Option A: Use the Deployment Scripts (Recommended)

1. **Make the bash script executable:**
   ```bash
   chmod +x deploy_to_vps.sh
   ```

2. **Test VPS connection:**
   ```bash
   ./deploy_to_vps.sh test
   ```

3. **Deploy everything:**
   ```bash
   ./deploy_to_vps.sh deploy
   ```

#### Option B: Manual Deployment

1. **Build the frontend first:**
   ```bash
   cd frontend
   npm run build
   cd ..
   ```

2. **Copy build files to root:**
   ```bash
   cp -r frontend/build/* .
   ```

3. **Upload to VPS using SCP:**
   ```bash
   # Upload frontend files
   scp -r index.html static manifest.json favicon.ico root@**************:/var/www/fanbet247.xyz/

   # Upload backend
   scp -r backend root@**************:/var/www/fanbet247.xyz/

   # Upload configuration files
   scp .htaccess debug.php vps_structure_checker.php root@**************:/var/www/fanbet247.xyz/
   ```

4. **Set permissions on VPS:**
   ```bash
   ssh root@************** "chown -R www-data:www-data /var/www/fanbet247.xyz"
   ssh root@************** "find /var/www/fanbet247.xyz -type d -exec chmod 755 {} \;"
   ssh root@************** "find /var/www/fanbet247.xyz -type f -exec chmod 644 {} \;"
   ssh root@************** "chmod 777 /var/www/fanbet247.xyz/uploads"
   ```

5. **Restart Apache:**
   ```bash
   ssh root@************** "systemctl restart apache2"
   ```

### 3. Verification Steps

After deployment, check these URLs:

1. **Structure Checker:** https://fanbet247.xyz/vps_structure_checker.php
2. **Debug Dashboard:** https://fanbet247.xyz/debug.php
3. **Main Site:** https://fanbet247.xyz
4. **API Test:** https://fanbet247.xyz/backend/handlers/homepage_data.php

### 4. Common Issues and Solutions

#### Issue: "Not Found" errors
**Solution:** Ensure `.htaccess` is in the root directory with proper rewrite rules.

#### Issue: API calls return "undefined"
**Solution:** Check that `backend/handlers/` directory exists and files are accessible.

#### Issue: Database connection fails
**Solution:** Verify database credentials in `backend/includes/db_connect.php`.

#### Issue: Permission denied
**Solution:** Set proper file permissions (755 for directories, 644 for files).

### 5. VPS Directory Structure Should Look Like:

```
/var/www/fanbet247.xyz/
├── index.html                 # React app entry point
├── .htaccess                  # Apache rewrite rules
├── static/                    # React build assets
│   ├── css/
│   └── js/
├── backend/                   # PHP backend
│   ├── handlers/              # API endpoints
│   ├── includes/              # Shared utilities
│   └── uploads/               # File uploads
├── uploads/                   # User uploads
├── debug.php                  # Debug tool
└── vps_structure_checker.php  # Structure checker
```

### 6. Troubleshooting Commands

```bash
# Check Apache error logs
ssh root@************** "tail -f /var/log/apache2/error.log"

# Check file permissions
ssh root@************** "ls -la /var/www/fanbet247.xyz/"

# Test Apache configuration
ssh root@************** "apache2ctl configtest"

# Check if mod_rewrite is enabled
ssh root@************** "apache2ctl -M | grep rewrite"

# Restart services
ssh root@************** "systemctl restart apache2"
ssh root@************** "systemctl restart mysql"
```

### 7. Quick Test Commands

```bash
# Test API endpoints directly
curl https://fanbet247.xyz/backend/handlers/homepage_data.php
curl https://fanbet247.xyz/debug.php?action=ping

# Test main site
curl -I https://fanbet247.xyz
```

### 8. If You Need to Enable mod_rewrite

```bash
ssh root@************** "a2enmod rewrite && systemctl restart apache2"
```

### 9. Database Configuration

Make sure your `backend/includes/db_connect.php` has the correct VPS database credentials:

```php
$host = 'localhost';
$dbname = 'fanbet247';
$username = 'your_db_user';
$password = 'your_db_password';
```

### 10. Next Steps After Deployment

1. Visit the structure checker to verify everything is working
2. Test the main application functionality
3. Check admin login and user registration
4. Verify API endpoints are responding correctly
5. Test the homepage data loading

## Contact Information

If you encounter issues:
- Check the debug dashboard first
- Review Apache error logs
- Verify file permissions and ownership
- Ensure database connection is working
