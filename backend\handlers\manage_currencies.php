<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

// Simple admin authentication check
function getAdminId() {
    $headers = getallheaders();
    if (isset($headers['X-Admin-ID'])) {
        return (int)$headers['X-Admin-ID'];
    }
    
    $input = json_decode(file_get_contents("php://input"), true);
    if (isset($input['admin_id'])) {
        return (int)$input['admin_id'];
    }
    
    return 1; // Default admin ID for now
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents("php://input"), true);

    if ($method === 'GET') {
        // Fetch all currencies with their exchange rates
        $query = "
            SELECT
                c.id,
                c.currency_code,
                c.currency_name,
                c.currency_symbol,
                c.is_active,
                c.created_at,
                c.updated_at,
                er.rate_to_fancoin,
                er.updated_at as rate_updated_at,
                er.notes as rate_notes
            FROM currencies c
            LEFT JOIN exchange_rates er ON c.id = er.currency_id
            ORDER BY c.currency_code ASC
        ";

        $stmt = $conn->prepare($query);
        $stmt->execute();
        $currencies = $stmt->fetchAll(PDO::FETCH_ASSOC);

        jsonResponse(200, "Currencies fetched successfully", $currencies);

    } elseif ($method === 'POST') {
        $action = $input['action'] ?? '';
        $adminId = getAdminId();
        
        switch ($action) {
            case 'create':
                // Create new currency
                $currencyCode = strtoupper(trim($input['currency_code'] ?? ''));
                $currencyName = trim($input['currency_name'] ?? '');
                $currencySymbol = trim($input['currency_symbol'] ?? '');
                $isActive = isset($input['is_active']) ? (bool)$input['is_active'] : true;
                
                if (empty($currencyCode) || empty($currencyName) || empty($currencySymbol)) {
                    jsonResponse(400, "Currency code, name, and symbol are required");
                }
                
                // Check if currency code already exists
                $checkStmt = $conn->prepare("SELECT id FROM currencies WHERE currency_code = ?");
                $checkStmt->execute([$currencyCode]);
                if ($checkStmt->fetch()) {
                    jsonResponse(400, "Currency code already exists");
                }
                
                // Insert new currency
                $insertStmt = $conn->prepare("
                    INSERT INTO currencies (currency_code, currency_name, currency_symbol, is_active, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, NOW(), NOW())
                ");
                
                if ($insertStmt->execute([$currencyCode, $currencyName, $currencySymbol, $isActive])) {
                    $newCurrencyId = $conn->lastInsertId();
                    jsonResponse(201, "Currency created successfully", [
                        'currency_id' => $newCurrencyId,
                        'currency_code' => $currencyCode,
                        'currency_name' => $currencyName,
                        'currency_symbol' => $currencySymbol,
                        'is_active' => $isActive
                    ]);
                } else {
                    jsonResponse(500, "Failed to create currency");
                }
                break;
                
            case 'toggle':
                // Toggle currency active status
                $currencyId = (int)($input['currency_id'] ?? 0);
                
                if (!$currencyId) {
                    jsonResponse(400, "Currency ID is required");
                }
                
                // Get current status
                $statusStmt = $conn->prepare("SELECT is_active FROM currencies WHERE id = ?");
                $statusStmt->execute([$currencyId]);
                $current = $statusStmt->fetch();
                
                if (!$current) {
                    jsonResponse(404, "Currency not found");
                }
                
                $newStatus = !$current['is_active'];
                
                // Update status
                $updateStmt = $conn->prepare("UPDATE currencies SET is_active = ?, updated_at = NOW() WHERE id = ?");
                
                if ($updateStmt->execute([$newStatus, $currencyId])) {
                    jsonResponse(200, "Currency status updated successfully", [
                        'currency_id' => $currencyId,
                        'is_active' => $newStatus
                    ]);
                } else {
                    jsonResponse(500, "Failed to update currency status");
                }
                break;
                
            case 'update':
                // Update currency details
                $currencyId = (int)($input['currency_id'] ?? 0);
                $currencyName = trim($input['currency_name'] ?? '');
                $currencySymbol = trim($input['currency_symbol'] ?? '');
                $isActive = isset($input['is_active']) ? (bool)$input['is_active'] : null;

                if (!$currencyId) {
                    jsonResponse(400, "Currency ID is required");
                }

                // Build update query dynamically
                $updateFields = [];
                $updateValues = [];

                if (!empty($currencyName)) {
                    $updateFields[] = "currency_name = ?";
                    $updateValues[] = $currencyName;
                }

                if (!empty($currencySymbol)) {
                    $updateFields[] = "currency_symbol = ?";
                    $updateValues[] = $currencySymbol;
                }

                if ($isActive !== null) {
                    $updateFields[] = "is_active = ?";
                    $updateValues[] = $isActive;
                }

                if (empty($updateFields)) {
                    jsonResponse(400, "No fields to update");
                }

                $updateFields[] = "updated_at = NOW()";
                $updateValues[] = $currencyId;

                $updateQuery = "UPDATE currencies SET " . implode(", ", $updateFields) . " WHERE id = ?";
                $updateStmt = $conn->prepare($updateQuery);

                if ($updateStmt->execute($updateValues)) {
                    jsonResponse(200, "Currency updated successfully", [
                        'currency_id' => $currencyId
                    ]);
                } else {
                    jsonResponse(500, "Failed to update currency");
                }
                break;

            case 'delete':
                // Delete currency and associated exchange rates
                $currencyId = (int)($input['currency_id'] ?? 0);

                if (!$currencyId) {
                    jsonResponse(400, "Currency ID is required");
                }

                // Check if currency exists
                $checkStmt = $conn->prepare("SELECT currency_code FROM currencies WHERE id = ?");
                $checkStmt->execute([$currencyId]);
                $currency = $checkStmt->fetch();

                if (!$currency) {
                    jsonResponse(404, "Currency not found");
                }

                // Start transaction
                $conn->beginTransaction();

                try {
                    // Delete associated exchange rates first
                    $deleteRatesStmt = $conn->prepare("DELETE FROM exchange_rates WHERE currency_id = ?");
                    $deleteRatesStmt->execute([$currencyId]);

                    // Delete the currency
                    $deleteCurrencyStmt = $conn->prepare("DELETE FROM currencies WHERE id = ?");
                    $deleteCurrencyStmt->execute([$currencyId]);

                    // Commit transaction
                    $conn->commit();

                    jsonResponse(200, "Currency deleted successfully", [
                        'currency_id' => $currencyId,
                        'currency_code' => $currency['currency_code']
                    ]);
                } catch (Exception $e) {
                    // Rollback transaction on error
                    $conn->rollback();
                    throw $e;
                }
                break;
                
            default:
                jsonResponse(400, "Invalid action specified");
        }
    } else {
        jsonResponse(405, "Method not allowed");
    }
    
} catch (PDOException $e) {
    error_log("Database error in manage_currencies.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    error_log("General error in manage_currencies.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred while managing currencies");
}
?>
