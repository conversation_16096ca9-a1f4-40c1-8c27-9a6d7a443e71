<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// --- Database Connection Check ---
$db_status = 'Disconnected';
$db_error = '';

// Include the database connection file
require_once 'backend/includes/db_connect.php';

// Check the connection
if (checkDatabaseConnection()) {
    $db_status = 'Connected';
} else {
    // Attempt to get more specific error information
    try {
        initializeDatabase();
    } catch (PDOException $e) {
        $db_error = $e->getMessage();
    }
}

// --- Backend Status Check ---
// For now, we'll just check if the backend directory exists.
// This can be expanded with more specific checks later.
$backend_status = 'Not Found';
if (is_dir('backend')) {
    $backend_status = 'OK';
}

// --- PHP Version and Configuration ---
$php_version = phpversion();
$memory_limit = ini_get('memory_limit');
$max_execution_time = ini_get('max_execution_time');

// --- Database Table Listing ---
$tables = [];
if ($db_status === 'Connected') {
    try {
        $stmt = getDBConnection()->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        $db_error .= " | Could not fetch tables: " . $e->getMessage();
    }
}

// --- API Endpoint for Frontend ---
if (isset($_GET['action']) && $_GET['action'] === 'ping') {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *'); // Allow requests from any origin
    echo json_encode(['status' => 'ok', 'message' => 'Backend is reachable']);
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Status</title>
    <style>
        body { font-family: sans-serif; background-color: #f4f4f4; color: #333; margin: 20px; }
        .container { max-width: 800px; margin: auto; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        h1 { color: #5a67d8; }
        .status-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .status-table th, .status-table td { padding: 12px; border: 1px solid #ddd; text-align: left; }
        .status-table th { background-color: #f2f2f2; }
        .status-ok { color: green; font-weight: bold; }
        .status-error { color: red; font-weight: bold; }
        .error-details { font-size: 0.9em; color: #777; }
    </style>
</head>
<body>
    <div class="container">
        <h1>System Health Check</h1>
        
        <table class="status-table">
            <tr>
                <th>Check</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>Database Connection</td>
                <td class="<?php echo ($db_status === 'Connected') ? 'status-ok' : 'status-error'; ?>">
                    <?php echo $db_status; ?>
                    <?php if ($db_error): ?>
                        <div class="error-details"><?php echo htmlspecialchars($db_error); ?></div>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td>Backend Sanity Check</td>
                <td class="<?php echo ($backend_status === 'OK') ? 'status-ok' : 'status-error'; ?>"><?php echo $backend_status; ?></td>
            </tr>
            <tr>
                <td>PHP Version</td>
                <td><?php echo $php_version; ?></td>
            </tr>
            <tr>
                <td>Memory Limit</td>
                <td><?php echo $memory_limit; ?></td>
            </tr>
            <tr>
                <td>Max Execution Time</td>
                <td><?php echo $max_execution_time; ?> seconds</td>
            </tr>
        </table>

        <h2>Database Tables</h2>
        <?php if (!empty($tables)): ?>
            <ul>
                <?php foreach ($tables as $table): ?>
                    <li><?php echo htmlspecialchars($table); ?></li>
                <?php endforeach; ?>
            </ul>
        <?php else: ?>
            <p>No tables found or database not connected.</p>
        <?php endif; ?>

        <h2>Frontend-Backend Connectivity Test</h2>
        <p>To test the connection from your React app, you can use the following JavaScript code:</p>
        <pre><code>
fetch('https://fanbet247.xyz/debug.php?action=ping')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
        </code></pre>
        <p>This should log <code>{status: "ok", message: "Backend is reachable"}</code> to your browser's console.</p>

    </div>
</body>
</html>