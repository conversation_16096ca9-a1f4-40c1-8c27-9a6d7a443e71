<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// --- Comprehensive System Debug Tool ---
function checkServerConfiguration() {
    return [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
        'http_host' => $_SERVER['HTTP_HOST'] ?? 'Unknown',
        'https' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off',
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'extensions' => [
            'pdo' => extension_loaded('pdo'),
            'pdo_mysql' => extension_loaded('pdo_mysql'),
            'curl' => extension_loaded('curl'),
            'json' => extension_loaded('json'),
            'mbstring' => extension_loaded('mbstring'),
            'openssl' => extension_loaded('openssl')
        ]
    ];
}

function checkFileSystemStructure() {
    $checks = [
        'backend_dir' => is_dir('backend'),
        'backend_handlers' => is_dir('backend/handlers'),
        'backend_includes' => is_dir('backend/includes'),
        'frontend_build' => is_dir('frontend/build'),
        'uploads_dir' => is_dir('uploads'),
        'htaccess_root' => file_exists('.htaccess'),
        'htaccess_backend' => file_exists('backend/.htaccess'),
        'index_html' => file_exists('index.html'),
        'frontend_index' => file_exists('frontend/build/index.html')
    ];

    return $checks;
}

function checkDatabaseConnection() {
    try {
        require_once 'backend/includes/db_connect.php';
        if (function_exists('checkDatabaseConnection') && checkDatabaseConnection()) {
            return ['status' => 'Connected', 'error' => null];
        } else {
            return ['status' => 'Failed', 'error' => 'Connection function failed'];
        }
    } catch (Exception $e) {
        return ['status' => 'Error', 'error' => $e->getMessage()];
    }
}

function checkAdminAuthentication() {
    try {
        require_once 'backend/includes/db_connect.php';
        $conn = getDBConnection();

        // Check if admins table exists and has data
        $stmt = $conn->query("SHOW TABLES LIKE 'admins'");
        $adminTableExists = $stmt->rowCount() > 0;

        $adminCount = 0;
        $adminUsers = [];

        if ($adminTableExists) {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM admins");
            $adminCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            $stmt = $conn->query("SELECT admin_id, username, email, role, auth_method, two_factor_enabled, account_locked_until FROM admins LIMIT 5");
            $adminUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        // Check users table for admin role
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $userAdminCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        return [
            'admin_table_exists' => $adminTableExists,
            'admin_count' => $adminCount,
            'user_admin_count' => $userAdminCount,
            'admin_users' => $adminUsers
        ];
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

function checkAPIEndpoints() {
    $endpoints = [
        'homepage_data.php' => 'backend/handlers/homepage_data.php',
        'get_site_config.php' => 'backend/handlers/get_site_config.php',
        'get_currencies.php' => 'backend/handlers/get_currencies.php',
        'user_data.php' => 'backend/handlers/user_data.php',
        'admin_login_handler.php' => 'backend/handlers/admin_login_handler.php'
    ];

    $results = [];
    foreach ($endpoints as $name => $path) {
        $results[$name] = [
            'exists' => file_exists($path),
            'readable' => file_exists($path) && is_readable($path),
            'size' => file_exists($path) ? filesize($path) : 0
        ];
    }

    return $results;
}

function testAPIConnectivity() {
    $tests = [];
    $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' ? 'https' : 'http') .
               '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']);

    $endpoints = [
        'homepage_data' => '/backend/handlers/homepage_data.php',
        'site_config' => '/backend/handlers/get_site_config.php',
        'ping_test' => '/debug.php?action=ping'
    ];

    foreach ($endpoints as $name => $endpoint) {
        $url = $baseUrl . $endpoint;
        $tests[$name] = [
            'url' => $url,
            'status' => 'Not tested (requires curl)',
            'accessible' => file_exists('.' . $endpoint)
        ];
    }

    return $tests;
}

// --- Database Connection Check ---
$db_check = checkDatabaseConnection();
$db_status = $db_check['status'];
$db_error = $db_check['error'];

// --- Get all system information ---
$server_config = checkServerConfiguration();
$filesystem_check = checkFileSystemStructure();
$admin_check = checkAdminAuthentication();
$api_endpoints = checkAPIEndpoints();
$api_connectivity = testAPIConnectivity();

// --- Database Table Listing ---
$tables = [];
if ($db_status === 'Connected') {
    try {
        require_once 'backend/includes/db_connect.php';
        $conn = getDBConnection();
        $stmt = $conn->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        $db_error .= " | Could not fetch tables: " . $e->getMessage();
    }
}

// --- API Endpoint for Frontend ---
if (isset($_GET['action']) && $_GET['action'] === 'ping') {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    echo json_encode(['status' => 'ok', 'message' => 'Backend is reachable', 'timestamp' => date('Y-m-d H:i:s')]);
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FanBet247 Comprehensive Debug Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2, h3 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status.ok {
            background-color: #d4edda;
            color: #155724;
            border-left: 5px solid #28a745;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 5px solid #dc3545;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 5px solid #ffc107;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .info-card {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .info-card h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.3em;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .code {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .btn {
            background: linear-gradient(145deg, #3498db, #2980b9);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }
        .btn.danger {
            background: linear-gradient(145deg, #e74c3c, #c0392b);
        }
        .btn.success {
            background: linear-gradient(145deg, #27ae60, #229954);
        }
        .icon {
            font-size: 1.2em;
            margin-right: 8px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .metric:last-child {
            border-bottom: none;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .badge.success {
            background-color: #28a745;
            color: white;
        }
        .badge.danger {
            background-color: #dc3545;
            color: white;
        }
        .badge.warning {
            background-color: #ffc107;
            color: #212529;
        }
        .collapsible {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 FanBet247 Comprehensive Debug Dashboard</h1>
        <p style="text-align: center; color: #7f8c8d;"><strong>Generated:</strong> <?php echo date('Y-m-d H:i:s'); ?> | <strong>Server:</strong> <?php echo $server_config['server_software']; ?></p>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="window.location.reload()">🔄 Refresh Status</button>
            <button class="btn success" onclick="testAllConnections()">🧪 Test All Connections</button>
            <button class="btn danger" onclick="testHomepageAPI()">🏠 Test Homepage API</button>
        </div>

        <!-- Critical Status Overview -->
        <h2>🚨 Critical System Status</h2>
        <div class="status <?php echo ($db_status === 'Connected') ? 'ok' : 'error'; ?>">
            <span class="icon"><?php echo ($db_status === 'Connected') ? '✅' : '❌'; ?></span>
            <div>
                <strong>Database Connection:</strong> <?php echo $db_status; ?>
                <?php if ($db_error): ?>
                    <br><small>Error: <?php echo htmlspecialchars($db_error); ?></small>
                <?php endif; ?>
            </div>
        </div>

        <div class="status <?php echo ($filesystem_check['backend_dir'] && $filesystem_check['backend_handlers']) ? 'ok' : 'error'; ?>">
            <span class="icon"><?php echo ($filesystem_check['backend_dir'] && $filesystem_check['backend_handlers']) ? '✅' : '❌'; ?></span>
            <strong>Backend Structure:</strong> <?php echo ($filesystem_check['backend_dir'] && $filesystem_check['backend_handlers']) ? 'OK' : 'Missing Files'; ?>
        </div>

        <div class="status <?php echo (!empty($admin_check['admin_count']) || !empty($admin_check['user_admin_count'])) ? 'ok' : 'warning'; ?>">
            <span class="icon"><?php echo (!empty($admin_check['admin_count']) || !empty($admin_check['user_admin_count'])) ? '✅' : '⚠️'; ?></span>
            <strong>Admin Access:</strong> <?php echo (!empty($admin_check['admin_count']) || !empty($admin_check['user_admin_count'])) ? 'Configured' : 'No Admins Found'; ?>
        </div>

        <div class="status <?php echo $filesystem_check['htaccess_root'] ? 'ok' : 'error'; ?>">
            <span class="icon"><?php echo $filesystem_check['htaccess_root'] ? '✅' : '❌'; ?></span>
            <strong>URL Routing (.htaccess):</strong> <?php echo $filesystem_check['htaccess_root'] ? 'Configured' : 'MISSING - This causes 404 errors!'; ?>
        </div>

        <!-- System Information Grid -->
        <h2>📊 System Information</h2>
        <div class="info-grid">
            <!-- Server Configuration -->
            <div class="info-card">
                <h3><span class="icon">🖥️</span>Server Configuration</h3>
                <div class="metric">
                    <span>PHP Version:</span>
                    <span class="code"><?php echo $server_config['php_version']; ?></span>
                </div>
                <div class="metric">
                    <span>Server Software:</span>
                    <span class="code"><?php echo $server_config['server_software']; ?></span>
                </div>
                <div class="metric">
                    <span>HTTPS:</span>
                    <span class="badge <?php echo $server_config['https'] ? 'success' : 'warning'; ?>">
                        <?php echo $server_config['https'] ? 'Enabled' : 'Disabled'; ?>
                    </span>
                </div>
                <div class="metric">
                    <span>Document Root:</span>
                    <span class="code" style="font-size: 0.7em;"><?php echo $server_config['document_root']; ?></span>
                </div>
                <div class="metric">
                    <span>Current URL:</span>
                    <span class="code" style="font-size: 0.7em;"><?php echo $server_config['http_host'] . $server_config['request_uri']; ?></span>
                </div>
            </div>

            <!-- PHP Extensions -->
            <div class="info-card">
                <h3><span class="icon">🔧</span>PHP Extensions</h3>
                <?php foreach ($server_config['extensions'] as $ext => $loaded): ?>
                <div class="metric">
                    <span><?php echo strtoupper($ext); ?>:</span>
                    <span class="badge <?php echo $loaded ? 'success' : 'danger'; ?>">
                        <?php echo $loaded ? 'Loaded' : 'Missing'; ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- File System Check -->
            <div class="info-card">
                <h3><span class="icon">📁</span>File System Structure</h3>
                <?php foreach ($filesystem_check as $check => $status): ?>
                <div class="metric">
                    <span><?php echo ucwords(str_replace('_', ' ', $check)); ?>:</span>
                    <span class="badge <?php echo $status ? 'success' : 'danger'; ?>">
                        <?php echo $status ? 'OK' : 'Missing'; ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Admin Authentication -->
            <div class="info-card">
                <h3><span class="icon">👤</span>Admin Authentication</h3>
                <?php if (isset($admin_check['error'])): ?>
                    <div class="status error">Error: <?php echo htmlspecialchars($admin_check['error']); ?></div>
                <?php else: ?>
                    <div class="metric">
                        <span>Admin Table:</span>
                        <span class="badge <?php echo $admin_check['admin_table_exists'] ? 'success' : 'danger'; ?>">
                            <?php echo $admin_check['admin_table_exists'] ? 'Exists' : 'Missing'; ?>
                        </span>
                    </div>
                    <div class="metric">
                        <span>Admin Users:</span>
                        <span class="code"><?php echo $admin_check['admin_count']; ?></span>
                    </div>
                    <div class="metric">
                        <span>User Admins:</span>
                        <span class="code"><?php echo $admin_check['user_admin_count']; ?></span>
                    </div>
                    <?php if (!empty($admin_check['admin_users'])): ?>
                        <h4>Admin Users:</h4>
                        <div class="collapsible">
                            <?php foreach ($admin_check['admin_users'] as $admin): ?>
                                <div style="margin: 5px 0; padding: 5px; background: white; border-radius: 4px;">
                                    <strong><?php echo htmlspecialchars($admin['username']); ?></strong>
                                    (<?php echo htmlspecialchars($admin['role']); ?>)
                                    <?php if ($admin['two_factor_enabled']): ?>
                                        <span class="badge success">2FA</span>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- API Endpoints Check -->
        <h2>🔗 API Endpoints Status</h2>
        <div class="info-grid">
            <div class="info-card">
                <h3><span class="icon">📡</span>Backend API Files</h3>
                <?php foreach ($api_endpoints as $endpoint => $info): ?>
                <div class="metric">
                    <span><?php echo $endpoint; ?>:</span>
                    <span class="badge <?php echo $info['exists'] ? 'success' : 'danger'; ?>">
                        <?php echo $info['exists'] ? 'Found' : 'Missing'; ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>

            <div class="info-card">
                <h3><span class="icon">🌐</span>API Connectivity Tests</h3>
                <?php foreach ($api_connectivity as $test => $info): ?>
                <div class="metric">
                    <span><?php echo ucwords(str_replace('_', ' ', $test)); ?>:</span>
                    <span class="badge <?php echo $info['accessible'] ? 'success' : 'danger'; ?>">
                        <?php echo $info['accessible'] ? 'Accessible' : 'Not Found'; ?>
                    </span>
                </div>
                <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
                    URL: <span class="code" style="font-size: 0.7em;"><?php echo $info['url']; ?></span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Database Tables -->
        <h2>🗄️ Database Information</h2>
        <div class="info-card">
            <h3><span class="icon">📊</span>Database Tables (<?php echo count($tables); ?> found)</h3>
            <?php if (!empty($tables)): ?>
                <div class="collapsible">
                    <?php foreach ($tables as $table): ?>
                        <span class="code" style="margin: 2px; display: inline-block;"><?php echo htmlspecialchars($table); ?></span>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="status error">No tables found or database connection failed.</div>
            <?php endif; ?>
        </div>

        <!-- Live Testing Section -->
        <h2>🧪 Live API Testing</h2>
        <div class="info-card">
            <h3><span class="icon">🔬</span>Real-time API Tests</h3>
            <p>Click the buttons below to test specific API endpoints that are causing issues:</p>

            <div style="margin: 15px 0;">
                <button class="btn" onclick="testEndpoint('backend/handlers/homepage_data.php', 'homepage-test')">Test Homepage Data</button>
                <button class="btn" onclick="testEndpoint('backend/handlers/get_currencies.php', 'currency-test')">Test Currency Data</button>
                <button class="btn" onclick="testEndpoint('backend/handlers/get_site_config.php', 'config-test')">Test Site Config</button>
                <button class="btn danger" onclick="testEndpoint('debug.php?action=ping', 'ping-test')">Test Backend Ping</button>
            </div>

            <div id="homepage-test" class="test-result" style="display: none;"></div>
            <div id="currency-test" class="test-result" style="display: none;"></div>
            <div id="config-test" class="test-result" style="display: none;"></div>
            <div id="ping-test" class="test-result" style="display: none;"></div>
        </div>

        <!-- Troubleshooting Guide -->
        <h2>🛠️ Common Issues & Solutions</h2>
        <div class="info-grid">
            <div class="info-card">
                <h3><span class="icon">❌</span>404 Not Found Errors</h3>
                <p><strong>Symptoms:</strong> Pages work locally but show "Not Found" on server</p>
                <p><strong>Causes:</strong></p>
                <ul>
                    <li>Missing .htaccess file in root directory</li>
                    <li>Apache mod_rewrite not enabled</li>
                    <li>Incorrect file permissions</li>
                </ul>
                <p><strong>Solutions:</strong></p>
                <ol>
                    <li>Create .htaccess file with proper rewrite rules</li>
                    <li>Ensure mod_rewrite is enabled on server</li>
                    <li>Check file permissions (755 for directories, 644 for files)</li>
                </ol>
            </div>

            <div class="info-card">
                <h3><span class="icon">🔗</span>API Connection Issues</h3>
                <p><strong>Symptoms:</strong> Frontend can't reach backend APIs</p>
                <p><strong>Common Causes:</strong></p>
                <ul>
                    <li>Incorrect API base URL configuration</li>
                    <li>CORS headers not properly set</li>
                    <li>Backend files not deployed</li>
                </ul>
                <p><strong>Check:</strong></p>
                <ul>
                    <li>Verify axiosConfig.js base URL</li>
                    <li>Ensure backend handlers have CORS headers</li>
                    <li>Test API endpoints directly in browser</li>
                </ul>
            </div>

            <div class="info-card">
                <h3><span class="icon">💾</span>Database Connection Issues</h3>
                <p><strong>Status:</strong> <span class="badge <?php echo ($db_status === 'Connected') ? 'success' : 'danger'; ?>"><?php echo $db_status; ?></span></p>
                <?php if ($db_status !== 'Connected'): ?>
                <p><strong>Troubleshooting:</strong></p>
                <ul>
                    <li>Check database credentials in db_connect.php</li>
                    <li>Verify MySQL service is running</li>
                    <li>Ensure database 'fanbet247' exists</li>
                    <li>Check user permissions</li>
                </ul>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <h2>⚡ Quick Actions</h2>
        <div class="info-card">
            <h3><span class="icon">🚀</span>Deployment Checklist</h3>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="checkDeployment()">🔍 Check Deployment Status</button>
                <button class="btn success" onclick="generateHtaccess()">📝 Generate .htaccess</button>
                <button class="btn danger" onclick="testAllAPIs()">🧪 Test All APIs</button>
            </div>
            <div id="deployment-check" class="test-result" style="display: none;"></div>
            <div id="htaccess-generator" class="test-result" style="display: none;"></div>
            <div id="api-test-results" class="test-result" style="display: none;"></div>
        </div>

    </div>

    <script>
        function testEndpoint(url, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="status warning">🔄 Testing ' + url + '...</div>';

            fetch(url)
                .then(response => {
                    const statusClass = response.ok ? 'ok' : 'error';
                    const statusIcon = response.ok ? '✅' : '❌';

                    return response.text().then(text => {
                        let content = text;
                        try {
                            const json = JSON.parse(text);
                            content = JSON.stringify(json, null, 2);
                        } catch (e) {
                            // Not JSON, keep as text
                        }

                        resultDiv.innerHTML = `
                            <div class="status ${statusClass}">
                                ${statusIcon} ${response.status} ${response.statusText}
                            </div>
                            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto; font-size: 0.8em;">${content.substring(0, 1000)}${content.length > 1000 ? '...' : ''}</pre>
                        `;
                    });
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="status error">❌ Error: ${error.message}</div>`;
                });
        }

        function testAllConnections() {
            testEndpoint('debug.php?action=ping', 'ping-test');
            testEndpoint('backend/handlers/homepage_data.php', 'homepage-test');
            testEndpoint('backend/handlers/get_currencies.php', 'currency-test');
        }

        function testHomepageAPI() {
            testEndpoint('backend/handlers/homepage_data.php?section=leagues&limit=3', 'homepage-test');
        }

        function checkDeployment() {
            const resultDiv = document.getElementById('deployment-check');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="status warning">🔄 Checking deployment...</div>';

            const checks = [
                { name: 'Frontend Build', check: () => fetch('index.html').then(r => r.ok) },
                { name: 'Backend Handlers', check: () => fetch('backend/handlers/homepage_data.php').then(r => r.status !== 404) },
                { name: 'Database Connection', check: () => fetch('debug.php?action=ping').then(r => r.ok) }
            ];

            Promise.allSettled(checks.map(c => c.check())).then(results => {
                let html = '<h4>Deployment Status:</h4>';
                results.forEach((result, index) => {
                    const status = result.status === 'fulfilled' && result.value ? 'success' : 'danger';
                    const icon = result.status === 'fulfilled' && result.value ? '✅' : '❌';
                    html += `<div class="metric"><span>${checks[index].name}:</span><span class="badge ${status}">${icon}</span></div>`;
                });
                resultDiv.innerHTML = html;
            });
        }

        function generateHtaccess() {
            const resultDiv = document.getElementById('htaccess-generator');
            resultDiv.style.display = 'block';

            const htaccessContent = `RewriteEngine On

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security headers
Header always set X-Frame-Options DENY
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"

# Handle React Router (SPA routing)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/backend/
RewriteRule ^(.*)$ index.html [L]

# Backend API routing (optional)
RewriteCond %{REQUEST_URI} ^/api/
RewriteRule ^api/(.*)$ backend/handlers/$1 [L]`;

            resultDiv.innerHTML = `
                <h4>Recommended .htaccess content:</h4>
                <pre style="background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 4px; font-size: 0.9em;">${htaccessContent}</pre>
                <p><strong>Instructions:</strong> Copy the above content and save it as <code>.htaccess</code> in your root directory.</p>
            `;
        }

        function testAllAPIs() {
            const resultDiv = document.getElementById('api-test-results');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="status warning">🔄 Testing all APIs...</div>';

            const apis = [
                'backend/handlers/homepage_data.php',
                'backend/handlers/get_currencies.php',
                'backend/handlers/get_site_config.php',
                'backend/handlers/user_data.php',
                'debug.php?action=ping'
            ];

            let results = [];
            let completed = 0;

            apis.forEach(api => {
                fetch(api)
                    .then(response => {
                        results.push({ api, status: response.status, ok: response.ok });
                    })
                    .catch(error => {
                        results.push({ api, status: 'Error', ok: false, error: error.message });
                    })
                    .finally(() => {
                        completed++;
                        if (completed === apis.length) {
                            let html = '<h4>API Test Results:</h4>';
                            results.forEach(result => {
                                const statusClass = result.ok ? 'success' : 'danger';
                                const icon = result.ok ? '✅' : '❌';
                                html += `<div class="metric">
                                    <span>${result.api}:</span>
                                    <span class="badge ${statusClass}">${icon} ${result.status}</span>
                                </div>`;
                            });
                            resultDiv.innerHTML = html;
                        }
                    });
            });
        }
    </script>
</body>
</html>