<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Validate input
    if (!isset($_GET['user_id'])) {
        throw new Exception('User ID is required');
    }

    $userId = $_GET['user_id'];

    // Get credit requests with payment method details
    $stmt = $conn->prepare("
        SELECT 
            cr.*,
            pm.name as payment_method_name,
            pm.type as payment_method_type,
            pm.fields as payment_method_fields
        FROM credit_requests cr
        JOIN payment_methods pm ON cr.payment_method_id = pm.id
        WHERE cr.user_id = :user_id
        ORDER BY cr.created_at DESC
    ");
    
    $stmt->bindParam(':user_id', $userId);
    $stmt->execute();
    
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Process the payment method fields
    foreach ($requests as &$request) {
        if ($request['payment_method_fields']) {
            $request['payment_method'] = [
                'id' => $request['payment_method_id'],
                'name' => $request['payment_method_name'],
                'type' => $request['payment_method_type'],
                'fields' => json_decode($request['payment_method_fields'], true)
            ];
        }

        // Remove raw fields from response
        unset($request['payment_method_name']);
        unset($request['payment_method_type']);
        unset($request['payment_method_fields']);
    }

    echo json_encode([
        'success' => true,
        'requests' => $requests
    ]);

} catch (Exception $e) {
    error_log("Error in get_credit_requests.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 