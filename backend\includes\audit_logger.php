<?php
/**
 * Audit Logger for Admin Authentication
 * Comprehensive logging system for security events
 */

class AdminAuditLogger {
    private $conn;
    private $logLevels = [
        'INFO' => 1,
        'WARNING' => 2,
        'ERROR' => 3,
        'CRITICAL' => 4
    ];

    public function __construct($connection) {
        $this->conn = $connection;
    }

    /**
     * Log authentication event
     */
    public function logAuthEvent($adminId, $authType, $action, $details = [], $level = 'INFO') {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO admin_auth_logs (
                    admin_id, 
                    auth_type, 
                    action, 
                    details, 
                    ip_address, 
                    user_agent,
                    log_level,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $adminId,
                $authType,
                $action,
                json_encode($details),
                $this->getClientIP(),
                $this->getUserAgent(),
                $level
            ]);

            // Log critical events to system log as well
            if ($level === 'CRITICAL' || $level === 'ERROR') {
                $this->logToSystemLog($adminId, $authType, $action, $details, $level);
            }

        } catch (PDOException $e) {
            error_log("Failed to log auth event: " . $e->getMessage());
            // Always log critical events to system log as fallback
            $this->logToSystemLog($adminId, $authType, $action, $details, $level);
        }
    }

    /**
     * Log security incident
     */
    public function logSecurityIncident($type, $description, $adminId = null, $severity = 'WARNING') {
        $details = [
            'incident_type' => $type,
            'description' => $description,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip_address' => $this->getClientIP(),
            'user_agent' => $this->getUserAgent()
        ];

        $this->logAuthEvent($adminId, 'security', 'incident', $details, $severity);

        // Send alert for critical incidents
        if ($severity === 'CRITICAL') {
            $this->sendSecurityAlert($type, $description, $details);
        }
    }

    /**
     * Log login attempt
     */
    public function logLoginAttempt($adminId, $success, $authMethod = 'password', $additionalInfo = []) {
        $action = $success ? 'login_success' : 'login_failed';
        $level = $success ? 'INFO' : 'WARNING';
        
        $details = array_merge([
            'auth_method' => $authMethod,
            'success' => $success,
            'timestamp' => date('Y-m-d H:i:s')
        ], $additionalInfo);

        $this->logAuthEvent($adminId, $authMethod, $action, $details, $level);
    }

    /**
     * Log OTP events
     */
    public function logOTPEvent($adminId, $action, $success = true, $additionalInfo = []) {
        $level = $success ? 'INFO' : 'WARNING';
        
        $details = array_merge([
            'success' => $success,
            'timestamp' => date('Y-m-d H:i:s')
        ], $additionalInfo);

        $this->logAuthEvent($adminId, 'otp', $action, $details, $level);
    }

    /**
     * Log 2FA events
     */
    public function log2FAEvent($adminId, $action, $success = true, $additionalInfo = []) {
        $level = $success ? 'INFO' : 'WARNING';
        
        $details = array_merge([
            'success' => $success,
            'timestamp' => date('Y-m-d H:i:s')
        ], $additionalInfo);

        $this->logAuthEvent($adminId, '2fa', $action, $details, $level);
    }

    /**
     * Log account lockout
     */
    public function logAccountLockout($adminId, $reason, $lockoutDuration) {
        $details = [
            'reason' => $reason,
            'lockout_duration' => $lockoutDuration,
            'locked_until' => date('Y-m-d H:i:s', time() + $lockoutDuration),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->logAuthEvent($adminId, 'security', 'account_locked', $details, 'WARNING');
    }

    /**
     * Log configuration changes
     */
    public function logConfigChange($adminId, $setting, $oldValue, $newValue) {
        $details = [
            'setting' => $setting,
            'old_value' => $oldValue,
            'new_value' => $newValue,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->logAuthEvent($adminId, 'config', 'setting_changed', $details, 'INFO');
    }

    /**
     * Get authentication logs with filtering
     */
    public function getAuthLogs($filters = [], $limit = 100, $offset = 0) {
        try {
            $whereConditions = [];
            $params = [];

            if (!empty($filters['admin_id'])) {
                $whereConditions[] = "admin_id = ?";
                $params[] = $filters['admin_id'];
            }

            if (!empty($filters['auth_type'])) {
                $whereConditions[] = "auth_type = ?";
                $params[] = $filters['auth_type'];
            }

            if (!empty($filters['action'])) {
                $whereConditions[] = "action = ?";
                $params[] = $filters['action'];
            }

            if (!empty($filters['level'])) {
                $whereConditions[] = "log_level = ?";
                $params[] = $filters['level'];
            }

            if (!empty($filters['date_from'])) {
                $whereConditions[] = "created_at >= ?";
                $params[] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $whereConditions[] = "created_at <= ?";
                $params[] = $filters['date_to'];
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
            
            $sql = "
                SELECT 
                    al.*,
                    a.username,
                    a.email
                FROM admin_auth_logs al
                LEFT JOIN admins a ON al.admin_id = a.admin_id
                {$whereClause}
                ORDER BY al.created_at DESC
                LIMIT ? OFFSET ?
            ";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Failed to get auth logs: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get security statistics
     */
    public function getSecurityStats($days = 30) {
        try {
            $dateFrom = date('Y-m-d H:i:s', time() - ($days * 24 * 60 * 60));

            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_events,
                    SUM(CASE WHEN action LIKE '%_success' THEN 1 ELSE 0 END) as successful_events,
                    SUM(CASE WHEN action LIKE '%_failed' THEN 1 ELSE 0 END) as failed_events,
                    SUM(CASE WHEN log_level = 'CRITICAL' THEN 1 ELSE 0 END) as critical_events,
                    SUM(CASE WHEN log_level = 'ERROR' THEN 1 ELSE 0 END) as error_events,
                    SUM(CASE WHEN action = 'account_locked' THEN 1 ELSE 0 END) as lockout_events,
                    COUNT(DISTINCT admin_id) as unique_admins,
                    COUNT(DISTINCT ip_address) as unique_ips
                FROM admin_auth_logs 
                WHERE created_at >= ?
            ");

            $stmt->execute([$dateFrom]);
            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Failed to get security stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean old logs (retention policy)
     */
    public function cleanOldLogs($retentionDays = 90) {
        try {
            $cutoffDate = date('Y-m-d H:i:s', time() - ($retentionDays * 24 * 60 * 60));

            $stmt = $this->conn->prepare("DELETE FROM admin_auth_logs WHERE created_at < ?");
            $stmt->execute([$cutoffDate]);

            $deletedRows = $stmt->rowCount();
            
            if ($deletedRows > 0) {
                $this->logAuthEvent(null, 'system', 'logs_cleaned', [
                    'deleted_rows' => $deletedRows,
                    'cutoff_date' => $cutoffDate
                ], 'INFO');
            }

            return $deletedRows;

        } catch (PDOException $e) {
            error_log("Failed to clean old logs: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (X-Forwarded-For)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    /**
     * Get user agent
     */
    private function getUserAgent() {
        return $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    }

    /**
     * Log to system log as fallback
     */
    private function logToSystemLog($adminId, $authType, $action, $details, $level) {
        $logMessage = sprintf(
            "[%s] Admin Auth - ID: %s, Type: %s, Action: %s, IP: %s, Details: %s",
            $level,
            $adminId ?? 'unknown',
            $authType,
            $action,
            $this->getClientIP(),
            json_encode($details)
        );

        error_log($logMessage);
    }

    /**
     * Send security alert (placeholder for email/notification system)
     */
    private function sendSecurityAlert($type, $description, $details) {
        // This would integrate with your notification system
        // For now, just log to system
        $alertMessage = sprintf(
            "SECURITY ALERT: %s - %s - IP: %s - Time: %s",
            $type,
            $description,
            $details['ip_address'],
            $details['timestamp']
        );

        error_log($alertMessage);
        
        // TODO: Implement email alerts, Slack notifications, etc.
    }
}
?>
