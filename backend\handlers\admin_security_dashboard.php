<?php
/**
 * Admin Security Dashboard
 * Provides security monitoring and analytics for admin authentication
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';
require_once '../includes/audit_logger.php';
require_once '../includes/error_handler.php';

try {
    $conn = getDBConnection();
    $auditLogger = new AdminAuditLogger($conn);
    $errorHandler = new AdminErrorHandler($conn, $auditLogger);
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    $action = $_GET['action'] ?? 'overview';
    $days = (int)($_GET['days'] ?? 30);
    $adminId = $_GET['adminId'] ?? null;

    switch ($action) {
        case 'overview':
            $result = getSecurityOverview($conn, $auditLogger, $errorHandler, $days);
            break;
            
        case 'logs':
            $result = getSecurityLogs($conn, $auditLogger, $_GET);
            break;
            
        case 'stats':
            $result = getSecurityStats($conn, $auditLogger, $days);
            break;
            
        case 'health':
            $result = $errorHandler->checkSystemHealth();
            break;
            
        case 'admin_activity':
            $result = getAdminActivity($conn, $adminId, $days);
            break;
            
        default:
            throw new Exception("Invalid action");
    }

    echo json_encode([
        'success' => true,
        'data' => $result,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Get security overview
 */
function getSecurityOverview($conn, $auditLogger, $errorHandler, $days) {
    $dateFrom = date('Y-m-d H:i:s', time() - ($days * 24 * 60 * 60));
    
    // Get basic stats
    $stats = $auditLogger->getSecurityStats($days);
    
    // Get system health
    $health = $errorHandler->checkSystemHealth();
    
    // Get recent critical events
    $criticalEvents = $auditLogger->getAuthLogs([
        'level' => 'CRITICAL',
        'date_from' => $dateFrom
    ], 10);
    
    // Get authentication method distribution
    $stmt = $conn->prepare("
        SELECT 
            auth_method,
            COUNT(*) as count
        FROM admins 
        GROUP BY auth_method
    ");
    $stmt->execute();
    $authMethodDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get top failed IPs
    $stmt = $conn->prepare("
        SELECT 
            ip_address,
            COUNT(*) as failed_attempts
        FROM admin_auth_logs 
        WHERE action LIKE '%_failed' 
        AND created_at >= ?
        GROUP BY ip_address 
        ORDER BY failed_attempts DESC 
        LIMIT 10
    ");
    $stmt->execute([$dateFrom]);
    $topFailedIPs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get daily activity trend
    $stmt = $conn->prepare("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as total_events,
            SUM(CASE WHEN action LIKE '%_success' THEN 1 ELSE 0 END) as successful_events,
            SUM(CASE WHEN action LIKE '%_failed' THEN 1 ELSE 0 END) as failed_events
        FROM admin_auth_logs 
        WHERE created_at >= ?
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
    ");
    $stmt->execute([$dateFrom]);
    $dailyTrend = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'stats' => $stats,
        'health' => $health,
        'critical_events' => $criticalEvents,
        'auth_method_distribution' => $authMethodDistribution,
        'top_failed_ips' => $topFailedIPs,
        'daily_trend' => $dailyTrend,
        'period_days' => $days
    ];
}

/**
 * Get security logs with filtering
 */
function getSecurityLogs($conn, $auditLogger, $params) {
    $filters = [];
    $limit = (int)($params['limit'] ?? 50);
    $offset = (int)($params['offset'] ?? 0);
    
    // Build filters
    if (!empty($params['admin_id'])) {
        $filters['admin_id'] = $params['admin_id'];
    }
    
    if (!empty($params['auth_type'])) {
        $filters['auth_type'] = $params['auth_type'];
    }
    
    if (!empty($params['action'])) {
        $filters['action'] = $params['action'];
    }
    
    if (!empty($params['level'])) {
        $filters['level'] = $params['level'];
    }
    
    if (!empty($params['date_from'])) {
        $filters['date_from'] = $params['date_from'];
    }
    
    if (!empty($params['date_to'])) {
        $filters['date_to'] = $params['date_to'];
    }
    
    $logs = $auditLogger->getAuthLogs($filters, $limit, $offset);
    
    // Get total count for pagination
    $totalCount = getTotalLogCount($conn, $filters);
    
    return [
        'logs' => $logs,
        'pagination' => [
            'total' => $totalCount,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $totalCount
        ]
    ];
}

/**
 * Get detailed security statistics
 */
function getSecurityStats($conn, $auditLogger, $days) {
    $dateFrom = date('Y-m-d H:i:s', time() - ($days * 24 * 60 * 60));
    
    // Authentication method success rates
    $stmt = $conn->prepare("
        SELECT 
            auth_type,
            COUNT(*) as total_attempts,
            SUM(CASE WHEN action LIKE '%_success' THEN 1 ELSE 0 END) as successful_attempts,
            ROUND(
                (SUM(CASE WHEN action LIKE '%_success' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 
                2
            ) as success_rate
        FROM admin_auth_logs 
        WHERE created_at >= ?
        AND auth_type IN ('password', 'otp', '2fa')
        GROUP BY auth_type
    ");
    $stmt->execute([$dateFrom]);
    $authMethodStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Hourly activity pattern
    $stmt = $conn->prepare("
        SELECT 
            HOUR(created_at) as hour,
            COUNT(*) as activity_count
        FROM admin_auth_logs 
        WHERE created_at >= ?
        GROUP BY HOUR(created_at)
        ORDER BY hour
    ");
    $stmt->execute([$dateFrom]);
    $hourlyActivity = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Most active admins
    $stmt = $conn->prepare("
        SELECT 
            al.admin_id,
            a.username,
            COUNT(*) as activity_count,
            MAX(al.created_at) as last_activity
        FROM admin_auth_logs al
        LEFT JOIN admins a ON al.admin_id = a.admin_id
        WHERE al.created_at >= ?
        AND al.admin_id IS NOT NULL
        GROUP BY al.admin_id, a.username
        ORDER BY activity_count DESC
        LIMIT 10
    ");
    $stmt->execute([$dateFrom]);
    $mostActiveAdmins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Security incidents by type
    $stmt = $conn->prepare("
        SELECT 
            JSON_EXTRACT(details, '$.incident_type') as incident_type,
            COUNT(*) as count
        FROM admin_auth_logs 
        WHERE action = 'incident'
        AND created_at >= ?
        GROUP BY JSON_EXTRACT(details, '$.incident_type')
        ORDER BY count DESC
    ");
    $stmt->execute([$dateFrom]);
    $incidentTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'auth_method_stats' => $authMethodStats,
        'hourly_activity' => $hourlyActivity,
        'most_active_admins' => $mostActiveAdmins,
        'incident_types' => $incidentTypes,
        'period_days' => $days
    ];
}

/**
 * Get individual admin activity
 */
function getAdminActivity($conn, $adminId, $days) {
    if (!$adminId) {
        throw new Exception("Admin ID required");
    }
    
    $dateFrom = date('Y-m-d H:i:s', time() - ($days * 24 * 60 * 60));
    
    // Get admin info
    $stmt = $conn->prepare("
        SELECT admin_id, username, email, role, auth_method, two_factor_enabled 
        FROM admins 
        WHERE admin_id = ?
    ");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Admin not found");
    }
    
    // Get activity summary
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_events,
            SUM(CASE WHEN action LIKE '%_success' THEN 1 ELSE 0 END) as successful_events,
            SUM(CASE WHEN action LIKE '%_failed' THEN 1 ELSE 0 END) as failed_events,
            MAX(created_at) as last_activity
        FROM admin_auth_logs 
        WHERE admin_id = ? AND created_at >= ?
    ");
    $stmt->execute([$adminId, $dateFrom]);
    $activitySummary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get recent activity
    $stmt = $conn->prepare("
        SELECT auth_type, action, details, ip_address, created_at 
        FROM admin_auth_logs 
        WHERE admin_id = ? AND created_at >= ?
        ORDER BY created_at DESC 
        LIMIT 50
    ");
    $stmt->execute([$adminId, $dateFrom]);
    $recentActivity = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get unique IPs used
    $stmt = $conn->prepare("
        SELECT 
            ip_address,
            COUNT(*) as usage_count,
            MAX(created_at) as last_used
        FROM admin_auth_logs 
        WHERE admin_id = ? AND created_at >= ?
        GROUP BY ip_address
        ORDER BY usage_count DESC
    ");
    $stmt->execute([$adminId, $dateFrom]);
    $ipUsage = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'admin' => $admin,
        'activity_summary' => $activitySummary,
        'recent_activity' => $recentActivity,
        'ip_usage' => $ipUsage,
        'period_days' => $days
    ];
}

/**
 * Get total log count for pagination
 */
function getTotalLogCount($conn, $filters) {
    $whereConditions = [];
    $params = [];
    
    if (!empty($filters['admin_id'])) {
        $whereConditions[] = "admin_id = ?";
        $params[] = $filters['admin_id'];
    }
    
    if (!empty($filters['auth_type'])) {
        $whereConditions[] = "auth_type = ?";
        $params[] = $filters['auth_type'];
    }
    
    if (!empty($filters['action'])) {
        $whereConditions[] = "action = ?";
        $params[] = $filters['action'];
    }
    
    if (!empty($filters['level'])) {
        $whereConditions[] = "log_level = ?";
        $params[] = $filters['level'];
    }
    
    if (!empty($filters['date_from'])) {
        $whereConditions[] = "created_at >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $whereConditions[] = "created_at <= ?";
        $params[] = $filters['date_to'];
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM admin_auth_logs {$whereClause}");
    $stmt->execute($params);
    
    return $stmt->fetch(PDO::FETCH_ASSOC)['total'];
}
?>
