<?php
/**
 * Rate Limiter for Admin Authentication
 * Implements rate limiting to prevent brute force attacks
 */

class AdminRateLimiter {
    private $conn;
    private $defaultLimits = [
        'login' => ['attempts' => 5, 'window' => 900], // 5 attempts per 15 minutes
        'otp' => ['attempts' => 3, 'window' => 300],   // 3 attempts per 5 minutes
        '2fa' => ['attempts' => 5, 'window' => 600],   // 5 attempts per 10 minutes
        'password_reset' => ['attempts' => 3, 'window' => 3600] // 3 attempts per hour
    ];

    public function __construct($connection) {
        $this->conn = $connection;
    }

    /**
     * Check if an IP or admin is rate limited for a specific action
     */
    public function isRateLimited($identifier, $action, $identifierType = 'ip') {
        try {
            $limits = $this->getLimitsForAction($action);
            $windowStart = date('Y-m-d H:i:s', time() - $limits['window']);

            if ($identifierType === 'ip') {
                $stmt = $this->conn->prepare("
                    SELECT COUNT(*) as attempts 
                    FROM admin_login_attempts 
                    WHERE ip_address = ? AND attempt_type = ? AND last_attempt >= ?
                ");
                $stmt->execute([$identifier, $action, $windowStart]);
            } else {
                $stmt = $this->conn->prepare("
                    SELECT COUNT(*) as attempts 
                    FROM admin_login_attempts 
                    WHERE admin_id = ? AND attempt_type = ? AND last_attempt >= ?
                ");
                $stmt->execute([$identifier, $action, $windowStart]);
            }

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['attempts'] >= $limits['attempts'];

        } catch (PDOException $e) {
            error_log("Rate limiter check failed: " . $e->getMessage());
            return false; // Fail open for availability
        }
    }

    /**
     * Record a failed attempt
     */
    public function recordAttempt($identifier, $action, $identifierType = 'ip', $adminId = null) {
        try {
            if ($identifierType === 'ip') {
                $stmt = $this->conn->prepare("
                    INSERT INTO admin_login_attempts (ip_address, attempt_type, attempts, admin_id) 
                    VALUES (?, ?, 1, ?)
                    ON DUPLICATE KEY UPDATE 
                    attempts = attempts + 1, 
                    last_attempt = NOW()
                ");
                $stmt->execute([$identifier, $action, $adminId]);
            } else {
                $stmt = $this->conn->prepare("
                    INSERT INTO admin_login_attempts (admin_id, ip_address, attempt_type, attempts) 
                    VALUES (?, ?, ?, 1)
                    ON DUPLICATE KEY UPDATE 
                    attempts = attempts + 1, 
                    last_attempt = NOW()
                ");
                $stmt->execute([$identifier, $_SERVER['REMOTE_ADDR'] ?? 'unknown', $action]);
            }

            // Check if we need to lock the account
            $this->checkAndApplyLockout($identifier, $action, $identifierType);

        } catch (PDOException $e) {
            error_log("Failed to record attempt: " . $e->getMessage());
        }
    }

    /**
     * Clear attempts for successful authentication
     */
    public function clearAttempts($identifier, $action, $identifierType = 'ip') {
        try {
            if ($identifierType === 'ip') {
                $stmt = $this->conn->prepare("
                    DELETE FROM admin_login_attempts 
                    WHERE ip_address = ? AND attempt_type = ?
                ");
                $stmt->execute([$identifier, $action]);
            } else {
                $stmt = $this->conn->prepare("
                    DELETE FROM admin_login_attempts 
                    WHERE admin_id = ? AND attempt_type = ?
                ");
                $stmt->execute([$identifier, $action]);
            }
        } catch (PDOException $e) {
            error_log("Failed to clear attempts: " . $e->getMessage());
        }
    }

    /**
     * Get remaining attempts before lockout
     */
    public function getRemainingAttempts($identifier, $action, $identifierType = 'ip') {
        try {
            $limits = $this->getLimitsForAction($action);
            $windowStart = date('Y-m-d H:i:s', time() - $limits['window']);

            if ($identifierType === 'ip') {
                $stmt = $this->conn->prepare("
                    SELECT attempts 
                    FROM admin_login_attempts 
                    WHERE ip_address = ? AND attempt_type = ? AND last_attempt >= ?
                    ORDER BY last_attempt DESC 
                    LIMIT 1
                ");
                $stmt->execute([$identifier, $action, $windowStart]);
            } else {
                $stmt = $this->conn->prepare("
                    SELECT attempts 
                    FROM admin_login_attempts 
                    WHERE admin_id = ? AND attempt_type = ? AND last_attempt >= ?
                    ORDER BY last_attempt DESC 
                    LIMIT 1
                ");
                $stmt->execute([$identifier, $action, $windowStart]);
            }

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $currentAttempts = $result ? $result['attempts'] : 0;
            
            return max(0, $limits['attempts'] - $currentAttempts);

        } catch (PDOException $e) {
            error_log("Failed to get remaining attempts: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get lockout time remaining in seconds
     */
    public function getLockoutTimeRemaining($identifier, $action, $identifierType = 'ip') {
        try {
            if ($identifierType === 'ip') {
                $stmt = $this->conn->prepare("
                    SELECT locked_until 
                    FROM admin_login_attempts 
                    WHERE ip_address = ? AND attempt_type = ? AND locked_until > NOW()
                    ORDER BY locked_until DESC 
                    LIMIT 1
                ");
                $stmt->execute([$identifier, $action]);
            } else {
                $stmt = $this->conn->prepare("
                    SELECT locked_until 
                    FROM admin_login_attempts 
                    WHERE admin_id = ? AND attempt_type = ? AND locked_until > NOW()
                    ORDER BY locked_until DESC 
                    LIMIT 1
                ");
                $stmt->execute([$identifier, $action]);
            }

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result && $result['locked_until']) {
                return max(0, strtotime($result['locked_until']) - time());
            }
            
            return 0;

        } catch (PDOException $e) {
            error_log("Failed to get lockout time: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Check and apply lockout if necessary
     */
    private function checkAndApplyLockout($identifier, $action, $identifierType) {
        $limits = $this->getLimitsForAction($action);
        $windowStart = date('Y-m-d H:i:s', time() - $limits['window']);

        try {
            if ($identifierType === 'ip') {
                $stmt = $this->conn->prepare("
                    SELECT attempts 
                    FROM admin_login_attempts 
                    WHERE ip_address = ? AND attempt_type = ? AND last_attempt >= ?
                    ORDER BY last_attempt DESC 
                    LIMIT 1
                ");
                $stmt->execute([$identifier, $action, $windowStart]);
            } else {
                $stmt = $this->conn->prepare("
                    SELECT attempts 
                    FROM admin_login_attempts 
                    WHERE admin_id = ? AND attempt_type = ? AND last_attempt >= ?
                    ORDER BY last_attempt DESC 
                    LIMIT 1
                ");
                $stmt->execute([$identifier, $action, $windowStart]);
            }

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result && $result['attempts'] >= $limits['attempts']) {
                // Apply lockout
                $lockoutDuration = $this->getLockoutDuration($action);
                $lockedUntil = date('Y-m-d H:i:s', time() + $lockoutDuration);

                if ($identifierType === 'ip') {
                    $stmt = $this->conn->prepare("
                        UPDATE admin_login_attempts 
                        SET locked_until = ? 
                        WHERE ip_address = ? AND attempt_type = ?
                    ");
                    $stmt->execute([$lockedUntil, $identifier, $action]);
                } else {
                    $stmt = $this->conn->prepare("
                        UPDATE admin_login_attempts 
                        SET locked_until = ? 
                        WHERE admin_id = ? AND attempt_type = ?
                    ");
                    $stmt->execute([$lockedUntil, $identifier, $action]);
                }
            }

        } catch (PDOException $e) {
            error_log("Failed to apply lockout: " . $e->getMessage());
        }
    }

    /**
     * Get limits for a specific action
     */
    private function getLimitsForAction($action) {
        // Try to get from database settings first
        try {
            $settingName = "admin_max_{$action}_attempts";
            $stmt = $this->conn->prepare("SELECT setting_value FROM admin_auth_settings WHERE setting_name = ?");
            $stmt->execute([$settingName]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                $attempts = (int)$result['setting_value'];
                return ['attempts' => $attempts, 'window' => $this->defaultLimits[$action]['window'] ?? 900];
            }
        } catch (PDOException $e) {
            // Fall back to defaults
        }

        return $this->defaultLimits[$action] ?? $this->defaultLimits['login'];
    }

    /**
     * Get lockout duration for a specific action
     */
    private function getLockoutDuration($action) {
        try {
            $stmt = $this->conn->prepare("SELECT setting_value FROM admin_auth_settings WHERE setting_name = 'admin_lockout_time'");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                return (int)$result['setting_value'];
            }
        } catch (PDOException $e) {
            // Fall back to default
        }

        return 1800; // 30 minutes default
    }
}
?>
