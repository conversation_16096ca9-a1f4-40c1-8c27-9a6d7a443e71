<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Credentials: true");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, X-Admin-ID");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

include_once '../includes/db_connect.php';

function validateAdminCredentials($identifier, $password) {
    try {
        $conn = getDBConnection();
        if (!$conn) {
            return ['success' => false, 'message' => 'Database connection failed.', 'error_code' => 'DB_CONNECTION_FAILED'];
        }

        $stmt = $conn->prepare("SELECT admin_id, username, password_hash, role FROM admins WHERE username = :identifier OR email = :identifier_email");
        $stmt->bindParam(':identifier', $identifier);
        $stmt->bindParam(':identifier_email', $identifier);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            if (password_verify($password, $row['password_hash'])) {
                // Update last_login
                $updateStmt = $conn->prepare("UPDATE admins SET last_login = CURRENT_TIMESTAMP WHERE admin_id = :admin_id");
                $updateStmt->bindParam(':admin_id', $row['admin_id']);
                $updateStmt->execute();

                return [
                    'success' => true,
                    'admin_id' => $row['admin_id'],
                    'username' => $row['username'],
                    'role' => $row['role']
                ];
            } else {
                error_log("Admin login failed for user: {$identifier}. Reason: Incorrect password.");
                return ['success' => false, 'message' => 'Invalid credentials provided.', 'error_code' => 'INCORRECT_PASSWORD'];
            }
        } else {
            error_log("Admin login failed. Reason: User '{$identifier}' not found.");
            return ['success' => false, 'message' => 'User not found.', 'error_code' => 'USER_NOT_FOUND'];
        }
    } catch (PDOException $e) {
        error_log("Admin Login PDOException: " . $e->getMessage());
        return ['success' => false, 'message' => 'A database error occurred.', 'error_code' => 'DB_ERROR', 'details' => $e->getMessage()];
    }
}

try {
    // Get database connection
    $conn = getDBConnection();
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    $rawInput = file_get_contents("php://input");
    $data = json_decode($rawInput);

    // Debug logging
    error_log("Admin login attempt - Raw input: " . $rawInput);
    error_log("Admin login attempt - Parsed data: " . json_encode($data));

    if (empty($data->identifier) || empty($data->password)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Username/email and password are required"
        ]);
        exit;
    }

    $result = validateAdminCredentials($data->identifier, $data->password);

    if ($result['success']) {
        // Check if enhanced authentication is required
        $adminId = $result['admin_id'];

        // Get admin's authentication method
        $stmt = $conn->prepare("SELECT auth_method, two_factor_enabled, account_locked_until FROM admins WHERE admin_id = ?");
        $stmt->execute([$adminId]);
        $adminAuth = $stmt->fetch(PDO::FETCH_ASSOC);

        // Check if account is locked
        if ($adminAuth && $adminAuth['account_locked_until'] && $adminAuth['account_locked_until'] > date('Y-m-d H:i:s')) {
            http_response_code(403);
            echo json_encode([
                "success" => false,
                "message" => "Account is temporarily locked. Please try again later.",
                "locked_until" => $adminAuth['account_locked_until']
            ]);
            exit;
        }

        // Check global authentication settings
        $stmt = $conn->query("SELECT setting_name, setting_value FROM admin_auth_settings WHERE setting_name IN ('admin_otp_enabled', 'admin_2fa_enabled', 'admin_auth_method')");
        $authSettings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $authSettings[$row['setting_name']] = $row['setting_value'];
        }

        $authMethod = $adminAuth['auth_method'] ?? 'password_only';
        $requiresAdditionalAuth = false;
        $nextStep = null;

        // Determine if additional authentication is required
        if ($authMethod === 'otp' && ($authSettings['admin_otp_enabled'] ?? 'false') === 'true') {
            $requiresAdditionalAuth = true;
            $nextStep = 'otp';
        } elseif ($authMethod === '2fa' && ($authSettings['admin_2fa_enabled'] ?? 'false') === 'true') {
            // Check if 2FA is properly set up
            $stmt = $conn->prepare("SELECT is_enabled, setup_completed FROM admin_2fa WHERE admin_id = ?");
            $stmt->execute([$adminId]);
            $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($tfaSetup && $tfaSetup['is_enabled'] && $tfaSetup['setup_completed']) {
                $requiresAdditionalAuth = true;
                $nextStep = '2fa';
            }
        }

        if ($requiresAdditionalAuth) {
            // Log partial login success
            $stmt = $conn->prepare("
                INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent)
                VALUES (?, 'password', 'login_attempt', ?, ?, ?)
            ");
            $stmt->execute([
                $adminId,
                json_encode(['password_verified' => true, 'next_step' => $nextStep]),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

            http_response_code(200);
            echo json_encode([
                "success" => true,
                "requires_additional_auth" => true,
                "next_step" => $nextStep,
                "admin_id" => $result['admin_id'],
                "username" => $result['username'],
                "message" => "Password verified. Additional authentication required."
            ]);
        } else {
            // Complete login (password only)
            $stmt = $conn->prepare("
                INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent)
                VALUES (?, 'password', 'login_success', ?, ?, ?)
            ");
            $stmt->execute([
                $adminId,
                json_encode(['auth_method' => 'password_only']),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

            http_response_code(200);
            echo json_encode([
                "success" => true,
                "message" => "Login successful",
                "admin_id" => $result['admin_id'],
                "username" => $result['username'],
                "role" => $result['role'],
                "auth_method" => "password_only"
            ]);
        }
    } else {
        http_response_code(401);
        echo json_encode([
            "success" => false,
            "message" => $result['message'] ?? 'Invalid username/email or password',
            "error_code" => $result['error_code'] ?? 'UNKNOWN_ERROR'
        ]);
    }
} catch (Exception $e) {
    error_log("Admin Login Error: " . $e->getMessage());
    error_log("Admin Login Error Stack: " . $e->getTraceAsString());
    
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "An error occurred during login",
        "debug_info" => [
            "error" => $e->getMessage(),
            "file" => $e->getFile(),
            "line" => $e->getLine()
        ]
    ]);
}
?>