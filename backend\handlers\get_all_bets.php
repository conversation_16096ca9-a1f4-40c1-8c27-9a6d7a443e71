<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();

    // Get parameters
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $dateFrom = isset($_GET['dateFrom']) ? $_GET['dateFrom'] : '';
    $dateTo = isset($_GET['dateTo']) ? $_GET['dateTo'] : '';
    $sortBy = isset($_GET['sortBy']) ? $_GET['sortBy'] : 'created_at';
    $order = isset($_GET['order']) ? $_GET['order'] : 'DESC';

    $offset = ($page - 1) * $limit;

    // Validate sort column
    $allowedSortColumns = ['bet_id', 'created_at', 'amount_user1', 'bet_status'];
    if (!in_array($sortBy, $allowedSortColumns)) {
        $sortBy = 'created_at';
    }

    // Validate order
    $order = ($order === 'ASC') ? 'ASC' : 'DESC';

    // Build WHERE conditions
    $whereConditions = [];
    $params = [];

    if (!empty($search)) {
        $whereConditions[] = "(b.unique_code LIKE :search OR u1.username LIKE :search OR u2.username LIKE :search OR c.team_a LIKE :search OR c.team_b LIKE :search)";
        $params[':search'] = "%$search%";
    }

    if (!empty($status)) {
        $whereConditions[] = "b.bet_status = :status";
        $params[':status'] = $status;
    }

    if (!empty($dateFrom)) {
        $whereConditions[] = "DATE(b.created_at) >= :dateFrom";
        $params[':dateFrom'] = $dateFrom;
    }

    if (!empty($dateTo)) {
        $whereConditions[] = "DATE(b.created_at) <= :dateTo";
        $params[':dateTo'] = $dateTo;
    }

    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

    // Get total count with filters
    $countQuery = "SELECT COUNT(*) as total FROM bets b
                   LEFT JOIN users u1 ON b.user1_id = u1.user_id
                   LEFT JOIN users u2 ON b.user2_id = u2.user_id
                   LEFT JOIN challenges c ON b.challenge_id = c.challenge_id
                   $whereClause";

    $countStmt = $conn->prepare($countQuery);
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Get paginated bets with enhanced data
    $query = "SELECT b.*,
              u1.username as user1_name,
              u1.full_name as user1_full_name,
              u2.username as user2_name,
              u2.full_name as user2_full_name,
              c.team_a, c.team_b, c.match_date,
              c.logo1 as team_a_logo, c.logo2 as team_b_logo
              FROM bets b
              LEFT JOIN users u1 ON b.user1_id = u1.user_id
              LEFT JOIN users u2 ON b.user2_id = u2.user_id
              LEFT JOIN challenges c ON b.challenge_id = c.challenge_id
              $whereClause
              ORDER BY b.$sortBy $order
              LIMIT :limit OFFSET :offset";

    $stmt = $conn->prepare($query);

    // Bind filter parameters
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }

    // Bind pagination parameters
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();

    $bets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'bets' => $bets,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => ceil($total / $limit),
            'totalItems' => $total,
            'itemsPerPage' => $limit
        ]
    ]);

} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => "Error: " . $e->getMessage()
    ]);
}
