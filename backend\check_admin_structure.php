<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Checking admins table structure...\n";
    $stmt = $conn->query('DESCRIBE admins');
    while ($row = $stmt->fetch()) {
        if ($row['Field'] === 'auth_method') {
            echo "auth_method column: {$row['Type']}\n";
            echo "Default: {$row['Default']}\n";
            echo "Null: {$row['Null']}\n";
        }
    }
    
    echo "\nCurrent admin auth methods:\n";
    $stmt = $conn->query('SELECT admin_id, username, auth_method FROM admins');
    while ($row = $stmt->fetch()) {
        echo "Admin {$row['admin_id']} ({$row['username']}): {$row['auth_method']}\n";
    }
    
    echo "\nUpdating admin auth method to password_only...\n";
    $stmt = $conn->prepare('UPDATE admins SET auth_method = ? WHERE admin_id = 1');
    $stmt->execute(['password_only']);
    
    echo "Updated successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>