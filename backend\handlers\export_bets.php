<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get parameters
    $format = isset($_GET['format']) ? $_GET['format'] : 'csv';
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $dateFrom = isset($_GET['dateFrom']) ? $_GET['dateFrom'] : '';
    $dateTo = isset($_GET['dateTo']) ? $_GET['dateTo'] : '';
    
    // Build WHERE conditions
    $whereConditions = [];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(b.unique_code LIKE :search OR u1.username LIKE :search OR u2.username LIKE :search OR c.team_a LIKE :search OR c.team_b LIKE :search)";
        $params[':search'] = "%$search%";
    }
    
    if (!empty($status)) {
        $whereConditions[] = "b.bet_status = :status";
        $params[':status'] = $status;
    }
    
    if (!empty($dateFrom)) {
        $whereConditions[] = "DATE(b.created_at) >= :dateFrom";
        $params[':dateFrom'] = $dateFrom;
    }
    
    if (!empty($dateTo)) {
        $whereConditions[] = "DATE(b.created_at) <= :dateTo";
        $params[':dateTo'] = $dateTo;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Get all bets for export
    $query = "SELECT 
              b.bet_id,
              b.unique_code,
              u1.username as user1_name, 
              u1.full_name as user1_full_name,
              u2.username as user2_name,
              u2.full_name as user2_full_name,
              c.team_a, 
              c.team_b,
              b.bet_choice_user1,
              b.bet_choice_user2,
              b.amount_user1,
              b.amount_user2,
              b.odds_user1,
              b.odds_user2,
              b.potential_return_user1,
              b.potential_return_user2,
              b.bet_status,
              b.created_at,
              c.match_date,
              c.result as match_result
              FROM bets b
              LEFT JOIN users u1 ON b.user1_id = u1.user_id
              LEFT JOIN users u2 ON b.user2_id = u2.user_id
              LEFT JOIN challenges c ON b.challenge_id = c.challenge_id
              $whereClause
              ORDER BY b.created_at DESC";

    $stmt = $conn->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    
    $bets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($format === 'csv') {
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="bets_export_' . date('Y-m-d') . '.csv"');
        
        // Create CSV output
        $output = fopen('php://output', 'w');
        
        // CSV headers
        $headers = [
            'Bet ID',
            'Reference',
            'User 1',
            'User 1 Full Name',
            'User 1 Choice',
            'User 1 Amount',
            'User 1 Odds',
            'User 1 Potential Return',
            'User 2',
            'User 2 Full Name',
            'User 2 Choice',
            'User 2 Amount',
            'User 2 Odds',
            'User 2 Potential Return',
            'Team A',
            'Team B',
            'Status',
            'Created Date',
            'Match Date',
            'Match Result'
        ];
        
        fputcsv($output, $headers);
        
        // CSV data
        foreach ($bets as $bet) {
            $row = [
                $bet['bet_id'],
                $bet['unique_code'] ?: $bet['bet_id'] . 'DNRBKCC',
                $bet['user1_name'],
                $bet['user1_full_name'],
                $bet['bet_choice_user1'],
                $bet['amount_user1'],
                $bet['odds_user1'],
                $bet['potential_return_user1'],
                $bet['user2_name'] ?: 'Waiting',
                $bet['user2_full_name'] ?: '',
                $bet['bet_choice_user2'] ?: '',
                $bet['amount_user2'] ?: '',
                $bet['odds_user2'] ?: '',
                $bet['potential_return_user2'] ?: '',
                $bet['team_a'],
                $bet['team_b'],
                $bet['bet_status'],
                $bet['created_at'],
                $bet['match_date'],
                $bet['match_result'] ?: 'Pending'
            ];
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit;
    } else {
        // Return JSON format
        echo json_encode([
            'success' => true,
            'data' => $bets,
            'count' => count($bets)
        ]);
    }

} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => "Error: " . $e->getMessage()
    ]);
}
?>
