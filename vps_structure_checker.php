<?php
/**
 * VPS Structure and Backend Connection Checker
 * Upload this file to your VPS at /var/www/fanbet247.xyz/vps_structure_checker.php
 * Then access it via https://fanbet247.xyz/vps_structure_checker.php
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

function checkVPSStructure() {
    $basePath = '/var/www/fanbet247.xyz';
    $webPath = $_SERVER['DOCUMENT_ROOT'] ?? $basePath;
    
    $structure = [
        'base_info' => [
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Not set',
            'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'Not set',
            'server_name' => $_SERVER['SERVER_NAME'] ?? 'Not set',
            'http_host' => $_SERVER['HTTP_HOST'] ?? 'Not set',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Not set',
            'current_working_dir' => getcwd(),
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
        ],
        'critical_files' => [
            'index.html' => file_exists($webPath . '/index.html'),
            '.htaccess' => file_exists($webPath . '/.htaccess'),
            'debug.php' => file_exists($webPath . '/debug.php'),
        ],
        'directories' => [
            'backend' => is_dir($webPath . '/backend'),
            'backend/handlers' => is_dir($webPath . '/backend/handlers'),
            'backend/includes' => is_dir($webPath . '/backend/includes'),
            'static' => is_dir($webPath . '/static'),
            'uploads' => is_dir($webPath . '/uploads'),
        ],
        'backend_files' => [
            'db_connect.php' => file_exists($webPath . '/backend/includes/db_connect.php'),
            'homepage_data.php' => file_exists($webPath . '/backend/handlers/homepage_data.php'),
            'get_currencies.php' => file_exists($webPath . '/backend/handlers/get_currencies.php'),
            'get_site_config.php' => file_exists($webPath . '/backend/handlers/get_site_config.php'),
            'admin_login_handler.php' => file_exists($webPath . '/backend/handlers/admin_login_handler.php'),
        ],
        'permissions' => [
            'backend_readable' => is_readable($webPath . '/backend'),
            'uploads_writable' => is_writable($webPath . '/uploads'),
            'backend_handlers_readable' => is_readable($webPath . '/backend/handlers'),
        ]
    ];
    
    return $structure;
}

function checkDatabaseConnection() {
    try {
        $webPath = $_SERVER['DOCUMENT_ROOT'] ?? '/var/www/fanbet247.xyz';
        $dbFile = $webPath . '/backend/includes/db_connect.php';
        
        if (!file_exists($dbFile)) {
            return ['status' => 'error', 'message' => 'db_connect.php not found at: ' . $dbFile];
        }
        
        include_once $dbFile;
        
        if (function_exists('getDBConnection')) {
            $conn = getDBConnection();
            if ($conn) {
                // Test the connection
                $stmt = $conn->query("SELECT 1");
                return ['status' => 'connected', 'message' => 'Database connection successful'];
            }
        }
        
        return ['status' => 'error', 'message' => 'Could not establish database connection'];
        
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
    }
}

function testAPIEndpoints() {
    $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' ? 'https' : 'http') . 
               '://' . $_SERVER['HTTP_HOST'];
    
    $endpoints = [
        'homepage_data' => '/backend/handlers/homepage_data.php',
        'currencies' => '/backend/handlers/get_currencies.php',
        'site_config' => '/backend/handlers/get_site_config.php',
        'ping_test' => '/debug.php?action=ping'
    ];
    
    $results = [];
    foreach ($endpoints as $name => $endpoint) {
        $url = $baseUrl . $endpoint;
        $results[$name] = [
            'url' => $url,
            'file_exists' => file_exists($_SERVER['DOCUMENT_ROOT'] . $endpoint),
            'accessible' => false,
            'response' => 'Not tested'
        ];
        
        // Simple file accessibility check
        if ($results[$name]['file_exists']) {
            $results[$name]['accessible'] = is_readable($_SERVER['DOCUMENT_ROOT'] . $endpoint);
        }
    }
    
    return $results;
}

function checkApacheConfiguration() {
    $checks = [
        'mod_rewrite' => function_exists('apache_get_modules') ? in_array('mod_rewrite', apache_get_modules()) : 'Unknown',
        'htaccess_support' => file_exists($_SERVER['DOCUMENT_ROOT'] . '/.htaccess'),
        'php_extensions' => [
            'pdo' => extension_loaded('pdo'),
            'pdo_mysql' => extension_loaded('pdo_mysql'),
            'curl' => extension_loaded('curl'),
            'json' => extension_loaded('json'),
            'mbstring' => extension_loaded('mbstring'),
        ]
    ];
    
    return $checks;
}

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    
    switch ($_GET['action']) {
        case 'ping':
            echo json_encode(['status' => 'ok', 'message' => 'VPS Backend is reachable', 'timestamp' => date('Y-m-d H:i:s')]);
            break;
        case 'structure':
            echo json_encode(checkVPSStructure());
            break;
        case 'database':
            echo json_encode(checkDatabaseConnection());
            break;
        case 'endpoints':
            echo json_encode(testAPIEndpoints());
            break;
        default:
            echo json_encode(['status' => 'error', 'message' => 'Unknown action']);
    }
    exit;
}

// Get all data for display
$structure = checkVPSStructure();
$dbStatus = checkDatabaseConnection();
$apiEndpoints = testAPIEndpoints();
$apacheConfig = checkApacheConfiguration();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FanBet247 VPS Structure Checker</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2, h3 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status.ok {
            background-color: #d4edda;
            color: #155724;
            border-left: 5px solid #28a745;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 5px solid #dc3545;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 5px solid #ffc107;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .info-card {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .info-card h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.3em;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .metric:last-child {
            border-bottom: none;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .badge.success {
            background-color: #28a745;
            color: white;
        }
        .badge.danger {
            background-color: #dc3545;
            color: white;
        }
        .badge.warning {
            background-color: #ffc107;
            color: #212529;
        }
        .code {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            word-break: break-all;
        }
        .btn {
            background: linear-gradient(145deg, #3498db, #2980b9);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 FanBet247 VPS Structure Checker</h1>
        <p style="text-align: center; color: #7f8c8d;">
            <strong>Generated:</strong> <?php echo date('Y-m-d H:i:s'); ?> | 
            <strong>Server:</strong> <?php echo $structure['base_info']['server_software']; ?> |
            <strong>Host:</strong> <?php echo $structure['base_info']['http_host']; ?>
        </p>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="window.location.reload()">🔄 Refresh Status</button>
            <button class="btn" onclick="testAllEndpoints()">🧪 Test All APIs</button>
            <button class="btn" onclick="downloadLogs()">📋 Download Report</button>
        </div>

        <!-- Critical Status Overview -->
        <h2>🚨 Critical System Status</h2>
        
        <div class="status <?php echo ($dbStatus['status'] === 'connected') ? 'ok' : 'error'; ?>">
            <span><?php echo ($dbStatus['status'] === 'connected') ? '✅' : '❌'; ?></span>
            <div>
                <strong>Database Connection:</strong> <?php echo $dbStatus['status']; ?>
                <br><small><?php echo htmlspecialchars($dbStatus['message']); ?></small>
            </div>
        </div>

        <div class="status <?php echo ($structure['critical_files']['index.html'] && $structure['critical_files']['.htaccess']) ? 'ok' : 'error'; ?>">
            <span><?php echo ($structure['critical_files']['index.html'] && $structure['critical_files']['.htaccess']) ? '✅' : '❌'; ?></span>
            <strong>Frontend Deployment:</strong> <?php echo ($structure['critical_files']['index.html'] && $structure['critical_files']['.htaccess']) ? 'Properly Deployed' : 'Missing Files'; ?>
        </div>

        <div class="status <?php echo ($structure['directories']['backend'] && $structure['directories']['backend/handlers']) ? 'ok' : 'error'; ?>">
            <span><?php echo ($structure['directories']['backend'] && $structure['directories']['backend/handlers']) ? '✅' : '❌'; ?></span>
            <strong>Backend Structure:</strong> <?php echo ($structure['directories']['backend'] && $structure['directories']['backend/handlers']) ? 'OK' : 'Missing Directories'; ?>
        </div>

        <!-- System Information Grid -->
        <h2>📊 VPS Information</h2>
        <div class="info-grid">
            <!-- Server Configuration -->
            <div class="info-card">
                <h3>🖥️ Server Configuration</h3>
                <?php foreach ($structure['base_info'] as $key => $value): ?>
                <div class="metric">
                    <span><?php echo ucwords(str_replace('_', ' ', $key)); ?>:</span>
                    <span class="code"><?php echo htmlspecialchars($value); ?></span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- File Structure -->
            <div class="info-card">
                <h3>📁 Critical Files</h3>
                <?php foreach ($structure['critical_files'] as $file => $exists): ?>
                <div class="metric">
                    <span><?php echo $file; ?>:</span>
                    <span class="badge <?php echo $exists ? 'success' : 'danger'; ?>">
                        <?php echo $exists ? 'Found' : 'Missing'; ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Directories -->
            <div class="info-card">
                <h3>📂 Directory Structure</h3>
                <?php foreach ($structure['directories'] as $dir => $exists): ?>
                <div class="metric">
                    <span><?php echo $dir; ?>:</span>
                    <span class="badge <?php echo $exists ? 'success' : 'danger'; ?>">
                        <?php echo $exists ? 'Exists' : 'Missing'; ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Backend Files -->
            <div class="info-card">
                <h3>⚙️ Backend Files</h3>
                <?php foreach ($structure['backend_files'] as $file => $exists): ?>
                <div class="metric">
                    <span><?php echo $file; ?>:</span>
                    <span class="badge <?php echo $exists ? 'success' : 'danger'; ?>">
                        <?php echo $exists ? 'Found' : 'Missing'; ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- API Endpoints Test -->
        <h2>🔗 API Endpoints Status</h2>
        <div class="info-card">
            <h3>📡 Backend API Accessibility</h3>
            <?php foreach ($apiEndpoints as $name => $info): ?>
            <div class="metric">
                <span><?php echo ucwords(str_replace('_', ' ', $name)); ?>:</span>
                <span class="badge <?php echo $info['file_exists'] ? 'success' : 'danger'; ?>">
                    <?php echo $info['file_exists'] ? 'Available' : 'Missing'; ?>
                </span>
            </div>
            <div style="font-size: 0.8em; color: #666; margin-bottom: 10px;">
                URL: <span class="code"><?php echo $info['url']; ?></span>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Live Testing -->
        <h2>🧪 Live API Testing</h2>
        <div class="info-card">
            <h3>🔬 Real-time Tests</h3>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="testEndpoint('/backend/handlers/homepage_data.php', 'homepage-test')">Test Homepage API</button>
                <button class="btn" onclick="testEndpoint('/backend/handlers/get_currencies.php', 'currency-test')">Test Currency API</button>
                <button class="btn" onclick="testEndpoint('?action=ping', 'ping-test')">Test Ping</button>
            </div>
            <div id="homepage-test" class="test-result" style="display: none;"></div>
            <div id="currency-test" class="test-result" style="display: none;"></div>
            <div id="ping-test" class="test-result" style="display: none;"></div>
        </div>

    </div>

    <script>
        function testEndpoint(url, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="status warning">🔄 Testing ' + url + '...</div>';
            
            fetch(url)
                .then(response => {
                    const statusClass = response.ok ? 'ok' : 'error';
                    const statusIcon = response.ok ? '✅' : '❌';
                    
                    return response.text().then(text => {
                        let content = text;
                        try {
                            const json = JSON.parse(text);
                            content = JSON.stringify(json, null, 2);
                        } catch (e) {
                            // Not JSON, keep as text
                        }
                        
                        resultDiv.innerHTML = `
                            <div class="status ${statusClass}">
                                ${statusIcon} ${response.status} ${response.statusText}
                            </div>
                            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto; font-size: 0.8em;">${content.substring(0, 1000)}${content.length > 1000 ? '...' : ''}</pre>
                        `;
                    });
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="status error">❌ Error: ${error.message}</div>`;
                });
        }

        function testAllEndpoints() {
            testEndpoint('?action=ping', 'ping-test');
            testEndpoint('/backend/handlers/homepage_data.php', 'homepage-test');
            testEndpoint('/backend/handlers/get_currencies.php', 'currency-test');
        }

        function downloadLogs() {
            const content = document.documentElement.outerHTML;
            const blob = new Blob([content], { type: 'text/html' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'fanbet247_vps_report_' + new Date().toISOString().slice(0,19).replace(/:/g, '-') + '.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
