<?php
// Check user_otp table structure
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "📊 Checking user_otp table structure:\n";
    
    // Check if table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'user_otp'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "❌ user_otp table does not exist!\n";
        
        echo "\n🔍 Checking for similar tables:\n";
        $stmt = $conn->prepare("SHOW TABLES LIKE '%otp%'");
        $stmt->execute();
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "   No OTP-related tables found.\n";
        } else {
            foreach ($tables as $table) {
                echo "   - $table\n";
            }
        }
        
        echo "\n🔧 Creating user_otp table...\n";
        $createTable = "
        CREATE TABLE user_otp (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            otp VARCHAR(6) NOT NULL,
            expiry DATETIME NOT NULL,
            used BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_otp_expiry (otp, expiry),
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        
        $conn->exec($createTable);
        echo "✅ Created user_otp table\n";
        
    } else {
        echo "✅ user_otp table exists\n";
        
        // Check table structure
        $stmt = $conn->prepare("DESCRIBE user_otp");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\n📋 Table structure:\n";
        foreach ($columns as $column) {
            echo "   - " . $column['Field'] . " (" . $column['Type'] . ")\n";
        }
    }
    
    // Also check if user_auth_logs table exists (needed for logging)
    echo "\n📊 Checking user_auth_logs table:\n";
    $stmt = $conn->prepare("SHOW TABLES LIKE 'user_auth_logs'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "❌ user_auth_logs table does not exist!\n";
        echo "🔧 Creating user_auth_logs table...\n";
        
        $createAuthLogs = "
        CREATE TABLE user_auth_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            auth_type ENUM('password', 'otp', '2fa', 'backup_code') NOT NULL,
            action VARCHAR(50) NOT NULL,
            details JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_auth_type (auth_type),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        
        $conn->exec($createAuthLogs);
        echo "✅ Created user_auth_logs table\n";
    } else {
        echo "✅ user_auth_logs table exists\n";
    }
    
    echo "\n✅ All required tables are now ready!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
