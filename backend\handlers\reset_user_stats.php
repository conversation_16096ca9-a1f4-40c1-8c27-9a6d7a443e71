<?php
require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $conn = getDBConnection();
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['user_id'])) {
        throw new Exception('User ID is required');
    }
    
    $userId = (int)$input['user_id'];
    
    // Verify user exists
    $stmt = $conn->prepare("SELECT user_id, username FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    $conn->beginTransaction();
    
    // Reset user stats in users table
    $stmt = $conn->prepare("
        UPDATE users 
        SET points = 0,
            total_points = 0,
            wins = 0,
            draws = 0,
            losses = 0,
            current_streak = 0,
            highest_streak = 0
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    
    // Reset user league stats
    $stmt = $conn->prepare("
        UPDATE user_league_stats 
        SET points = 0,
            wins = 0,
            draws = 0,
            losses = 0,
            streak = 0
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    
    // Reset leaderboard entries
    $stmt = $conn->prepare("
        UPDATE leaderboards 
        SET points = 0,
            wins = 0,
            draws = 0,
            losses = 0
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    
    // Reset league membership stats
    $stmt = $conn->prepare("
        UPDATE league_memberships 
        SET current_points = 0,
            wins = 0,
            draws = 0,
            losses = 0,
            total_bets = 0
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => "User stats reset successfully for {$user['username']}"
    ]);
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
