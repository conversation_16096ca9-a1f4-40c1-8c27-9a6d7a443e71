<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Database connection
    $conn = new PDO(
        "mysql:host=localhost;dbname=fanbet247",
        'root',
        'root',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Create general_settings table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS general_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_name VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    $conn->exec($createTableSQL);

    // Insert default general settings if table is empty
    $checkStmt = $conn->query("SELECT COUNT(*) as count FROM general_settings");
    $count = $checkStmt->fetch()['count'];

    if ($count == 0) {
        $defaultSettings = [
            ['site_name', 'FanBet247', 'The name of the website'],
            ['site_logo', 'uploads/logo/default_logo.png', 'Path to the site logo'],
            ['contact_email', '<EMAIL>', 'Contact email address'],
            ['contact_phone', '+1234567890', 'Contact phone number'],
            ['facebook_url', '', 'Facebook page URL'],
            ['twitter_url', '', 'Twitter profile URL'],
            ['instagram_url', '', 'Instagram profile URL'],
            ['about_text', 'FanBet247 is a sports betting platform.', 'About us text'],
            ['terms_conditions', 'Terms and conditions text goes here.', 'Terms and conditions text'],
            ['privacy_policy', 'Privacy policy text goes here.', 'Privacy policy text'],
            ['footer_text', '© 2024 FanBet247. All rights reserved.', 'Footer text'],
            ['site_favicon', 'uploads/favicon/favicon.ico', 'Path to the site favicon']
        ];

        $insertStmt = $conn->prepare("INSERT INTO general_settings (setting_name, setting_value, description) VALUES (?, ?, ?)");
        foreach ($defaultSettings as $setting) {
            $insertStmt->execute($setting);
        }
    }
    
    $stmt = $conn->prepare("SELECT setting_name, setting_value, description FROM general_settings");
    $stmt->execute();

    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_name']] = [
            'value' => $row['setting_value'],
            'description' => $row['description']
        ];
    }

    echo json_encode([
        "success" => true,
        "settings" => $settings
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
}
?>
