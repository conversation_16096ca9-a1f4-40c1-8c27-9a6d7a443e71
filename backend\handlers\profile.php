<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$username = $_GET['username'] ?? null;

if (!$username) {
    echo json_encode(['success' => false, 'message' => 'Username required']);
    exit;
}

// Get user profile with comprehensive stats
$query = "SELECT
            u.user_id,
            u.username,
            u.email,
            u.favorite_team,
            u.balance,
            u.points,
            u.wins,
            u.draws,
            u.losses,
            COUNT(DISTINCT b.bet_id) as total_bets,
            ROUND(
                (u.wins / NULLIF((u.wins + u.draws + u.losses), 0)) * 100,
                2
            ) as win_rate,
            COUNT(DISTINCT f.friend_id) as friends_count,
            COUNT(DISTINCT fr.id) as friend_requests_count
          FROM users u
          LEFT JOIN bets b ON (u.user_id = b.user1_id OR u.user_id = b.user2_id)
          LEFT JOIN user_friends f ON (u.user_id = f.user_id AND f.status = 'accepted')
          LEFT JOIN user_friends fr ON (u.user_id = fr.friend_id AND fr.status = 'pending')
          WHERE u.username = :username
          GROUP BY u.user_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':username', $username);
$stmt->execute();
$profile = $stmt->fetch(PDO::FETCH_ASSOC);

// **IMPORTANT FIX:** Get userId from the $profile array
if ($profile) {
    $userId = $profile['user_id']; 
} else {
    echo json_encode(['success' => false, 'message' => 'Profile not found']);
    exit; // Exit if profile is not found
}

// Get recent bets
$betsQuery = "SELECT b.*, 
              c.team_a, c.team_b, c.logo1, c.logo2,
              u1.username as user1_name, u2.username as user2_name
              FROM bets b
              JOIN challenges c ON b.challenge_id = c.challenge_id
              JOIN users u1 ON b.user1_id = u1.user_id
              JOIN users u2 ON b.user2_id = u2.user_id
              WHERE (b.user1_id = :userId OR b.user2_id = :userId)
              ORDER BY b.created_at DESC
              LIMIT 5";

$betsStmt = $conn->prepare($betsQuery);
$betsStmt->bindParam(':userId', $userId); // Now $userId is defined
$betsStmt->execute();
$recentBets = $betsStmt->fetchAll(PDO::FETCH_ASSOC);

// Get friends list
$friendsQuery = "SELECT u.*, 
    COUNT(b.bet_id) as total_bets,
    ROUND(SUM(CASE WHEN b.outcome = 'win' THEN 1 ELSE 0 END) / 
          NULLIF(COUNT(DISTINCT b.bet_id), 0) * 100, 2) as win_rate
FROM users u
INNER JOIN user_friends uf ON (u.user_id = uf.friend_id OR u.user_id = uf.user_id)
LEFT JOIN bets b ON u.user_id = b.user1_id OR u.user_id = b.user2_id
WHERE (uf.user_id = :userId OR uf.friend_id = :userId)
AND u.user_id != :userId
AND uf.status = 'accepted'
GROUP BY u.user_id";

$friendsStmt = $conn->prepare($friendsQuery);
$friendsStmt->bindParam(':userId', $userId); // Now $userId is defined
$friendsStmt->execute();
$friends = $friendsStmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate leaderboard position based on points
$leaderboardQuery = "SELECT COUNT(*) + 1 as position
                     FROM users
                     WHERE points > :userPoints";
$leaderboardStmt = $conn->prepare($leaderboardQuery);
$leaderboardStmt->bindParam(':userPoints', $profile['points']);
$leaderboardStmt->execute();
$leaderboardResult = $leaderboardStmt->fetch(PDO::FETCH_ASSOC);

// Add this after getting the profile data
$isOwnProfile = false;
if (isset($_GET['currentUserId'])) {
    $isOwnProfile = ($userId == $_GET['currentUserId']);
}

if ($profile) {
    // Convert numeric fields to proper numbers
    $profile['balance'] = floatval($profile['balance']);
    $profile['points'] = intval($profile['points']);
    $profile['wins'] = intval($profile['wins']);
    $profile['draws'] = intval($profile['draws']);
    $profile['losses'] = intval($profile['losses']);
    $profile['total_bets'] = intval($profile['total_bets']);
    $profile['win_rate'] = floatval($profile['win_rate']);
    $profile['friends_count'] = intval($profile['friends_count']);
    $profile['friend_requests_count'] = intval($profile['friend_requests_count']);

    // Add calculated fields
    $profile['leaderboard_position'] = intval($leaderboardResult['position']);
    $profile['leaderboard_score'] = $profile['points'];
    $profile['friend_requests'] = $profile['friend_requests_count'];
    $profile['friends'] = $friends;
    $profile['recent_bets'] = $recentBets;
    $profile['isOwnProfile'] = $isOwnProfile;

    echo json_encode(['success' => true, 'profile' => $profile]);
} else {
    echo json_encode(['success' => false, 'message' => 'Profile not found']);
}
