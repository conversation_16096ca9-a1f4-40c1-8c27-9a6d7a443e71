<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

require_once '../includes/db_connect.php';

try {
    // Get all users in league 7 with their stats
    $query = "SELECT 
        u.user_id,
        u.username,
        u.full_name,
        ul.join_date,
        ul.status as league_status,
        lm.status as membership_status,
        lm.deposit_amount,
        lm.current_points,
        lm.wins,
        lm.draws,
        lm.losses,
        lm.total_bets,
        ((lm.wins * 3) + lm.draws) as points
    FROM users u
    INNER JOIN user_leagues ul ON u.user_id = ul.user_id AND ul.league_id = 7 AND ul.status = 'active'
    LEFT JOIN league_memberships lm ON lm.user_id = u.user_id AND lm.league_id = 7
    ORDER BY points DESC, lm.wins DESC";

    $stmt = $conn->prepare($query);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add debug info
    error_log("Retrieved users data: " . print_r($users, true));

    echo json_encode([
        'status' => 200,
        'message' => 'League users data retrieved',
        'data' => $users
    ]);

} catch (Exception $e) {
    error_log("Error in check_league_users.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'status' => 500,
        'message' => 'Error: ' . $e->getMessage()
    ]);
} 