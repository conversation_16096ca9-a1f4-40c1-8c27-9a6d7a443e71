<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, PUT, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

// Simple admin authentication check
function getAdminId() {
    $headers = getallheaders();
    if (isset($headers['X-Admin-ID'])) {
        return (int)$headers['X-Admin-ID'];
    }
    
    $input = json_decode(file_get_contents("php://input"), true);
    if (isset($input['admin_id'])) {
        return (int)$input['admin_id'];
    }
    
    return 1; // Default admin ID for now
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }
    
    // Only allow POST and PUT methods
    if (!in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT'])) {
        jsonResponse(405, "Method not allowed");
    }
    
    // Get input data
    $input = json_decode(file_get_contents("php://input"), true);
    
    if (!$input) {
        jsonResponse(400, "Invalid JSON input");
    }
    
    // Validate required fields
    $currencyId = (int)($input['currency_id'] ?? 0);
    $rateToFancoin = (float)($input['rate_to_fancoin'] ?? 0);
    $notes = trim($input['notes'] ?? '');
    $adminId = getAdminId();
    
    if (!$currencyId) {
        jsonResponse(400, "Currency ID is required");
    }
    
    if ($rateToFancoin <= 0) {
        jsonResponse(400, "Exchange rate must be greater than 0");
    }
    
    // Verify currency exists and is active
    $currencyStmt = $conn->prepare("SELECT id, currency_code, currency_name, currency_symbol, is_active FROM currencies WHERE id = ?");
    $currencyStmt->execute([$currencyId]);
    $currency = $currencyStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$currency) {
        jsonResponse(404, "Currency not found");
    }
    
    if (!$currency['is_active']) {
        jsonResponse(400, "Cannot update exchange rate for inactive currency");
    }
    
    // Check if exchange rate already exists for this currency
    $existingRateStmt = $conn->prepare("SELECT id FROM exchange_rates WHERE currency_id = ?");
    $existingRateStmt->execute([$currencyId]);
    $existingRate = $existingRateStmt->fetch();
    
    if ($existingRate) {
        // Update existing rate
        $updateStmt = $conn->prepare("
            UPDATE exchange_rates 
            SET rate_to_fancoin = ?, notes = ?, updated_at = NOW(), updated_by_admin_id = ?
            WHERE currency_id = ?
        ");
        
        $success = $updateStmt->execute([$rateToFancoin, $notes, $adminId, $currencyId]);
        $rateId = $existingRate['id'];
    } else {
        // Insert new rate
        $insertStmt = $conn->prepare("
            INSERT INTO exchange_rates (currency_id, rate_to_fancoin, notes, created_at, updated_at, updated_by_admin_id)
            VALUES (?, ?, ?, NOW(), NOW(), ?)
        ");
        
        $success = $insertStmt->execute([$currencyId, $rateToFancoin, $notes, $adminId]);
        $rateId = $conn->lastInsertId();
    }
    
    if ($success) {
        // Get the updated rate information
        $rateInfoStmt = $conn->prepare("
            SELECT 
                er.id as rate_id,
                er.rate_to_fancoin,
                er.notes,
                er.updated_at,
                er.updated_by_admin_id,
                c.currency_code,
                c.currency_name,
                c.currency_symbol,
                a.username as updated_by_admin_username
            FROM exchange_rates er
            JOIN currencies c ON er.currency_id = c.id
            LEFT JOIN admins a ON er.updated_by_admin_id = a.admin_id
            WHERE er.currency_id = ?
            ORDER BY er.updated_at DESC
            LIMIT 1
        ");
        
        $rateInfoStmt->execute([$currencyId]);
        $rateInfo = $rateInfoStmt->fetch(PDO::FETCH_ASSOC);
        
        $responseData = [
            'rate_id' => (int)$rateInfo['rate_id'],
            'currency_id' => $currencyId,
            'currency_code' => $rateInfo['currency_code'],
            'currency_name' => $rateInfo['currency_name'],
            'currency_symbol' => $rateInfo['currency_symbol'],
            'rate_to_fancoin' => (float)$rateInfo['rate_to_fancoin'],
            'formatted_rate' => $rateInfo['currency_symbol'] . ' ' . number_format($rateInfo['rate_to_fancoin'], 4),
            'conversion_example' => '1 FanCoin = ' . $rateInfo['currency_symbol'] . number_format($rateInfo['rate_to_fancoin'], 2),
            'notes' => $rateInfo['notes'],
            'updated_at' => $rateInfo['updated_at'],
            'updated_by_admin_id' => (int)$rateInfo['updated_by_admin_id'],
            'updated_by_admin_username' => $rateInfo['updated_by_admin_username']
        ];
        
        jsonResponse(200, "Exchange rate updated successfully", $responseData);
    } else {
        jsonResponse(500, "Failed to update exchange rate");
    }
    
} catch (PDOException $e) {
    error_log("Database error in update_exchange_rate.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    error_log("General error in update_exchange_rate.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred while updating exchange rate");
}
?>
