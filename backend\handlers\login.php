<?php
/**
 * Simple Login Handler for FanBet247
 * Clean, simple authentication system
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Only POST method allowed']);
    exit;
}

// Start session
session_start();

// Include database connection
require_once '../includes/db_connect.php';

try {
    // Get database connection
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get input data
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception("Invalid JSON data received");
    }
    
    $username = trim($data['username'] ?? '');
    $password = $data['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        throw new Exception("Username and password are required");
    }
    
    // Find user by username or email
    $stmt = $conn->prepare("
        SELECT user_id, username, email, password_hash, full_name, role, status, balance, points,
               auth_method, otp_enabled, tfa_enabled
        FROM users
        WHERE (username = ? OR email = ?) AND status = 'active'
    ");
    $stmt->execute([$username, $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("Invalid username or password");
    }
    
    // Verify password
    $passwordValid = password_verify($password, $user['password_hash']);

    if (!$passwordValid) {
        throw new Exception("Invalid username or password");
    }

    // Check if additional authentication is required
    $authMethod = $user['auth_method'] ?? 'password_only';
    $requiresAdditionalAuth = false;
    $nextSteps = [];

    if ($authMethod !== 'password_only') {
        $requiresAdditionalAuth = true;

        // Determine which additional authentication steps are needed
        if (strpos($authMethod, 'otp') !== false && $user['otp_enabled']) {
            $nextSteps[] = 'otp';
        }
        if (strpos($authMethod, '2fa') !== false && $user['tfa_enabled']) {
            $nextSteps[] = '2fa';
        }
    }

    if ($requiresAdditionalAuth && !empty($nextSteps)) {
        // Store partial login state for multi-step authentication
        $_SESSION['partial_login'] = [
            'user_id' => $user['user_id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'auth_method' => $authMethod,
            'completed_steps' => ['password'],
            'remaining_steps' => $nextSteps,
            'timestamp' => time()
        ];

        // Return partial success requiring additional authentication
        echo json_encode([
            'success' => true,
            'requires_additional_auth' => true,
            'auth_method' => $authMethod,
            'next_steps' => $nextSteps,
            'user_id' => $user['user_id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'message' => 'Password verified. Additional authentication required.'
        ]);
    } else {
        // Complete login (password only or no additional auth required)
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['login_time'] = time();

        // Clear any partial login state
        unset($_SESSION['partial_login']);

        // Generate session token
        $token = session_id();

        // Update last_active if column exists
        try {
            $stmt = $conn->prepare("UPDATE users SET last_active = NOW() WHERE user_id = ?");
            $stmt->execute([$user['user_id']]);
        } catch (PDOException $e) {
            // Column might not exist, ignore this error
            error_log("Could not update last_active: " . $e->getMessage());
        }

        // Return complete success response
        echo json_encode([
            'success' => true,
            'message' => 'Login successful',
            'user' => [
                'id' => $user['user_id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'full_name' => $user['full_name'],
                'role' => $user['role'],
                'balance' => $user['balance'],
                'points' => $user['points']
            ],
            'token' => $token
        ]);
    }
    
} catch (Exception $e) {
    error_log("Login error: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
