<?php
/**
 * User Payment Methods Management API
 * Handles user payment methods for withdrawals
 */

require_once '../config/database.php';
require_once '../config/cors.php';

header('Content-Type: application/json');

// Get request method and user authentication
$method = $_SERVER['REQUEST_METHOD'];
$user_id = $_SESSION['user_id'] ?? null;

if (!$user_id) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

try {
    switch ($method) {
        case 'GET':
            handleGetPaymentMethods($pdo, $user_id);
            break;
        case 'POST':
            handleAddPaymentMethod($pdo, $user_id);
            break;
        case 'PUT':
            handleUpdatePaymentMethod($pdo, $user_id);
            break;
        case 'DELETE':
            handleDeletePaymentMethod($pdo, $user_id);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleGetPaymentMethods($pdo, $user_id) {
    $stmt = $pdo->prepare("
        SELECT 
            payment_method_id,
            method_type,
            method_name,
            account_details,
            is_primary,
            is_verified,
            status,
            created_at
        FROM user_payment_methods 
        WHERE user_id = ? 
        ORDER BY is_primary DESC, created_at DESC
    ");
    
    $stmt->execute([$user_id]);
    $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Decode account details for each method
    foreach ($methods as &$method) {
        $method['account_details'] = json_decode($method['account_details'], true);
    }
    
    echo json_encode([
        'success' => true,
        'data' => $methods
    ]);
}

function handleAddPaymentMethod($pdo, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $required_fields = ['method_type', 'method_name', 'account_details'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "Field '$field' is required"]);
            return;
        }
    }
    
    // Validate method type
    $valid_types = ['bank_account', 'crypto_wallet', 'paypal', 'mobile_money'];
    if (!in_array($input['method_type'], $valid_types)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid method type']);
        return;
    }
    
    // Validate account details based on method type
    $account_details = validateAccountDetails($input['method_type'], $input['account_details']);
    if (!$account_details) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid account details for method type']);
        return;
    }
    
    $pdo->beginTransaction();
    
    try {
        // If this is set as primary, unset other primary methods
        if (isset($input['is_primary']) && $input['is_primary']) {
            $stmt = $pdo->prepare("UPDATE user_payment_methods SET is_primary = 0 WHERE user_id = ?");
            $stmt->execute([$user_id]);
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO user_payment_methods 
            (user_id, method_type, method_name, account_details, is_primary, status) 
            VALUES (?, ?, ?, ?, ?, 'pending_verification')
        ");
        
        $stmt->execute([
            $user_id,
            $input['method_type'],
            $input['method_name'],
            json_encode($account_details),
            isset($input['is_primary']) ? (int)$input['is_primary'] : 0
        ]);
        
        $payment_method_id = $pdo->lastInsertId();
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Payment method added successfully',
            'payment_method_id' => $payment_method_id
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function handleUpdatePaymentMethod($pdo, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['payment_method_id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Payment method ID is required']);
        return;
    }
    
    // Verify ownership
    $stmt = $pdo->prepare("SELECT payment_method_id FROM user_payment_methods WHERE payment_method_id = ? AND user_id = ?");
    $stmt->execute([$input['payment_method_id'], $user_id]);
    
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Payment method not found']);
        return;
    }
    
    $pdo->beginTransaction();
    
    try {
        // If setting as primary, unset other primary methods
        if (isset($input['is_primary']) && $input['is_primary']) {
            $stmt = $pdo->prepare("UPDATE user_payment_methods SET is_primary = 0 WHERE user_id = ? AND payment_method_id != ?");
            $stmt->execute([$user_id, $input['payment_method_id']]);
        }
        
        $update_fields = [];
        $params = [];
        
        if (isset($input['method_name'])) {
            $update_fields[] = "method_name = ?";
            $params[] = $input['method_name'];
        }
        
        if (isset($input['is_primary'])) {
            $update_fields[] = "is_primary = ?";
            $params[] = (int)$input['is_primary'];
        }
        
        if (isset($input['account_details'])) {
            // Re-validate account details
            $method_type_stmt = $pdo->prepare("SELECT method_type FROM user_payment_methods WHERE payment_method_id = ?");
            $method_type_stmt->execute([$input['payment_method_id']]);
            $method_type = $method_type_stmt->fetchColumn();
            
            $account_details = validateAccountDetails($method_type, $input['account_details']);
            if (!$account_details) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid account details']);
                return;
            }
            
            $update_fields[] = "account_details = ?";
            $params[] = json_encode($account_details);
            $update_fields[] = "status = 'pending_verification'"; // Reset verification status
        }
        
        if (empty($update_fields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $params[] = $input['payment_method_id'];
        $params[] = $user_id;
        
        $sql = "UPDATE user_payment_methods SET " . implode(', ', $update_fields) . " WHERE payment_method_id = ? AND user_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Payment method updated successfully'
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function handleDeletePaymentMethod($pdo, $user_id) {
    $payment_method_id = $_GET['payment_method_id'] ?? null;
    
    if (!$payment_method_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Payment method ID is required']);
        return;
    }
    
    // Check if method exists and belongs to user
    $stmt = $pdo->prepare("SELECT payment_method_id FROM user_payment_methods WHERE payment_method_id = ? AND user_id = ?");
    $stmt->execute([$payment_method_id, $user_id]);
    
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Payment method not found']);
        return;
    }
    
    // Check if method is used in pending withdrawals
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM withdrawal_requests WHERE payment_method_id = ? AND status IN ('pending', 'approved', 'processing')");
    $stmt->execute([$payment_method_id]);
    
    if ($stmt->fetchColumn() > 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Cannot delete payment method with pending withdrawals']);
        return;
    }
    
    $stmt = $pdo->prepare("DELETE FROM user_payment_methods WHERE payment_method_id = ? AND user_id = ?");
    $stmt->execute([$payment_method_id, $user_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Payment method deleted successfully'
    ]);
}

function validateAccountDetails($method_type, $details) {
    switch ($method_type) {
        case 'bank_account':
            $required = ['account_number', 'bank_name', 'account_holder_name'];
            break;
        case 'crypto_wallet':
            $required = ['wallet_address', 'currency_type'];
            break;
        case 'paypal':
            $required = ['email'];
            break;
        case 'mobile_money':
            $required = ['phone_number', 'provider'];
            break;
        default:
            return false;
    }
    
    foreach ($required as $field) {
        if (!isset($details[$field]) || empty($details[$field])) {
            return false;
        }
    }
    
    return $details;
}
?>
