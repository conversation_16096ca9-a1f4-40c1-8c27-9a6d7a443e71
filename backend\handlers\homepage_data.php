<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
include_once '../includes/error_logger.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Get section parameter for specific data requests
    $section = isset($_GET['section']) ? $_GET['section'] : 'all';
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : null;

    $response = [
        'status' => 'success',
        'message' => 'Homepage data retrieved successfully',
        'data' => []
    ];

    switch ($section) {
        case 'leagues':
            $response['data'] = getTopLeagues($conn, $limit ?: 6);
            break;
        case 'challenges':
            $response['data'] = getLiveChallenges($conn, $limit ?: 8);
            break;
        case 'bets':
            $response['data'] = getRecentBets($conn, $limit ?: 10);
            break;
        case 'blog':
            $response['data'] = getBlogPosts($conn, $limit ?: 4);
            break;
        case 'stats':
            $response['data'] = getSiteStatistics($conn);
            break;
        case 'all':
        default:
            $response['data'] = [
                'topLeagues' => getTopLeagues($conn, 6),
                'liveChallenges' => getLiveChallenges($conn, 8),
                'recentBets' => getRecentBets($conn, 10),
                'blogPosts' => getBlogPosts($conn, 4),
                'siteStats' => getSiteStatistics($conn)
            ];
            break;
    }

    echo json_encode($response);

} catch (Exception $e) {
    error_log("Homepage data error: " . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to retrieve homepage data',
        'error' => $e->getMessage()
    ]);
}

/**
 * Get top performing leagues with statistics
 */
function getTopLeagues($conn, $limit = 6) {
    try {
        $stmt = $conn->prepare("
            SELECT 
                l.league_id,
                l.name,
                l.theme_color,
                l.description,
                l.icon_path,
                l.banner_path,
                l.status,
                l.min_bet_amount,
                l.max_bet_amount,
                COALESCE(ls.total_participants, 0) as total_participants,
                COALESCE(active_members.count, 0) as active_members,
                COALESCE(prize_pool.total, 0) as prize_pool
            FROM leagues l
            LEFT JOIN league_seasons ls ON l.league_id = ls.league_id AND ls.status = 'active'
            LEFT JOIN (
                SELECT 
                    lm.league_id,
                    COUNT(lm.membership_id) as count
                FROM league_memberships lm
                WHERE lm.status = 'active'
                GROUP BY lm.league_id
            ) active_members ON l.league_id = active_members.league_id
            LEFT JOIN (
                SELECT 
                    lm.league_id,
                    SUM(lm.deposit_amount) as total
                FROM league_memberships lm
                WHERE lm.status = 'active'
                GROUP BY lm.league_id
            ) prize_pool ON l.league_id = prize_pool.league_id
            WHERE l.status IN ('active', 'upcoming')
            ORDER BY active_members DESC, prize_pool DESC
            LIMIT :limit
        ");
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Error fetching top leagues: " . $e->getMessage());
        return [];
    }
}

/**
 * Get live challenges with betting statistics
 */
function getLiveChallenges($conn, $limit = 8) {
    try {
        $stmt = $conn->prepare("
            SELECT 
                c.challenge_id,
                c.team_a,
                c.team_b,
                c.logo1,
                c.logo2,
                c.odds_team_a,
                c.odds_team_b,
                c.odds_draw,
                c.match_date,
                c.status,
                c.match_type,
                COALESCE(bet_stats.total_bets, 0) as total_bets,
                COALESCE(bet_stats.active_bets, 0) as active_bets,
                TIMESTAMPDIFF(SECOND, NOW(), c.match_date) as time_remaining_seconds
            FROM challenges c
            LEFT JOIN (
                SELECT 
                    b.challenge_id,
                    COUNT(b.bet_id) as total_bets,
                    COUNT(CASE WHEN b.user2_id IS NOT NULL THEN b.bet_id END) as active_bets
                FROM bets b
                GROUP BY b.challenge_id
            ) bet_stats ON c.challenge_id = bet_stats.challenge_id
            WHERE c.status = 'Open' 
            AND c.match_date > NOW()
            ORDER BY c.match_date ASC
            LIMIT :limit
        ");
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        $challenges = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Calculate time remaining for each challenge
        foreach ($challenges as &$challenge) {
            $challenge['time_remaining'] = max(0, intval($challenge['time_remaining_seconds']));
            unset($challenge['time_remaining_seconds']);
        }
        
        return $challenges;
        
    } catch (Exception $e) {
        error_log("Error fetching live challenges: " . $e->getMessage());
        return [];
    }
}

/**
 * Get recent betting activity
 */
function getRecentBets($conn, $limit = 10) {
    try {
        $stmt = $conn->prepare("
            SELECT 
                b.bet_id,
                b.amount_user1 as amount,
                b.bet_choice_user1 as bet_choice,
                b.created_at,
                b.bet_status as status,
                b.user1_outcome,
                u.username,
                c.team_a,
                c.team_b,
                c.match_date
            FROM bets b
            JOIN users u ON b.user1_id = u.user_id
            JOIN challenges c ON b.challenge_id = c.challenge_id
            WHERE b.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY b.created_at DESC
            LIMIT :limit
        ");
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Error fetching recent bets: " . $e->getMessage());
        return [];
    }
}

/**
 * Get blog posts (placeholder implementation)
 * Note: This requires the blog_posts table to be created
 */
function getBlogPosts($conn, $limit = 4) {
    try {
        // Check if blog_posts table exists
        $stmt = $conn->query("SHOW TABLES LIKE 'blog_posts'");
        if ($stmt->rowCount() == 0) {
            // Return sample blog posts if table doesn't exist
            return getSampleBlogPosts($limit);
        }
        
        $stmt = $conn->prepare("
            SELECT 
                post_id,
                title,
                excerpt,
                featured_image,
                author_name,
                category,
                published_at,
                status
            FROM blog_posts
            WHERE status = 'published'
            ORDER BY published_at DESC
            LIMIT :limit
        ");
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Error fetching blog posts: " . $e->getMessage());
        return getSampleBlogPosts($limit);
    }
}

/**
 * Get site statistics
 */
function getSiteStatistics($conn) {
    try {
        $stats = [];
        
        // Total users
        $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users");
        $stats['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];
        
        // Active leagues
        $stmt = $conn->query("SELECT COUNT(*) as active_leagues FROM leagues WHERE status = 'active'");
        $stats['active_leagues'] = $stmt->fetch(PDO::FETCH_ASSOC)['active_leagues'];
        
        // Total bets
        $stmt = $conn->query("SELECT COUNT(*) as total_bets FROM bets");
        $stats['total_bets'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_bets'];
        
        // Active challenges
        $stmt = $conn->query("SELECT COUNT(*) as active_challenges FROM challenges WHERE status = 'Open'");
        $stats['active_challenges'] = $stmt->fetch(PDO::FETCH_ASSOC)['active_challenges'];
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Error fetching site statistics: " . $e->getMessage());
        return [
            'total_users' => 0,
            'active_leagues' => 0,
            'total_bets' => 0,
            'active_challenges' => 0
        ];
    }
}

/**
 * Sample blog posts for when blog_posts table doesn't exist
 */
function getSampleBlogPosts($limit = 4) {
    $samplePosts = [
        [
            'post_id' => 1,
            'title' => 'Top 5 Soccer Betting Strategies for Beginners',
            'excerpt' => 'Learn the fundamental strategies that can help you make smarter betting decisions and improve your success rate.',
            'featured_image' => '/images/blog/betting-strategies.jpg',
            'author_name' => 'FanBet247 Expert',
            'category' => 'Strategy',
            'published_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
            'status' => 'published'
        ],
        [
            'post_id' => 2,
            'title' => 'Understanding Soccer Odds: A Complete Guide',
            'excerpt' => 'Master the art of reading and interpreting soccer betting odds to make more informed wagering decisions.',
            'featured_image' => '/images/blog/understanding-odds.jpg',
            'author_name' => 'Sports Analyst',
            'category' => 'Education',
            'published_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
            'status' => 'published'
        ],
        [
            'post_id' => 3,
            'title' => 'Premier League Preview: Key Matches This Week',
            'excerpt' => 'Get insights into the most exciting Premier League fixtures and discover the best betting opportunities.',
            'featured_image' => '/images/blog/premier-league.jpg',
            'author_name' => 'Match Predictor',
            'category' => 'Analysis',
            'published_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
            'status' => 'published'
        ],
        [
            'post_id' => 4,
            'title' => 'Bankroll Management: Protecting Your Betting Budget',
            'excerpt' => 'Learn essential bankroll management techniques to ensure long-term success in sports betting.',
            'featured_image' => '/images/blog/bankroll-management.jpg',
            'author_name' => 'Financial Expert',
            'category' => 'Money Management',
            'published_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
            'status' => 'published'
        ]
    ];
    
    return array_slice($samplePosts, 0, $limit);
}
?>