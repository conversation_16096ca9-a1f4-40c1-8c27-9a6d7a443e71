<?php
/**
 * Admin 2FA Backup Codes Management
 * Handles viewing and regenerating backup codes for admin 2FA
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    $adminId = $_GET['adminId'] ?? null;
    if (!$adminId) {
        throw new Exception("Admin ID required");
    }
    
    // Verify admin exists
    $stmt = $conn->prepare("SELECT admin_id, username, email, role FROM admins WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Admin not found");
    }
    
    // Get admin's 2FA setup
    $stmt = $conn->prepare("
        SELECT backup_codes, is_enabled, setup_completed 
        FROM admin_2fa 
        WHERE admin_id = ? AND is_enabled = 1 AND setup_completed = 1
    ");
    $stmt->execute([$adminId]);
    $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tfaSetup) {
        throw new Exception("2FA is not set up for this admin account");
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // View current backup codes
        
        $backupCodes = json_decode($tfaSetup['backup_codes'], true);
        
        echo json_encode([
            'success' => true,
            'backup_codes' => $backupCodes,
            'total_codes' => count($backupCodes),
            'message' => 'Backup codes retrieved successfully'
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Regenerate backup codes
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Optional: require current password or 2FA code for security
        if (isset($input['require_verification']) && $input['require_verification']) {
            if (!isset($input['current_password']) && !isset($input['verification_code'])) {
                throw new Exception("Current password or 2FA verification code required for backup code regeneration");
            }
            
            if (isset($input['current_password'])) {
                // Verify current password
                $stmt = $conn->prepare("SELECT password_hash FROM admins WHERE admin_id = ?");
                $stmt->execute([$adminId]);
                $adminData = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!password_verify($input['current_password'], $adminData['password_hash'])) {
                    throw new Exception("Invalid current password");
                }
            }
            
            if (isset($input['verification_code'])) {
                // Verify 2FA code
                require_once '../vendor/autoload.php';
                use PragmaRX\Google2FA\Google2FA;
                
                $stmt = $conn->prepare("SELECT secret_key FROM admin_2fa WHERE admin_id = ?");
                $stmt->execute([$adminId]);
                $secretKey = $stmt->fetch(PDO::FETCH_ASSOC)['secret_key'];
                
                $google2fa = new Google2FA();
                if (!$google2fa->verifyKey($secretKey, $input['verification_code'])) {
                    throw new Exception("Invalid 2FA verification code");
                }
            }
        }
        
        // Get backup codes count setting
        $stmt = $conn->prepare("SELECT setting_value FROM admin_auth_settings WHERE setting_name = 'admin_backup_codes_count'");
        $stmt->execute();
        $backupCodesCount = $stmt->fetch(PDO::FETCH_ASSOC)['setting_value'] ?? 10;
        
        // Generate new backup codes
        $newBackupCodes = [];
        for ($i = 0; $i < $backupCodesCount; $i++) {
            $newBackupCodes[] = strtoupper(substr(bin2hex(random_bytes(4)), 0, 8));
        }
        
        $conn->beginTransaction();
        
        // Update backup codes
        $stmt = $conn->prepare("
            UPDATE admin_2fa 
            SET backup_codes = ?, updated_at = NOW() 
            WHERE admin_id = ?
        ");
        $stmt->execute([json_encode($newBackupCodes), $adminId]);
        
        // Log backup codes regeneration
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, '2fa', 'backup_codes_regenerated', ?, ?, ?)
        ");
        $stmt->execute([
            $adminId,
            json_encode(['codes_count' => count($newBackupCodes)]),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'backup_codes' => $newBackupCodes,
            'total_codes' => count($newBackupCodes),
            'message' => 'Backup codes regenerated successfully',
            'warning' => 'Please save these new backup codes in a secure location. The old codes are no longer valid.'
        ]);
    }
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
