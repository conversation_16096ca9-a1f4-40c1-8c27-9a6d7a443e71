<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, PUT, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

// Simple admin authentication check
function getAdminId() {
    // In a real implementation, this would validate the admin session/token
    // For now, we'll check if admin_id is provided in the request
    $headers = getallheaders();
    if (isset($headers['Admin-ID'])) {
        return (int)$headers['Admin-ID'];
    }
    
    // Fallback: get from POST data
    $input = json_decode(file_get_contents("php://input"), true);
    if (isset($input['admin_id'])) {
        return (int)$input['admin_id'];
    }
    
    return null;
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }
    
    // Only allow POST and PUT methods
    if (!in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT'])) {
        jsonResponse(405, "Method not allowed");
    }
    
    // Get request data
    $input = json_decode(file_get_contents("php://input"), true);
    
    if (!$input) {
        jsonResponse(400, "Invalid JSON data");
    }
    
    // Validate required fields
    $currencyId = isset($input['currency_id']) ? (int)$input['currency_id'] : null;
    $rateToFanCoin = isset($input['rate_to_fancoin']) ? (float)$input['rate_to_fancoin'] : null;
    $notes = isset($input['notes']) ? trim($input['notes']) : '';
    $adminId = getAdminId();
    
    if (!$currencyId) {
        jsonResponse(400, "Currency ID is required");
    }
    
    if (!$rateToFanCoin || $rateToFanCoin <= 0) {
        jsonResponse(400, "Valid exchange rate is required (must be greater than 0)");
    }
    
    if (!$adminId) {
        jsonResponse(401, "Admin authentication required");
    }
    
    // Verify admin exists
    $stmt = $conn->prepare("SELECT admin_id, username FROM admins WHERE admin_id = :admin_id");
    $stmt->execute(['admin_id' => $adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        jsonResponse(401, "Invalid admin credentials");
    }
    
    // Verify currency exists and is active
    $stmt = $conn->prepare("SELECT id, currency_code, currency_name, currency_symbol FROM currencies WHERE id = :id AND is_active = 1");
    $stmt->execute(['id' => $currencyId]);
    $currency = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$currency) {
        jsonResponse(404, "Currency not found or inactive");
    }
    
    // Start transaction
    $conn->beginTransaction();
    
    try {
        // Check if exchange rate already exists for this currency
        $stmt = $conn->prepare("SELECT id FROM exchange_rates WHERE currency_id = :currency_id ORDER BY updated_at DESC LIMIT 1");
        $stmt->execute(['currency_id' => $currencyId]);
        $existingRate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingRate) {
            // Update existing rate
            $stmt = $conn->prepare("
                UPDATE exchange_rates 
                SET rate_to_fancoin = :rate_to_fancoin,
                    notes = :notes,
                    updated_by_admin_id = :admin_id,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = :rate_id
            ");
            
            $stmt->execute([
                'rate_to_fancoin' => $rateToFanCoin,
                'notes' => $notes,
                'admin_id' => $adminId,
                'rate_id' => $existingRate['id']
            ]);
            
            $rateId = $existingRate['id'];
            $action = 'updated';
        } else {
            // Insert new rate
            $stmt = $conn->prepare("
                INSERT INTO exchange_rates (currency_id, rate_to_fancoin, notes, updated_by_admin_id)
                VALUES (:currency_id, :rate_to_fancoin, :notes, :admin_id)
            ");
            
            $stmt->execute([
                'currency_id' => $currencyId,
                'rate_to_fancoin' => $rateToFanCoin,
                'notes' => $notes,
                'admin_id' => $adminId
            ]);
            
            $rateId = $conn->lastInsertId();
            $action = 'created';
        }
        
        // Log the action (optional - you can create an audit log table)
        error_log("Exchange rate {$action} by admin {$admin['username']} (ID: {$adminId}) for {$currency['currency_code']}: {$rateToFanCoin}");
        
        $conn->commit();
        
        // Return success response with updated rate information
        jsonResponse(200, "Exchange rate {$action} successfully", [
            'rate_id' => (int)$rateId,
            'currency_id' => (int)$currencyId,
            'currency_code' => $currency['currency_code'],
            'currency_name' => $currency['currency_name'],
            'currency_symbol' => $currency['currency_symbol'],
            'rate_to_fancoin' => (float)$rateToFanCoin,
            'formatted_rate' => $currency['currency_symbol'] . number_format($rateToFanCoin, 4),
            'conversion_example' => '1 FanCoin = ' . $currency['currency_symbol'] . number_format($rateToFanCoin, 2) . ' ' . $currency['currency_code'],
            'notes' => $notes,
            'updated_by_admin_id' => (int)$adminId,
            'updated_by_admin_username' => $admin['username'],
            'action' => $action,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
    
} catch (PDOException $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    error_log("Database error in update_exchange_rate.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    error_log("General error in update_exchange_rate.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred while updating exchange rate");
}
?>
