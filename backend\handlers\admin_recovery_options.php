<?php
/**
 * Admin Recovery Options
 * Handles backup recovery methods for admin authentication
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';
require_once '../includes/audit_logger.php';
require_once '../vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    $auditLogger = new AdminAuditLogger($conn);
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get recovery options for an admin
        
        $adminId = $_GET['adminId'] ?? null;
        if (!$adminId) {
            throw new Exception("Admin ID required");
        }

        // Verify admin exists
        $stmt = $conn->prepare("SELECT admin_id, username, email, role FROM admins WHERE admin_id = ?");
        $stmt->execute([$adminId]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$admin) {
            throw new Exception("Admin not found");
        }

        // Get 2FA setup status
        $stmt = $conn->prepare("
            SELECT 
                auth_type, 
                is_enabled, 
                setup_completed, 
                backup_codes,
                last_used 
            FROM admin_2fa 
            WHERE admin_id = ?
        ");
        $stmt->execute([$adminId]);
        $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);

        // Count remaining backup codes
        $remainingBackupCodes = 0;
        if ($tfaSetup && $tfaSetup['backup_codes']) {
            $backupCodes = json_decode($tfaSetup['backup_codes'], true);
            $remainingBackupCodes = count($backupCodes);
        }

        // Get recent recovery attempts
        $stmt = $conn->prepare("
            SELECT action, details, created_at 
            FROM admin_auth_logs 
            WHERE admin_id = ? AND action IN ('recovery_initiated', 'recovery_completed', 'backup_code_used')
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $stmt->execute([$adminId]);
        $recentRecovery = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'admin' => $admin,
            'tfa_setup' => $tfaSetup,
            'remaining_backup_codes' => $remainingBackupCodes,
            'recent_recovery' => $recentRecovery,
            'recovery_options' => [
                'backup_codes' => $tfaSetup && $tfaSetup['is_enabled'],
                'email_recovery' => true, // Always available
                'admin_override' => true  // Super admin can override
            ]
        ]);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle recovery request
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception("Invalid JSON input");
        }

        $action = $input['action'] ?? null;
        $adminId = $input['admin_id'] ?? null;

        if (!$adminId || !$action) {
            throw new Exception("Admin ID and action are required");
        }

        // Verify admin exists
        $stmt = $conn->prepare("SELECT admin_id, username, email FROM admins WHERE admin_id = ?");
        $stmt->execute([$adminId]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$admin) {
            throw new Exception("Admin not found");
        }

        switch ($action) {
            case 'generate_recovery_code':
                $result = generateRecoveryCode($conn, $auditLogger, $admin);
                break;
                
            case 'verify_recovery_code':
                $result = verifyRecoveryCode($conn, $auditLogger, $admin, $input);
                break;
                
            case 'reset_2fa':
                $result = reset2FA($conn, $auditLogger, $admin, $input);
                break;
                
            case 'regenerate_backup_codes':
                $result = regenerateBackupCodes($conn, $auditLogger, $admin, $input);
                break;
                
            default:
                throw new Exception("Invalid action");
        }

        echo json_encode($result);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Generate a recovery code and send via email
 */
function generateRecoveryCode($conn, $auditLogger, $admin) {
    // Generate 8-character recovery code
    $recoveryCode = strtoupper(substr(bin2hex(random_bytes(4)), 0, 8));
    $expiresAt = date('Y-m-d H:i:s', time() + 3600); // 1 hour expiry

    $conn->beginTransaction();

    try {
        // Invalidate any existing recovery codes
        $stmt = $conn->prepare("DELETE FROM admin_recovery_codes WHERE admin_id = ?");
        $stmt->execute([$admin['admin_id']]);

        // Insert new recovery code
        $stmt = $conn->prepare("
            INSERT INTO admin_recovery_codes (admin_id, recovery_code, expires_at, used) 
            VALUES (?, ?, ?, 0)
        ");
        $stmt->execute([$admin['admin_id'], $recoveryCode, $expiresAt]);

        // Send recovery email
        $emailSent = sendRecoveryEmail($conn, $admin, $recoveryCode);

        if (!$emailSent) {
            throw new Exception("Failed to send recovery email");
        }

        // Log the recovery initiation
        $auditLogger->logAuthEvent(
            $admin['admin_id'],
            'recovery',
            'recovery_initiated',
            ['expires_at' => $expiresAt, 'email' => $admin['email']],
            'INFO'
        );

        $conn->commit();

        return [
            'success' => true,
            'message' => 'Recovery code sent to your registered email address',
            'expires_at' => $expiresAt,
            'email_masked' => substr($admin['email'], 0, 2) . '***@' . substr($admin['email'], strpos($admin['email'], '@') + 1)
        ];

    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * Verify recovery code
 */
function verifyRecoveryCode($conn, $auditLogger, $admin, $input) {
    $recoveryCode = $input['recovery_code'] ?? null;
    
    if (!$recoveryCode) {
        throw new Exception("Recovery code is required");
    }

    // Get valid recovery code
    $stmt = $conn->prepare("
        SELECT id, recovery_code, expires_at, used 
        FROM admin_recovery_codes 
        WHERE admin_id = ? AND recovery_code = ? AND expires_at > NOW() AND used = 0
    ");
    $stmt->execute([$admin['admin_id'], strtoupper($recoveryCode)]);
    $codeRecord = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$codeRecord) {
        $auditLogger->logAuthEvent(
            $admin['admin_id'],
            'recovery',
            'recovery_failed',
            ['reason' => 'invalid_recovery_code'],
            'WARNING'
        );
        throw new Exception("Invalid or expired recovery code");
    }

    $conn->beginTransaction();

    try {
        // Mark recovery code as used
        $stmt = $conn->prepare("UPDATE admin_recovery_codes SET used = 1 WHERE id = ?");
        $stmt->execute([$codeRecord['id']]);

        // Generate temporary access token
        $accessToken = bin2hex(random_bytes(32));
        $tokenExpiry = date('Y-m-d H:i:s', time() + 1800); // 30 minutes

        $stmt = $conn->prepare("
            INSERT INTO admin_recovery_tokens (admin_id, access_token, expires_at) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            access_token = VALUES(access_token), 
            expires_at = VALUES(expires_at)
        ");
        $stmt->execute([$admin['admin_id'], $accessToken, $tokenExpiry]);

        // Log successful recovery
        $auditLogger->logAuthEvent(
            $admin['admin_id'],
            'recovery',
            'recovery_verified',
            ['access_token_expires' => $tokenExpiry],
            'INFO'
        );

        $conn->commit();

        return [
            'success' => true,
            'message' => 'Recovery code verified successfully',
            'access_token' => $accessToken,
            'expires_at' => $tokenExpiry,
            'available_actions' => ['reset_2fa', 'regenerate_backup_codes']
        ];

    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * Reset 2FA using recovery token
 */
function reset2FA($conn, $auditLogger, $admin, $input) {
    $accessToken = $input['access_token'] ?? null;
    
    if (!$accessToken) {
        throw new Exception("Access token is required");
    }

    // Verify access token
    $stmt = $conn->prepare("
        SELECT id FROM admin_recovery_tokens 
        WHERE admin_id = ? AND access_token = ? AND expires_at > NOW()
    ");
    $stmt->execute([$admin['admin_id'], $accessToken]);
    
    if (!$stmt->fetch()) {
        throw new Exception("Invalid or expired access token");
    }

    $conn->beginTransaction();

    try {
        // Disable 2FA
        $stmt = $conn->prepare("
            UPDATE admin_2fa 
            SET is_enabled = 0, setup_completed = 0 
            WHERE admin_id = ?
        ");
        $stmt->execute([$admin['admin_id']]);

        // Update admin record
        $stmt = $conn->prepare("
            UPDATE admins 
            SET two_factor_enabled = 0, auth_method = 'password_only' 
            WHERE admin_id = ?
        ");
        $stmt->execute([$admin['admin_id']]);

        // Invalidate recovery token
        $stmt = $conn->prepare("DELETE FROM admin_recovery_tokens WHERE admin_id = ?");
        $stmt->execute([$admin['admin_id']]);

        // Log 2FA reset
        $auditLogger->logAuthEvent(
            $admin['admin_id'],
            'recovery',
            '2fa_reset',
            ['method' => 'recovery_token'],
            'WARNING'
        );

        $conn->commit();

        return [
            'success' => true,
            'message' => '2FA has been reset successfully. You can now log in with password only.',
            'auth_method' => 'password_only'
        ];

    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * Send recovery email
 */
function sendRecoveryEmail($conn, $admin, $recoveryCode) {
    try {
        // Get SMTP settings
        $stmt = $conn->query("SELECT * FROM smtp_settings WHERE is_active = 1 LIMIT 1");
        $smtpSettings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$smtpSettings) {
            return false;
        }

        $mail = new PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtpSettings['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtpSettings['username'];
        $mail->Password = $smtpSettings['password'];
        $mail->SMTPSecure = $smtpSettings['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $smtpSettings['port'];
        
        // Recipients
        $mail->setFrom($smtpSettings['from_email'], $smtpSettings['from_name']);
        $mail->addAddress($admin['email'], $admin['username']);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'FanBet247 Admin Account Recovery';
        
        $mail->Body = "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: #dc2626;'>Account Recovery Request</h1>
                </div>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                    <h2 style='color: #333; margin-top: 0;'>Recovery Code</h2>
                    <p>Hello <strong>{$admin['username']}</strong>,</p>
                    <p>You have requested to recover your FanBet247 Admin account. Use the following recovery code:</p>
                    
                    <div style='text-align: center; margin: 30px 0;'>
                        <div style='background: #dc2626; color: white; padding: 15px 30px; border-radius: 8px; font-size: 24px; font-weight: bold; letter-spacing: 3px; display: inline-block;'>
                            {$recoveryCode}
                        </div>
                    </div>
                    
                    <p><strong>Important:</strong></p>
                    <ul>
                        <li>This recovery code is valid for <strong>1 hour</strong></li>
                        <li>Do not share this code with anyone</li>
                        <li>If you did not request this recovery, please contact support immediately</li>
                        <li>This code can only be used once</li>
                    </ul>
                </div>
                
                <div style='border-top: 1px solid #ddd; padding-top: 20px; font-size: 12px; color: #666;'>
                    <p>This is an automated message from FanBet247 Admin Portal. Please do not reply to this email.</p>
                    <p>Recovery request from IP: {$_SERVER['REMOTE_ADDR']}</p>
                    <p>Time: " . date('Y-m-d H:i:s') . "</p>
                </div>
            </div>
        </body>
        </html>";
        
        $mail->send();
        return true;
        
    } catch (Exception $e) {
        error_log("Recovery email failed: " . $e->getMessage());
        return false;
    }
}
?>
