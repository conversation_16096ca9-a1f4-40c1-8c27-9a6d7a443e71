<?php
echo "Testing admin_security_management.php endpoint...\n";

// Set up the environment
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['REQUEST_URI'] = '/test';

// Capture output
ob_start();

try {
    include 'handlers/admin_security_management.php';
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$output = ob_get_clean();
echo "Response:\n";
echo $output;
echo "\n";
?>
