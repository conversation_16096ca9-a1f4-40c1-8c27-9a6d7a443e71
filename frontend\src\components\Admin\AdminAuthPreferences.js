import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaShieldAlt, <PERSON>a<PERSON>ey, FaEnvelope, FaCheck, FaTimes, FaSave, FaSpinner, FaExclamationTriangle, FaCog } from 'react-icons/fa';

const API_BASE_URL = '/backend';

const AdminAuthPreferences = ({ adminId, onUpdate }) => {
    const [preferences, setPreferences] = useState({
        auth_method: 'password_only',
        two_factor_enabled: false,
        can_use_otp: false,
        can_use_2fa: false
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [adminInfo, setAdminInfo] = useState({});
    const [twoFactorSetup, setTwoFactorSetup] = useState({});

    useEffect(() => {
        fetchPreferences();
    }, [adminId]);

    const fetchPreferences = async () => {
        try {
            setLoading(true);
            setError('');

            const response = await axios.get(`${API_BASE_URL}/handlers/admin_auth_preferences.php?adminId=${adminId}`);

            if (response.data.success) {
                setAdminInfo(response.data.admin);
                setPreferences({
                    auth_method: response.data.auth_settings.auth_method || 'password_only',
                    two_factor_enabled: response.data.auth_settings.two_factor_enabled || false,
                    can_use_otp: response.data.can_use_otp,
                    can_use_2fa: response.data.can_use_2fa
                });
                setTwoFactorSetup(response.data.two_factor_setup);
            } else {
                setError(response.data.message || 'Failed to load authentication preferences');
            }
        } catch (err) {
            setError('Failed to load authentication preferences');
            console.error('Error fetching preferences:', err);
        } finally {
            setLoading(false);
        }
    };

    const updatePreferences = async () => {
        try {
            setSaving(true);
            setError('');

            const response = await axios.post(`${API_BASE_URL}/handlers/admin_auth_preferences.php?adminId=${adminId}`, {
                auth_method: preferences.auth_method,
                enable_two_factor: preferences.two_factor_enabled
            });

            if (response.data.success) {
                setSuccess('Authentication preferences updated successfully!');
                setTimeout(() => setSuccess(''), 3000);
                
                if (onUpdate) {
                    onUpdate(preferences);
                }
            } else {
                setError(response.data.message || 'Failed to update preferences');
            }
        } catch (err) {
            setError('Failed to update authentication preferences');
            console.error('Error updating preferences:', err);
        } finally {
            setSaving(false);
        }
    };

    const handleAuthMethodChange = (method) => {
        setPreferences(prev => ({
            ...prev,
            auth_method: method,
            two_factor_enabled: method === '2fa'
        }));
    };

    if (loading) {
        return (
            <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center justify-center h-32">
                    <FaSpinner className="animate-spin text-2xl text-blue-600" />
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6">
                <div className="flex items-center gap-3 mb-6">
                    <FaShieldAlt className="text-blue-500 text-xl" />
                    <div>
                        <h3 className="text-lg font-semibold text-gray-800">Authentication Preferences</h3>
                        <p className="text-sm text-gray-600">Configure your personal authentication method</p>
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                        <FaExclamationTriangle className="text-red-500" />
                        <span className="text-red-700 text-sm">{error}</span>
                    </div>
                )}

                {/* Success Message */}
                {success && (
                    <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
                        <FaCheck className="text-green-500" />
                        <span className="text-green-700 text-sm">{success}</span>
                    </div>
                )}

                {/* Current Status */}
                <div className="mb-6 bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-800 mb-2">Current Status</h4>
                    <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                            <span className="text-gray-600">Authentication Method:</span>
                            <span className="font-medium capitalize">{preferences.auth_method.replace('_', ' ')}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600">2FA Setup:</span>
                            <span className={`font-medium ${twoFactorSetup.setup_completed ? 'text-green-600' : 'text-gray-500'}`}>
                                {twoFactorSetup.setup_completed ? 'Completed' : 'Not Set Up'}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Authentication Method Selection */}
                <div className="space-y-4">
                    <h4 className="font-medium text-gray-800">Choose Authentication Method</h4>
                    
                    {/* Password Only */}
                    <div className="border rounded-lg p-4">
                        <label className="flex items-start gap-3 cursor-pointer">
                            <input
                                type="radio"
                                name="auth_method"
                                value="password_only"
                                checked={preferences.auth_method === 'password_only'}
                                onChange={(e) => handleAuthMethodChange(e.target.value)}
                                className="mt-1"
                            />
                            <div className="flex-1">
                                <div className="flex items-center gap-2">
                                    <FaKey className="text-gray-500" />
                                    <span className="font-medium">Password Only</span>
                                </div>
                                <p className="text-sm text-gray-600 mt-1">
                                    Use only your username and password to log in
                                </p>
                            </div>
                        </label>
                    </div>

                    {/* OTP */}
                    <div className="border rounded-lg p-4">
                        <label className="flex items-start gap-3 cursor-pointer">
                            <input
                                type="radio"
                                name="auth_method"
                                value="otp"
                                checked={preferences.auth_method === 'otp'}
                                onChange={(e) => handleAuthMethodChange(e.target.value)}
                                disabled={!preferences.can_use_otp}
                                className="mt-1"
                            />
                            <div className="flex-1">
                                <div className="flex items-center gap-2">
                                    <FaEnvelope className="text-blue-500" />
                                    <span className="font-medium">Email OTP</span>
                                    {!preferences.can_use_otp && (
                                        <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">Disabled</span>
                                    )}
                                </div>
                                <p className="text-sm text-gray-600 mt-1">
                                    Receive a one-time password via email after entering your password
                                </p>
                                {!preferences.can_use_otp && (
                                    <p className="text-xs text-red-600 mt-1">
                                        OTP is not enabled globally. Contact system administrator.
                                    </p>
                                )}
                            </div>
                        </label>
                    </div>

                    {/* 2FA */}
                    <div className="border rounded-lg p-4">
                        <label className="flex items-start gap-3 cursor-pointer">
                            <input
                                type="radio"
                                name="auth_method"
                                value="2fa"
                                checked={preferences.auth_method === '2fa'}
                                onChange={(e) => handleAuthMethodChange(e.target.value)}
                                disabled={!preferences.can_use_2fa}
                                className="mt-1"
                            />
                            <div className="flex-1">
                                <div className="flex items-center gap-2">
                                    <FaShieldAlt className="text-green-500" />
                                    <span className="font-medium">Two-Factor Authentication</span>
                                    {!preferences.can_use_2fa && (
                                        <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">Disabled</span>
                                    )}
                                </div>
                                <p className="text-sm text-gray-600 mt-1">
                                    Use Google Authenticator for enhanced security
                                </p>
                                {!preferences.can_use_2fa && (
                                    <p className="text-xs text-red-600 mt-1">
                                        2FA is not enabled globally. Contact system administrator.
                                    </p>
                                )}
                                {preferences.auth_method === '2fa' && !twoFactorSetup.setup_completed && (
                                    <p className="text-xs text-yellow-600 mt-1">
                                        You will need to complete 2FA setup on your next login.
                                    </p>
                                )}
                            </div>
                        </label>
                    </div>
                </div>

                {/* Save Button */}
                <div className="flex justify-end pt-6 border-t mt-6">
                    <button
                        onClick={updatePreferences}
                        disabled={saving}
                        className="flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                        {saving ? <FaSpinner className="animate-spin" /> : <FaSave />}
                        {saving ? 'Saving...' : 'Save Preferences'}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default AdminAuthPreferences;
