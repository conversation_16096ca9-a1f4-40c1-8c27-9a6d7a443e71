<?php
/**
 * FanBet247 Currency System Integration Test
 * 
 * This script performs end-to-end testing of the complete currency system
 * including user registration, currency selection, and display functionality.
 */

header("Content-Type: application/json; charset=UTF-8");

require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/currency_utils.php';

class IntegrationTester {
    private $conn;
    private $testResults = [];
    private $testUserId = null;
    private $testAdminId = null;
    private $baseUrl;

    public function __construct() {
        $this->conn = getDBConnection();
        $this->baseUrl = 'http://localhost/backend/handlers';
        
        // Get test admin ID
        $stmt = $this->conn->prepare("SELECT admin_id FROM admins LIMIT 1");
        $stmt->execute();
        $this->testAdminId = $stmt->fetchColumn();
    }

    public function runIntegrationTests() {
        echo "🚀 Starting Currency System Integration Tests...\n\n";

        try {
            // Test 1: Database Setup Verification
            $this->testDatabaseSetup();
            
            // Test 2: User Registration with Currency Selection
            $this->testUserRegistrationWithCurrency();
            
            // Test 3: Currency Conversion APIs
            $this->testCurrencyConversionAPIs();
            
            // Test 4: Admin Currency Management
            $this->testAdminCurrencyManagement();
            
            // Test 5: User Currency Preference Updates
            $this->testUserCurrencyPreferenceUpdates();
            
            // Test 6: Error Handling and Edge Cases
            $this->testErrorHandlingAndEdgeCases();
            
            // Test 7: Performance and Caching
            $this->testPerformanceAndCaching();
            
            $this->generateIntegrationReport();
            
        } catch (Exception $e) {
            echo "❌ Integration test failed: " . $e->getMessage() . "\n";
            $this->generateIntegrationReport();
        }
    }

    private function testDatabaseSetup() {
        echo "📊 Testing Database Setup...\n";
        
        // Verify all required tables exist
        $requiredTables = ['currencies', 'exchange_rates'];
        foreach ($requiredTables as $table) {
            $stmt = $this->conn->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            $this->addResult("Table {$table} exists", $stmt->rowCount() > 0);
        }
        
        // Verify users table has currency field
        $stmt = $this->conn->prepare("SHOW COLUMNS FROM users LIKE 'preferred_currency_id'");
        $stmt->execute();
        $this->addResult("Users table has preferred_currency_id", $stmt->rowCount() > 0);
        
        // Verify default data exists
        $stmt = $this->conn->prepare("SELECT COUNT(*) FROM currencies WHERE is_active = 1");
        $stmt->execute();
        $activeCurrencies = $stmt->fetchColumn();
        $this->addResult("Active currencies exist", $activeCurrencies >= 2);
        
        $stmt = $this->conn->prepare("SELECT COUNT(*) FROM exchange_rates");
        $stmt->execute();
        $exchangeRates = $stmt->fetchColumn();
        $this->addResult("Exchange rates exist", $exchangeRates >= 2);
    }

    private function testUserRegistrationWithCurrency() {
        echo "👤 Testing User Registration with Currency Selection...\n";
        
        // Test user registration with currency preference
        $registrationData = [
            'username' => 'testuser_' . time(),
            'full_name' => 'Test User',
            'email' => 'test_' . time() . '@example.com',
            'password' => 'testpassword123',
            'favorite_team' => 'Test Team',
            'preferred_currency_id' => 2 // ZAR
        ];
        
        $response = $this->makeAPIRequest('user_registration.php', 'POST', $registrationData);
        $this->addResult("User registration with currency", 
            $response && $response['success'] && isset($response['data']['user_id']));
        
        if ($response && $response['success']) {
            $this->testUserId = $response['data']['user_id'];
            
            // Verify user has correct currency preference
            $stmt = $this->conn->prepare("SELECT preferred_currency_id FROM users WHERE user_id = ?");
            $stmt->execute([$this->testUserId]);
            $userCurrency = $stmt->fetchColumn();
            $this->addResult("User currency preference saved correctly", $userCurrency == 2);
        }
    }

    private function testCurrencyConversionAPIs() {
        echo "🔄 Testing Currency Conversion APIs...\n";
        
        // Test get currencies API
        $response = $this->makeAPIRequest('get_currencies.php');
        $this->addResult("Get currencies API", 
            $response && $response['success'] && isset($response['data']['currencies']));
        
        // Test get exchange rates API
        $response = $this->makeAPIRequest('get_exchange_rates.php');
        $this->addResult("Get exchange rates API", 
            $response && $response['success'] && isset($response['data']['exchange_rates']));
        
        // Test currency conversion API
        $response = $this->makeAPIRequest('convert_currency.php?amount=100&currency_id=1');
        $this->addResult("Currency conversion API", 
            $response && $response['success'] && isset($response['data']['converted_amount']));
        
        // Test conversion with different currency
        $response = $this->makeAPIRequest('convert_currency.php?amount=100&currency_id=2');
        $this->addResult("ZAR conversion API", 
            $response && $response['success'] && $response['data']['converted_amount'] > 100);
        
        // Test user currency preference API
        if ($this->testUserId) {
            $response = $this->makeAPIRequest("get_user_currency_preference.php?user_id={$this->testUserId}");
            $this->addResult("Get user currency preference API", 
                $response && $response['success'] && isset($response['data']['preferred_currency']));
        }
    }

    private function testAdminCurrencyManagement() {
        echo "👨‍💼 Testing Admin Currency Management...\n";
        
        if (!$this->testAdminId) {
            $this->addResult("Admin currency management (skipped - no admin)", true);
            return;
        }
        
        // Test updating exchange rate
        $updateData = [
            'currency_id' => 2,
            'rate_to_fancoin' => 19.5,
            'notes' => 'Integration test rate update',
            'admin_id' => $this->testAdminId
        ];
        
        $response = $this->makeAPIRequest('update_exchange_rate.php', 'POST', $updateData);
        $this->addResult("Update exchange rate API", 
            $response && $response['success']);
        
        // Verify rate was updated
        $stmt = $this->conn->prepare("
            SELECT rate_to_fancoin 
            FROM exchange_rates 
            WHERE currency_id = 2 
            ORDER BY updated_at DESC 
            LIMIT 1
        ");
        $stmt->execute();
        $newRate = $stmt->fetchColumn();
        $this->addResult("Exchange rate updated in database", abs($newRate - 19.5) < 0.01);
        
        // Test manage currencies API
        $response = $this->makeAPIRequest('manage_currencies.php');
        $this->addResult("Manage currencies API", 
            $response && $response['success'] && isset($response['data']['currencies']));
    }

    private function testUserCurrencyPreferenceUpdates() {
        echo "🔄 Testing User Currency Preference Updates...\n";
        
        if (!$this->testUserId) {
            $this->addResult("User preference updates (skipped - no test user)", true);
            return;
        }
        
        // Test updating user currency preference
        $updateData = [
            'user_id' => $this->testUserId,
            'currency_id' => 1 // Change to USD
        ];
        
        $response = $this->makeAPIRequest('update_user_currency_preference.php', 'POST', $updateData);
        $this->addResult("Update user currency preference API", 
            $response && $response['success']);
        
        // Verify preference was updated
        $stmt = $this->conn->prepare("SELECT preferred_currency_id FROM users WHERE user_id = ?");
        $stmt->execute([$this->testUserId]);
        $updatedCurrency = $stmt->fetchColumn();
        $this->addResult("User currency preference updated in database", $updatedCurrency == 1);
    }

    private function testErrorHandlingAndEdgeCases() {
        echo "⚠️ Testing Error Handling and Edge Cases...\n";
        
        // Test invalid currency ID
        $response = $this->makeAPIRequest('convert_currency.php?amount=100&currency_id=99999');
        $this->addResult("Invalid currency ID handling", 
            $response && !$response['success']);
        
        // Test negative amount
        $response = $this->makeAPIRequest('convert_currency.php?amount=-100&currency_id=1');
        $this->addResult("Negative amount handling", 
            $response !== false); // Should handle gracefully
        
        // Test invalid user ID
        $response = $this->makeAPIRequest('get_user_currency_preference.php?user_id=99999');
        $this->addResult("Invalid user ID handling", 
            $response && !$response['success']);
        
        // Test malformed requests
        $response = $this->makeAPIRequest('convert_currency.php?amount=abc&currency_id=xyz');
        $this->addResult("Malformed request handling", 
            $response && !$response['success']);
    }

    private function testPerformanceAndCaching() {
        echo "⚡ Testing Performance and Caching...\n";
        
        // Test multiple rapid requests
        $startTime = microtime(true);
        for ($i = 0; $i < 5; $i++) {
            $this->makeAPIRequest('get_currencies.php');
        }
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        
        $this->addResult("Multiple requests performance", $totalTime < 2.0); // Should complete in under 2 seconds
        
        // Test large amount conversion
        $startTime = microtime(true);
        $response = $this->makeAPIRequest('convert_currency.php?amount=1000000&currency_id=2');
        $endTime = microtime(true);
        $conversionTime = $endTime - $startTime;
        
        $this->addResult("Large amount conversion performance", 
            $response && $response['success'] && $conversionTime < 0.5);
    }

    private function makeAPIRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->baseUrl . '/' . $endpoint;
        
        $options = [
            'http' => [
                'method' => $method,
                'header' => "Content-Type: application/json\r\n",
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ];
        
        if ($data && $method === 'POST') {
            $options['http']['content'] = json_encode($data);
        }
        
        $context = stream_context_create($options);
        $response = @file_get_contents($url, false, $context);
        
        return $response ? json_decode($response, true) : false;
    }

    private function addResult($testName, $success) {
        $this->testResults[] = [
            'test' => $testName,
            'status' => $success ? 'PASS' : 'FAIL',
            'timestamp' => date('H:i:s')
        ];
        
        echo ($success ? "✅" : "❌") . " {$testName}\n";
    }

    private function generateIntegrationReport() {
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($test) {
            return $test['status'] === 'PASS';
        }));
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📋 INTEGRATION TEST REPORT\n";
        echo str_repeat("=", 60) . "\n";
        echo "Total Tests: {$totalTests}\n";
        echo "✅ Passed: {$passedTests}\n";
        echo "❌ Failed: " . ($totalTests - $passedTests) . "\n";
        echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 2) . "%\n";
        echo "Status: " . ($passedTests === $totalTests ? "ALL TESTS PASSED" : "SOME TESTS FAILED") . "\n";
        echo str_repeat("=", 60) . "\n\n";
        
        // Cleanup test data
        if ($this->testUserId) {
            $stmt = $this->conn->prepare("DELETE FROM users WHERE user_id = ?");
            $stmt->execute([$this->testUserId]);
            echo "🧹 Cleaned up test user data\n";
        }
        
        echo json_encode([
            'integration_test_summary' => [
                'total_tests' => $totalTests,
                'passed' => $passedTests,
                'failed' => $totalTests - $passedTests,
                'success_rate' => round(($passedTests / $totalTests) * 100, 2),
                'status' => $passedTests === $totalTests ? 'ALL_PASSED' : 'SOME_FAILED'
            ],
            'test_results' => $this->testResults,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    }
}

// Run integration tests
try {
    $tester = new IntegrationTester();
    $tester->runIntegrationTests();
} catch (Exception $e) {
    echo "💥 Integration test suite failed: " . $e->getMessage() . "\n";
}
?>
