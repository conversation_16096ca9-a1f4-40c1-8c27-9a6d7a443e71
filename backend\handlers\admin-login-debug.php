<?php
// Place this file in the same directory as admin_login_handler.php
header("Access-Control-Allow-Origin: *"); // Temporarily allow all origins for testing
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

error_reporting(E_ALL);
ini_set('display_errors', 1);

function checkServerConfiguration() {
    $results = [
        'php_version' => PHP_VERSION,
        'extensions' => [
            'pdo' => extension_loaded('pdo'),
            'pdo_mysql' => extension_loaded('pdo_mysql'),
        ],
        'error_log_writable' => is_writable(ini_get('error_log')),
        'post_max_size' => ini_get('post_max_size'),
        'upload_max_filesize' => ini_get('upload_max_filesize')
    ];
    
    return $results;
}

function testDatabaseConnection() {
    try {
        require_once '../includes/db_connect.php';
        $conn = getDBConnection();
        
        // Test if we can query the Admins table
        $stmt = $conn->prepare("SELECT COUNT(*) FROM Admins");
        $stmt->execute();
        $count = $stmt->fetchColumn();
        
        return [
            'success' => true,
            'message' => 'Database connection successful',
            'admins_count' => $count
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => $e->getMessage(),
            'code' => $e->getCode()
        ];
    }
}

// Run diagnostics
$diagnostics = [
    'server_config' => checkServerConfiguration(),
    'database_connection' => testDatabaseConnection(),
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'headers' => getallheaders(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'],
    'document_root' => $_SERVER['DOCUMENT_ROOT'],
    'script_filename' => $_SERVER['SCRIPT_FILENAME']
];

echo json_encode($diagnostics, JSON_PRETTY_PRINT);
?>
