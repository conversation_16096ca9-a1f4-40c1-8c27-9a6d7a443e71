<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Database connection
    $conn = new PDO(
        "mysql:host=localhost;dbname=fanbet247",
        'root',
        'root',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Create smtp_settings table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS smtp_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            host VARCHAR(255) NOT NULL DEFAULT 'smtp.gmail.com',
            port INT NOT NULL DEFAULT 587,
            username VA<PERSON>HA<PERSON>(255) NOT NULL DEFAULT '',
            password VARCHAR(255) NOT NULL DEFAULT '',
            encryption ENUM('none', 'ssl', 'tls') NOT NULL DEFAULT 'tls',
            from_email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>',
            from_name VARCHAR(255) NOT NULL DEFAULT 'FanBet247',
            is_active BOOLEAN NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    $conn->exec($createTableSQL);
    
    $stmt = $conn->prepare("SELECT * FROM smtp_settings ORDER BY id DESC LIMIT 1");
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);

        // Convert boolean to string for frontend
        $settings['is_active'] = $settings['is_active'] ? 'true' : 'false';
        $settings['port'] = (string)$settings['port'];

        // Don't send the actual password for security
        $settings['password'] = $settings['password'] ? '••••••••' : '';

        echo json_encode([
            'success' => true,
            'settings' => $settings
        ]);
    } else {
        // Insert default settings if none exist
        $insertStmt = $conn->prepare("
            INSERT INTO smtp_settings (host, port, username, password, encryption, from_email, from_name, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $insertStmt->execute(['smtp.gmail.com', 587, '', '', 'tls', '<EMAIL>', 'FanBet247', 0]);

        echo json_encode([
            'success' => true,
            'settings' => [
                'host' => 'smtp.gmail.com',
                'port' => '587',
                'username' => '',
                'password' => '',
                'encryption' => 'tls',
                'from_email' => '<EMAIL>',
                'from_name' => 'FanBet247',
                'is_active' => 'false'
            ]
        ]);
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
}
?>
