<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
    exit();
}

try {
    // Get POST data
    $data = json_decode(file_get_contents('php://input'), true);
    error_log("Received join league request: " . print_r($data, true));

    if (!isset($data['league_id']) || !isset($data['amount'])) {
        jsonResponse(400, 'League ID and amount are required');
    }

    // Get user ID from request
    $userId = isset($data['user_id']) ? intval($data['user_id']) : 0;
    if (!$userId) {
        jsonResponse(400, 'Invalid user ID');
    }

    $leagueId = intval($data['league_id']);
    $amount = floatval($data['amount']);

    error_log("Processing join request for user_id: $userId, league_id: $leagueId, amount: $amount");

    // Start transaction
    $conn->beginTransaction();

    try {
        // First, verify the league exists and is active
        $leagueQuery = "SELECT * FROM leagues WHERE league_id = ? AND status IN ('active', 'upcoming')";
        $stmt = $conn->prepare($leagueQuery);
        $stmt->execute([$leagueId]);
        $league = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$league) {
            throw new Exception('League not found or not available for joining');
        }

        error_log("League found: " . print_r($league, true));

        // Check if user is already in this league
        $checkQuery = "SELECT lm.membership_id, lm.status
                       FROM league_memberships lm
                       WHERE lm.user_id = ? AND lm.league_id = ?
                       AND lm.status != 'completed'";
        $stmt = $conn->prepare($checkQuery);
        $stmt->execute([$userId, $leagueId]);
        $existingMembership = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingMembership) {
            if ($existingMembership['status'] === 'active') {
                throw new Exception('You already have an active membership in this league');
            } else {
                throw new Exception('You have a pending membership request for this league');
            }
        }

        // Check user balance
        $stmt = $conn->prepare("SELECT balance FROM users WHERE user_id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('User not found');
        }

        if ($user['balance'] < $amount) {
            throw new Exception('Insufficient balance to join this league');
        }

        // Get or create active season
        $seasonQuery = "SELECT season_id FROM league_seasons WHERE league_id = ? AND status = 'active' LIMIT 1";
        $stmt = $conn->prepare($seasonQuery);
        $stmt->execute([$leagueId]);
        $season = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$season) {
            // Create new season
            $seasonName = "Season " . date('Y');
            $stmt = $conn->prepare("INSERT INTO league_seasons (league_id, season_name, start_date, end_date, status, created_by) VALUES (?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 'active', ?)");
            $stmt->execute([$leagueId, $seasonName, $userId]);
            $seasonId = $conn->lastInsertId();
            error_log("Created new season with ID: $seasonId");
        } else {
            $seasonId = $season['season_id'];
            error_log("Using existing season with ID: $seasonId");
        }

        // Handle user_leagues record
        $joinDate = date('Y-m-d H:i:s');
        error_log("Processing user_leagues for user_id: $userId, league_id: $leagueId");

        // Check for existing record first
        $stmt = $conn->prepare("SELECT id, status FROM user_leagues WHERE user_id = ? AND league_id = ?");
        $stmt->execute([$userId, $leagueId]);
        $existingRecord = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingRecord) {
            // Update existing record
            $stmt = $conn->prepare("UPDATE user_leagues SET status = 'active', join_date = ? WHERE id = ?");
            $stmt->execute([$joinDate, $existingRecord['id']]);
            $userLeagueId = $existingRecord['id'];
            error_log("Updated existing user_leagues record with ID: $userLeagueId");
        } else {
            // Create new record
            $stmt = $conn->prepare("INSERT INTO user_leagues (user_id, league_id, join_date, status, created_at) VALUES (?, ?, ?, 'active', NOW())");
            $stmt->execute([$userId, $leagueId, $joinDate]);
            $userLeagueId = $conn->lastInsertId();
            error_log("Created new user_leagues record with ID: $userLeagueId");
        }

        if (!$userLeagueId) {
            throw new Exception('Failed to create or retrieve user_leagues record');
        }

        error_log("Final user league ID: $userLeagueId");
        
        error_log("Cleaning up and creating league records...");
        
        // Clean up existing records in related tables
        $stmt = $conn->prepare("DELETE FROM league_memberships WHERE user_id = ? AND league_id = ?");
        $stmt->execute([$userId, $leagueId]);
        error_log("Cleaned up existing league_memberships");

        $stmt = $conn->prepare("DELETE FROM user_league_stats WHERE user_id = ? AND league_id = ?");
        $stmt->execute([$userId, $leagueId]);
        error_log("Cleaned up existing user_league_stats");

        // Create new league membership
        $stmt = $conn->prepare("INSERT INTO league_memberships
            (user_league_id, user_id, league_id, season_id, deposit_amount, status, join_date)
            VALUES (?, ?, ?, ?, ?, 'active', ?)");
        $stmt->execute([$userLeagueId, $userId, $leagueId, $seasonId, $amount, $joinDate]);
        $membershipId = $conn->lastInsertId();
        error_log("Created new league_membership with ID: $membershipId");

        // Initialize fresh user league stats
        $stmt = $conn->prepare("INSERT INTO user_league_stats
            (user_id, league_id, points, wins, draws, losses, streak)
            VALUES (?, ?, 0, 0, 0, 0, 0)");
        $stmt->execute([$userId, $leagueId]);
        error_log("Initialized user_league_stats");

        // Update user's league and balance
        $stmt = $conn->prepare("UPDATE users
            SET current_league_id = ?,
                balance = balance - ?
            WHERE user_id = ?");
        $stmt->execute([$leagueId, $amount, $userId]);
        error_log("Updated user's current league and balance");

        // Record the transaction
        $stmt = $conn->prepare("INSERT INTO transactions
            (user_id, amount, type, status, description, created_at)
            VALUES (?, ?, 'admin_debit', 'completed', 'League join fee', NOW())");
        $stmt->execute([$userId, $amount]);
        error_log("Recorded transaction");

        $conn->commit();
        error_log("Successfully completed league join process");
        
        jsonResponse(200, 'Successfully joined the league', [
            'league_id' => $leagueId,
            'user_league_id' => $userLeagueId,
            'membership_id' => $membershipId
        ]);

    } catch (Exception $e) {
        error_log("Error in join league transaction: " . $e->getMessage());
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        throw $e;
    }

} catch (Exception $e) {
    error_log("Error in join_league.php: " . $e->getMessage());
    jsonResponse(400, 'Error joining league: ' . $e->getMessage());
}
