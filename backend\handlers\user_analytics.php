<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

if (isset($_GET['id'])) {
    $userId = $_GET['id'];
    
    // This is a simplified example. You may need to adjust this based on your actual data structure and requirements.
    $query = "SELECT 
                COUNT(CASE WHEN outcome = 'win' THEN 1 END) as wins,
                COUNT(CASE WHEN outcome = 'loss' THEN 1 END) as losses,
                SUM(CASE WHEN type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
                SUM(CASE WHEN type = 'withdrawal' THEN amount ELSE 0 END) as total_withdrawals
              FROM bets b
              LEFT JOIN transactions t ON b.user_id = t.user_id
              WHERE b.user_id = :userId";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(":userId", $userId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $analytics = $stmt->fetch(PDO::FETCH_ASSOC);
        echo json_encode(array("success" => true, "analytics" => $analytics));
    } else {
        echo json_encode(array("success" => false, "message" => "No analytics data found"));
    }
} else {
    echo json_encode(array("success" => false, "message" => "User ID not provided"));
}
