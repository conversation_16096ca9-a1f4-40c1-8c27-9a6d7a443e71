<?php
require_once 'includes/db_connect.php';
require_once 'vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

echo "🔍 COMPLETE 2FA SIMULATION & DEBUG\n";
echo "==================================\n\n";

try {
    $conn = getDBConnection();
    echo "✅ Database connection successful\n\n";
    
    // Check babademo user
    $username = 'babademo';
    echo "👤 Checking user: $username\n";
    
    $stmt = $conn->prepare("
        SELECT user_id, username, email, password_hash, tfa_enabled, auth_method 
        FROM users 
        WHERE username = ?
    ");
    $stmt->execute([$username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "❌ User '$username' not found!\n";
        exit;
    }
    
    echo "✅ User found:\n";
    echo "   ID: {$user['user_id']}\n";
    echo "   Email: {$user['email']}\n";
    echo "   TFA Enabled: " . ($user['tfa_enabled'] ? 'Yes' : 'No') . "\n";
    echo "   Auth Method: {$user['auth_method']}\n\n";
    
    // Check password
    $password = 'loving12';
    $passwordValid = password_verify($password, $user['password_hash']);
    echo "🔐 Password check for '$password': " . ($passwordValid ? "✅ VALID" : "❌ INVALID") . "\n\n";
    
    if (!$passwordValid) {
        echo "❌ Password is incorrect! Cannot proceed with 2FA test.\n";
        exit;
    }
    
    // Check 2FA setup
    $stmt = $conn->prepare("
        SELECT * FROM user_2fa 
        WHERE user_id = ?
    ");
    $stmt->execute([$user['user_id']]);
    $tfa = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tfa) {
        echo "❌ No 2FA setup found for this user!\n";
        echo "🔧 Let's create a 2FA setup...\n\n";
        
        // Create 2FA setup
        $google2fa = new Google2FA();
        $secretKey = $google2fa->generateSecretKey();
        
        // Generate backup codes
        $backupCodes = [];
        for ($i = 0; $i < 10; $i++) {
            $backupCodes[] = strtoupper(substr(bin2hex(random_bytes(4)), 0, 8));
        }
        
        $stmt = $conn->prepare("
            INSERT INTO user_2fa (user_id, secret_key, auth_type, backup_codes, is_enabled, setup_completed) 
            VALUES (?, ?, 'google_auth', ?, 1, 1)
            ON DUPLICATE KEY UPDATE 
            secret_key = VALUES(secret_key), 
            backup_codes = VALUES(backup_codes), 
            is_enabled = 1,
            setup_completed = 1
        ");
        
        $stmt->execute([$user['user_id'], $secretKey, json_encode($backupCodes)]);
        
        // Update user
        $stmt = $conn->prepare("
            UPDATE users 
            SET tfa_enabled = 1, auth_method = 'password_2fa' 
            WHERE user_id = ?
        ");
        $stmt->execute([$user['user_id']]);
        
        echo "✅ Created 2FA setup with secret: $secretKey\n";
        echo "✅ Updated user to enable 2FA\n\n";
        
        $tfa = [
            'secret_key' => $secretKey,
            'backup_codes' => json_encode($backupCodes),
            'is_enabled' => 1,
            'setup_completed' => 1
        ];
        
    } else {
        echo "✅ 2FA setup found:\n";
        echo "   Secret: " . substr($tfa['secret_key'], 0, 8) . "...\n";
        echo "   Enabled: " . ($tfa['is_enabled'] ? 'Yes' : 'No') . "\n";
        echo "   Setup Complete: " . ($tfa['setup_completed'] ? 'Yes' : 'No') . "\n\n";
    }
    
    // Test Google2FA library
    echo "📱 GOOGLE2FA SIMULATION:\n";
    $google2fa = new Google2FA();
    $secretKey = $tfa['secret_key'];
    
    echo "🔑 Secret Key: $secretKey\n";
    echo "📱 QR Code URL: " . $google2fa->getQRCodeUrl('FanBet247', $user['email'], $secretKey) . "\n\n";
    
    // Generate current TOTP codes
    echo "🕐 CURRENT TOTP CODES:\n";
    $currentTime = time();
    
    for ($offset = -2; $offset <= 2; $offset++) {
        $testTime = $currentTime + ($offset * 30);
        $code = $google2fa->getCurrentOtp($secretKey, $testTime);
        $timeStr = date('H:i:s', $testTime);
        echo "   Time $timeStr (offset ${offset}): $code\n";
    }
    
    echo "\n⏰ Current server time: " . date('Y-m-d H:i:s') . "\n";
    echo "⏰ Unix timestamp: $currentTime\n\n";
    
    // Test verification with current code
    $currentCode = $google2fa->getCurrentOtp($secretKey);
    echo "🧪 VERIFICATION TESTS:\n";
    echo "📱 Current code to test: $currentCode\n\n";
    
    // Test different window sizes
    for ($window = 0; $window <= 4; $window++) {
        $isValid = $google2fa->verifyKey($secretKey, $currentCode, $window);
        echo "   Window $window: " . ($isValid ? "✅ VALID" : "❌ INVALID") . "\n";
    }
    
    echo "\n🔍 TESTING ACTUAL VERIFICATION HANDLER:\n";
    
    // Simulate the actual verification process
    $verificationCode = $currentCode;
    
    if (strlen($verificationCode) !== 6 || !ctype_digit($verificationCode)) {
        echo "❌ Code format invalid\n";
    } else {
        echo "✅ Code format valid: $verificationCode\n";
        
        $window = 2;
        $isValid = $google2fa->verifyKey($secretKey, $verificationCode, $window);
        
        echo "🎯 Final verification result: " . ($isValid ? "✅ SUCCESS" : "❌ FAILED") . "\n";
        
        if ($isValid) {
            echo "\n🎉 2FA VERIFICATION WORKS!\n";
            echo "✅ The user should be able to login with this code: $verificationCode\n";
        } else {
            echo "\n❌ 2FA VERIFICATION FAILED!\n";
            echo "🔧 Trying alternative approaches...\n";
            
            // Try with different timestamps
            for ($i = -5; $i <= 5; $i++) {
                $testTime = $currentTime + ($i * 30);
                $testCode = $google2fa->getCurrentOtp($secretKey, $testTime);
                $testValid = $google2fa->verifyKey($secretKey, $testCode, 2);
                
                if ($testValid) {
                    echo "✅ Code $testCode (time offset ${i}*30s) would work!\n";
                }
            }
        }
    }
    
    echo "\n📋 SUMMARY FOR USER:\n";
    echo "==================\n";
    echo "Username: $username\n";
    echo "Password: $password (✅ Valid)\n";
    echo "2FA Secret: $secretKey\n";
    echo "Current TOTP Code: $currentCode\n";
    echo "QR Code URL: " . $google2fa->getQRCodeUrl('FanBet247', $user['email'], $secretKey) . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
