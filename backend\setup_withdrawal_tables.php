<?php
/**
 * Setup script for withdrawal system tables
 * Run this script once to create the necessary database tables
 */

require_once 'includes/db_connect.php';

// Get database connection
$conn = getDBConnection();
$pdo = $conn;

try {
    echo "Setting up withdrawal system tables...\n";

    // User Payment Methods Table
    $sql1 = "
    CREATE TABLE IF NOT EXISTS `user_payment_methods` (
      `payment_method_id` int(11) NOT NULL AUTO_INCREMENT,
      `user_id` int(11) NOT NULL,
      `method_type` enum('bank_account','crypto_wallet','paypal','mobile_money') NOT NULL,
      `method_name` varchar(100) NOT NULL COMMENT 'User-friendly name for the method',
      `account_details` text NOT NULL COMMENT 'JSON encoded account details',
      `is_primary` tinyint(1) DEFAULT 0 COMMENT 'Primary withdrawal method',
      `is_verified` tinyint(1) DEFAULT 0 COMMENT 'Whether method is verified',
      `status` enum('active','inactive','pending_verification') DEFAULT 'pending_verification',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`payment_method_id`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_status` (`status`),
      CONSTRAINT `fk_payment_methods_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql1);
    echo "✓ Created user_payment_methods table\n";

    // Withdrawal Requests Table
    $sql2 = "
    CREATE TABLE IF NOT EXISTS `withdrawal_requests` (
      `withdrawal_id` int(11) NOT NULL AUTO_INCREMENT,
      `user_id` int(11) NOT NULL,
      `payment_method_id` int(11) NOT NULL,
      `amount_fancoins` decimal(15,4) NOT NULL COMMENT 'Amount in FanCoins',
      `amount_currency` decimal(15,4) NOT NULL COMMENT 'Amount in target currency',
      `target_currency_id` int(11) NOT NULL COMMENT 'Currency to withdraw to',
      `exchange_rate` decimal(10,6) NOT NULL COMMENT 'Exchange rate used for conversion',
      `withdrawal_fee` decimal(15,4) DEFAULT 0.0000 COMMENT 'Fee charged for withdrawal',
      `net_amount` decimal(15,4) NOT NULL COMMENT 'Final amount after fees',
      `reference_number` varchar(50) NOT NULL COMMENT 'Unique withdrawal reference',
      `status` enum('pending','approved','processing','completed','rejected','cancelled') DEFAULT 'pending',
      `admin_notes` text DEFAULT NULL COMMENT 'Admin notes for approval/rejection',
      `processed_by` int(11) DEFAULT NULL COMMENT 'Admin who processed the request',
      `processed_at` timestamp NULL DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`withdrawal_id`),
      UNIQUE KEY `idx_reference_number` (`reference_number`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_status` (`status`),
      KEY `idx_created_at` (`created_at`),
      CONSTRAINT `fk_withdrawal_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
      CONSTRAINT `fk_withdrawal_payment_method` FOREIGN KEY (`payment_method_id`) REFERENCES `user_payment_methods` (`payment_method_id`) ON DELETE RESTRICT
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql2);
    echo "✓ Created withdrawal_requests table\n";

    // Withdrawal Notifications Table
    $sql3 = "
    CREATE TABLE IF NOT EXISTS `withdrawal_notifications` (
      `notification_id` int(11) NOT NULL AUTO_INCREMENT,
      `withdrawal_id` int(11) NOT NULL,
      `user_id` int(11) NOT NULL,
      `notification_type` enum('request_received','approved','rejected','processing','completed','cancelled') NOT NULL,
      `title` varchar(255) NOT NULL,
      `message` text NOT NULL,
      `is_read` tinyint(1) DEFAULT 0,
      `sent_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`notification_id`),
      KEY `idx_withdrawal_id` (`withdrawal_id`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_is_read` (`is_read`),
      CONSTRAINT `fk_notification_withdrawal` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawal_requests` (`withdrawal_id`) ON DELETE CASCADE,
      CONSTRAINT `fk_notification_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql3);
    echo "✓ Created withdrawal_notifications table\n";

    // Withdrawal Limits Table
    $sql4 = "
    CREATE TABLE IF NOT EXISTS `withdrawal_limits` (
      `limit_id` int(11) NOT NULL AUTO_INCREMENT,
      `user_level` varchar(50) NOT NULL DEFAULT 'standard',
      `daily_limit` decimal(15,4) NOT NULL DEFAULT 1000.0000,
      `weekly_limit` decimal(15,4) NOT NULL DEFAULT 5000.0000,
      `monthly_limit` decimal(15,4) NOT NULL DEFAULT 20000.0000,
      `min_withdrawal` decimal(15,4) NOT NULL DEFAULT 10.0000,
      `max_withdrawal` decimal(15,4) NOT NULL DEFAULT 10000.0000,
      `withdrawal_fee_percentage` decimal(5,4) NOT NULL DEFAULT 0.0200 COMMENT '2% default fee',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`limit_id`),
      UNIQUE KEY `idx_user_level` (`user_level`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql4);
    echo "✓ Created withdrawal_limits table\n";

    // Insert default withdrawal limits
    $sql5 = "
    INSERT IGNORE INTO `withdrawal_limits` (`user_level`, `daily_limit`, `weekly_limit`, `monthly_limit`, `min_withdrawal`, `max_withdrawal`, `withdrawal_fee_percentage`) VALUES
    ('standard', 1000.0000, 5000.0000, 20000.0000, 10.0000, 1000.0000, 0.0200),
    ('premium', 5000.0000, 25000.0000, 100000.0000, 5.0000, 5000.0000, 0.0150),
    ('vip', 10000.0000, 50000.0000, 200000.0000, 1.0000, 10000.0000, 0.0100);
    ";

    $pdo->exec($sql5);
    echo "✓ Inserted default withdrawal limits\n";

    // Add user_level column to users table if it doesn't exist
    try {
        $sql6 = "ALTER TABLE `users` ADD COLUMN `user_level` varchar(50) DEFAULT 'standard' AFTER `preferred_currency_id`";
        $pdo->exec($sql6);
        echo "✓ Added user_level column to users table\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "✓ user_level column already exists in users table\n";
        } else {
            throw $e;
        }
    }

    // Create additional indexes
    try {
        $pdo->exec("CREATE INDEX `idx_withdrawal_user_status` ON `withdrawal_requests` (`user_id`, `status`)");
        echo "✓ Created withdrawal_user_status index\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✓ withdrawal_user_status index already exists\n";
        } else {
            throw $e;
        }
    }

    try {
        $pdo->exec("CREATE INDEX `idx_withdrawal_date_status` ON `withdrawal_requests` (`created_at`, `status`)");
        echo "✓ Created withdrawal_date_status index\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✓ withdrawal_date_status index already exists\n";
        } else {
            throw $e;
        }
    }

    try {
        $pdo->exec("CREATE INDEX `idx_payment_method_user_status` ON `user_payment_methods` (`user_id`, `status`)");
        echo "✓ Created payment_method_user_status index\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✓ payment_method_user_status index already exists\n";
        } else {
            throw $e;
        }
    }

    echo "\n🎉 Withdrawal system tables setup completed successfully!\n";
    echo "You can now use the withdrawal system features.\n";

} catch (PDOException $e) {
    echo "❌ Error setting up tables: " . $e->getMessage() . "\n";
    exit(1);
}
?>
