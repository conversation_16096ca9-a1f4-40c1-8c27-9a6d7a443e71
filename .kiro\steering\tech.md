# FanBet247 Technology Stack

## Frontend Stack
- **Framework**: React 18.2.0 with Create React App
- **Routing**: React Router DOM v6.4.1
- **HTTP Client**: Axios 0.27.2
- **UI Components**: React Icons, Chart.js with react-chartjs-2
- **Styling**: Tailwind CSS 3.4.15 with PostCSS and Autoprefixer
- **Testing**: Jest, React Testing Library
- **Build Tool**: React Scripts 5.0.1

## Backend Stack
- **Language**: PHP with PDO for database operations
- **Dependencies**: Composer for package management
- **Key Libraries**:
  - PHPMailer 6.8 (email functionality)
  - Google2FA 8.0 (two-factor authentication)
  - Endroid QR Code 6.0 (QR code generation)
- **Database**: MySQL with PDO abstraction layer
- **Architecture**: RESTful API with JSON responses

## Development Environment
- **Local Server**: MAMP (Apache/MySQL/PHP)
- **Proxy Configuration**: Frontend proxies to `http://localhost/FanBet247`
- **File Structure**: Monorepo with separate frontend/backend directories

## Common Commands

### Frontend Development
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server (port 3000)
npm start
# or
npm run dev

# Build for production
npm run build

# Run tests
npm test

# Clear cache (Windows)
clear-cache.bat
```

### Backend Development
```bash
# Navigate to backend directory
cd backend

# Install PHP dependencies
composer install

# Update dependencies
composer update

# Run database setup scripts
php setup/verify_installation.php

# Test database connection
php test_db_connection.php
```

### Database Operations
```bash
# Import database schema
mysql -u root -p fanbet247 < sql/fanbet247.sql

# Run specific schema updates
php sql/setup_tables.php
php sql/setup_currency_system.php
```

## API Conventions
- All endpoints return JSON with standardized format: `{status, message, data}`
- CORS headers included in all responses
- Error logging to `backend/logs/` directory
- File uploads handled in `backend/uploads/` with organized subdirectories

## Security Practices
- Password hashing with PHP `password_hash()`/`password_verify()`
- Prepared statements for all database queries
- Input validation and sanitization
- CSRF protection and XSS prevention
- Rate limiting implementation (in progress)
- Comprehensive audit logging for admin actions