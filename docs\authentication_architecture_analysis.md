# FanBet247 Authentication Architecture Analysis

## **EXECUTIVE SUMMARY**

FanBet247 implements a dual-tier authentication system with separate user and admin authentication flows. The system supports multiple authentication methods including password-only, OTP via email, and 2FA via Google Authenticator. The architecture is designed for scalability and security with comprehensive logging and session management.

## **1. AUTHENTICATION ARCHITECTURE OVERVIEW**

### **1.1 Dual Authentication System**
```
┌─────────────────────────────────────────────────────────────┐
│                    FanBet247 Authentication                 │
├─────────────────────────────────────────────────────────────┤
│  USER AUTHENTICATION          │  ADMIN AUTHENTICATION       │
│  ├─ Simple Login               │  ├─ Multi-Step Login        │
│  ├─ Registration               │  ├─ OTP Support             │
│  ├─ Password Reset             │  ├─ 2FA Support             │
│  ├─ localStorage Storage       │  ├─ Enhanced Security       │
│  └─ Basic Session Management   │  └─ Audit Logging          │
└─────────────────────────────────────────────────────────────┘
```

### **1.2 Authentication Methods Supported**
- **Password Only**: Basic username/password authentication
- **Password + OTP**: Email-based one-time password verification
- **Password + 2FA**: Google Authenticator-based two-factor authentication
- **Password + OTP + 2FA**: Dual verification (email + authenticator)

## **2. USER AUTHENTICATION SYSTEM**

### **2.1 User Authentication Components**

#### **Frontend Components:**
- **`Login.js`**: Main user login component with UserAuthLayout
- **`UserRegistration.js`**: User registration with team/currency selection
- **`ForgotPassword.js`**: Password reset flow with OTP verification
- **`UserAuthLayout.js`**: Shared authentication layout component

#### **Backend Handlers:**
- **`login.php`**: Simple user authentication handler
- **`user_registration.php`**: User registration with validation
- **`logout.php`**: Session cleanup and logout
- **`reset_password.php`**: Password reset functionality

### **2.2 User Authentication Flow**
```
1. User Login Process:
   ┌─ User enters credentials
   ├─ POST /backend/handlers/login.php
   ├─ Validate against users table
   ├─ Create PHP session + generate token
   ├─ Store user data in localStorage
   └─ Redirect to /user/dashboard

2. User Registration Process:
   ┌─ User fills registration form
   ├─ POST /backend/handlers/user_registration.php
   ├─ Validate required fields
   ├─ Check username/email uniqueness
   ├─ Hash password + store user data
   └─ Redirect to login page

3. Password Reset Process:
   ┌─ User enters email
   ├─ Generate and send OTP
   ├─ Verify OTP code
   ├─ Allow password reset
   └─ Redirect to login
```

### **2.3 User Protection Patterns**
- **ProtectedRoute**: Checks localStorage for `userId` and `userToken`
- **Session Storage**: Uses localStorage for authentication state
- **Redirect Logic**: Saves current path for post-login redirect
- **Route Guards**: Automatic redirect to login if not authenticated

## **3. ADMIN AUTHENTICATION SYSTEM**

### **3.1 Admin Authentication Components**

#### **Frontend Components:**
- **`AdminLoginPage.js`**: Multi-step admin login with 2FA/OTP support
- **`AdminOTPVerification.js`**: Email OTP verification component
- **`Admin2FAVerification.js`**: Google Authenticator verification
- **`Admin2FASetup.js`**: Initial 2FA setup with QR code
- **`AdminAuthPreferences.js`**: Authentication method management

#### **Backend Handlers:**
- **`admin_login_handler.php`**: Primary admin authentication
- **`admin_verify_otp.php`**: Email OTP verification
- **`admin_verify_2fa.php`**: Google Authenticator verification
- **`admin_setup_2fa.php`**: 2FA setup and QR code generation
- **`admin_auth_preferences.php`**: Authentication settings management

### **3.2 Admin Authentication Flow**
```
1. Multi-Step Admin Login:
   ┌─ Admin enters credentials
   ├─ POST /backend/handlers/admin_login_handler.php
   ├─ Validate password
   ├─ Check authentication method
   ├─ If OTP required → AdminOTPVerification
   ├─ If 2FA required → Admin2FAVerification
   ├─ Complete authentication
   └─ Redirect to /admin/dashboard

2. OTP Verification Flow:
   ┌─ Send OTP to admin email
   ├─ Admin enters OTP code
   ├─ POST /backend/handlers/admin_verify_otp.php
   ├─ Validate OTP against database
   └─ Complete login or proceed to 2FA

3. 2FA Setup Flow:
   ┌─ Generate secret key
   ├─ Create QR code URL
   ├─ Admin scans with Google Authenticator
   ├─ Verify test code
   └─ Enable 2FA for account
```

### **3.3 Admin Security Features**
- **Account Locking**: Temporary lockout after failed attempts
- **Audit Logging**: Comprehensive authentication event logging
- **Session Tokens**: Secure session management with tokens
- **Backup Codes**: Emergency access codes for 2FA
- **IP Tracking**: Login attempt tracking by IP address

## **4. DATABASE SCHEMA ARCHITECTURE**

### **4.1 User Authentication Tables**
```sql
users                    -- Primary user accounts
├─ user_id (PK)
├─ username, email, password_hash
├─ auth_method, otp_enabled, tfa_enabled
└─ status, role, balance, points

user_sessions           -- User session management
├─ session_id (PK)
├─ user_id (FK)
├─ token, expires_at
└─ created_at

user_otp               -- Email OTP codes
├─ otp_id (PK)
├─ user_id (FK)
├─ otp_code, expires_at
└─ is_used, created_at

user_2fa               -- Google Authenticator setup
├─ tfa_id (PK)
├─ user_id (FK)
├─ secret_key, backup_codes
└─ is_enabled, setup_completed
```

### **4.2 Admin Authentication Tables**
```sql
admins                  -- Primary admin accounts
├─ admin_id (PK)
├─ username, email, password_hash
├─ auth_method, two_factor_enabled
├─ account_locked_until, failed_login_attempts
└─ role, status, last_login

admin_2fa              -- Admin 2FA setup
├─ tfa_id (PK)
├─ admin_id (FK)
├─ secret_key, backup_codes
├─ auth_type, is_enabled
└─ setup_completed, created_at

admin_otp              -- Admin OTP codes
├─ otp_id (PK)
├─ admin_id (FK)
├─ otp_code, expires_at
└─ is_used, purpose

admin_auth_logs        -- Comprehensive audit logging
├─ log_id (PK)
├─ admin_id (FK)
├─ auth_type, action, details
├─ ip_address, user_agent
└─ created_at

admin_login_attempts   -- Failed login tracking
├─ attempt_id (PK)
├─ identifier, ip_address
├─ success, failure_reason
└─ attempted_at
```

## **5. SECURITY IMPLEMENTATION**

### **5.1 Password Security**
- **Hashing**: PHP `password_hash()` with bcrypt
- **Verification**: `password_verify()` for authentication
- **Strength Requirements**: Minimum length and complexity rules
- **Reset Security**: OTP-based password reset flow

### **5.2 Session Management**
- **User Sessions**: PHP sessions + localStorage tokens
- **Admin Sessions**: Enhanced session tokens with expiration
- **Token Generation**: Cryptographically secure random tokens
- **Session Cleanup**: Automatic cleanup on logout

### **5.3 Multi-Factor Authentication**
- **OTP Implementation**: Email-based verification codes
- **2FA Implementation**: Google Authenticator integration
- **Backup Codes**: Emergency access for 2FA users
- **Setup Verification**: Mandatory test verification during setup

### **5.4 Security Monitoring**
- **Audit Logging**: All authentication events logged
- **Failed Attempt Tracking**: IP-based attempt monitoring
- **Account Locking**: Automatic lockout after failed attempts
- **Activity Monitoring**: Login patterns and anomaly detection

## **6. INTEGRATION POINTS**

### **6.1 Frontend Integration**
- **React Router**: Protected routes with authentication checks
- **Context Providers**: User and admin state management
- **Component Reusability**: Shared authentication layouts
- **Error Handling**: Centralized error management

### **6.2 Backend Integration**
- **Database Abstraction**: PDO-based database interactions
- **API Consistency**: Standardized JSON response format
- **CORS Support**: Proper cross-origin request handling
- **Error Logging**: Comprehensive error tracking

### **6.3 Third-Party Integration**
- **Google Authenticator**: TOTP-based 2FA implementation
- **Email Service**: OTP delivery system
- **QR Code Generation**: 2FA setup QR codes
- **Backup Systems**: Emergency access mechanisms

## **7. ENHANCEMENT OPPORTUNITIES**

### **7.1 Current Strengths**
✅ **Dual-tier authentication** for users and admins  
✅ **Multiple authentication methods** supported  
✅ **Comprehensive audit logging** for security  
✅ **Modern React components** with consistent styling  
✅ **Secure password handling** with proper hashing  
✅ **Session management** with token-based authentication  

### **7.2 Potential Improvements**
🔄 **JWT Implementation**: Replace simple tokens with JWT  
🔄 **Rate Limiting**: API-level rate limiting for authentication  
🔄 **Device Management**: Track and manage user devices  
🔄 **SSO Integration**: Single sign-on capabilities  
🔄 **Biometric Support**: Fingerprint/face recognition  
🔄 **Risk-based Authentication**: Adaptive authentication based on risk  

This analysis provides a comprehensive understanding of the FanBet247 authentication architecture, serving as a foundation for future enhancements including OTP/2FA implementation strategies.
