import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import LeagueCard from './LeagueCard';
import { useHomepageData } from '../../hooks/useHomepageData';
import './TopLeaguesSection.css';

/**
 * Top Leagues Section Component
 * Displays the most popular and active betting leagues
 */
const TopLeaguesSection = () => {
  const { data, loading, errors, actions } = useHomepageData();
  const [displayCount, setDisplayCount] = useState(6);
  const [gridColumns, setGridColumns] = useState('1fr');

  // Sample data for testing layout (remove when backend is working)
  const sampleLeagues = [
    {
      league_id: 1,
      name: 'Premier League Champions',
      description: 'Compete with the best in our most prestigious league. High stakes, high rewards for serious bettors!',
      theme_color: '#1a365d',
      status: 'active',
      active_members: 24,
      prize_pool: 150000,
      min_bet_amount: 1000,
      max_bet_amount: 50000
    },
    {
      league_id: 2,
      name: 'Weekend Warriors',
      description: 'Perfect for casual bettors who love weekend matches. Join the fun every Saturday and Sunday!',
      theme_color: '#38a169',
      status: 'active',
      active_members: 18,
      prize_pool: 85000,
      min_bet_amount: 500,
      max_bet_amount: 25000
    },
    {
      league_id: 3,
      name: 'Champions League Elite',
      description: 'For the elite bettors only. Experience the thrill of European football betting at its finest.',
      theme_color: '#ed8936',
      status: 'active',
      active_members: 12,
      prize_pool: 200000,
      min_bet_amount: 2000,
      max_bet_amount: 100000
    },
    {
      league_id: 4,
      name: 'Rookie League',
      description: 'New to betting? Start here! Learn the ropes with lower stakes and friendly competition.',
      theme_color: '#3182ce',
      status: 'active',
      active_members: 35,
      prize_pool: 45000,
      min_bet_amount: 100,
      max_bet_amount: 5000
    },
    {
      league_id: 5,
      name: 'La Liga Masters',
      description: 'Spanish football at its finest. Join fellow La Liga enthusiasts in this exciting league.',
      theme_color: '#e53e3e',
      status: 'upcoming',
      active_members: 8,
      prize_pool: 120000,
      min_bet_amount: 1500,
      max_bet_amount: 75000
    },
    {
      league_id: 6,
      name: 'Serie A Specialists',
      description: 'Italian football betting league for true connoisseurs of the beautiful game.',
      theme_color: '#805ad5',
      status: 'upcoming',
      active_members: 6,
      prize_pool: 95000,
      min_bet_amount: 1200,
      max_bet_amount: 60000
    }
  ];

  // Use sample data if no real data is available (for testing)
  const displayLeagues = data.topLeagues.length > 0 ? data.topLeagues : sampleLeagues;

  // Handle responsive grid columns
  useEffect(() => {
    const updateGridColumns = () => {
      const width = window.innerWidth;
      if (width >= 1024) {
        setGridColumns('repeat(3, 1fr)');
      } else if (width >= 640) {
        setGridColumns('repeat(2, 1fr)');
      } else {
        setGridColumns('1fr');
      }
    };

    updateGridColumns();
    window.addEventListener('resize', updateGridColumns);
    return () => window.removeEventListener('resize', updateGridColumns);
  }, []);

  // Refresh data on component mount
  useEffect(() => {
    if (!data.topLeagues.length && !loading.topLeagues) {
      actions.fetchTopLeagues(6);
    }
  }, [data.topLeagues.length, loading.topLeagues, actions]);

  const handleLoadMore = () => {
    setDisplayCount(prev => prev + 3);
  };

  const handleRefresh = () => {
    actions.fetchTopLeagues(displayCount);
  };

  // Loading skeleton component
  const LeagueCardSkeleton = () => (
    <div className="league-card-skeleton">
      <div className="skeleton-header">
        <div className="skeleton-icon"></div>
        <div className="skeleton-status"></div>
      </div>
      <div className="skeleton-content">
        <div className="skeleton-title"></div>
        <div className="skeleton-description"></div>
        <div className="skeleton-stats">
          <div className="skeleton-stat"></div>
          <div className="skeleton-stat"></div>
        </div>
        <div className="skeleton-range"></div>
        <div className="skeleton-button"></div>
      </div>
    </div>
  );

  // Empty state component
  const EmptyState = () => (
    <div className="leagues-empty-state">
      <div className="empty-state-icon">🏆</div>
      <h3 className="empty-state-title">No Active Leagues</h3>
      <p className="empty-state-description">
        Be the first to create a league and start competing with other soccer fans!
      </p>
      <div style={{ marginBottom: '1rem', fontSize: '0.875rem', color: '#666' }}>
        Debug: {data.topLeagues.length} leagues loaded
      </div>
      <Link to="/user/leagues" className="empty-state-button">
        Create League
      </Link>
    </div>
  );

  // Error state component
  const ErrorState = () => (
    <div className="leagues-error-state">
      <div className="error-state-icon">⚠️</div>
      <h3 className="error-state-title">Unable to Load Leagues</h3>
      <p className="error-state-description">
        {errors.topLeagues || 'Something went wrong while loading the leagues.'}
      </p>
      <button onClick={handleRefresh} className="error-state-button">
        Try Again
      </button>
    </div>
  );

  return (
    <section className="top-leagues-section">
      <div className="leagues-container">
        {/* Section Header */}
        <div className="leagues-header">
          <div className="leagues-header-content">
            <h2 className="leagues-title">Top Performing Leagues</h2>
            <p className="leagues-subtitle">
              Join the most active and competitive soccer betting leagues
            </p>
          </div>
          
          <div className="leagues-header-actions">
            <button 
              onClick={handleRefresh}
              className="leagues-refresh-button"
              disabled={loading.topLeagues}
              title="Refresh leagues"
            >
              <svg 
                className={`refresh-icon ${loading.topLeagues ? 'spinning' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
            
            <Link to="/user/leagues" className="leagues-view-all-button">
              View All Leagues
              <svg className="view-all-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </Link>
          </div>
        </div>

        {/* Content Area */}
        <div className="leagues-content">
          {/* Loading State */}
          {loading.topLeagues && data.topLeagues.length === 0 && (
            <div className="leagues-grid">
              {Array.from({ length: 6 }, (_, index) => (
                <LeagueCardSkeleton key={index} />
              ))}
            </div>
          )}

          {/* Error State */}
          {errors.topLeagues && data.topLeagues.length === 0 && !loading.topLeagues && (
            <ErrorState />
          )}

          {/* Empty State */}
          {!loading.topLeagues && !errors.topLeagues && displayLeagues.length === 0 && (
            <EmptyState />
          )}

          {/* Leagues Grid */}
          {displayLeagues.length > 0 && (
            <>
              <div 
                className="leagues-grid"
                style={{
                  display: 'grid',
                  gridTemplateColumns: gridColumns,
                  gap: '1.5rem',
                  marginBottom: '2rem'
                }}
              >
                {displayLeagues.slice(0, displayCount).map((league) => (
                  <LeagueCard 
                    key={league.league_id} 
                    league={league}
                  />
                ))}
              </div>

              {/* Load More Button */}
              {displayLeagues.length > displayCount && (
                <div className="leagues-load-more">
                  <button 
                    onClick={handleLoadMore}
                    className="load-more-button"
                    disabled={loading.topLeagues}
                  >
                    {loading.topLeagues ? (
                      <>
                        <svg className="loading-spinner" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading...
                      </>
                    ) : (
                      <>
                        Load More Leagues
                        <svg className="load-more-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                      </>
                    )}
                  </button>
                </div>
              )}
            </>
          )}
        </div>


      </div>
    </section>
  );
};

export default TopLeaguesSection;