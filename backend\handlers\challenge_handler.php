<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

$conn = getDBConnection();

// Function to get all challenges
function getAllChallenges() {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM challenges ORDER BY match_date DESC");
    $stmt->execute();
    return $stmt->fetchAll();
}

// Function to create a new challenge
function createChallenge($teamA, $teamB, $oddsTeamA, $oddsTeamB, $oddsDraw, $matchDate) {
    global $conn;
    $stmt = $conn->prepare("INSERT INTO Challenges (team_a, team_b, odds_team_a, odds_team_b, odds_draw, match_date) VALUES (:teamA, :teamB, :oddsTeamA, :oddsTeamB, :oddsDraw, :matchDate)");
    $stmt->bindParam(':teamA', $teamA);
    $stmt->bindParam(':teamB', $teamB);
    $stmt->bindParam(':oddsTeamA', $oddsTeamA);
    $stmt->bindParam(':oddsTeamB', $oddsTeamB);
    $stmt->bindParam(':oddsDraw', $oddsDraw);
    $stmt->bindParam(':matchDate', $matchDate);
    return $stmt->execute();
}

// Handle GET request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['action']) && $_GET['action'] === 'get_all_challenges') {
        $challenges = getAllChallenges();
        echo json_encode($challenges);
    }
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents("php://input"));
    
    if (isset($data->action) && $data->action === 'create_challenge') {
        if (!empty($data->teamA) && !empty($data->teamB) && !empty($data->oddsTeamA) && !empty($data->oddsTeamB) && !empty($data->oddsDraw) && !empty($data->matchDate)) {
            if (createChallenge($data->teamA, $data->teamB, $data->oddsTeamA, $data->oddsTeamB, $data->oddsDraw, $data->matchDate)) {
                http_response_code(201);
                echo json_encode(["success" => true, "message" => "Challenge created successfully"]);
            } else {
                http_response_code(503);
                echo json_encode(["success" => false, "message" => "Unable to create challenge"]);
            }
        } else {
            http_response_code(400);
            echo json_encode(["success" => false, "message" => "Incomplete data"]);
        }
    }
}
?>
