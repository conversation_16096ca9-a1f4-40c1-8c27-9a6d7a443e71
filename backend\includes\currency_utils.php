<?php
/**
 * Currency Utility Functions for FanBet247
 * 
 * This file contains reusable functions for currency conversion and formatting
 * Used across the application for consistent currency handling
 */

/**
 * Get user's preferred currency information
 * 
 * @param PDO $conn Database connection
 * @param int $userId User ID
 * @return array|false Currency information or false if not found
 */
function getUserPreferredCurrency($conn, $userId) {
    try {
        $stmt = $conn->prepare("
            SELECT 
                c.id,
                c.currency_code,
                c.currency_name,
                c.currency_symbol,
                er.rate_to_fancoin
            FROM users u
            JOIN currencies c ON u.preferred_currency_id = c.id
            LEFT JOIN exchange_rates er ON c.id = er.currency_id
            WHERE u.user_id = :user_id AND c.is_active = 1
            ORDER BY er.updated_at DESC
            LIMIT 1
        ");
        
        $stmt->execute(['user_id' => $userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            return [
                'id' => (int)$result['id'],
                'currency_code' => $result['currency_code'],
                'currency_name' => $result['currency_name'],
                'currency_symbol' => $result['currency_symbol'],
                'rate_to_fancoin' => $result['rate_to_fancoin'] ? (float)$result['rate_to_fancoin'] : 1.0
            ];
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error getting user preferred currency: " . $e->getMessage());
        return false;
    }
}

/**
 * Convert FanCoin amount to user's preferred currency
 * 
 * @param PDO $conn Database connection
 * @param float $fanCoinAmount Amount in FanCoin
 * @param int $userId User ID
 * @return array Conversion result
 */
function convertFanCoinForUser($conn, $fanCoinAmount, $userId) {
    $userCurrency = getUserPreferredCurrency($conn, $userId);
    
    if (!$userCurrency) {
        // Fallback to USD if user currency not found
        return [
            'success' => true,
            'original_amount' => (float)$fanCoinAmount,
            'converted_amount' => (float)$fanCoinAmount,
            'currency_code' => 'USD',
            'currency_symbol' => '$',
            'exchange_rate' => 1.0,
            'formatted_amount' => '$' . number_format($fanCoinAmount, 2),
            'is_fallback' => true
        ];
    }
    
    $convertedAmount = $fanCoinAmount * $userCurrency['rate_to_fancoin'];
    
    return [
        'success' => true,
        'original_amount' => (float)$fanCoinAmount,
        'converted_amount' => (float)$convertedAmount,
        'currency_code' => $userCurrency['currency_code'],
        'currency_symbol' => $userCurrency['currency_symbol'],
        'currency_name' => $userCurrency['currency_name'],
        'exchange_rate' => $userCurrency['rate_to_fancoin'],
        'formatted_amount' => $userCurrency['currency_symbol'] . number_format($convertedAmount, 2),
        'is_fallback' => false
    ];
}

/**
 * Convert FanCoin amount to specific currency
 * 
 * @param PDO $conn Database connection
 * @param float $fanCoinAmount Amount in FanCoin
 * @param int $currencyId Currency ID
 * @return array Conversion result
 */
function convertFanCoinToCurrency($conn, $fanCoinAmount, $currencyId) {
    try {
        $stmt = $conn->prepare("
            SELECT 
                c.currency_code,
                c.currency_name,
                c.currency_symbol,
                er.rate_to_fancoin
            FROM currencies c
            LEFT JOIN exchange_rates er ON c.id = er.currency_id
            WHERE c.id = :currency_id AND c.is_active = 1
            ORDER BY er.updated_at DESC
            LIMIT 1
        ");
        
        $stmt->execute(['currency_id' => $currencyId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            return [
                'success' => false,
                'message' => 'Currency not found or inactive'
            ];
        }
        
        if (!$result['rate_to_fancoin']) {
            return [
                'success' => false,
                'message' => 'No exchange rate available for this currency'
            ];
        }
        
        $convertedAmount = $fanCoinAmount * $result['rate_to_fancoin'];
        
        return [
            'success' => true,
            'original_amount' => (float)$fanCoinAmount,
            'converted_amount' => (float)$convertedAmount,
            'currency_code' => $result['currency_code'],
            'currency_symbol' => $result['currency_symbol'],
            'currency_name' => $result['currency_name'],
            'exchange_rate' => (float)$result['rate_to_fancoin'],
            'formatted_amount' => $result['currency_symbol'] . number_format($convertedAmount, 2)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Conversion error: ' . $e->getMessage()
        ];
    }
}

/**
 * Format FanCoin amount with user's preferred currency
 * 
 * @param PDO $conn Database connection
 * @param float $fanCoinAmount Amount in FanCoin
 * @param int $userId User ID
 * @param bool $showBoth Show both FanCoin and converted amount
 * @return string Formatted amount string
 */
function formatAmountForUser($conn, $fanCoinAmount, $userId, $showBoth = true) {
    $conversion = convertFanCoinForUser($conn, $fanCoinAmount, $userId);
    
    if (!$conversion['success']) {
        return number_format($fanCoinAmount, 2) . ' FanCoin';
    }
    
    if ($showBoth && $conversion['currency_code'] !== 'USD') {
        return $conversion['formatted_amount'] . ' (' . number_format($fanCoinAmount, 2) . ' FanCoin)';
    } else {
        return $conversion['formatted_amount'];
    }
}

/**
 * Get all active currencies with their exchange rates
 * 
 * @param PDO $conn Database connection
 * @return array Array of currencies with rates
 */
function getAllCurrenciesWithRates($conn) {
    try {
        $stmt = $conn->prepare("
            SELECT 
                c.id,
                c.currency_code,
                c.currency_name,
                c.currency_symbol,
                c.is_active,
                er.rate_to_fancoin,
                er.updated_at as rate_updated_at
            FROM currencies c
            LEFT JOIN exchange_rates er ON c.id = er.currency_id
            WHERE c.is_active = 1
            ORDER BY c.currency_code ASC, er.updated_at DESC
        ");
        
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $currencies = [];
        $processedIds = [];
        
        foreach ($results as $row) {
            if (in_array($row['id'], $processedIds)) {
                continue; // Skip duplicates, keep latest rate
            }
            
            $currencies[] = [
                'id' => (int)$row['id'],
                'currency_code' => $row['currency_code'],
                'currency_name' => $row['currency_name'],
                'currency_symbol' => $row['currency_symbol'],
                'rate_to_fancoin' => $row['rate_to_fancoin'] ? (float)$row['rate_to_fancoin'] : null,
                'has_rate' => !is_null($row['rate_to_fancoin']),
                'display_name' => $row['currency_symbol'] . ' ' . $row['currency_code'] . ' - ' . $row['currency_name']
            ];
            
            $processedIds[] = $row['id'];
        }
        
        return $currencies;
    } catch (Exception $e) {
        error_log("Error getting currencies with rates: " . $e->getMessage());
        return [];
    }
}

/**
 * Validate currency ID exists and is active
 * 
 * @param PDO $conn Database connection
 * @param int $currencyId Currency ID to validate
 * @return bool True if valid, false otherwise
 */
function isValidCurrency($conn, $currencyId) {
    try {
        $stmt = $conn->prepare("SELECT id FROM currencies WHERE id = :id AND is_active = 1");
        $stmt->execute(['id' => $currencyId]);
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        error_log("Error validating currency: " . $e->getMessage());
        return false;
    }
}

/**
 * Get currency by code
 * 
 * @param PDO $conn Database connection
 * @param string $currencyCode Currency code (e.g., 'USD', 'ZAR')
 * @return array|false Currency information or false if not found
 */
function getCurrencyByCode($conn, $currencyCode) {
    try {
        $stmt = $conn->prepare("
            SELECT 
                c.id,
                c.currency_code,
                c.currency_name,
                c.currency_symbol,
                c.is_active,
                er.rate_to_fancoin
            FROM currencies c
            LEFT JOIN exchange_rates er ON c.id = er.currency_id
            WHERE c.currency_code = :currency_code AND c.is_active = 1
            ORDER BY er.updated_at DESC
            LIMIT 1
        ");
        
        $stmt->execute(['currency_code' => strtoupper($currencyCode)]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            return [
                'id' => (int)$result['id'],
                'currency_code' => $result['currency_code'],
                'currency_name' => $result['currency_name'],
                'currency_symbol' => $result['currency_symbol'],
                'is_active' => (bool)$result['is_active'],
                'rate_to_fancoin' => $result['rate_to_fancoin'] ? (float)$result['rate_to_fancoin'] : null
            ];
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error getting currency by code: " . $e->getMessage());
        return false;
    }
}
?>
