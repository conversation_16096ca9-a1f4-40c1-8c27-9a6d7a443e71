<?php
// Test PHPMailer availability
echo "🧪 Testing PHPMailer availability...\n\n";

// Check if vendor/autoload.php exists
if (file_exists('vendor/autoload.php')) {
    echo "✅ vendor/autoload.php exists\n";
    
    try {
        require 'vendor/autoload.php';
        echo "✅ Autoload included successfully\n";
        
        // Try to create PHPMailer instance
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        echo "✅ PHPMailer instance created successfully\n";
        
    } catch (Exception $e) {
        echo "❌ Error with PHPMailer: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "❌ vendor/autoload.php does not exist\n";
    echo "🔍 Checking for alternative PHPMailer locations...\n";
    
    // Check if PHPMailer is installed elsewhere
    $possiblePaths = [
        '../vendor/autoload.php',
        '../../vendor/autoload.php',
        'vendor/phpmailer/phpmailer/src/PHPMailer.php'
    ];
    
    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            echo "✅ Found: $path\n";
        } else {
            echo "❌ Not found: $path\n";
        }
    }
}

// Check if the simple PHPMailer class exists without namespace
echo "\n🔍 Testing simple PHPMailer class...\n";
try {
    if (class_exists('PHPMailer')) {
        echo "✅ PHPMailer class exists (without namespace)\n";
        $mail = new PHPMailer(true);
        echo "✅ Simple PHPMailer instance created\n";
    } else {
        echo "❌ PHPMailer class not found\n";
    }
} catch (Exception $e) {
    echo "❌ Error with simple PHPMailer: " . $e->getMessage() . "\n";
}

echo "\n📋 Current working directory: " . getcwd() . "\n";
echo "📋 Files in current directory:\n";
$files = scandir('.');
foreach ($files as $file) {
    if ($file !== '.' && $file !== '..') {
        echo "   - $file\n";
    }
}
?>
