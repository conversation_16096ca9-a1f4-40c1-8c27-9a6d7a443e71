{"ast": null, "code": "// Enhanced Dynamic API Configuration - Works on ANY domain without hardcoding\nconst isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';\n\n// Smart project path detection\nconst getProjectPath = () => {\n  // Priority 1: Environment variable override\n  if (process.env.REACT_APP_PROJECT_PATH) {\n    return process.env.REACT_APP_PROJECT_PATH;\n  }\n\n  // Priority 2: Development with React dev server (port 3000)\n  if (isDevelopment && window.location.port === '3000') {\n    // Use proxy - no project path needed\n    return '';\n  }\n\n  // Priority 3: Auto-detect from current URL for production\n  const currentPath = window.location.pathname;\n  const pathParts = currentPath.split('/').filter(part => part);\n\n  // If URL has path segments and first segment is not a known route\n  const knownRoutes = ['admin', 'user', 'login', 'register', 'dashboard', 'api', 'backend'];\n  if (pathParts.length > 0 && !knownRoutes.includes(pathParts[0])) {\n    // First segment is likely the project folder\n    return '/' + pathParts[0];\n  }\n\n  // Priority 4: Check if we're in a subfolder by testing common patterns\n  // This handles cases where the app is deployed in a subfolder\n  const possiblePaths = ['/FanBet247', '/fanbet247', '/app', '/betting'];\n  for (const path of possiblePaths) {\n    if (currentPath.startsWith(path)) {\n      return path;\n    }\n  }\n\n  // Default: Root domain deployment (no project folder)\n  return '';\n};\n\n// Dynamic API URL with intelligent detection\nconst getApiBaseUrl = () => {\n  const projectPath = getProjectPath();\n\n  // Development with React dev server - use proxy\n  if (isDevelopment && window.location.port === '3000') {\n    return '/backend/handlers'; // Proxy routes to localhost/FanBet247/backend/handlers\n  }\n\n  // Production: domain.com or domain.com/projectfolder\n  // Ensure we always have a valid URL, never undefined\n  const baseUrl = `${projectPath}/backend/handlers`;\n\n  // Fallback for production if projectPath is empty\n  if (!projectPath || projectPath === '') {\n    return '/backend/handlers';\n  }\n  return baseUrl;\n};\nexport const API_BASE_URL = getApiBaseUrl();\nexport const PROJECT_PATH = getProjectPath();\n\n// Enhanced asset URL helper for images, uploads, etc.\nexport const getAssetUrl = path => {\n  const projectPath = getProjectPath();\n  const cleanPath = path.startsWith('/') ? path : `/${path}`;\n\n  // Development with proxy\n  if (isDevelopment && window.location.port === '3000') {\n    return `/backend${cleanPath}`; // Proxy handles routing\n  }\n\n  // Production: use project path + backend + asset path\n  return `${projectPath}/backend${cleanPath}`;\n};\n\n// Get full backend URL (for cases where you need the complete URL)\nexport const getBackendUrl = () => {\n  const projectPath = getProjectPath();\n  if (isDevelopment && window.location.port === '3000') {\n    return `${window.location.origin}/backend`;\n  }\n  return `${window.location.origin}${projectPath}/backend`;\n};\n\n// Debug logging (only in development)\nif (isDevelopment) {\n  console.log('🔧 Enhanced Dynamic API Configuration:');\n  console.log('Environment:', process.env.NODE_ENV);\n  console.log('Current URL:', window.location.href);\n  console.log('Detected Project Path:', PROJECT_PATH);\n  console.log('Final API Base URL:', API_BASE_URL);\n  console.log('Backend URL:', getBackendUrl());\n  console.log('');\n  console.log('📝 This will work on ANY domain:');\n  console.log('   - Development: localhost:3000 → proxy to localhost/FanBet247/backend/handlers');\n  console.log('   - Production Root: yourdomain.com → yourdomain.com/backend/handlers');\n  console.log('   - Production Subfolder: yourdomain.com/myapp → yourdomain.com/myapp/backend/handlers');\n  console.log('   - Subdomain: api.yourdomain.com → api.yourdomain.com/backend/handlers');\n}", "map": {"version": 3, "names": ["isDevelopment", "process", "env", "NODE_ENV", "window", "location", "hostname", "getProjectPath", "REACT_APP_PROJECT_PATH", "port", "currentPath", "pathname", "pathParts", "split", "filter", "part", "knownRoutes", "length", "includes", "possiblePaths", "path", "startsWith", "getApiBaseUrl", "projectPath", "baseUrl", "API_BASE_URL", "PROJECT_PATH", "getAssetUrl", "cleanPath", "getBackendUrl", "origin", "console", "log", "href"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/config.js"], "sourcesContent": ["// Enhanced Dynamic API Configuration - Works on ANY domain without hardcoding\nconst isDevelopment = process.env.NODE_ENV === 'development' ||\n                     window.location.hostname === 'localhost' ||\n                     window.location.hostname === '127.0.0.1';\n\n// Smart project path detection\nconst getProjectPath = () => {\n    // Priority 1: Environment variable override\n    if (process.env.REACT_APP_PROJECT_PATH) {\n        return process.env.REACT_APP_PROJECT_PATH;\n    }\n\n    // Priority 2: Development with React dev server (port 3000)\n    if (isDevelopment && window.location.port === '3000') {\n        // Use proxy - no project path needed\n        return '';\n    }\n\n    // Priority 3: Auto-detect from current URL for production\n    const currentPath = window.location.pathname;\n    const pathParts = currentPath.split('/').filter(part => part);\n\n    // If URL has path segments and first segment is not a known route\n    const knownRoutes = ['admin', 'user', 'login', 'register', 'dashboard', 'api', 'backend'];\n    if (pathParts.length > 0 && !knownRoutes.includes(pathParts[0])) {\n        // First segment is likely the project folder\n        return '/' + pathParts[0];\n    }\n\n    // Priority 4: Check if we're in a subfolder by testing common patterns\n    // This handles cases where the app is deployed in a subfolder\n    const possiblePaths = ['/FanBet247', '/fanbet247', '/app', '/betting'];\n    for (const path of possiblePaths) {\n        if (currentPath.startsWith(path)) {\n            return path;\n        }\n    }\n\n    // Default: Root domain deployment (no project folder)\n    return '';\n};\n\n// Dynamic API URL with intelligent detection\nconst getApiBaseUrl = () => {\n    const projectPath = getProjectPath();\n\n    // Development with React dev server - use proxy\n    if (isDevelopment && window.location.port === '3000') {\n        return '/backend/handlers'; // Proxy routes to localhost/FanBet247/backend/handlers\n    }\n\n    // Production: domain.com or domain.com/projectfolder\n    // Ensure we always have a valid URL, never undefined\n    const baseUrl = `${projectPath}/backend/handlers`;\n\n    // Fallback for production if projectPath is empty\n    if (!projectPath || projectPath === '') {\n        return '/backend/handlers';\n    }\n\n    return baseUrl;\n};\n\nexport const API_BASE_URL = getApiBaseUrl();\nexport const PROJECT_PATH = getProjectPath();\n\n// Enhanced asset URL helper for images, uploads, etc.\nexport const getAssetUrl = (path) => {\n    const projectPath = getProjectPath();\n    const cleanPath = path.startsWith('/') ? path : `/${path}`;\n\n    // Development with proxy\n    if (isDevelopment && window.location.port === '3000') {\n        return `/backend${cleanPath}`; // Proxy handles routing\n    }\n\n    // Production: use project path + backend + asset path\n    return `${projectPath}/backend${cleanPath}`;\n};\n\n// Get full backend URL (for cases where you need the complete URL)\nexport const getBackendUrl = () => {\n    const projectPath = getProjectPath();\n\n    if (isDevelopment && window.location.port === '3000') {\n        return `${window.location.origin}/backend`;\n    }\n\n    return `${window.location.origin}${projectPath}/backend`;\n};\n\n// Debug logging (only in development)\nif (isDevelopment) {\n    console.log('🔧 Enhanced Dynamic API Configuration:');\n    console.log('Environment:', process.env.NODE_ENV);\n    console.log('Current URL:', window.location.href);\n    console.log('Detected Project Path:', PROJECT_PATH);\n    console.log('Final API Base URL:', API_BASE_URL);\n    console.log('Backend URL:', getBackendUrl());\n    console.log('');\n    console.log('📝 This will work on ANY domain:');\n    console.log('   - Development: localhost:3000 → proxy to localhost/FanBet247/backend/handlers');\n    console.log('   - Production Root: yourdomain.com → yourdomain.com/backend/handlers');\n    console.log('   - Production Subfolder: yourdomain.com/myapp → yourdomain.com/myapp/backend/handlers');\n    console.log('   - Subdomain: api.yourdomain.com → api.yourdomain.com/backend/handlers');\n}"], "mappings": "AAAA;AACA,MAAMA,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IACvCC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,IACxCF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW;;AAE7D;AACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EACzB;EACA,IAAIN,OAAO,CAACC,GAAG,CAACM,sBAAsB,EAAE;IACpC,OAAOP,OAAO,CAACC,GAAG,CAACM,sBAAsB;EAC7C;;EAEA;EACA,IAAIR,aAAa,IAAII,MAAM,CAACC,QAAQ,CAACI,IAAI,KAAK,MAAM,EAAE;IAClD;IACA,OAAO,EAAE;EACb;;EAEA;EACA,MAAMC,WAAW,GAAGN,MAAM,CAACC,QAAQ,CAACM,QAAQ;EAC5C,MAAMC,SAAS,GAAGF,WAAW,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC;;EAE7D;EACA,MAAMC,WAAW,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC;EACzF,IAAIJ,SAAS,CAACK,MAAM,GAAG,CAAC,IAAI,CAACD,WAAW,CAACE,QAAQ,CAACN,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7D;IACA,OAAO,GAAG,GAAGA,SAAS,CAAC,CAAC,CAAC;EAC7B;;EAEA;EACA;EACA,MAAMO,aAAa,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,CAAC;EACtE,KAAK,MAAMC,IAAI,IAAID,aAAa,EAAE;IAC9B,IAAIT,WAAW,CAACW,UAAU,CAACD,IAAI,CAAC,EAAE;MAC9B,OAAOA,IAAI;IACf;EACJ;;EAEA;EACA,OAAO,EAAE;AACb,CAAC;;AAED;AACA,MAAME,aAAa,GAAGA,CAAA,KAAM;EACxB,MAAMC,WAAW,GAAGhB,cAAc,CAAC,CAAC;;EAEpC;EACA,IAAIP,aAAa,IAAII,MAAM,CAACC,QAAQ,CAACI,IAAI,KAAK,MAAM,EAAE;IAClD,OAAO,mBAAmB,CAAC,CAAC;EAChC;;EAEA;EACA;EACA,MAAMe,OAAO,GAAG,GAAGD,WAAW,mBAAmB;;EAEjD;EACA,IAAI,CAACA,WAAW,IAAIA,WAAW,KAAK,EAAE,EAAE;IACpC,OAAO,mBAAmB;EAC9B;EAEA,OAAOC,OAAO;AAClB,CAAC;AAED,OAAO,MAAMC,YAAY,GAAGH,aAAa,CAAC,CAAC;AAC3C,OAAO,MAAMI,YAAY,GAAGnB,cAAc,CAAC,CAAC;;AAE5C;AACA,OAAO,MAAMoB,WAAW,GAAIP,IAAI,IAAK;EACjC,MAAMG,WAAW,GAAGhB,cAAc,CAAC,CAAC;EACpC,MAAMqB,SAAS,GAAGR,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,GAAGD,IAAI,GAAG,IAAIA,IAAI,EAAE;;EAE1D;EACA,IAAIpB,aAAa,IAAII,MAAM,CAACC,QAAQ,CAACI,IAAI,KAAK,MAAM,EAAE;IAClD,OAAO,WAAWmB,SAAS,EAAE,CAAC,CAAC;EACnC;;EAEA;EACA,OAAO,GAAGL,WAAW,WAAWK,SAAS,EAAE;AAC/C,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC/B,MAAMN,WAAW,GAAGhB,cAAc,CAAC,CAAC;EAEpC,IAAIP,aAAa,IAAII,MAAM,CAACC,QAAQ,CAACI,IAAI,KAAK,MAAM,EAAE;IAClD,OAAO,GAAGL,MAAM,CAACC,QAAQ,CAACyB,MAAM,UAAU;EAC9C;EAEA,OAAO,GAAG1B,MAAM,CAACC,QAAQ,CAACyB,MAAM,GAAGP,WAAW,UAAU;AAC5D,CAAC;;AAED;AACA,IAAIvB,aAAa,EAAE;EACf+B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EACrDD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE/B,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EACjD4B,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE5B,MAAM,CAACC,QAAQ,CAAC4B,IAAI,CAAC;EACjDF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEN,YAAY,CAAC;EACnDK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEP,YAAY,CAAC;EAChDM,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEH,aAAa,CAAC,CAAC,CAAC;EAC5CE,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC;EACfD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAC/CD,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC;EAC/FD,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;EACrFD,OAAO,CAACC,GAAG,CAAC,yFAAyF,CAAC;EACtGD,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}