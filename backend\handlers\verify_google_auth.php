<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

include_once '../includes/db_connect.php';
require '../vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

try {
    $conn = getDBConnection();
    
    // Get request data
    $data = json_decode(file_get_contents("php://input"));
    
    // Validate required fields
    if (!isset($data->email) || !isset($data->code)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Email and verification code are required"
        ]);
        exit;
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT user_id, username, email, role FROM users WHERE email = :email");
    $stmt->bindParam(':email', $data->email);
    $stmt->execute();
    if ($stmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode([
            "success" => false,
            "message" => "User not found"
        ]);
        exit;
    }
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get user's Google Auth secret
    $stmt = $conn->prepare("
        SELECT secret_key, backup_codes FROM user_2fa 
        WHERE user_id = :user_id AND auth_type = 'google_auth' AND is_enabled = 1
    ");
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Google Authenticator is not set up for this user"
        ]);
        exit;
    }
    
    $authData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Check if code is a backup code
    $backupCodes = json_decode($authData['backup_codes'], true);
    if (in_array($data->code, $backupCodes)) {
        // Remove used backup code
        $backupCodes = array_diff($backupCodes, [$data->code]);
        $backupCodesJson = json_encode(array_values($backupCodes));

        // Update backup codes
        $stmt = $conn->prepare("
            UPDATE user_2fa 
            SET backup_codes = :backup_codes 
            WHERE user_id = :user_id AND auth_type = 'google_auth'
        ");
        $stmt->bindParam(':backup_codes', $backupCodesJson);
        $stmt->bindParam(':user_id', $user['user_id']);
        $stmt->execute();

        // Generate a simple session token (for basic session management)
        $token = bin2hex(random_bytes(32));
        try {
            $stmt = $conn->prepare("
                INSERT INTO user_sessions (user_id, token, expires_at) 
                VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR))
                ON DUPLICATE KEY UPDATE 
                token = VALUES(token), 
                expires_at = VALUES(expires_at)
            ");
            $stmt->execute([$user['user_id'], $token]);
        } catch (PDOException $e) {
            error_log("Session storage failed (table may not exist): " . $e->getMessage());
        }

        http_response_code(200);
        echo json_encode([
            "success" => true,
            "message" => "Backup code verified successfully",
            "userId" => $user['user_id'],
            "username" => $user['username'],
            "email" => $user['email'],
            "role" => $user['role'],
            "token" => $token
        ]);
        exit;
    }
    
    // Verify Google Auth code
    $google2fa = new Google2FA();
    $valid = $google2fa->verifyKey($authData['secret_key'], $data->code);

    if (!$valid) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Invalid verification code"
        ]);
        exit;
    }

    // Generate a simple session token (for basic session management)
    $token = bin2hex(random_bytes(32));
    try {
        $stmt = $conn->prepare("
            INSERT INTO user_sessions (user_id, token, expires_at) 
            VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR))
            ON DUPLICATE KEY UPDATE 
            token = VALUES(token), 
            expires_at = VALUES(expires_at)
        ");
        $stmt->execute([$user['user_id'], $token]);
    } catch (PDOException $e) {
        error_log("Session storage failed (table may not exist): " . $e->getMessage());
    }

    http_response_code(200);
    echo json_encode([
        "success" => true,
        "message" => "Google Authenticator code verified successfully",
        "userId" => $user['user_id'],
        "username" => $user['username'],
        "email" => $user['email'],
        "role" => $user['role'],
        "token" => $token
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Error: " . $e->getMessage()
    ]);
}
?>
