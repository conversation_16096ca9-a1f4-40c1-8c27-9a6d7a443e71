<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Checking admin 2FA setup...\n";
    
    // Check admin auth method
    $stmt = $conn->query('SELECT admin_id, username, auth_method, two_factor_enabled FROM admins WHERE admin_id = 1');
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "Admin: {$admin['username']}\n";
        echo "Auth Method: {$admin['auth_method']}\n";
        echo "2FA Enabled: " . ($admin['two_factor_enabled'] ? 'Yes' : 'No') . "\n";
        
        // Check admin 2FA setup
        $stmt = $conn->prepare('SELECT * FROM admin_2fa WHERE admin_id = ?');
        $stmt->execute([$admin['admin_id']]);
        $tfa = $stmt->fetch();
        
        if ($tfa) {
            echo "2FA Setup Found:\n";
            echo "  Secret: {$tfa['secret_key']}\n";
            echo "  Enabled: " . ($tfa['is_enabled'] ? 'Yes' : 'No') . "\n";
            echo "  Setup Complete: " . ($tfa['setup_completed'] ? 'Yes' : 'No') . "\n";
            echo "  Backup Codes: {$tfa['backup_codes']}\n";
        } else {
            echo "No 2FA setup found\n";
        }
        
        // Disable 2FA for testing
        echo "\nDisabling 2FA for testing...\n";
        $stmt = $conn->prepare('UPDATE admins SET auth_method = "password", two_factor_enabled = 0 WHERE admin_id = ?');
        $stmt->execute([$admin['admin_id']]);
        
        $stmt = $conn->prepare('UPDATE admin_2fa SET is_enabled = 0 WHERE admin_id = ?');
        $stmt->execute([$admin['admin_id']]);
        
        echo "2FA disabled for superadmin\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
