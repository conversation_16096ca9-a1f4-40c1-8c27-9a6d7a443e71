<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    // Check if file was uploaded
    if (!isset($_FILES['favicon']) || $_FILES['favicon']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception("No file uploaded or upload error occurred");
    }

    $file = $_FILES['favicon'];
    
    // Validate file type
    $allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/ico', 'image/icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    // Also check file extension
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowedExtensions = ['ico', 'png', 'jpg', 'jpeg', 'svg'];
    
    if (!in_array($mimeType, $allowedTypes) && !in_array($fileExtension, $allowedExtensions)) {
        throw new Exception("Invalid file type. Please upload an ICO, PNG, JPG, or SVG file.");
    }
    
    // Validate file size (max 2MB for favicon)
    if ($file['size'] > 2 * 1024 * 1024) {
        throw new Exception("File size must be less than 2MB");
    }
    
    // Create uploads directory if it doesn't exist
    $uploadDir = '../uploads/favicon/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Generate unique filename
    $timestamp = time();
    $originalName = pathinfo($file['name'], PATHINFO_FILENAME);
    $extension = $fileExtension;
    
    // For favicon, we'll keep it simple and always use .ico extension for the main favicon
    // But store the original file with its extension for backup
    $originalFileName = "favicon_original_{$timestamp}.{$extension}";
    $faviconFileName = "favicon.ico";
    
    $originalFilePath = $uploadDir . $originalFileName;
    $faviconFilePath = $uploadDir . $faviconFileName;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $originalFilePath)) {
        throw new Exception("Failed to save uploaded file");
    }
    
    // If the uploaded file is not ICO, we'll copy it as favicon.ico anyway
    // Modern browsers support PNG/JPG as favicon
    if ($extension !== 'ico') {
        copy($originalFilePath, $faviconFilePath);
    } else {
        // If it's already ICO, just copy it
        copy($originalFilePath, $faviconFilePath);
    }
    
    // Also copy to the frontend public directory for immediate use
    $publicFaviconPath = '../../frontend/public/favicon.ico';
    if (file_exists($faviconFilePath)) {
        copy($faviconFilePath, $publicFaviconPath);
    }
    
    // Update database with favicon path
    $conn = getDBConnection();
    
    // Check if favicon setting exists
    $checkStmt = $conn->prepare("SELECT setting_id FROM general_settings WHERE setting_name = 'site_favicon'");
    $checkStmt->execute();
    
    $relativePath = "uploads/favicon/{$faviconFileName}";
    
    if ($checkStmt->rowCount() > 0) {
        // Update existing setting
        $updateStmt = $conn->prepare("UPDATE general_settings SET setting_value = :value, updated_at = NOW() WHERE setting_name = 'site_favicon'");
        $updateStmt->bindParam(':value', $relativePath);
        $updateStmt->execute();
    } else {
        // Insert new setting
        $insertStmt = $conn->prepare("INSERT INTO general_settings (setting_name, setting_value, description, created_at, updated_at) VALUES ('site_favicon', :value, 'Path to the site favicon', NOW(), NOW())");
        $insertStmt->bindParam(':value', $relativePath);
        $insertStmt->execute();
    }
    
    // Clean up old favicon files (keep only the 5 most recent)
    $files = glob($uploadDir . 'favicon_original_*.{ico,png,jpg,jpeg,svg}', GLOB_BRACE);
    if (count($files) > 5) {
        // Sort by modification time, oldest first
        usort($files, function($a, $b) {
            return filemtime($a) - filemtime($b);
        });
        
        // Remove oldest files, keep 5 most recent
        $filesToDelete = array_slice($files, 0, count($files) - 5);
        foreach ($filesToDelete as $fileToDelete) {
            unlink($fileToDelete);
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Favicon uploaded successfully',
        'file_path' => $relativePath,
        'original_file' => "uploads/favicon/{$originalFileName}",
        'public_path' => '/favicon.ico'
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
