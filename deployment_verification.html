<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FanBet247 Deployment Verification</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .status-card h3 { margin-top: 0; color: #495057; }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .status-item:last-child { border-bottom: none; }
        .status-indicator {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-indicator.success { background: #28a745; color: white; }
        .status-indicator.error { background: #dc3545; color: white; }
        .status-indicator.warning { background: #ffc107; color: #212529; }
        .status-indicator.info { background: #17a2b8; color: white; }
        .btn {
            background: linear-gradient(145deg, #3498db, #2980b9);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 12px rgba(0,0,0,0.2); }
        .btn.success { background: linear-gradient(145deg, #27ae60, #229954); }
        .btn.danger { background: linear-gradient(145deg, #e74c3c, #c0392b); }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 FanBet247 Deployment Verification</h1>
        <p style="text-align: center; color: #7f8c8d;">
            <strong>Environment:</strong> <span id="environment"></span> | 
            <strong>Status:</strong> <span id="overallStatus">Checking...</span>
        </p>

        <!-- Progress Bar -->
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%;">0%</div>
        </div>

        <!-- Control Buttons -->
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="runFullVerification()">🔍 Run Full Verification</button>
            <button class="btn success" onclick="testLoginSystems()">🔐 Test Login Systems</button>
            <button class="btn danger" onclick="generateReport()">📋 Generate Report</button>
        </div>

        <!-- Status Grid -->
        <div class="status-grid">
            <!-- Server Status -->
            <div class="status-card">
                <h3>🖥️ Server Status</h3>
                <div class="status-item">
                    <span>Server Response</span>
                    <span class="status-indicator info" id="serverStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Database Connection</span>
                    <span class="status-indicator info" id="dbStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Backend Structure</span>
                    <span class="status-indicator info" id="backendStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>CORS Configuration</span>
                    <span class="status-indicator info" id="corsStatus">Checking...</span>
                </div>
            </div>

            <!-- API Endpoints -->
            <div class="status-card">
                <h3>📡 API Endpoints</h3>
                <div class="status-item">
                    <span>Admin Login Handler</span>
                    <span class="status-indicator info" id="adminEndpointStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>User Login Handler</span>
                    <span class="status-indicator info" id="userEndpointStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Homepage Data</span>
                    <span class="status-indicator info" id="homepageStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Currency System</span>
                    <span class="status-indicator info" id="currencyStatus">Checking...</span>
                </div>
            </div>

            <!-- Authentication Tests -->
            <div class="status-card">
                <h3>🔐 Authentication Tests</h3>
                <div class="status-item">
                    <span>Admin Login Test</span>
                    <span class="status-indicator info" id="adminLoginStatus">Not Tested</span>
                </div>
                <div class="status-item">
                    <span>User Login Test</span>
                    <span class="status-indicator info" id="userLoginStatus">Not Tested</span>
                </div>
                <div class="status-item">
                    <span>Session Management</span>
                    <span class="status-indicator info" id="sessionStatus">Not Tested</span>
                </div>
                <div class="status-item">
                    <span>Token Validation</span>
                    <span class="status-indicator info" id="tokenStatus">Not Tested</span>
                </div>
            </div>

            <!-- Frontend Status -->
            <div class="status-card">
                <h3>🎨 Frontend Status</h3>
                <div class="status-item">
                    <span>React Build</span>
                    <span class="status-indicator info" id="reactStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Static Assets</span>
                    <span class="status-indicator info" id="assetsStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Routing (.htaccess)</span>
                    <span class="status-indicator info" id="routingStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>API Configuration</span>
                    <span class="status-indicator info" id="apiConfigStatus">Checking...</span>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <h2>📊 Verification Results</h2>
        <div id="results" class="result" style="display: none;"></div>

        <!-- Recommendations -->
        <h2>💡 Recommendations</h2>
        <div id="recommendations" style="display: none;">
            <div class="status-card">
                <h3>Next Steps</h3>
                <ul id="recommendationsList"></ul>
            </div>
        </div>
    </div>

    <script>
        let verificationProgress = 0;
        let totalChecks = 12;
        let results = {};

        // Update progress bar
        function updateProgress() {
            verificationProgress++;
            const percentage = Math.round((verificationProgress / totalChecks) * 100);
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '%';
        }

        // Update status indicator
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator ${status}`;
            element.textContent = text;
            updateProgress();
        }

        // Generic API call function
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    ...options
                });
                
                const data = await response.text();
                let jsonData;
                try {
                    jsonData = JSON.parse(data);
                } catch (e) {
                    jsonData = { raw: data };
                }
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: jsonData,
                    response: response
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // Run full verification
        async function runFullVerification() {
            verificationProgress = 0;
            document.getElementById('overallStatus').textContent = 'Running...';
            document.getElementById('results').style.display = 'block';
            document.getElementById('results').textContent = 'Starting verification...\n';
            
            let output = 'FanBet247 Deployment Verification Report\n';
            output += '==========================================\n';
            output += `Started: ${new Date().toLocaleString()}\n\n`;

            // Test 1: Server Response
            output += '1. Testing server response...\n';
            const serverTest = await apiCall('/api_config_test.php?action=ping');
            if (serverTest.success) {
                updateStatus('serverStatus', 'success', 'Online');
                output += '   ✅ Server is responding\n';
                results.server = true;
            } else {
                updateStatus('serverStatus', 'error', 'Offline');
                output += '   ❌ Server not responding\n';
                results.server = false;
            }

            // Test 2: Database Connection
            output += '\n2. Testing database connection...\n';
            const dbTest = await apiCall('/api_config_test.php?action=test_db');
            if (dbTest.success && dbTest.data.status === 'success') {
                updateStatus('dbStatus', 'success', 'Connected');
                output += '   ✅ Database connection successful\n';
                output += `   📊 Admin count: ${dbTest.data.data.admin_count}\n`;
                results.database = true;
            } else {
                updateStatus('dbStatus', 'error', 'Failed');
                output += '   ❌ Database connection failed\n';
                results.database = false;
            }

            // Test 3: Backend Structure
            output += '\n3. Testing backend structure...\n';
            const structureTest = await apiCall('/api_config_test.php?action=info');
            if (structureTest.success) {
                const structure = structureTest.data.data.file_structure;
                const allGood = structure.backend_dir && structure.handlers_dir && structure.includes_dir;
                updateStatus('backendStatus', allGood ? 'success' : 'warning', allGood ? 'Complete' : 'Incomplete');
                output += `   Backend dir: ${structure.backend_dir ? '✅' : '❌'}\n`;
                output += `   Handlers dir: ${structure.handlers_dir ? '✅' : '❌'}\n`;
                output += `   Includes dir: ${structure.includes_dir ? '✅' : '❌'}\n`;
                results.backend = allGood;
            } else {
                updateStatus('backendStatus', 'error', 'Failed');
                output += '   ❌ Backend structure test failed\n';
                results.backend = false;
            }

            // Test 4: CORS Configuration
            output += '\n4. Testing CORS configuration...\n';
            const corsTest = await apiCall('/api_config_test.php?action=test_cors');
            if (corsTest.success) {
                updateStatus('corsStatus', 'success', 'Configured');
                output += '   ✅ CORS headers properly configured\n';
                results.cors = true;
            } else {
                updateStatus('corsStatus', 'error', 'Failed');
                output += '   ❌ CORS configuration issues\n';
                results.cors = false;
            }

            // Test 5: Admin Endpoint
            output += '\n5. Testing admin login endpoint...\n';
            const adminEndpointTest = await apiCall('/api_config_test.php?action=test_admin_endpoint');
            if (adminEndpointTest.success && adminEndpointTest.data.data.file_exists) {
                updateStatus('adminEndpointStatus', 'success', 'Available');
                output += '   ✅ Admin login handler found\n';
                results.adminEndpoint = true;
            } else {
                updateStatus('adminEndpointStatus', 'error', 'Missing');
                output += '   ❌ Admin login handler missing\n';
                results.adminEndpoint = false;
            }

            // Test 6: User Endpoint
            output += '\n6. Testing user login endpoint...\n';
            const userEndpointTest = await apiCall('/api_config_test.php?action=test_user_endpoint');
            if (userEndpointTest.success && userEndpointTest.data.data.file_exists) {
                updateStatus('userEndpointStatus', 'success', 'Available');
                output += '   ✅ User login handler found\n';
                results.userEndpoint = true;
            } else {
                updateStatus('userEndpointStatus', 'error', 'Missing');
                output += '   ❌ User login handler missing\n';
                results.userEndpoint = false;
            }

            // Test 7: Homepage Data
            output += '\n7. Testing homepage data endpoint...\n';
            const homepageTest = await apiCall('/backend/handlers/homepage_data.php');
            if (homepageTest.success) {
                updateStatus('homepageStatus', 'success', 'Working');
                output += '   ✅ Homepage data endpoint working\n';
                results.homepage = true;
            } else {
                updateStatus('homepageStatus', 'error', 'Failed');
                output += '   ❌ Homepage data endpoint failed\n';
                results.homepage = false;
            }

            // Test 8: Currency System
            output += '\n8. Testing currency system...\n';
            const currencyTest = await apiCall('/backend/handlers/get_currencies.php');
            if (currencyTest.success) {
                updateStatus('currencyStatus', 'success', 'Working');
                output += '   ✅ Currency system working\n';
                results.currency = true;
            } else {
                updateStatus('currencyStatus', 'error', 'Failed');
                output += '   ❌ Currency system failed\n';
                results.currency = false;
            }

            // Test 9: React Build
            output += '\n9. Testing React build...\n';
            const reactTest = await apiCall('/static/js/', { method: 'HEAD' });
            if (reactTest.success || reactTest.status === 403) { // 403 means directory exists but listing disabled
                updateStatus('reactStatus', 'success', 'Deployed');
                output += '   ✅ React build deployed\n';
                results.react = true;
            } else {
                updateStatus('reactStatus', 'warning', 'Check Required');
                output += '   ⚠️ React build status unclear\n';
                results.react = false;
            }

            // Test 10: Static Assets
            output += '\n10. Testing static assets...\n';
            const assetsTest = await apiCall('/favicon.ico', { method: 'HEAD' });
            if (assetsTest.success) {
                updateStatus('assetsStatus', 'success', 'Available');
                output += '   ✅ Static assets available\n';
                results.assets = true;
            } else {
                updateStatus('assetsStatus', 'warning', 'Some Missing');
                output += '   ⚠️ Some static assets missing\n';
                results.assets = false;
            }

            // Test 11: Routing
            output += '\n11. Testing routing (.htaccess)...\n';
            const routingTest = await apiCall('/.htaccess', { method: 'HEAD' });
            if (routingTest.status === 403) { // 403 means file exists but not accessible (good)
                updateStatus('routingStatus', 'success', 'Configured');
                output += '   ✅ .htaccess file configured\n';
                results.routing = true;
            } else {
                updateStatus('routingStatus', 'warning', 'Check Required');
                output += '   ⚠️ .htaccess status unclear\n';
                results.routing = false;
            }

            // Test 12: API Configuration
            output += '\n12. Testing API configuration...\n';
            const apiConfigTest = await apiCall('/debug.php?action=ping');
            if (apiConfigTest.success) {
                updateStatus('apiConfigStatus', 'success', 'Working');
                output += '   ✅ API configuration working\n';
                results.apiConfig = true;
            } else {
                updateStatus('apiConfigStatus', 'error', 'Failed');
                output += '   ❌ API configuration failed\n';
                results.apiConfig = false;
            }

            // Calculate overall status
            const successCount = Object.values(results).filter(r => r === true).length;
            const totalTests = Object.keys(results).length;
            const successRate = Math.round((successCount / totalTests) * 100);

            output += `\n\nVerification Complete!\n`;
            output += `Success Rate: ${successCount}/${totalTests} (${successRate}%)\n`;

            if (successRate >= 90) {
                document.getElementById('overallStatus').textContent = '✅ Excellent';
                document.getElementById('overallStatus').style.color = '#28a745';
            } else if (successRate >= 70) {
                document.getElementById('overallStatus').textContent = '⚠️ Good';
                document.getElementById('overallStatus').style.color = '#ffc107';
            } else {
                document.getElementById('overallStatus').textContent = '❌ Needs Work';
                document.getElementById('overallStatus').style.color = '#dc3545';
            }

            document.getElementById('results').textContent = output;
            generateRecommendations();
        }

        // Test login systems
        async function testLoginSystems() {
            let output = 'Login Systems Test\n';
            output += '==================\n\n';

            // Test admin login
            output += '1. Testing admin login...\n';
            const adminLoginTest = await apiCall('/backend/handlers/admin_login_handler.php', {
                method: 'POST',
                body: JSON.stringify({
                    identifier: 'admin',
                    password: 'loving12'
                })
            });

            if (adminLoginTest.success && adminLoginTest.data.success) {
                updateStatus('adminLoginStatus', 'success', 'Working');
                output += '   ✅ Admin login successful\n';
            } else {
                updateStatus('adminLoginStatus', 'error', 'Failed');
                output += '   ❌ Admin login failed\n';
                output += `   Error: ${adminLoginTest.data?.message || adminLoginTest.error}\n`;
            }

            // Test user login (if endpoint exists)
            output += '\n2. Testing user login...\n';
            const userLoginTest = await apiCall('/backend/handlers/login.php', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'testuser',
                    password: 'password123'
                })
            });

            if (userLoginTest.success) {
                updateStatus('userLoginStatus', 'success', 'Working');
                output += '   ✅ User login endpoint responding\n';
            } else {
                updateStatus('userLoginStatus', 'warning', 'Check Required');
                output += '   ⚠️ User login needs verification\n';
            }

            updateStatus('sessionStatus', 'info', 'Manual Test Required');
            updateStatus('tokenStatus', 'info', 'Manual Test Required');

            document.getElementById('results').style.display = 'block';
            document.getElementById('results').textContent = output;
        }

        // Generate recommendations
        function generateRecommendations() {
            const recommendations = [];

            if (!results.server) {
                recommendations.push('Fix server connectivity issues');
            }
            if (!results.database) {
                recommendations.push('Check database configuration and credentials');
            }
            if (!results.backend) {
                recommendations.push('Ensure all backend directories and files are uploaded');
            }
            if (!results.cors) {
                recommendations.push('Configure CORS headers properly');
            }
            if (!results.adminEndpoint || !results.userEndpoint) {
                recommendations.push('Upload missing login handler files');
            }
            if (!results.homepage || !results.currency) {
                recommendations.push('Check API endpoint functionality');
            }
            if (!results.react || !results.assets) {
                recommendations.push('Run npm run build and upload frontend files');
            }
            if (!results.routing) {
                recommendations.push('Create and configure .htaccess file');
            }
            if (!results.apiConfig) {
                recommendations.push('Fix API configuration issues');
            }

            if (recommendations.length === 0) {
                recommendations.push('🎉 All systems are working correctly!');
                recommendations.push('Test the actual login functionality manually');
                recommendations.push('Monitor error logs for any issues');
            }

            const recommendationsDiv = document.getElementById('recommendations');
            const recommendationsList = document.getElementById('recommendationsList');
            
            recommendationsList.innerHTML = '';
            recommendations.forEach(rec => {
                const li = document.createElement('li');
                li.textContent = rec;
                recommendationsList.appendChild(li);
            });
            
            recommendationsDiv.style.display = 'block';
        }

        // Generate report
        function generateReport() {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const report = document.documentElement.outerHTML;
            
            const blob = new Blob([report], { type: 'text/html' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `fanbet247_verification_report_${timestamp}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('environment').textContent = 
                window.location.hostname === 'localhost' ? 'Development' : 'Production';
        });
    </script>
</body>
</html>
