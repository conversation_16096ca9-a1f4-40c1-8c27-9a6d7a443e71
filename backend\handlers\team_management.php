<?php
header("Access-Control-Allow-Origin: *"); 
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
  exit(0);
}

include_once '../includes/db_connect.php';
$conn = getDBConnection();

function jsonResponse($status, $message, $data = []) {
  http_response_code($status);
  echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
  exit();
}

// GET: Retrieve all teams
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
  try {
    // Query to get teams with their supporter counts
    $query = "SELECT 
        teams.*,
        (SELECT COUNT(*) FROM users WHERE users.favorite_team = teams.name) as user_count
    FROM teams";

    $stmt = $conn->query($query);
    $teams = $stmt->fetchAll(PDO::FETCH_ASSOC);
    jsonResponse(200, 'Teams fetched successfully', $teams);
  } catch (PDOException $e) {
    jsonResponse(500, 'Database error: ' . $e->getMessage());
  }
}

// POST: Create a new team
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  try {
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
      $targetDir = '../uploads/';
      if (!file_exists($targetDir)) {
        mkdir($targetDir, 0777, true);
      }
      
      $fileName = uniqid() . '_' . basename($_FILES['logo']['name']);
      $targetFile = $targetDir . $fileName;
      $imageFileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));

      // Check if image file is an actual image
      $check = getimagesize($_FILES['logo']['tmp_name']);
      if ($check === false) {
        jsonResponse(400, 'File is not an image.', ['success' => false, 'message' => 'File is not an image']);
      }

      // Check file size (optional)
      if ($_FILES['logo']['size'] > 500000) { // 500KB limit
        jsonResponse(400, 'Sorry, your file is too large.', ['success' => false, 'message' => 'File is too large']);
      }

      // Allow certain file formats
      if ($imageFileType != 'jpg' && $imageFileType != 'png' && $imageFileType != 'jpeg') {
        jsonResponse(400, 'Sorry, only JPG, JPEG, and PNG files are allowed.', ['success' => false, 'message' => 'Invalid file format']);
      }

      if (move_uploaded_file($_FILES['logo']['tmp_name'], $targetFile)) {
        $name = $_POST['name'];
        $logoPath = 'uploads/' . $fileName; // Path to store in the database

        $stmt = $conn->prepare("INSERT INTO teams (name, logo) VALUES (:name, :logo)");
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':logo', $logoPath);

        if ($stmt->execute()) {
          jsonResponse(201, 'Team created successfully', ['success' => true]);
        } else {
          jsonResponse(500, 'Failed to create team', ['success' => false, 'message' => 'Failed to insert into database']);
        }
      } else {
        jsonResponse(500, 'Sorry, there was an error uploading your file.', ['success' => false, 'message' => 'Error uploading file']);
      }
    } else {
      jsonResponse(400, 'Logo upload failed.', ['success' => false, 'message' => 'Logo upload failed']);
    }
  } catch (PDOException $e) {
    jsonResponse(500, 'Database error: ' . $e->getMessage(), ['success' => false, 'message' => $e->getMessage()]);
  }
}

// DELETE: Delete a team
if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
  $id = isset($_GET['id']) ? $_GET['id'] : null;
  if (!$id) {
    jsonResponse(400, 'Team ID is required');
  }

  try {
    // Delete the logo image from the server
    $stmt = $conn->prepare("SELECT logo FROM teams WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $team = $stmt->fetch();

    if ($team && file_exists('../' . $team['logo'])) {
      unlink('../' . $team['logo']);
    }

    // Delete the team from the database
    $stmt = $conn->prepare("DELETE FROM teams WHERE id = :id");
    $stmt->bindParam(':id', $id);
    if ($stmt->execute()) {
      jsonResponse(200, 'Team deleted successfully');
    } else {
      jsonResponse(500, 'Failed to delete team');
    }
  } catch (PDOException $e) {
    jsonResponse(500, 'Database error: ' . $e->getMessage());
  }
}
