<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Database connection
    $conn = new PDO(
        "mysql:host=localhost;dbname=fanbet247",
        'root',
        'root',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Get site configuration
    $stmt = $conn->prepare("SELECT setting_name, setting_value FROM general_settings WHERE setting_name IN ('site_name', 'site_logo', 'contact_email', 'footer_text')");
    $stmt->execute();
    
    $config = [
        'site_name' => 'FanBet247',
        'site_logo' => 'uploads/logo/fanbet247_logo.svg',
        'contact_email' => '<EMAIL>',
        'footer_text' => '© 2024 FanBet247. All rights reserved.'
    ];
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $config[$row['setting_name']] = $row['setting_value'];
    }
    
    echo json_encode([
        'success' => true,
        'config' => $config
    ]);
    
} catch (PDOException $e) {
    // Return default values if database is not available
    echo json_encode([
        'success' => true,
        'config' => [
            'site_name' => 'FanBet247',
            'site_logo' => 'uploads/logo/fanbet247_logo.svg',
            'contact_email' => '<EMAIL>',
            'footer_text' => '© 2024 FanBet247. All rights reserved.'
        ]
    ]);
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
