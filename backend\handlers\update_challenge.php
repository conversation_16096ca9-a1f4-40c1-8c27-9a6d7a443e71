<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$challenge_id = $_POST['challenge_id'] ?? null;

if (!$challenge_id) {
echo json_encode(['success' => false, 'message' => 'Challenge ID required']);
exit;
}

try {
// First, get the team logos
$team_a = $_POST['team_a'];
$team_b = $_POST['team_b'];
    
$logoQuery = "SELECT logo FROM teams WHERE name = :name";
    
$stmt = $conn->prepare($logoQuery);
$stmt->bindParam(':name', $team_a);
$stmt->execute();
$logo1 = $stmt->fetchColumn();
    
$stmt = $conn->prepare($logoQuery);
$stmt->bindParam(':name', $team_b);
$stmt->execute();
$logo2 = $stmt->fetchColumn();

// Update the challenge
$sql = "UPDATE challenges SET 
        team_a = :team_a,
        team_b = :team_b,
        logo1 = :logo1,
        logo2 = :logo2,
        odds_team_a = :odds_team_a,
        odds_team_b = :odds_team_b,
        team_a_goal_advantage = :team_a_goal_advantage,
        team_b_goal_advantage = :team_b_goal_advantage,
        start_time = :start_time,
        end_time = :end_time,
        match_date = :match_date,
        status = :status
        WHERE challenge_id = :challenge_id";

$stmt = $conn->prepare($sql);
    
$stmt->bindParam(':challenge_id', $challenge_id);
$stmt->bindParam(':team_a', $team_a);
$stmt->bindParam(':team_b', $team_b);
$stmt->bindParam(':logo1', $logo1);
$stmt->bindParam(':logo2', $logo2);
$stmt->bindParam(':odds_team_a', $_POST['odds_team_a']);
$stmt->bindParam(':odds_team_b', $_POST['odds_team_b']);
$stmt->bindParam(':team_a_goal_advantage', $_POST['team_a_goal_advantage']);
$stmt->bindParam(':team_b_goal_advantage', $_POST['team_b_goal_advantage']);
$stmt->bindParam(':start_time', $_POST['start_time']);
$stmt->bindParam(':end_time', $_POST['end_time']);
$stmt->bindParam(':match_date', $_POST['match_date']);
$stmt->bindParam(':status', $_POST['status']);

$stmt->execute();
    
echo json_encode([
    'success' => true, 
    'message' => 'Challenge updated successfully'
]);

} catch (PDOException $e) {
echo json_encode([
    'success' => false, 
    'message' => 'Database error: ' . $e->getMessage()
]);
}
