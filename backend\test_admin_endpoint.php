<?php
/**
 * Test Admin Login Endpoint
 */

echo "🔧 Testing Admin Login Endpoint...\n\n";

// Test the endpoint with a POST request
$url = 'http://localhost/FanBet247/backend/handlers/admin_login_handler.php';
$data = json_encode([
    'identifier' => 'superadmin',
    'password' => 'admin123'
]);

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n",
        'method' => 'POST',
        'content' => $data
    ]
];

$context = stream_context_create($options);

echo "Making request to: $url\n";
echo "Request data: $data\n\n";

try {
    $result = file_get_contents($url, false, $context);
    
    if ($result === false) {
        echo "❌ Request failed!\n";
        echo "HTTP Response Headers:\n";
        print_r($http_response_header);
    } else {
        echo "✅ Request successful!\n";
        echo "Response: $result\n";
        
        $responseData = json_decode($result, true);
        if ($responseData) {
            echo "\nParsed response:\n";
            print_r($responseData);
        }
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>