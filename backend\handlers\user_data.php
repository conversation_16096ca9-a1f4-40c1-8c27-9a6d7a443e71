<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

if (!$conn) {
    http_response_code(500);
    echo json_encode(["success" => false, "message" => "Database connection failed"]);
    exit;
}

// Function to get user ID from token
function getUserIdFromToken($conn) {
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';

    if ($authHeader && str_starts_with($authHeader, 'Bearer ')) {
        $token = substr($authHeader, 7);

        $stmt = $conn->prepare("
            SELECT user_id FROM user_sessions
            WHERE token = ? AND expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);

        return $session ? $session['user_id'] : null;
    }

    return null;
}

// Try to get userId from token first, then fall back to URL parameters
$userId = getUserIdFromToken($conn);
if (!$userId) {
    // Accept both 'id' and 'userId' parameters for backward compatibility
    $userId = isset($_GET['userId']) ? $_GET['userId'] : (isset($_GET['id']) ? $_GET['id'] : null);
}

// Debug logging
error_log("🔍 USER_DATA DEBUG - Received userId: " . $userId);
error_log("🔍 USER_DATA DEBUG - GET parameters: " . json_encode($_GET));

if ($userId) {
    try {
        $query = "SELECT u.user_id, u.username, u.full_name, u.email, u.favorite_team, u.balance, 
                COALESCE((SELECT SUM(points) FROM user_achievements WHERE user_id = u.user_id), 0) as points 
                FROM users u WHERE u.user_id = :userId";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(":userId", $userId);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            // Debug logging
            error_log("💰 USER_DATA DEBUG - Found user: " . $user['username'] . " (ID: " . $user['user_id'] . ")");
            error_log("💰 USER_DATA DEBUG - Balance: " . $user['balance']);

            // Ensure numeric values are properly formatted
            $user['balance'] = floatval($user['balance']);
            $user['points'] = intval($user['points']);
            
                echo json_encode([
                    "success" => true,
                    "balance" => $user['balance'],
                    "points" => $user['points'],
                    "username" => $user['username'],
                    "email" => $user['email'],
                    "fullName" => $user['full_name'],
                    "role" => $user['role'] ?? 'user',
                    "user" => $user
                ]);
        } else {
            http_response_code(404);
            echo json_encode(["success" => false, "message" => "User not found"]);
        }
    } catch (PDOException $e) {
        error_log("Error fetching user data: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(["success" => false, "message" => "Error fetching user data"]);
    }
} else {
    http_response_code(400);
    echo json_encode(["success" => false, "message" => "User ID not provided"]);
}
?>
