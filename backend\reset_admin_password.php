<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    $adminId = 1; // Jamesbong01
    $newPassword = 'admin123';
    $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE user_id = ? AND role = 'admin'");
    $stmt->execute([$passwordHash, $adminId]);
    
    if ($stmt->rowCount() > 0) {
        echo "Admin password reset successfully!\n";
        echo "Username: Jamesbong01\n";
        echo "Email: <EMAIL>\n";
        echo "New Password: $newPassword\n";
    } else {
        echo "Failed to reset password or user not found.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
