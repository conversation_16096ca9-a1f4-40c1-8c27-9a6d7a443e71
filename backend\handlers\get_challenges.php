<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

$conn = getDBConnection();

if (!$conn) {
    http_response_code(500);
    echo json_encode(["success" => false, "message" => "Database connection failed"]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['status'])) {
        $status = $_GET['status'];
        try {
            $stmt = $conn->prepare("SELECT * FROM challenges WHERE status = ? ORDER BY challenge_date DESC");
            $stmt->execute([$status]);
            $challenges = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo json_encode(["success" => true, "challenges" => $challenges]);
        } catch (PDOException $e) {
            error_log("Error fetching challenges by status: " . $e->getMessage());
            http_response_code(500); 
            echo json_encode(["success" => false, "message" => "Error fetching challenges"]);
        }
    } else if (isset($_GET['id'])) {
        $challengeId = $_GET['id'];

        try {
            $stmt = $conn->prepare("SELECT * FROM challenges WHERE challenge_id = ?");
            $stmt->execute([$challengeId]);

            if ($stmt->rowCount() > 0) {
                $challenge = $stmt->fetch(PDO::FETCH_ASSOC);
                echo json_encode(["success" => true, "challenge" => $challenge]);
            } else {
                echo json_encode(["success" => false, "message" => "Challenge not found"]); 
            }
        } catch (PDOException $e) {
            error_log("Error fetching challenge by ID: " . $e->getMessage());
            http_response_code(500); 
            echo json_encode(["success" => false, "message" => "Error fetching challenge"]);
        }
    } else {
        try {
            $stmt = $conn->prepare("SELECT * FROM challenges ORDER BY challenge_date DESC");
            $stmt->execute();
            $challenges = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode(["success" => true, "challenges" => $challenges]); 
        } catch (PDOException $e) {
            error_log("Error fetching all challenges: " . $e->getMessage());
            http_response_code(500); 
            echo json_encode(["success" => false, "message" => "Error fetching challenges"]);
        }
    }
}
?>
