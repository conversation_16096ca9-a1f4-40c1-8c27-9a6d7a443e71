<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Checking for admins table...\n";
    try {
        $stmt = $conn->query('DESCRIBE admins');
        echo "✅ Admins table exists\n";
        
        $stmt = $conn->query('SELECT COUNT(*) as count FROM admins');
        $result = $stmt->fetch();
        echo "   Admin records: " . $result['count'] . "\n";
        
    } catch(Exception $e) {
        echo "❌ Admins table does not exist: " . $e->getMessage() . "\n";
    }
    
    echo "\nChecking users table for admin role...\n";
    $stmt = $conn->query('SELECT COUNT(*) as count FROM users WHERE role = "admin"');
    $result = $stmt->fetch();
    echo "✅ Users with admin role: " . $result['count'] . "\n";
    
    if ($result['count'] > 0) {
        $stmt = $conn->query('SELECT user_id, username, email, role FROM users WHERE role = "admin"');
        while($row = $stmt->fetch()) {
            echo "   - {$row['username']} ({$row['email']}) - ID: {$row['user_id']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
