<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Database connection
    $conn = new PDO(
        "mysql:host=localhost;dbname=fanbet247",
        'root',
        'root',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Create smtp_settings table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS smtp_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            host VARCHAR(255) NOT NULL DEFAULT 'smtp.gmail.com',
            port INT NOT NULL DEFAULT 587,
            username VARCHAR(255) NOT NULL DEFAULT '',
            password VARCHAR(255) NOT NULL DEFAULT '',
            encryption ENUM('none', 'ssl', 'tls') NOT NULL DEFAULT 'tls',
            from_email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>',
            from_name VARCHAR(255) NOT NULL DEFAULT 'FanBet247',
            is_active BOOLEAN NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    $conn->exec($createTableSQL);
    
    // Get request data
    $data = json_decode(file_get_contents("php://input"));

    // Handle both direct data and nested settings object
    $settings = isset($data->settings) ? $data->settings : $data;

    // Validate required fields
    if (
        !isset($settings->host) ||
        !isset($settings->port) ||
        !isset($settings->username) ||
        !isset($settings->password) ||
        !isset($settings->encryption) ||
        !isset($settings->from_email) ||
        !isset($settings->from_name)
    ) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Missing required fields"
        ]);
        exit;
    }
    
    // Check if settings already exist
    $checkStmt = $conn->prepare("SELECT id FROM smtp_settings LIMIT 1");
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() > 0) {
        // Update existing settings
        $row = $checkStmt->fetch(PDO::FETCH_ASSOC);
        $id = $row['id'];
        
        $stmt = $conn->prepare("
            UPDATE smtp_settings 
            SET host = :host, 
                port = :port, 
                username = :username, 
                password = :password, 
                encryption = :encryption, 
                from_email = :from_email, 
                from_name = :from_name, 
                is_active = :is_active
            WHERE id = :id
        ");
        
        $stmt->bindParam(':id', $id);
    } else {
        // Insert new settings
        $stmt = $conn->prepare("
            INSERT INTO smtp_settings 
            (host, port, username, password, encryption, from_email, from_name, is_active) 
            VALUES 
            (:host, :port, :username, :password, :encryption, :from_email, :from_name, :is_active)
        ");
    }
    
    // Handle password - if password is masked (••••••••), get current password
    $password = $settings->password;
    if ($settings->password === '••••••••' && isset($id)) {
        // Get current password from database for updates
        $currentStmt = $conn->prepare("SELECT password FROM smtp_settings WHERE id = :id");
        $currentStmt->bindParam(':id', $id);
        $currentStmt->execute();
        $currentRow = $currentStmt->fetch(PDO::FETCH_ASSOC);
        $password = $currentRow['password'];
    }

    // Bind parameters
    $stmt->bindParam(':host', $settings->host);
    $stmt->bindParam(':port', $settings->port);
    $stmt->bindParam(':username', $settings->username);
    $stmt->bindParam(':password', $password);
    $stmt->bindParam(':encryption', $settings->encryption);
    $stmt->bindParam(':from_email', $settings->from_email);
    $stmt->bindParam(':from_name', $settings->from_name);
    $isActive = isset($settings->is_active) ? ($settings->is_active === 'true' || $settings->is_active === true ? 1 : 0) : 1;
    $stmt->bindParam(':is_active', $isActive);
    
    if ($stmt->execute()) {
        http_response_code(200);
        echo json_encode([
            "success" => true,
            "message" => "SMTP settings updated successfully"
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Failed to update SMTP settings"
        ]);
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
}
?>
