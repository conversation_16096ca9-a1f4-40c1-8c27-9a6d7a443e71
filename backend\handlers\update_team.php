<?php
header("Access-Control-Allow-Origin: *"); 
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS"); // Adjusted to POST
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
  exit(0);
}

include_once '../includes/db_connect.php';
$conn = getDBConnection();

function jsonResponse($status, $message, $data = []) {
  http_response_code($status);
  echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
  exit();
}

// Update a team
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $id = isset($_GET['id']) ? $_GET['id'] : null;
  if (!$id) {
    jsonResponse(400, 'Team ID is required', ['success' => false]);
  }

  try {
    $name = $_POST['name'];
    $logoPath = null; // Initialize logo path

    // Handle logo upload if a new logo is provided
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
      $targetDir =  '../uploads/';
      $targetFile = $targetDir . basename($_FILES['logo']['name']);
      $imageFileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));

      // Check if image file is an actual image
      $check = getimagesize($_FILES['logo']['tmp_name']);
      if ($check === false) {
        jsonResponse(400, 'File is not an image.', ['success' => false]);
      }

      // Check file size (optional)
      if ($_FILES['logo']['size'] > 500000) {
        jsonResponse(400, 'Sorry, your file is too large.', ['success' => false]);
      }

      // Allow certain file formats
      if ($imageFileType != 'jpg' && $imageFileType != 'png' && $imageFileType != 'jpeg' && $imageFileType != 'svg') {
        jsonResponse(400, 'Sorry, only JPG, JPEG, PNG & SVG files are allowed.', ['success' => false]);
      }

      if (move_uploaded_file($_FILES['logo']['tmp_name'], $targetFile)) {
        $logoPath = 'uploads/' . basename($_FILES['logo']['name']);
      } else {
        jsonResponse(500, 'Sorry, there was an error uploading your file.', ['success' => false]);
      }
    }

    // Prepare the SQL query based on whether a new logo was uploaded
    if ($logoPath) {
      $stmt = $conn->prepare("UPDATE teams SET name = :name, logo = :logo WHERE id = :id");
      $stmt->bindParam(':logo', $logoPath);
    } else {
      $stmt = $conn->prepare("UPDATE teams SET name = :name WHERE id = :id");
    }

    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':id', $id);

    if ($stmt->execute()) {
      jsonResponse(200, 'Team updated successfully', ['success' => true]);
    } else {
      jsonResponse(500, 'Failed to update team', ['success' => false]);
    }
  } catch (PDOException $e) {
    jsonResponse(500, 'Database error: ' . $e->getMessage(), ['success' => false]);
  }
}
?>
