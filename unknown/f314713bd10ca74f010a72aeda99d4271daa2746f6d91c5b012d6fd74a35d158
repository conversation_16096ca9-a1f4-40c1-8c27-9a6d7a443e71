<?php
/**
 * Clear Rate Limits for Testing
 * Clears all rate limiting records to allow testing
 */

header('Content-Type: text/plain');

require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    echo "Clearing rate limits for testing...\n\n";

    // Clear admin login attempts
    $stmt = $conn->query("DELETE FROM admin_login_attempts");
    $deletedAttempts = $stmt->rowCount();
    echo "✅ Cleared $deletedAttempts login attempt records\n";

    // Clear used OTP records
    $stmt = $conn->query("DELETE FROM admin_otp WHERE used = 1 OR expires_at < NOW()");
    $deletedOtps = $stmt->rowCount();
    echo "✅ Cleared $deletedOtps expired/used OTP records\n";

    // Reset admin account lockouts
    $stmt = $conn->query("UPDATE admins SET failed_login_attempts = 0, account_locked_until = NULL");
    $resetAccounts = $stmt->rowCount();
    echo "✅ Reset $resetAccounts admin account lockouts\n";

    // Clear recent auth logs (optional - for cleaner testing)
    $stmt = $conn->query("DELETE FROM admin_auth_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $deletedLogs = $stmt->rowCount();
    echo "✅ Cleared $deletedLogs old auth log records\n";

    echo "\n🎉 Rate limits cleared! You can now test OTP functionality.\n";

} catch (Exception $e) {
    echo "❌ Failed to clear rate limits: " . $e->getMessage() . "\n";
}
?>
