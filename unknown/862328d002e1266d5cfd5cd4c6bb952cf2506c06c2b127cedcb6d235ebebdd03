<?php
/**
 * Send OTP to Admin Email
 * Generates and sends OTP code to admin's registered email address
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';
require_once '../includes/rate_limiter.php';
require_once '../includes/audit_logger.php';
require_once '../vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    $rateLimiter = new AdminRateLimiter($conn);
    $auditLogger = new AdminAuditLogger($conn);

    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['admin_id'])) {
        throw new Exception("Admin ID required");
    }
    
    $adminId = $input['admin_id'];
    
    // Verify admin exists and get details
    $stmt = $conn->prepare("SELECT admin_id, username, email, auth_method FROM admins WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Admin not found");
    }
    
    // Check if OTP is enabled globally
    $stmt = $conn->prepare("SELECT setting_value FROM admin_auth_settings WHERE setting_name = 'admin_otp_enabled'");
    $stmt->execute();
    $otpEnabled = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$otpEnabled || $otpEnabled['setting_value'] !== 'true') {
        $auditLogger->logSecurityIncident(
            'unauthorized_otp_request',
            'OTP request when OTP is disabled',
            $adminId,
            'WARNING'
        );
        throw new Exception("OTP authentication is not enabled");
    }

    // Check rate limiting
    if ($rateLimiter->isRateLimited($_SERVER['REMOTE_ADDR'], 'otp', 'ip')) {
        $auditLogger->logSecurityIncident(
            'rate_limit_exceeded',
            'OTP request rate limit exceeded',
            $adminId,
            'WARNING'
        );
        throw new Exception("Too many OTP requests. Please wait before requesting another OTP.");
    }
    
    // Get OTP settings
    $stmt = $conn->query("SELECT setting_name, setting_value FROM admin_auth_settings WHERE setting_name IN ('admin_otp_expiry_time', 'admin_max_otp_attempts')");
    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_name']] = $row['setting_value'];
    }
    
    $otpExpiryTime = $settings['admin_otp_expiry_time'] ?? 300; // 5 minutes default
    $maxAttempts = $settings['admin_max_otp_attempts'] ?? 3;
    
    // Check for existing active OTP (check expiry in PHP to avoid timezone issues)
    $stmt = $conn->prepare("SELECT id, attempts, expires_at FROM admin_otp WHERE admin_id = ? AND used = 0 ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$adminId]);
    $existingOtp = $stmt->fetch(PDO::FETCH_ASSOC);

    // Check if OTP is still valid
    if ($existingOtp && strtotime($existingOtp['expires_at']) > time()) {
        if ($existingOtp['attempts'] >= $maxAttempts) {
            throw new Exception("Maximum OTP attempts exceeded. Please wait before requesting a new OTP.");
        } else {
            throw new Exception("An active OTP already exists. Please use the existing OTP or wait for it to expire.");
        }
    }
    
    // Check rate limiting (max 3 OTP requests per 15 minutes)
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM admin_otp WHERE admin_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)");
    $stmt->execute([$adminId]);
    $recentOtps = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($recentOtps >= 3) {
        throw new Exception("Too many OTP requests. Please wait 15 minutes before requesting another OTP.");
    }
    
    // Generate 6-digit OTP
    $otp = sprintf('%06d', random_int(100000, 999999));
    $expiresAt = date('Y-m-d H:i:s', time() + $otpExpiryTime);
    
    $conn->beginTransaction();
    
    // Invalidate any existing OTPs for this admin
    $stmt = $conn->prepare("UPDATE admin_otp SET used = 1 WHERE admin_id = ? AND used = 0");
    $stmt->execute([$adminId]);
    
    // Insert new OTP
    $stmt = $conn->prepare("INSERT INTO admin_otp (admin_id, otp, expires_at, attempts, used) VALUES (?, ?, ?, 0, 0)");
    $stmt->execute([$adminId, $otp, $expiresAt]);
    
    // Get SMTP settings
    $stmt = $conn->query("SELECT * FROM smtp_settings WHERE is_active = 1 LIMIT 1");
    $smtpSettings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$smtpSettings) {
        $conn->rollBack();
        throw new Exception("SMTP is not configured. Please configure email settings first.");
    }
    
    // Send OTP email
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtpSettings['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtpSettings['username'];
        $mail->Password = $smtpSettings['password'];
        $mail->SMTPSecure = $smtpSettings['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $smtpSettings['port'];
        
        // Recipients
        $mail->setFrom($smtpSettings['from_email'], $smtpSettings['from_name']);
        $mail->addAddress($admin['email'], $admin['username']);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'FanBet247 Admin Login - OTP Verification [' . date('Y-m-d H:i:s') . ']';
        
        $expiryMinutes = ceil($otpExpiryTime / 60);
        $currentDateTime = date('Y-m-d H:i:s');
        $mail->Body = "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <title>FanBet247 Admin Login OTP</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
</head>
<body style='margin: 0; padding: 0; background-color: #f4f4f4; font-family: Arial, Helvetica, sans-serif; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;'>
    <!-- Wrapper Table for Outlook -->
    <table role='presentation' cellspacing='0' cellpadding='0' border='0' width='100%' style='background-color: #f4f4f4;'>
        <tr>
            <td align='center' style='padding: 20px 0;'>
                <!-- Main Container -->
                <table role='presentation' cellspacing='0' cellpadding='0' border='0' width='600' style='max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>
                    <!-- Header -->
                    <tr>
                        <td align='center' style='padding: 40px 30px 20px; background: linear-gradient(135deg, #2C5F2D 0%, #4CAF50 100%); border-radius: 8px 8px 0 0;'>
                            <table role='presentation' cellspacing='0' cellpadding='0' border='0'>
                                <tr>
                                    <td align='center'>
                                        <div style='width: 60px; height: 60px; background-color: rgba(255,255,255,0.2); border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 15px;'>
                                            <div style='width: 30px; height: 30px; border: 3px solid white; border-radius: 50%; position: relative;'>
                                                <div style='position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 8px; height: 8px; background-color: white; border-radius: 50%;'></div>
                                            </div>
                                        </div>
                                        <h1 style='margin: 0; color: #ffffff; font-size: 28px; font-weight: bold; text-align: center;'>FanBet247</h1>
                                        <p style='margin: 5px 0 0; color: rgba(255,255,255,0.9); font-size: 14px; text-align: center;'>Admin Portal Security</p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td style='padding: 40px 30px;'>
                            <table role='presentation' cellspacing='0' cellpadding='0' border='0' width='100%'>
                                <tr>
                                    <td>
                                        <h2 style='margin: 0 0 20px; color: #333333; font-size: 24px; font-weight: bold; text-align: center;'>Login Verification Required</h2>
                                        <p style='margin: 0 0 20px; color: #555555; font-size: 16px; line-height: 1.6;'>Hello <strong style='color: #2C5F2D;'>{$admin['username']}</strong>,</p>
                                        <p style='margin: 0 0 30px; color: #555555; font-size: 16px; line-height: 1.6;'>A login attempt has been made to your FanBet247 Admin Portal account. Please use the following One-Time Password (OTP) to complete your authentication:</p>
                                    </td>
                                </tr>

                                <!-- OTP Code Box -->
                                <tr>
                                    <td align='center' style='padding: 20px 0;'>
                                        <table role='presentation' cellspacing='0' cellpadding='0' border='0'>
                                            <tr>
                                                <td align='center' style='background: linear-gradient(135deg, #2C5F2D 0%, #4CAF50 100%); border-radius: 12px; padding: 25px 40px; box-shadow: 0 4px 15px rgba(44, 95, 45, 0.3);'>
                                                    <div style='color: #ffffff; font-size: 36px; font-weight: bold; letter-spacing: 8px; text-align: center; font-family: \"Courier New\", monospace;'>{$otp}</div>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>

                                <!-- Instructions -->
                                <tr>
                                    <td style='padding: 20px 0;'>
                                        <div style='background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;'>
                                            <h3 style='margin: 0 0 15px; color: #856404; font-size: 18px; font-weight: bold;'>⚠️ Important Security Information</h3>
                                            <ul style='margin: 0; padding-left: 20px; color: #856404; font-size: 14px; line-height: 1.6;'>
                                                <li style='margin-bottom: 8px;'>This OTP is valid for <strong>{$expiryMinutes} minutes only</strong></li>
                                                <li style='margin-bottom: 8px;'>Never share this code with anyone, including FanBet247 staff</li>
                                                <li style='margin-bottom: 8px;'>If you did not request this login, contact support immediately</li>
                                                <li style='margin-bottom: 0;'>This code can only be used once</li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Login Details -->
                                <tr>
                                    <td style='padding: 20px 0;'>
                                        <div style='background-color: #f8f9fa; border-radius: 8px; padding: 20px; border-left: 4px solid #2C5F2D;'>
                                            <h4 style='margin: 0 0 10px; color: #2C5F2D; font-size: 16px; font-weight: bold;'>Login Attempt Details:</h4>
                                            <p style='margin: 5px 0; color: #666666; font-size: 14px;'><strong>IP Address:</strong> {$_SERVER['REMOTE_ADDR']}</p>
                                            <p style='margin: 5px 0; color: #666666; font-size: 14px;'><strong>Date & Time:</strong> {$currentDateTime}</p>
                                            <p style='margin: 5px 0; color: #666666; font-size: 14px;'><strong>User Agent:</strong> " . (isset($_SERVER['HTTP_USER_AGENT']) ? htmlspecialchars(substr($_SERVER['HTTP_USER_AGENT'], 0, 100)) : 'Unknown') . "</p>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td style='padding: 30px; background-color: #f8f9fa; border-radius: 0 0 8px 8px; border-top: 1px solid #e9ecef;'>
                            <table role='presentation' cellspacing='0' cellpadding='0' border='0' width='100%'>
                                <tr>
                                    <td align='center'>
                                        <p style='margin: 0 0 10px; color: #6c757d; font-size: 12px; text-align: center;'>This is an automated security message from FanBet247 Admin Portal</p>
                                        <p style='margin: 0 0 10px; color: #6c757d; font-size: 12px; text-align: center;'>Please do not reply to this email</p>
                                        <p style='margin: 0; color: #6c757d; font-size: 12px; text-align: center;'>© " . date('Y') . " FanBet247. All rights reserved.</p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>";
        
        $mail->AltBody = "
===========================================
FanBet247 ADMIN PORTAL - LOGIN VERIFICATION
===========================================

Hello {$admin['username']},

A login attempt has been made to your FanBet247 Admin Portal account.

YOUR ONE-TIME PASSWORD (OTP):
┌─────────────────┐
│      {$otp}      │
└─────────────────┘

IMPORTANT SECURITY INFORMATION:
• This OTP is valid for {$expiryMinutes} minutes only
• Never share this code with anyone
• If you did not request this login, contact support immediately
• This code can only be used once

LOGIN ATTEMPT DETAILS:
• IP Address: {$_SERVER['REMOTE_ADDR']}
• Date & Time: {$currentDateTime}
• User Agent: " . (isset($_SERVER['HTTP_USER_AGENT']) ? substr($_SERVER['HTTP_USER_AGENT'], 0, 80) : 'Unknown') . "

This is an automated security message from FanBet247 Admin Portal.
Please do not reply to this email.

© " . date('Y') . " FanBet247. All rights reserved.
===========================================
";
        
        $mail->send();
        
        // Log the OTP send action
        $auditLogger->logOTPEvent($adminId, 'otp_sent', true, [
            'expires_at' => $expiresAt,
            'email' => $admin['email']
        ]);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'OTP sent successfully to your registered email address',
            'expires_in' => $otpExpiryTime,
            'expires_at' => $expiresAt,
            'email_masked' => substr($admin['email'], 0, 2) . '***@' . substr($admin['email'], strpos($admin['email'], '@') + 1)
        ]);
        
    } catch (Exception $e) {
        $conn->rollBack();

        // Log failed OTP send
        $auditLogger->logOTPEvent($adminId, 'otp_send_failed', false, [
            'error' => $e->getMessage()
        ]);

        // Record rate limiting attempt
        $rateLimiter->recordAttempt($_SERVER['REMOTE_ADDR'], 'otp', 'ip', $adminId);

        throw new Exception("Failed to send OTP email: " . $e->getMessage());
    }
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
