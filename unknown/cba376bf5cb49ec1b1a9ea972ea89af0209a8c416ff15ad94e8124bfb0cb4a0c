<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$data = json_decode(file_get_contents("php://input"));

if (
    !empty($data->username) &&
    !empty($data->full_name) &&
    !empty($data->email) &&
    !empty($data->password) &&
    !empty($data->favorite_team) &&
    !empty($data->preferred_currency)
) {
    $username = htmlspecialchars(strip_tags($data->username));
    $full_name = htmlspecialchars(strip_tags($data->full_name));
    $email = htmlspecialchars(strip_tags($data->email));
    $password = password_hash($data->password, PASSWORD_DEFAULT);
    $favorite_team = htmlspecialchars(strip_tags($data->favorite_team));
    $preferred_currency_code = htmlspecialchars(strip_tags($data->preferred_currency));
    $balance = floatval($data->balance);

    // Get currency ID from currency code
    $currency_query = "SELECT id FROM currencies WHERE currency_code = :currency_code AND is_active = 1";
    $currency_stmt = $conn->prepare($currency_query);
    $currency_stmt->bindParam(":currency_code", $preferred_currency_code);
    $currency_stmt->execute();
    $currency_result = $currency_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$currency_result) {
        http_response_code(400);
        echo json_encode(array("success" => false, "message" => "Invalid currency selected."));
        exit;
    }

    $preferred_currency_id = $currency_result['id'];

    $query = "INSERT INTO users (username, full_name, email, password_hash, favorite_team, preferred_currency_id, balance) VALUES (:username, :full_name, :email, :password_hash, :favorite_team, :preferred_currency_id, :balance)";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(":username", $username);
    $stmt->bindParam(":full_name", $full_name);
    $stmt->bindParam(":email", $email);
    $stmt->bindParam(":password_hash", $password);
    $stmt->bindParam(":favorite_team", $favorite_team);
    $stmt->bindParam(":preferred_currency_id", $preferred_currency_id);
    $stmt->bindParam(":balance", $balance);

    if ($stmt->execute()) {
        http_response_code(201);
        echo json_encode(array("success" => true, "message" => "User was created."));
    } else {
        http_response_code(503);
        echo json_encode(array("success" => false, "message" => "Unable to create user."));
    }
} else {
    http_response_code(400);
    echo json_encode(array("success" => false, "message" => "Unable to create user. Data is incomplete."));
}
