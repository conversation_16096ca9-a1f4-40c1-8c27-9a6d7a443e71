<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Admin Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; text-align: center; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #0056b3; }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .result.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; font-weight: bold; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Simple Admin Login Test</h1>
        
        <!-- Connection Status -->
        <div id="connectionStatus" class="status">Checking connection...</div>
        
        <!-- Login Form -->
        <form id="loginForm">
            <div class="form-group">
                <label for="identifier">Username/Email:</label>
                <input type="text" id="identifier" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="loving12" required>
            </div>
            <button type="submit">🔐 Test Admin Login</button>
            <button type="button" onclick="testEndpoint()">📡 Test Endpoint</button>
            <button type="button" onclick="checkDatabase()">💾 Check Database</button>
        </form>
        
        <!-- Results -->
        <div id="result" class="result" style="display: none;"></div>
        
        <!-- Quick Actions -->
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <h3>🛠️ Quick Actions</h3>
            <button onclick="window.open('/fix_admin_login.php', '_blank')">🔧 Open Fix Tool</button>
            <button onclick="window.open('/debug.php', '_blank')">🐛 Open Debug Dashboard</button>
            <button onclick="window.open('/comprehensive_login_test.html', '_blank')">🧪 Full Test Suite</button>
        </div>
    </div>

    <script>
        // Check initial connection
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
        });

        async function checkConnection() {
            const statusDiv = document.getElementById('connectionStatus');
            try {
                const response = await fetch('/debug.php?action=ping');
                if (response.ok) {
                    statusDiv.className = 'status online';
                    statusDiv.textContent = '✅ Server connection: Online';
                } else {
                    statusDiv.className = 'status offline';
                    statusDiv.textContent = '❌ Server connection: Issues detected';
                }
            } catch (error) {
                statusDiv.className = 'status offline';
                statusDiv.textContent = '❌ Server connection: Failed';
            }
        }

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const identifier = document.getElementById('identifier').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Testing admin login...';
            
            try {
                const response = await fetch('/backend/handlers/admin_login_handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ identifier, password })
                });
                
                const responseText = await response.text();
                let result = `Admin Login Test Results:
Status: ${response.status} ${response.statusText}
Response Headers:
  Content-Type: ${response.headers.get('content-type')}
  
Response Body:
${responseText}`;

                if (response.ok) {
                    try {
                        const jsonData = JSON.parse(responseText);
                        if (jsonData.success) {
                            resultDiv.className = 'result success';
                            result += `

✅ LOGIN SUCCESSFUL!
Admin ID: ${jsonData.admin_id}
Username: ${jsonData.username}
Role: ${jsonData.role}
Email: ${jsonData.email}`;
                        } else {
                            resultDiv.className = 'result error';
                            result += `

❌ LOGIN FAILED: ${jsonData.message}`;
                        }
                    } catch (e) {
                        resultDiv.className = 'result error';
                        result += `

❌ Invalid JSON response`;
                    }
                } else {
                    resultDiv.className = 'result error';
                    result += `

❌ HTTP Error: ${response.status}`;
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Network Error: ${error.message}

This usually means:
1. The admin login handler file has syntax errors
2. The server is not responding
3. There's a network connectivity issue

Try using the Fix Tool to resolve syntax issues.`;
            }
        });

        async function testEndpoint() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Testing endpoint accessibility...';
            
            try {
                const response = await fetch('/backend/handlers/admin_login_handler.php', {
                    method: 'GET'
                });
                
                const text = await response.text();
                
                let result = `Endpoint Test Results:
Status: ${response.status} ${response.statusText}
Content-Type: ${response.headers.get('content-type')}
Content Length: ${text.length} characters

Response Preview:
${text.substring(0, 500)}${text.length > 500 ? '...' : ''}`;

                if (response.status === 200) {
                    resultDiv.className = 'result success';
                    result = '✅ Endpoint is accessible\n\n' + result;
                } else if (response.status === 500) {
                    resultDiv.className = 'result error';
                    result = '❌ Internal Server Error (500) - Syntax error in PHP file\n\n' + result;
                } else {
                    resultDiv.className = 'result error';
                    result = `❌ HTTP ${response.status} Error\n\n` + result;
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Endpoint Test Failed: ${error.message}`;
            }
        }

        async function checkDatabase() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Checking database connection...';
            
            try {
                const response = await fetch('/api_config_test_fixed.php?action=test_db');
                const data = await response.json();
                
                let result = `Database Test Results:
Status: ${data.status}
Message: ${data.message}`;

                if (data.status === 'success') {
                    resultDiv.className = 'result success';
                    result += `
Admin Count: ${data.data.admin_count}
Connection Type: ${data.data.connection_type}

✅ Database is working correctly!`;
                } else {
                    resultDiv.className = 'result error';
                    result += `

❌ Database connection failed`;
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Database Test Failed: ${error.message}`;
            }
        }
    </script>
</body>
</html>
