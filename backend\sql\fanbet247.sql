-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Feb 08, 2025 at 04:44 PM
-- Server version: 5.7.24
-- PHP Version: 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `fanbet247`
--

-- --------------------------------------------------------

--
-- Table structure for table `achievements`
--

CREATE TABLE `achievements` (
  `achievement_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `icon` varchar(50) DEFAULT NULL,
  `points_required` int(11) DEFAULT '0',
  `streak_required` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `achievements`
--

INSERT INTO `achievements` (`achievement_id`, `name`, `description`, `icon`, `points_required`, `streak_required`, `created_at`) VALUES
(1, 'Rookie Bettor', 'Earn your first 10 points', '', 10, 0, '2024-12-15 15:07:53'),
(2, 'Rising Star', 'Accumulate 50 points', '', 50, 0, '2024-12-15 15:07:53'),
(3, 'Betting Pro', 'Reach 100 points', '', 100, 0, '2024-12-15 15:07:53'),
(4, 'Master Predictor', 'Achieve 500 points', '', 500, 0, '2024-12-15 15:07:53'),
(5, 'Hot Streak', 'Win 5 bets in a row', '', 0, 5, '2024-12-15 15:07:53'),
(6, 'Unstoppable', 'Win 10 bets in a row', '', 0, 10, '2024-12-15 15:07:53'),
(7, 'Perfect Season', 'Finish a season at rank #1', '', 0, 0, '2024-12-15 15:07:53');

-- --------------------------------------------------------

--
-- Table structure for table `adminactions`
--

CREATE TABLE `adminactions` (
  `action_id` int(11) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `action_type` enum('challenge_created','challenge_settled','user_banned','points_awarded','points_deducted','balance_adjusted') DEFAULT NULL,
  `details` text,
  `action_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `adminactions`
--

INSERT INTO `adminactions` (`action_id`, `admin_id`, `action_type`, `details`, `action_date`) VALUES
(1, 1, 'points_awarded', 'Credited user ID: 3 with amount: 500 FC', '2024-10-29 21:17:02'),
(2, 1, 'points_awarded', 'Credited user ID: 3 with amount: 50000 FC', '2024-10-30 10:57:44'),
(3, 1, 'points_awarded', 'Credited user ID: 6 with amount: 5000 FC', '2024-10-30 11:51:40'),
(4, 1, 'points_awarded', 'Credited user ID: 4 with amount: 16398 FC', '2024-11-03 12:12:15'),
(5, 1, 'points_awarded', 'Credited user ID: 2 with amount: 50000999 FC', '2025-02-08 15:07:56');

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` enum('super_admin','moderator','support') NOT NULL,
  `last_login` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`admin_id`, `username`, `password_hash`, `email`, `role`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'superadmin', '$2y$10$AJkQuNkasNKPdI1lBaen0uGgaVAjCinFSHZKfE5qIfV69azyEvNY2', '<EMAIL>', 'moderator', '2025-02-07 06:03:49', '2024-09-06 08:47:14', '2025-02-07 06:03:49'),
(2, 'jamesbong', '$2y$10$J4gUr26QS.gnQIzzK2Hr7.tBIdy4v3/qBRODgylocvIq3.r4ZL4cu', '<EMAIL>', 'moderator', '2024-09-06 20:07:14', '2024-09-06 20:07:14', '2024-09-06 20:07:14');

-- --------------------------------------------------------

--
-- Table structure for table `bets`
--

CREATE TABLE `bets` (
  `bet_id` int(11) NOT NULL,
  `challenge_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `outcome` enum('team_a_win','team_b_win','draw','pending') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user1_id` int(11) DEFAULT NULL,
  `user2_id` int(11) DEFAULT NULL,
  `team1_id` int(11) DEFAULT NULL,
  `team2_id` int(11) DEFAULT NULL,
  `amount_user1` decimal(10,2) DEFAULT NULL,
  `amount_user2` decimal(10,2) DEFAULT NULL,
  `bet_choice_user1` varchar(50) DEFAULT NULL,
  `bet_choice_user2` varchar(50) DEFAULT NULL,
  `odds_user1` decimal(5,2) DEFAULT NULL,
  `odds_user2` decimal(5,2) DEFAULT NULL,
  `bet_status` enum('open','joined','completed') DEFAULT 'open',
  `potential_return_user1` decimal(10,2) DEFAULT NULL,
  `potential_return_user2` decimal(10,2) DEFAULT NULL,
  `unique_code` varchar(255) DEFAULT NULL,
  `potential_return_win_user1` decimal(10,2) DEFAULT NULL,
  `potential_return_draw_user1` decimal(10,2) DEFAULT NULL,
  `potential_return_loss_user1` decimal(10,2) DEFAULT NULL,
  `potential_return_win_user2` decimal(10,2) DEFAULT NULL,
  `potential_return_draw_user2` decimal(10,2) DEFAULT NULL,
  `potential_return_loss_user2` decimal(10,2) DEFAULT NULL,
  `user1_outcome` enum('win','loss','pending') DEFAULT 'pending',
  `user2_outcome` enum('win','loss','pending') DEFAULT 'pending'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `bets`
--

INSERT INTO `bets` (`bet_id`, `challenge_id`, `amount`, `outcome`, `created_at`, `user1_id`, `user2_id`, `team1_id`, `team2_id`, `amount_user1`, `amount_user2`, `bet_choice_user1`, `bet_choice_user2`, `odds_user1`, `odds_user2`, `bet_status`, `potential_return_user1`, `potential_return_user2`, `unique_code`, `potential_return_win_user1`, `potential_return_draw_user1`, `potential_return_loss_user1`, `potential_return_win_user2`, `potential_return_draw_user2`, `potential_return_loss_user2`, `user1_outcome`, `user2_outcome`) VALUES
(73, 8, NULL, NULL, '2025-01-26 16:03:53', 2, 1, NULL, NULL, '5000.00', '5000.00', 'team_a_win', 'team_b_win', '1.00', '1.00', 'completed', '1000.00', '5000.00', '739a5c56', '5000.00', '4000.00', '1000.00', '5000.00', '4000.00', '1000.00', 'pending', 'pending'),
(75, 9, NULL, NULL, '2025-01-27 13:37:02', 2, 1, NULL, NULL, '7500.00', '7500.00', 'team_a_win', 'team_b_win', '1.80', '1.80', 'completed', '1500.00', '13500.00', '75f2dc7d', '13500.00', '6000.00', '1500.00', '13500.00', '6000.00', '1500.00', 'pending', 'pending'),
(76, 11, NULL, NULL, '2025-01-27 13:39:18', 2, 1, NULL, NULL, '8500.00', '8500.00', 'team_a_win', 'team_b_win', '1.80', '1.80', 'completed', '15300.00', '1700.00', '766b41aa', '15300.00', '6800.00', '1700.00', '15300.00', '6800.00', '1700.00', 'pending', 'pending'),
(77, 12, NULL, NULL, '2025-01-27 14:42:51', 1, 2, NULL, NULL, '8500.00', '8500.00', 'team_a_win', 'team_b_win', '1.00', '1.00', 'completed', '8500.00', '1700.00', '77b62ad4', '8500.00', '6800.00', '1700.00', '8500.00', '6800.00', '1700.00', 'pending', 'pending'),
(78, 14, NULL, NULL, '2025-01-27 19:44:52', 2, NULL, NULL, NULL, '7800.00', NULL, 'team_b_win', NULL, '1.80', NULL, 'open', '14040.00', NULL, '78496d2e', '14040.00', '6240.00', '1560.00', NULL, NULL, NULL, 'pending', 'pending'),
(79, 15, NULL, 'team_a_win', '2025-01-27 21:35:35', 2, 1, NULL, NULL, '8700.00', '8700.00', 'team_b_win', 'team_a_win', '1.80', '1.80', 'completed', '15660.00', '15660.00', '7975dc74', '15660.00', '6960.00', '1740.00', '15660.00', '6960.00', '1740.00', 'pending', 'pending'),
(80, 16, NULL, NULL, '2025-01-30 13:35:20', 2, NULL, NULL, NULL, '5000.00', NULL, 'team_a_win', NULL, '1.80', NULL, 'open', '9000.00', NULL, '80935a5b', '9000.00', '4000.00', '1000.00', NULL, NULL, NULL, 'pending', 'pending'),
(81, 16, NULL, NULL, '2025-02-01 03:06:20', 2, 8, NULL, NULL, '5000.00', '5000.00', 'team_a_win', 'team_b_win', '1.80', '1.80', 'joined', '9000.00', '9000.00', '81c65008', '9000.00', '4000.00', '1000.00', '9000.00', '4000.00', '1000.00', 'pending', 'pending'),
(82, 15, NULL, NULL, '2025-02-01 03:13:33', 2, NULL, NULL, NULL, '5553.00', NULL, 'team_a_win', NULL, '1.80', NULL, 'open', '9995.40', NULL, '82d4f44e', '9995.40', '4442.40', '1110.60', NULL, NULL, NULL, 'pending', 'pending'),
(83, 17, NULL, NULL, '2025-02-01 03:20:46', 2, NULL, NULL, NULL, '666.00', NULL, 'team_a_win', NULL, '1.00', NULL, 'open', '666.00', NULL, '83e0ae88', '666.00', '532.80', '133.20', NULL, NULL, NULL, 'pending', 'pending'),
(84, 17, NULL, NULL, '2025-02-01 03:22:36', 8, NULL, NULL, NULL, '8975.00', NULL, 'team_b_win', NULL, '1.00', NULL, 'open', '8975.00', NULL, '84c39648', '8975.00', '7180.00', '1795.00', NULL, NULL, NULL, 'pending', 'pending'),
(85, 15, NULL, 'team_a_win', '2025-02-01 03:37:48', 8, 2, NULL, NULL, '4998.00', '4998.00', 'draw', 'team_a_win', '0.80', '0.80', 'completed', '3998.40', '3998.40', '85cb3cf5', '3998.40', '3998.40', '999.60', '3998.40', '3998.40', '999.60', 'pending', 'pending');

-- --------------------------------------------------------

--
-- Table structure for table `challenges`
--

CREATE TABLE `challenges` (
  `challenge_id` int(11) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `team_a` varchar(100) DEFAULT NULL,
  `logo1` varchar(255) DEFAULT NULL,
  `team_b` varchar(100) DEFAULT NULL,
  `logo2` varchar(255) DEFAULT NULL,
  `odds_team_a` decimal(5,2) DEFAULT NULL,
  `odds_team_b` decimal(5,2) DEFAULT NULL,
  `odds_draw` decimal(5,2) DEFAULT NULL,
  `team_a_goal_advantage` int(11) DEFAULT '0',
  `team_b_goal_advantage` int(11) DEFAULT '0',
  `challenge_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `match_date` datetime NOT NULL,
  `result` enum('team_a_win','team_b_win','draw') DEFAULT NULL,
  `status` enum('Open','Closed','Settled') DEFAULT 'Open',
  `team_a_score` int(11) DEFAULT NULL,
  `team_b_score` int(11) DEFAULT NULL,
  `match_type` enum('half_time','full_time') DEFAULT 'full_time',
  `odds_lost` decimal(5,2) DEFAULT '0.20'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `challenges`
--

INSERT INTO `challenges` (`challenge_id`, `admin_id`, `team_a`, `logo1`, `team_b`, `logo2`, `odds_team_a`, `odds_team_b`, `odds_draw`, `team_a_goal_advantage`, `team_b_goal_advantage`, `challenge_date`, `start_time`, `end_time`, `match_date`, `result`, `status`, `team_a_score`, `team_b_score`, `match_type`, `odds_lost`) VALUES
(8, NULL, 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', 'Everton', '/backend/uploads/Everton Logo.png', '1.00', '1.00', '0.80', 0, 0, '2025-01-24 11:10:00', '2025-01-24 13:10:00', '2025-01-27 13:10:00', '2025-01-28 13:10:00', 'team_b_win', 'Settled', 1, 2, 'half_time', '0.20'),
(9, NULL, 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', 'Chelsea Fc', '/backend/uploads/image.png', '1.80', '1.80', '0.80', 1, 1, '2025-01-27 13:10:00', '2025-01-27 15:10:00', '2025-01-29 16:10:00', '2025-02-01 15:11:00', 'team_a_win', 'Settled', 2, 1, 'full_time', '0.20'),
(10, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Everton', '/backend/uploads/Everton Logo.png', '1.80', '1.80', '0.80', 2, 1, '2025-01-27 13:13:00', '2025-01-27 15:13:00', '2025-01-28 15:13:00', '2025-01-29 15:13:00', 'team_a_win', 'Settled', 3, 1, 'full_time', '0.20'),
(11, NULL, 'Manchester United', '/backend/uploads/Manchester United Logo.png', 'Liverpool Fc', '/backend/uploads/pngwing.com.png', '1.80', '1.80', '0.80', 0, 0, '2025-01-27 13:18:00', '2025-01-27 15:18:00', '2025-01-28 15:18:00', '2025-01-29 15:18:00', 'draw', 'Settled', 1, 1, 'full_time', '0.20'),
(12, NULL, 'Manchester City', '/backend/uploads/Manchester City.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.00', '1.00', '0.80', 0, 0, '2025-01-28 13:18:00', '2025-01-28 15:18:00', '2025-01-28 15:18:00', '2025-01-29 15:19:00', 'team_a_win', 'Settled', 2, 1, 'full_time', '0.20'),
(13, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', '1.00', '1.00', '0.80', 0, 0, '2025-02-01 13:27:00', '2025-02-01 15:27:00', '2025-02-02 15:27:00', '2025-02-03 15:27:00', 'team_b_win', 'Closed', 1, 2, 'full_time', '0.20'),
(14, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.80', '1.80', '0.80', 1, 1, '2025-01-27 19:35:00', '2025-01-27 21:35:00', '2025-01-27 22:35:00', '2025-01-27 23:35:00', NULL, 'Closed', NULL, NULL, 'full_time', '0.20'),
(15, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Liverpool Fc', '/backend/uploads/pngwing.com.png', '1.80', '1.80', '0.80', 1, 1, '2025-01-27 21:12:00', '2025-01-27 23:12:00', '2025-01-28 12:12:00', '2025-01-30 23:12:00', 'team_a_win', 'Closed', 1, 2, 'half_time', '0.20'),
(16, NULL, 'Brentford FC', '/backend/uploads/Brentford FC.png', 'Manchester City', '/backend/uploads/Manchester City.png', '1.80', '1.80', '0.80', 1, 1, '2025-01-27 21:19:00', '2025-01-27 23:19:00', '2025-01-28 23:19:00', '2025-01-29 23:19:00', NULL, 'Closed', NULL, NULL, 'full_time', '0.20'),
(17, NULL, 'Manchester City', '/backend/uploads/Manchester City.png', 'Brentford FC', '/backend/uploads/Brentford FC.png', '1.00', '1.00', '0.80', 0, 0, '2025-01-27 21:20:00', '2025-01-27 23:20:00', '2025-01-28 23:20:00', '2025-01-30 23:20:00', 'team_a_win', 'Settled', 3, 2, 'full_time', '0.20'),
(18, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', '1.80', '1.80', '0.80', 2, 1, '2025-01-27 21:21:00', '2025-01-27 23:21:00', '2025-01-28 23:21:00', '2025-01-31 23:21:00', NULL, 'Closed', NULL, NULL, 'half_time', '0.20'),
(22, NULL, 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', 'Brentford FC', '/backend/uploads/Brentford FC.png', '1.80', '1.80', '0.80', 1, 2, '2025-02-07 07:22:00', '2025-02-07 09:22:00', '2025-02-07 10:22:00', '2025-02-10 09:22:00', NULL, 'Closed', NULL, NULL, 'full_time', '0.20'),
(23, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.80', '1.80', '0.80', 0, 0, '2025-02-08 07:36:00', '2025-02-08 09:36:00', '2025-02-09 09:36:00', '2025-02-10 09:36:00', NULL, 'Open', NULL, NULL, 'full_time', '0.20'),
(25, NULL, 'Everton', '/backend/uploads/Everton Logo.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.80', '1.80', '0.80', 1, 0, '2025-02-08 07:42:00', '2025-02-08 09:42:00', '2025-02-09 09:42:00', '2025-02-10 09:42:00', NULL, 'Open', NULL, NULL, 'half_time', '0.20'),
(26, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Brentford FC', '/backend/uploads/Brentford FC.png', '1.80', '1.80', '0.80', 0, 0, '2025-02-08 07:42:00', '2025-02-08 09:42:00', '2025-02-10 09:42:00', '2025-02-10 10:42:00', NULL, 'Open', NULL, NULL, 'half_time', '0.20'),
(27, NULL, 'Everton', '/backend/uploads/Everton Logo.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.80', '1.80', '0.80', 1, 1, '2025-02-08 11:40:00', '2025-02-08 13:40:00', '2025-02-09 13:40:00', '2025-02-11 13:40:00', NULL, 'Open', NULL, NULL, 'half_time', '0.20');

-- --------------------------------------------------------

--
-- Table structure for table `credit_requests`
--

CREATE TABLE `credit_requests` (
  `request_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `proof_image` varchar(255) NOT NULL,
  `status` enum('pending','approved','rejected','expired') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `credit_requests`
--

INSERT INTO `credit_requests` (`request_id`, `user_id`, `amount`, `payment_method_id`, `proof_image`, `status`, `created_at`, `expires_at`, `approved_at`, `approved_by`) VALUES
(1, 4, '1000.00', 3, 'uploads/payment_proofs/6780c417c15ef_1736492055.jpg', 'approved', '2025-01-10 06:54:15', '2025-01-11 06:54:15', NULL, NULL),
(2, 5, '10000.00', 3, 'uploads/payment_proofs/6780daa8e4014_1736497832.png', 'approved', '2025-01-10 08:30:32', '2025-01-11 08:30:32', NULL, NULL),
(3, 4, '47000.00', 2, 'uploads/payment_proofs/6780dae8ee0b0_1736497896.png', 'approved', '2025-01-10 08:31:36', '2025-01-11 08:31:36', NULL, NULL),
(4, 4, '6000.00', 1, 'uploads/payment_proofs/67866e0c01568_1736863244.jpg', 'approved', '2025-01-14 14:00:44', '2025-01-15 14:00:44', NULL, NULL),
(5, 4, '19999.00', 3, 'uploads/payment_proofs/678bdd7d2c345_1737219453.png', 'approved', '2025-01-18 16:57:33', '2025-01-19 16:57:33', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `leaderboards`
--

CREATE TABLE `leaderboards` (
  `leaderboard_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `points` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `division` int(11) DEFAULT '5',
  `season_start` date DEFAULT NULL,
  `season_end` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `leagues`
--

CREATE TABLE `leagues` (
  `league_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `min_bet_amount` decimal(20,2) NOT NULL,
  `max_bet_amount` decimal(20,2) NOT NULL,
  `description` text,
  `icon_path` varchar(255) DEFAULT NULL,
  `banner_path` varchar(255) DEFAULT NULL,
  `theme_color` varchar(7) DEFAULT '#007bff',
  `created_by` int(11) NOT NULL,
  `season_duration` int(11) DEFAULT '90',
  `league_icon` varchar(255) DEFAULT NULL,
  `league_banner` varchar(255) DEFAULT NULL,
  `league_rules` text,
  `reward_description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` enum('active','inactive','upcoming','archived') DEFAULT 'upcoming'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `leagues`
--

INSERT INTO `leagues` (`league_id`, `name`, `min_bet_amount`, `max_bet_amount`, `description`, `icon_path`, `banner_path`, `theme_color`, `created_by`, `season_duration`, `league_icon`, `league_banner`, `league_rules`, `reward_description`, `created_at`, `updated_at`, `status`) VALUES
(1, 'Katsina Leaguex', '1000.00', '10000.00', 'Entry level league for beginners', 'league_icon_675ee6dabdaea.png', 'league_banner_675ee6dabdeee.jpg', '#3700B3', 1, 90, 'premier_league.png', 'premier_league_banner.jpg', 'null', 'null', '2024-12-14 18:33:27', '2025-01-30 14:02:01', 'upcoming'),
(2, 'Calabar League', '10000.00', '100000.00', 'Intermediate league for regular players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(3, 'Rivers League', '100000.00', '1000000.00', 'Advanced league for experienced players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(4, 'Delta League', '1000000.00', '10000000.00', 'Professional league for high-stakes players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(5, 'Lagos League', '10000000.00', '100000000.00', 'Elite league for expert players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(6, 'Abuja League', '100000000.00', '1000000000.00', 'Premium league for master players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(7, 'UEFA Europa League', '1000.00', '10000.00', 'The English Europa League ', 'league_icon_675e893646abe.png', 'league_banner_675e893646eee.jpeg', '#007bff', 1, 90, NULL, NULL, 'Rules and Guideline', 'Structure', '2024-12-15 07:45:58', '2024-12-15 07:45:58', 'upcoming'),
(8, 'Abujax', '500.00', '1000.00', 'describe', NULL, NULL, '#007bff', 1, 90, NULL, NULL, 'jhghgg', 'hjhghgh', '2024-12-15 21:38:45', '2024-12-15 21:38:45', 'upcoming'),
(9, 'Premier League', '100.00', '10000.00', 'The most competitive league', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2025-01-30 13:57:02', '2025-01-30 13:57:02', 'active'),
(10, 'Premier League 2023', '100.00', '10000.00', 'The most competitive league', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2025-01-30 13:57:15', '2025-01-30 15:32:30', 'active'),
(11, 'Premier League 2024', '100.00', '10000.00', 'The most competitive league', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2025-01-30 13:57:50', '2025-01-30 15:32:30', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `league_audit_log`
--

CREATE TABLE `league_audit_log` (
  `log_id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action_type` enum('create_league','edit_league','start_season','end_season','approve_member','suspend_member','verify_results','other') NOT NULL,
  `league_id` int(11) DEFAULT NULL,
  `season_id` int(11) DEFAULT NULL,
  `membership_id` int(11) DEFAULT NULL,
  `action_details` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `league_history`
--

CREATE TABLE `league_history` (
  `history_id` int(11) NOT NULL,
  `season_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `final_points` int(11) NOT NULL,
  `final_rank` int(11) NOT NULL,
  `total_bets` int(11) NOT NULL,
  `wins` int(11) NOT NULL,
  `draws` int(11) NOT NULL,
  `losses` int(11) NOT NULL,
  `rewards_earned` text,
  `verified_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `league_memberships`
--

CREATE TABLE `league_memberships` (
  `membership_id` int(11) NOT NULL,
  `user_league_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `season_id` int(11) NOT NULL,
  `deposit_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `current_points` int(11) DEFAULT '0',
  `total_bets` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `approved_by` int(11) DEFAULT NULL,
  `join_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','active','suspended','completed') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `active_membership` int(11) GENERATED ALWAYS AS (if((`status` = 'active'),`user_id`,NULL)) VIRTUAL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `league_memberships`
--

INSERT INTO `league_memberships` (`membership_id`, `user_league_id`, `user_id`, `league_id`, `season_id`, `deposit_amount`, `current_points`, `total_bets`, `wins`, `draws`, `losses`, `approved_by`, `join_date`, `status`, `created_at`, `updated_at`) VALUES
(2, 19, 2, 11, 19, '100.00', 3, 2, 1, 0, 1, NULL, '2025-02-01 09:22:35', 'active', '2025-02-01 11:22:35', '2025-02-01 15:30:20'),
(3, 20, 5, 11, 19, '100.00', 0, 0, 0, 0, 0, NULL, '2025-02-01 09:31:54', 'active', '2025-02-01 11:31:55', '2025-02-01 11:31:55'),
(4, 21, 3, 11, 19, '1200.00', 0, 0, 0, 0, 0, NULL, '2025-02-01 09:33:27', 'active', '2025-02-01 11:33:27', '2025-02-01 11:33:27'),
(5, 22, 4, 11, 19, '10000.00', 0, 0, 0, 0, 0, NULL, '2025-02-01 09:35:30', 'active', '2025-02-01 11:35:31', '2025-02-01 11:35:31'),
(8, 23, 1, 7, 4, '10000.00', 3, 1, 1, 0, 0, NULL, '2025-02-01 12:44:07', 'active', '2025-02-01 14:44:07', '2025-02-01 15:30:20'),
(9, 24, 6, 11, 19, '1000.00', 0, 0, 0, 0, 0, NULL, '2025-02-01 12:45:30', 'active', '2025-02-01 14:45:30', '2025-02-01 14:45:30'),
(10, 25, 8, 11, 19, '100.00', 0, 0, 0, 0, 0, NULL, '2025-02-01 13:31:13', 'active', '2025-02-01 15:31:13', '2025-02-01 15:31:13');

-- --------------------------------------------------------

--
-- Table structure for table `league_memberships_backup`
--

CREATE TABLE `league_memberships_backup` (
  `membership_id` int(11) NOT NULL DEFAULT '0',
  `user_league_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `season_id` int(11) NOT NULL,
  `deposit_amount` decimal(20,2) NOT NULL,
  `current_points` int(11) DEFAULT '0',
  `total_bets` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `approved_by` int(11) DEFAULT NULL,
  `join_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','active','suspended','completed') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `active_membership` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `league_memberships_backup`
--

INSERT INTO `league_memberships_backup` (`membership_id`, `user_league_id`, `user_id`, `league_id`, `season_id`, `deposit_amount`, `current_points`, `total_bets`, `wins`, `draws`, `losses`, `approved_by`, `join_date`, `status`, `created_at`, `updated_at`, `active_membership`) VALUES
(15, 20, 4, 7, 4, '1000.00', 0, 7, 5, 0, 2, NULL, '2025-01-09 16:30:51', 'completed', '2025-01-09 18:30:51', '2025-02-01 09:26:08', NULL),
(16, 21, 2, 7, 4, '1000.00', 0, 7, 4, 0, 3, NULL, '2025-01-09 16:31:09', 'completed', '2025-01-09 18:31:09', '2025-02-01 09:26:08', NULL),
(17, 22, 3, 7, 4, '10000.00', 0, 7, 3, 0, 4, NULL, '2025-01-10 02:19:35', 'completed', '2025-01-10 04:19:35', '2025-02-01 09:26:08', NULL),
(18, 23, 5, 7, 4, '10000.00', 0, 7, 2, 0, 5, NULL, '2025-01-10 02:22:25', 'completed', '2025-01-10 04:22:25', '2025-02-01 09:26:08', NULL),
(39, 29, 1, 1, 5, '10000.00', 0, 0, 0, 0, 0, NULL, '2025-01-30 14:27:55', 'completed', '2025-01-30 16:27:55', '2025-02-01 09:26:08', NULL),
(40, 30, 8, 1, 5, '1000.00', 0, 0, 0, 0, 0, NULL, '2025-01-30 14:31:07', 'completed', '2025-01-30 16:31:08', '2025-02-01 09:26:08', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `league_seasons`
--

CREATE TABLE `league_seasons` (
  `season_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `season_name` varchar(100) NOT NULL,
  `start_date` timestamp NULL DEFAULT NULL,
  `end_date` timestamp NULL DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `total_participants` int(11) DEFAULT '0',
  `prize_pool` decimal(20,2) DEFAULT '0.00',
  `status` enum('upcoming','active','completed','cancelled') DEFAULT 'upcoming',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `league_seasons`
--

INSERT INTO `league_seasons` (`season_id`, `league_id`, `season_name`, `start_date`, `end_date`, `created_by`, `total_participants`, `prize_pool`, `status`, `created_at`, `updated_at`) VALUES
(4, 7, 'Season 2025', '2025-01-09 16:48:17', '2025-02-08 16:48:17', 2, 0, '0.00', 'active', '2025-01-09 16:48:17', '2025-01-09 16:48:17'),
(5, 1, 'Season 1', '2025-01-30 13:57:50', '2025-04-30 13:57:50', 1, 0, '0.00', 'active', '2025-01-30 13:57:50', '2025-01-30 13:57:50'),
(6, 1, 'Season 1', '2025-01-30 13:58:16', '2025-04-30 13:58:16', 1, 0, '0.00', 'active', '2025-01-30 13:58:16', '2025-01-30 13:58:16'),
(19, 11, 'Season 2025', '2025-02-01 11:22:35', '2025-03-03 11:22:35', 2, 0, '0.00', 'active', '2025-02-01 11:22:35', '2025-02-01 11:22:35');

-- --------------------------------------------------------

--
-- Table structure for table `messages`
--

CREATE TABLE `messages` (
  `id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `deleted_by_sender` tinyint(1) DEFAULT '0',
  `deleted_by_recipient` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `messages`
--

INSERT INTO `messages` (`id`, `sender_id`, `recipient_id`, `content`, `created_at`, `is_read`, `deleted_by_sender`, `deleted_by_recipient`) VALUES
(1, 2, 4, 'how arer you /?', '2024-11-29 16:32:36', 1, 0, 0),
(2, 2, 4, 'are you there?', '2024-11-29 16:34:34', 1, 0, 0),
(3, 4, 2, 'yes i am there', '2024-11-29 16:44:58', 1, 0, 0),
(4, 2, 4, 'i see am like that', '2024-11-29 16:45:10', 1, 0, 0),
(5, 4, 2, 'this is from lil wayne', '2024-11-29 17:05:36', 1, 0, 0),
(6, 2, 4, 'this lil wayne you are not lili wayne werytin make you dey lie na me be the lliwayne andf you are demohomex', '2024-11-29 19:03:44', 1, 0, 0),
(7, 4, 2, 'bas guy how you take catch me lol', '2024-11-29 19:04:07', 1, 0, 0),
(8, 2, 3, 'jameslink how far', '2024-11-30 10:09:08', 0, 0, 0),
(9, 2, 1, 'testingther mic', '2024-11-30 11:16:06', 1, 0, 0),
(10, 2, 8, 'how far blood', '2024-11-30 11:18:03', 0, 0, 0),
(11, 2, 6, 'baba Blue King wad up', '2024-11-30 11:20:28', 1, 0, 0),
(12, 6, 2, 'how far Liwayen this one you write me so hope all is well', '2024-11-30 11:21:41', 1, 0, 0),
(13, 2, 6, 'My bloda all is well i just de y check which game you play', '2024-11-30 11:22:02', 1, 0, 0),
(14, 2, 4, 'baba demhomex you dey play with me i go win your team this week ', '2024-11-30 17:40:42', 1, 0, 0),
(15, 2, 4, 'demohomex  let me know if you got this message', '2024-11-30 17:56:03', 1, 0, 0),
(16, 4, 2, 'lilwayne i got your message', '2024-11-30 18:03:35', 1, 0, 0),
(17, 2, 4, 'demohomex i feel say you dont lost', '2024-11-30 18:05:52', 1, 0, 0),
(18, 4, 2, 'how i won take lose i full ground', '2024-11-30 18:06:38', 1, 0, 0),
(19, 4, 2, 'make you sure you play the odss i tell you', '2024-11-30 18:07:15', 1, 0, 0),
(20, 6, 2, 'lil wayne na man you be ', '2024-11-30 18:44:50', 1, 0, 0),
(21, 2, 6, 'baba blue', '2024-11-30 18:45:22', 1, 0, 0),
(22, 6, 2, 'lol', '2024-11-30 18:45:34', 1, 0, 0),
(23, 2, 4, 'hi', '2024-12-15 21:39:46', 1, 0, 0),
(24, 4, 2, 'i see you', '2024-12-15 21:40:15', 1, 0, 0),
(25, 2, 4, 'hu', '2024-12-15 21:41:56', 1, 0, 0),
(26, 4, 5, 'thanks for the money my broda i apprciate ', '2025-01-10 05:47:33', 1, 0, 0),
(27, 5, 4, 'you welcome', '2025-01-10 05:48:05', 1, 0, 0),
(28, 1, 2, 'Hello', '2025-01-14 13:54:49', 1, 0, 0),
(29, 1, 4, 'wad up thanks for friend rerquest ', '2025-01-14 13:55:08', 1, 0, 0),
(30, 4, 1, 'thanks my broda', '2025-01-14 13:56:10', 1, 0, 0),
(31, 4, 1, 'Hello', '2025-01-18 16:53:28', 0, 0, 0),
(32, 2, 1, 'its working now', '2025-01-31 14:47:00', 0, 0, 0),
(33, 2, 1, 'thank you', '2025-01-31 14:53:14', 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `paymentverifications`
--

CREATE TABLE `paymentverifications` (
  `verification_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_slip_url` varchar(255) DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `submitted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `verified_at` timestamp NULL DEFAULT NULL,
  `verified_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL,
  `type` enum('bank','crypto') NOT NULL,
  `bank_name` varchar(255) DEFAULT NULL,
  `account_number` varchar(50) DEFAULT NULL,
  `account_name` varchar(255) DEFAULT NULL,
  `account_type` varchar(50) DEFAULT NULL,
  `wallet_name` varchar(255) DEFAULT NULL,
  `wallet_address` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `type`, `bank_name`, `account_number`, `account_name`, `account_type`, `wallet_name`, `wallet_address`, `created_at`, `updated_at`) VALUES
(1, 'crypto', '', '', '', '', 'Bitcoin', '*************010010102', '2024-09-23 14:31:17', '2024-09-23 14:35:13'),
(2, 'bank', 'Fnb', '*************', 'Fanbet247', 'Checking Account', '', '', '2024-09-23 14:32:25', '2024-09-23 14:32:25'),
(3, 'bank', 'Standard Bank', '*************', 'Fanbet247', 'business', '', '', '2024-09-23 14:44:02', '2024-09-23 14:44:02');

-- --------------------------------------------------------

--
-- Table structure for table `seasons`
--

CREATE TABLE `seasons` (
  `season_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `status` enum('upcoming','active','completed') DEFAULT 'upcoming',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `season_history`
--

CREATE TABLE `season_history` (
  `history_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `season_id` int(11) NOT NULL,
  `final_points` int(11) DEFAULT '0',
  `final_rank` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `teams`
--

CREATE TABLE `teams` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `logo` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `teams`
--

INSERT INTO `teams` (`id`, `name`, `logo`, `created_at`, `updated_at`) VALUES
(11, 'Arsenal', 'uploads/Arsenal-FC-logo.png', '2024-09-06 21:35:14', '2024-09-06 21:35:14'),
(12, 'Chelsea Fc', 'uploads/image.png', '2024-09-06 21:36:07', '2024-09-06 21:36:07'),
(13, 'Everton', 'uploads/Everton Logo.png', '2024-09-06 21:46:02', '2024-09-06 21:46:02'),
(14, 'Liverpool Fc', 'uploads/pngwing.com.png', '2024-09-06 21:46:38', '2024-09-06 21:51:05'),
(15, 'Manchester United', 'uploads/Manchester United Logo.png', '2024-09-06 21:50:36', '2024-09-06 21:50:36'),
(16, 'Brentford FC', 'uploads/Brentford FC.png', '2024-11-01 16:18:58', '2024-11-01 16:18:58'),
(17, 'Manchester City', 'uploads/Manchester City.png', '2024-11-03 10:12:26', '2024-11-03 10:12:26');

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `transaction_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `type` enum('deposit','withdrawal','bet','win','refund','admin_credit','admin_debit','transfer_sent','transfer_received') NOT NULL,
  `status` enum('pending','completed','failed') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `related_bet_id` int(11) DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `related_user_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `transactions`
--

INSERT INTO `transactions` (`transaction_id`, `user_id`, `amount`, `type`, `status`, `created_at`, `related_bet_id`, `admin_id`, `description`, `related_user_id`) VALUES
(1, 4, '55553.00', 'bet', 'completed', '2024-10-28 15:55:27', 55, NULL, NULL, NULL),
(2, 4, '100999.00', 'bet', 'completed', '2024-10-28 16:01:53', 56, NULL, NULL, NULL),
(3, 2, '8998.20', 'win', 'completed', '2024-10-29 18:51:05', 52, NULL, NULL, NULL),
(5, 2, '14173.20', 'win', 'completed', '2024-10-29 20:53:40', 53, NULL, NULL, NULL),
(6, 4, '2344.00', 'win', 'completed', '2024-10-29 20:53:40', 53, NULL, NULL, NULL),
(7, 2, '99995.40', 'win', 'completed', '2024-10-29 20:53:40', 55, NULL, NULL, NULL),
(8, 4, '11110.60', 'win', 'completed', '2024-10-29 20:53:40', 55, NULL, NULL, NULL),
(9, 3, '500.00', 'admin_credit', 'completed', '2024-10-29 21:17:02', NULL, 1, NULL, NULL),
(10, 3, '50000.00', 'admin_credit', 'completed', '2024-10-30 10:57:44', NULL, 1, NULL, NULL),
(11, 6, '5000.00', 'admin_credit', 'completed', '2024-10-30 11:51:40', NULL, 1, NULL, NULL),
(12, 4, '5000.00', 'bet', 'completed', '2024-10-30 11:59:41', 57, NULL, NULL, NULL),
(13, 6, '9000.00', 'win', 'completed', '2024-10-30 12:03:17', 57, NULL, NULL, NULL),
(14, 4, '1000.00', 'win', 'completed', '2024-10-30 12:03:17', 57, NULL, NULL, NULL),
(15, 8, '5000.00', 'bet', 'completed', '2024-11-01 17:37:22', 59, NULL, NULL, NULL),
(16, 2, '8000.00', 'bet', 'completed', '2024-11-01 18:00:29', 60, NULL, NULL, NULL),
(17, 8, '14400.00', 'win', 'completed', '2024-11-03 11:28:39', 60, NULL, NULL, NULL),
(18, 2, '1600.00', 'win', 'completed', '2024-11-03 11:28:39', 60, NULL, NULL, NULL),
(19, 2, '9000.00', 'win', 'completed', '2024-11-03 12:02:24', 59, NULL, NULL, NULL),
(20, 8, '1000.00', 'win', 'completed', '2024-11-03 12:02:24', 59, NULL, NULL, NULL),
(21, 4, '16398.00', 'admin_credit', 'completed', '2024-11-03 12:12:15', NULL, 1, NULL, NULL),
(22, 4, '16398.00', 'bet', 'completed', '2024-11-03 12:12:22', 62, NULL, NULL, NULL),
(23, 3, '1000.00', 'bet', 'completed', '2024-11-03 12:18:28', 63, NULL, NULL, NULL),
(24, 8, '29516.40', 'win', 'completed', '2024-11-03 12:24:21', 62, NULL, NULL, NULL),
(25, 4, '3279.60', 'win', 'completed', '2024-11-03 12:24:21', 62, NULL, NULL, NULL),
(26, 5, '200.00', 'win', 'completed', '2024-11-03 12:24:21', 63, NULL, NULL, NULL),
(27, 3, '1800.00', 'win', 'completed', '2024-11-03 12:24:21', 63, NULL, NULL, NULL),
(28, 3, '40000.00', 'bet', 'completed', '2024-11-03 12:43:54', 64, NULL, NULL, NULL),
(29, 5, '40000.00', 'win', 'completed', '2024-11-03 12:45:58', 64, NULL, NULL, NULL),
(30, 3, '8000.00', 'win', 'completed', '2024-11-03 12:45:58', 64, NULL, NULL, NULL),
(31, 3, '5000.00', 'bet', 'completed', '2024-11-03 13:20:58', 65, NULL, NULL, NULL),
(32, 5, '9000.00', 'win', 'completed', '2024-11-03 13:27:10', 65, NULL, NULL, NULL),
(33, 3, '1000.00', 'win', 'completed', '2024-11-03 13:27:10', 65, NULL, NULL, NULL),
(34, 3, '3998.00', 'bet', 'completed', '2024-11-03 13:32:15', 66, NULL, NULL, NULL),
(35, 5, '7196.40', 'win', 'completed', '2024-11-03 13:33:27', 66, NULL, NULL, NULL),
(36, 3, '799.60', 'win', 'completed', '2024-11-03 13:33:27', 66, NULL, NULL, NULL),
(37, 3, '4998.00', 'bet', 'completed', '2024-11-03 13:37:59', 67, NULL, NULL, NULL),
(38, 5, '8996.40', 'win', 'completed', '2024-11-03 13:39:00', 67, NULL, NULL, NULL),
(39, 3, '999.60', 'win', 'completed', '2024-11-03 13:39:00', 67, NULL, NULL, NULL),
(40, 2, '1000.00', 'admin_debit', 'completed', '2024-12-15 20:05:26', NULL, NULL, NULL, NULL),
(41, 2, '3000.00', 'admin_debit', 'completed', '2024-12-15 20:07:29', NULL, NULL, NULL, NULL),
(42, 2, '10000.00', 'admin_debit', 'completed', '2024-12-15 20:18:01', NULL, NULL, NULL, NULL),
(43, 4, '1000.00', 'admin_debit', 'completed', '2024-12-15 22:13:31', NULL, NULL, NULL, NULL),
(44, 4, '1000.00', 'admin_debit', 'completed', '2024-12-15 22:14:01', NULL, NULL, NULL, NULL),
(45, 4, '1000.00', 'admin_debit', 'completed', '2025-01-09 16:48:18', NULL, NULL, NULL, NULL),
(46, 2, '10000.00', 'admin_debit', 'completed', '2025-01-09 17:34:02', NULL, NULL, NULL, NULL),
(47, 4, '1000.00', 'admin_debit', 'completed', '2025-01-09 17:45:47', NULL, NULL, NULL, NULL),
(48, 2, '10000.00', 'admin_debit', 'completed', '2025-01-09 17:46:03', NULL, NULL, NULL, NULL),
(49, 4, '1000.00', 'admin_debit', 'completed', '2025-01-09 18:07:35', NULL, NULL, NULL, NULL),
(50, 2, '1000.00', 'admin_debit', 'completed', '2025-01-09 18:07:47', NULL, NULL, NULL, NULL),
(51, 4, '1000.00', 'bet', 'completed', '2025-01-09 18:30:51', NULL, NULL, NULL, NULL),
(52, 2, '1000.00', 'bet', 'completed', '2025-01-09 18:31:09', NULL, NULL, NULL, NULL),
(53, 3, '10000.00', 'admin_debit', 'completed', '2025-01-10 04:19:35', NULL, NULL, NULL, NULL),
(54, 5, '10000.00', 'admin_debit', 'completed', '2025-01-10 04:22:25', NULL, NULL, NULL, NULL),
(55, 4, '-500.00', 'transfer_sent', 'completed', '2025-01-10 05:45:24', NULL, NULL, 'Transfer between users', 5),
(56, 5, '500.00', 'transfer_received', 'completed', '2025-01-10 05:45:24', NULL, NULL, 'Transfer between users', 4),
(57, 4, '-234.00', 'transfer_sent', 'completed', '2025-01-10 05:46:08', NULL, NULL, 'Transfer between users', 5),
(58, 5, '234.00', 'transfer_received', 'completed', '2025-01-10 05:46:08', NULL, NULL, 'Transfer between users', 4),
(59, 5, '-40000.00', 'transfer_sent', 'completed', '2025-01-10 05:46:51', NULL, NULL, 'Transfer between users', 4),
(60, 4, '40000.00', 'transfer_received', 'completed', '2025-01-10 05:46:51', NULL, NULL, 'Transfer between users', 5),
(61, 4, '1000.00', 'admin_credit', 'completed', '2025-01-10 08:28:36', NULL, 1, NULL, NULL),
(62, 5, '10000.00', 'admin_credit', 'completed', '2025-01-10 08:31:54', NULL, 1, NULL, NULL),
(63, 4, '47000.00', 'admin_credit', 'completed', '2025-01-10 08:33:52', NULL, 1, NULL, NULL),
(64, 4, '-500.00', 'transfer_sent', 'completed', '2025-01-14 13:56:45', NULL, NULL, 'Transfer between users', 1),
(65, 1, '500.00', 'transfer_received', 'completed', '2025-01-14 13:56:45', NULL, NULL, 'Transfer between users', 4),
(66, 4, '6000.00', 'admin_credit', 'completed', '2025-01-14 14:08:17', NULL, 1, NULL, NULL),
(67, 4, '-100.00', 'transfer_sent', 'completed', '2025-01-18 16:54:08', NULL, NULL, 'Transfer between users', 1),
(68, 1, '100.00', 'transfer_received', 'completed', '2025-01-18 16:54:08', NULL, NULL, 'Transfer between users', 4),
(69, 4, '19999.00', 'admin_credit', 'completed', '2025-01-18 17:08:54', NULL, 1, NULL, NULL),
(70, 1, '5000.00', 'bet', 'completed', '2025-01-26 16:57:46', 73, NULL, NULL, NULL),
(71, 1, '7500.00', 'bet', 'completed', '2025-01-27 13:40:04', 75, NULL, NULL, NULL),
(72, 2, '8500.00', 'bet', 'completed', '2025-01-27 14:44:17', 77, NULL, NULL, NULL),
(74, 1, '8500.00', 'bet', 'completed', '2025-01-27 18:56:02', 77, NULL, 'Bet outcome: Win - Points earned: 3', NULL),
(75, 2, '1700.00', 'bet', 'completed', '2025-01-27 18:56:02', 77, NULL, 'Bet outcome: Loss - Points earned: 0', NULL),
(76, 2, '1500.00', 'bet', 'completed', '2025-01-27 19:02:30', 75, NULL, 'Bet outcome: Loss - Points earned: 0', NULL),
(77, 1, '13500.00', 'bet', 'completed', '2025-01-27 19:02:30', 75, NULL, 'Bet outcome: Win - Points earned: 3', NULL),
(78, 2, '1000.00', 'bet', 'completed', '2025-01-27 19:06:50', 73, NULL, 'Bet outcome: Loss - Points earned: 0', NULL),
(79, 1, '5000.00', 'bet', 'completed', '2025-01-27 19:06:50', 73, NULL, 'Bet outcome: Win - Points earned: 3', NULL),
(80, 1, '8500.00', 'bet', 'completed', '2025-01-27 19:13:19', 76, NULL, NULL, NULL),
(81, 2, '15300.00', 'bet', 'completed', '2025-01-27 19:14:38', 76, NULL, 'Bet outcome: Win - Points earned: 3', NULL),
(82, 1, '1700.00', 'bet', 'completed', '2025-01-27 19:14:38', 76, NULL, 'Bet outcome: Loss - Points earned: 0', NULL),
(83, 1, '8700.00', 'bet', 'completed', '2025-01-27 21:37:14', 79, NULL, NULL, NULL),
(84, 1, '10000.00', 'admin_debit', 'completed', '2025-01-30 16:27:55', NULL, NULL, NULL, NULL),
(85, 8, '1000.00', 'admin_debit', 'completed', '2025-01-30 16:31:08', NULL, NULL, NULL, NULL),
(86, 8, '5000.00', 'bet', 'completed', '2025-02-01 03:22:07', 81, NULL, NULL, NULL),
(87, 2, '4998.00', 'bet', 'completed', '2025-02-01 03:38:18', 85, NULL, NULL, NULL),
(88, 2, '100.00', 'admin_debit', 'completed', '2025-02-01 11:22:35', NULL, NULL, 'League join fee', NULL),
(89, 5, '100.00', 'admin_debit', 'completed', '2025-02-01 11:31:55', NULL, NULL, 'League join fee', NULL),
(90, 3, '1200.00', 'admin_debit', 'completed', '2025-02-01 11:33:27', NULL, NULL, 'League join fee', NULL),
(91, 4, '10000.00', 'admin_debit', 'completed', '2025-02-01 11:35:31', NULL, NULL, 'League join fee', NULL),
(92, 1, '10000.00', 'admin_debit', 'completed', '2025-02-01 14:44:07', NULL, NULL, 'League join fee', NULL),
(93, 6, '1000.00', 'admin_debit', 'completed', '2025-02-01 14:45:30', NULL, NULL, 'League join fee', NULL),
(101, 2, '-8700.00', 'bet', 'completed', '2025-02-01 15:30:20', 79, NULL, 'Bet outcome: Loss - Amount: -8700', NULL),
(102, 1, '15660.00', 'bet', 'completed', '2025-02-01 15:30:20', 79, NULL, 'Bet outcome: Win - Amount: +15660.00', NULL),
(103, 8, '-4998.00', 'bet', 'completed', '2025-02-01 15:30:20', 85, NULL, 'Bet outcome: Loss - Amount: -4998', NULL),
(104, 2, '3998.40', 'bet', 'completed', '2025-02-01 15:30:20', 85, NULL, 'Bet outcome: Win - Amount: +3998.40', NULL),
(105, 8, '100.00', 'admin_debit', 'completed', '2025-02-01 15:31:13', NULL, NULL, 'League join fee', NULL),
(106, 2, '50000999.00', 'admin_credit', 'completed', '2025-02-08 15:07:56', NULL, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `favorite_team` varchar(100) DEFAULT NULL,
  `balance` decimal(10,2) DEFAULT '0.00',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `points` int(11) DEFAULT '0',
  `current_league_id` int(11) DEFAULT NULL,
  `total_points` int(11) DEFAULT '0',
  `current_streak` int(11) DEFAULT '0',
  `highest_streak` int(11) DEFAULT '0',
  `current_season_id` int(11) DEFAULT NULL,
  `last_active` timestamp NULL DEFAULT NULL,
  `role` enum('user','admin') NOT NULL DEFAULT 'user',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `username`, `full_name`, `password_hash`, `email`, `favorite_team`, `balance`, `created_at`, `updated_at`, `points`, `current_league_id`, `total_points`, `current_streak`, `highest_streak`, `current_season_id`, `last_active`, `role`, `wins`, `draws`, `losses`) VALUES
(1, 'Jamesbong01', 'James Bellengi', '$2y$10$cRil26laQt9KepdLzZwDp.xhM.zntH2yLsBMeAuqaJPfi4dymDh7i', '<EMAIL>', 'Manchester United', '5923206.00', '2024-09-23 15:25:21', '2025-02-01 15:30:20', 9, 7, 9, 0, 0, NULL, NULL, 'admin', 1, 0, 0),
(2, 'lilwayne', 'Lil Wayne', '$2y$10$FYcSfZpqua0E/NXqcCBzoe38AfqcEeW.EROWgVffLub0Pnui1UDTK', '<EMAIL>', 'Everton', '49998651.20', '2024-09-23 15:44:57', '2025-02-08 15:07:56', 3, 11, 3, 0, 0, NULL, NULL, 'user', 1, 0, 1),
(3, 'jameslink01', 'James Link', '$2y$10$upz956oskiIMk07QUxDYXO5i9D/R8mSX0DXcHoC/pybrHKaOWMt66', '<EMAIL>', 'Manchester United', '16903.20', '2024-09-24 17:28:36', '2025-02-01 11:33:27', 0, 11, 0, 0, 0, NULL, NULL, 'user', 0, 0, 0),
(4, 'demohomexx', 'Mr Owner', '$2y$10$Tq.WGMvqcPthx/WepKxfpuw2D4mbrEXK0JF4jkLss8zJAms6PDMEC', '<EMAIL>', 'Arsenal', '109399.20', '2024-10-22 17:22:47', '2025-02-01 11:35:31', 0, 11, 0, 0, 0, NULL, NULL, 'user', 0, 0, 0),
(5, 'Bobyanka01', 'Bobby Yanky', '$2y$10$XPWcZna1yubnBHFwZXDT.OlufpWuXBncqLlvktEg.PP8FuTYPETkq', '<EMAIL>', 'Arsenal', '11830.80', '2024-10-30 11:05:24', '2025-02-01 11:31:55', 0, 11, 0, 0, 0, NULL, NULL, 'user', 0, 0, 0),
(6, 'BlueeyeKing', 'Blue King', '$2y$10$QX9Q8B5nLBxkclgjz/CmnuS03m4xICojFifwkWwcyG4RMKgDFp7Sq', '<EMAIL>', 'Liverpool Fc', '8000.00', '2024-10-30 11:07:25', '2025-02-01 14:45:30', 0, 11, 0, 0, 0, NULL, NULL, 'user', 0, 0, 0),
(7, 'jesm', 'look', '$2y$10$2s9g1YuO8H/4ta9dwZ8UQuF7PopxCreXnnTV.Zu9TgvQuf3xQuEqW', '<EMAIL>', 'Arsenal', '1999.00', '2024-10-31 12:49:30', '2024-10-31 12:49:30', 0, NULL, 0, 0, 0, NULL, NULL, 'user', 0, 0, 0),
(8, 'Tegowolo', 'Teg Tega', '$2y$10$NZCrsp4JDQQbVfBbnuBZGOxdrgxuYHI53ES8K4ED.PZaWhpRgYVoq', '<EMAIL>', 'Brentford FC', '4147.40', '2024-11-01 16:24:32', '2025-02-01 15:31:13', 0, 11, 0, 0, 0, NULL, NULL, 'user', 0, 0, 1);

--
-- Triggers `users`
--
DELIMITER $$
CREATE TRIGGER `check_achievements` AFTER UPDATE ON `users` FOR EACH ROW BEGIN
    -- Check points-based achievements
    INSERT IGNORE INTO user_achievements (user_id, achievement_id)
    SELECT NEW.user_id, achievement_id
    FROM achievements
    WHERE points_required > 0 
    AND points_required <= NEW.total_points
    AND achievement_id NOT IN (
        SELECT achievement_id 
        FROM user_achievements 
        WHERE user_id = NEW.user_id
    );

    -- Check streak-based achievements
    IF NEW.current_streak > OLD.current_streak THEN
        INSERT IGNORE INTO user_achievements (user_id, achievement_id)
        SELECT NEW.user_id, achievement_id
        FROM achievements
        WHERE streak_required > 0 
        AND streak_required <= NEW.current_streak
        AND achievement_id NOT IN (
            SELECT achievement_id 
            FROM user_achievements 
            WHERE user_id = NEW.user_id
        );
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `user_achievements`
--

CREATE TABLE `user_achievements` (
  `user_id` int(11) NOT NULL,
  `achievement_id` int(11) NOT NULL,
  `earned_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user_activity_log`
--

CREATE TABLE `user_activity_log` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `activity_type` varchar(50) NOT NULL,
  `details` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `user_activity_log`
--

INSERT INTO `user_activity_log` (`log_id`, `user_id`, `activity_type`, `details`, `created_at`) VALUES
(1, 2, 'league_join', 'Joined league: UEFA Europa League', '2024-12-15 15:48:49'),
(2, 2, 'league_join', 'Joined league: Katsina Leaguex', '2024-12-15 15:54:29'),
(3, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\"}', '2024-12-15 16:02:50'),
(4, 2, 'league_join', '{\"league_id\":1,\"league_name\":\"Katsina Leaguex\"}', '2024-12-15 16:02:53'),
(5, 2, 'league_join', '{\"league_id\":2,\"league_name\":\"Calabar League\"}', '2024-12-15 16:19:27'),
(6, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2024-12-15 18:23:35'),
(7, 2, 'league_join', '{\"league_id\":1,\"league_name\":\"Katsina Leaguex\",\"min_bet\":\"1000.00\"}', '2024-12-15 18:25:14'),
(8, 2, 'league_join', '{\"league_id\":2,\"league_name\":\"Calabar League\",\"min_bet\":\"10000.00\"}', '2024-12-15 18:27:52'),
(9, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2024-12-15 20:05:26'),
(10, 2, 'league_join', '{\"league_id\":1,\"league_name\":\"Katsina Leaguex\",\"min_bet\":\"1000.00\"}', '2024-12-15 20:07:29'),
(11, 2, 'league_join', '{\"league_id\":2,\"league_name\":\"Calabar League\",\"min_bet\":\"10000.00\"}', '2024-12-15 20:18:01'),
(12, 4, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2024-12-15 22:13:31'),
(13, 4, 'league_join', '{\"league_id\":1,\"league_name\":\"Katsina Leaguex\",\"min_bet\":\"1000.00\"}', '2024-12-15 22:14:01'),
(14, 4, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 16:48:18'),
(15, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 17:34:02'),
(16, 4, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 17:45:47'),
(17, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 17:46:03'),
(18, 4, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 18:07:35'),
(19, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 18:07:47');

-- --------------------------------------------------------

--
-- Table structure for table `user_friends`
--

CREATE TABLE `user_friends` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `friend_id` int(11) NOT NULL,
  `status` enum('pending','accepted','rejected') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `user_friends`
--

INSERT INTO `user_friends` (`id`, `user_id`, `friend_id`, `status`, `created_at`) VALUES
(2, 2, 4, 'accepted', '2024-11-30 16:20:42'),
(3, 4, 1, 'accepted', '2024-11-30 18:07:44'),
(4, 2, 1, 'accepted', '2024-11-30 18:38:05'),
(5, 2, 6, 'accepted', '2024-11-30 18:44:06'),
(6, 2, 8, 'pending', '2024-11-30 18:46:57'),
(7, 4, 5, 'accepted', '2025-01-10 04:47:38');

-- --------------------------------------------------------

--
-- Table structure for table `user_leagues`
--

CREATE TABLE `user_leagues` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `join_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','active','suspended','completed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_leagues`
--

INSERT INTO `user_leagues` (`id`, `user_id`, `league_id`, `join_date`, `status`, `created_at`) VALUES
(19, 2, 11, '2025-02-01 09:22:35', 'active', '2025-02-01 11:22:35'),
(20, 5, 11, '2025-02-01 09:31:54', 'active', '2025-02-01 11:31:54'),
(21, 3, 11, '2025-02-01 09:33:27', 'active', '2025-02-01 11:33:27'),
(22, 4, 11, '2025-02-01 09:35:30', 'active', '2025-02-01 11:35:30'),
(23, 1, 7, '2025-02-01 12:44:07', 'active', '2025-02-01 14:44:07'),
(24, 6, 11, '2025-02-01 12:45:30', 'active', '2025-02-01 14:45:30'),
(25, 8, 11, '2025-02-01 13:31:13', 'active', '2025-02-01 15:31:13');

-- --------------------------------------------------------

--
-- Table structure for table `user_leagues_backup`
--

CREATE TABLE `user_leagues_backup` (
  `id` int(11) NOT NULL DEFAULT '0',
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `join_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','active','suspended','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `user_leagues_backup`
--

INSERT INTO `user_leagues_backup` (`id`, `user_id`, `league_id`, `join_date`, `status`, `created_at`) VALUES
(20, 4, 7, '2025-01-09 16:30:51', 'active', '2025-01-09 18:30:51'),
(21, 2, 7, '2025-01-09 16:31:09', 'active', '2025-01-09 18:31:09'),
(22, 3, 7, '2025-01-10 02:19:35', 'active', '2025-01-10 04:19:35'),
(23, 5, 7, '2025-01-10 02:22:25', 'active', '2025-01-10 04:22:25'),
(29, 1, 1, '2025-01-30 14:27:55', 'active', '2025-01-30 16:27:55'),
(30, 8, 1, '2025-01-30 14:31:07', 'active', '2025-01-30 16:31:07');

-- --------------------------------------------------------

--
-- Table structure for table `user_league_stats`
--

CREATE TABLE `user_league_stats` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `points` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `streak` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_league_stats`
--

INSERT INTO `user_league_stats` (`id`, `user_id`, `league_id`, `points`, `wins`, `draws`, `losses`, `streak`, `created_at`, `updated_at`) VALUES
(2, 2, 11, 3, 1, 0, 1, 0, '2025-02-01 11:22:35', '2025-02-01 15:30:20'),
(3, 5, 11, 0, 0, 0, 0, 0, '2025-02-01 11:31:55', '2025-02-01 11:31:55'),
(4, 3, 11, 0, 0, 0, 0, 0, '2025-02-01 11:33:27', '2025-02-01 11:33:27'),
(5, 4, 11, 0, 0, 0, 0, 0, '2025-02-01 11:35:31', '2025-02-01 11:35:31'),
(6, 1, 7, 3, 1, 0, 0, 0, '2025-02-01 14:44:07', '2025-02-01 15:30:20'),
(7, 6, 11, 0, 0, 0, 0, 0, '2025-02-01 14:45:30', '2025-02-01 14:45:30'),
(18, 8, 11, 0, 0, 0, 0, 0, '2025-02-01 15:31:13', '2025-02-01 15:31:13');

-- --------------------------------------------------------

--
-- Table structure for table `user_league_stats_backup`
--

CREATE TABLE `user_league_stats_backup` (
  `id` int(11) NOT NULL DEFAULT '0',
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `points` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `streak` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `achievements`
--
ALTER TABLE `achievements`
  ADD PRIMARY KEY (`achievement_id`);

--
-- Indexes for table `adminactions`
--
ALTER TABLE `adminactions`
  ADD PRIMARY KEY (`action_id`),
  ADD KEY `admin_id` (`admin_id`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`admin_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_admin_username` (`username`),
  ADD KEY `idx_admin_email` (`email`);

--
-- Indexes for table `bets`
--
ALTER TABLE `bets`
  ADD PRIMARY KEY (`bet_id`),
  ADD KEY `idx_bet_challenge_id` (`challenge_id`),
  ADD KEY `user1_id` (`user1_id`),
  ADD KEY `user2_id` (`user2_id`),
  ADD KEY `team1_id` (`team1_id`),
  ADD KEY `team2_id` (`team2_id`),
  ADD KEY `idx_bets_user1` (`user1_id`),
  ADD KEY `idx_bets_user2` (`user2_id`),
  ADD KEY `idx_bets_challenge` (`challenge_id`);

--
-- Indexes for table `challenges`
--
ALTER TABLE `challenges`
  ADD PRIMARY KEY (`challenge_id`),
  ADD KEY `idx_challenge_admin_id` (`admin_id`),
  ADD KEY `idx_challenge_status` (`status`);

--
-- Indexes for table `credit_requests`
--
ALTER TABLE `credit_requests`
  ADD PRIMARY KEY (`request_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `payment_method_id` (`payment_method_id`),
  ADD KEY `approved_by` (`approved_by`);

--
-- Indexes for table `leaderboards`
--
ALTER TABLE `leaderboards`
  ADD PRIMARY KEY (`leaderboard_id`),
  ADD KEY `idx_leaderboard_user_id` (`user_id`),
  ADD KEY `idx_leaderboard_points` (`points`);

--
-- Indexes for table `leagues`
--
ALTER TABLE `leagues`
  ADD PRIMARY KEY (`league_id`),
  ADD UNIQUE KEY `idx_league_name` (`name`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `league_audit_log`
--
ALTER TABLE `league_audit_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `season_id` (`season_id`),
  ADD KEY `membership_id` (`membership_id`),
  ADD KEY `idx_league_audit_admin` (`admin_id`),
  ADD KEY `idx_league_audit_league` (`league_id`);

--
-- Indexes for table `league_history`
--
ALTER TABLE `league_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `league_id` (`league_id`),
  ADD KEY `verified_by` (`verified_by`),
  ADD KEY `idx_league_history_season` (`season_id`),
  ADD KEY `idx_league_history_user` (`user_id`);

--
-- Indexes for table `league_memberships`
--
ALTER TABLE `league_memberships`
  ADD PRIMARY KEY (`membership_id`),
  ADD UNIQUE KEY `idx_unique_active_membership` (`user_id`,`league_id`,`status`),
  ADD UNIQUE KEY `unique_active_membership` (`league_id`,`active_membership`),
  ADD KEY `approved_by` (`approved_by`),
  ADD KEY `idx_league_memberships_user` (`user_id`),
  ADD KEY `idx_league_memberships_league` (`league_id`),
  ADD KEY `idx_league_memberships_season` (`season_id`),
  ADD KEY `fk_league_memberships_user_league` (`user_league_id`);

--
-- Indexes for table `league_seasons`
--
ALTER TABLE `league_seasons`
  ADD PRIMARY KEY (`season_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_league_seasons_league` (`league_id`);

--
-- Indexes for table `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_sender` (`sender_id`),
  ADD KEY `idx_recipient` (`recipient_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `paymentverifications`
--
ALTER TABLE `paymentverifications`
  ADD PRIMARY KEY (`verification_id`),
  ADD KEY `verified_by` (`verified_by`),
  ADD KEY `idx_payment_verification_user_id` (`user_id`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `seasons`
--
ALTER TABLE `seasons`
  ADD PRIMARY KEY (`season_id`);

--
-- Indexes for table `season_history`
--
ALTER TABLE `season_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `season_id` (`season_id`);

--
-- Indexes for table `teams`
--
ALTER TABLE `teams`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`transaction_id`),
  ADD KEY `related_bet_id` (`related_bet_id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `idx_transaction_user_id` (`user_id`),
  ADD KEY `related_user_id` (`related_user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_user_username` (`username`),
  ADD KEY `idx_user_email` (`email`),
  ADD KEY `fk_user_current_season` (`current_season_id`),
  ADD KEY `idx_last_active` (`last_active`),
  ADD KEY `idx_users_league` (`current_league_id`);

--
-- Indexes for table `user_achievements`
--
ALTER TABLE `user_achievements`
  ADD PRIMARY KEY (`user_id`,`achievement_id`),
  ADD KEY `achievement_id` (`achievement_id`);

--
-- Indexes for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `user_friends`
--
ALTER TABLE `user_friends`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_friendship` (`user_id`,`friend_id`),
  ADD KEY `friend_id` (`friend_id`);

--
-- Indexes for table `user_leagues`
--
ALTER TABLE `user_leagues`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_league` (`user_id`,`league_id`),
  ADD KEY `fk_user_leagues_league` (`league_id`);

--
-- Indexes for table `user_league_stats`
--
ALTER TABLE `user_league_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_league_stats` (`user_id`,`league_id`),
  ADD UNIQUE KEY `user_league_unique` (`user_id`,`league_id`),
  ADD KEY `fk_user_league_stats_league` (`league_id`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `achievements`
--
ALTER TABLE `achievements`
  MODIFY `achievement_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `adminactions`
--
ALTER TABLE `adminactions`
  MODIFY `action_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `bets`
--
ALTER TABLE `bets`
  MODIFY `bet_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=90;

--
-- AUTO_INCREMENT for table `challenges`
--
ALTER TABLE `challenges`
  MODIFY `challenge_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `credit_requests`
--
ALTER TABLE `credit_requests`
  MODIFY `request_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `leaderboards`
--
ALTER TABLE `leaderboards`
  MODIFY `leaderboard_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `leagues`
--
ALTER TABLE `leagues`
  MODIFY `league_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `league_audit_log`
--
ALTER TABLE `league_audit_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `league_history`
--
ALTER TABLE `league_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `league_memberships`
--
ALTER TABLE `league_memberships`
  MODIFY `membership_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `league_seasons`
--
ALTER TABLE `league_seasons`
  MODIFY `season_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `messages`
--
ALTER TABLE `messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

--
-- AUTO_INCREMENT for table `paymentverifications`
--
ALTER TABLE `paymentverifications`
  MODIFY `verification_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `seasons`
--
ALTER TABLE `seasons`
  MODIFY `season_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `season_history`
--
ALTER TABLE `season_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `teams`
--
ALTER TABLE `teams`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `transaction_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=107;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `user_friends`
--
ALTER TABLE `user_friends`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `user_leagues`
--
ALTER TABLE `user_leagues`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `user_league_stats`
--
ALTER TABLE `user_league_stats`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `user_sessions`
--
ALTER TABLE `user_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `adminactions`
--
ALTER TABLE `adminactions`
  ADD CONSTRAINT `adminactions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `bets`
--
ALTER TABLE `bets`
  ADD CONSTRAINT `bets_ibfk_1` FOREIGN KEY (`user1_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `bets_ibfk_2` FOREIGN KEY (`user2_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `bets_ibfk_3` FOREIGN KEY (`team1_id`) REFERENCES `teams` (`id`),
  ADD CONSTRAINT `bets_ibfk_4` FOREIGN KEY (`team2_id`) REFERENCES `teams` (`id`);

--
-- Constraints for table `challenges`
--
ALTER TABLE `challenges`
  ADD CONSTRAINT `challenges_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `credit_requests`
--
ALTER TABLE `credit_requests`
  ADD CONSTRAINT `credit_requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `credit_requests_ibfk_2` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`),
  ADD CONSTRAINT `credit_requests_ibfk_3` FOREIGN KEY (`approved_by`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `leaderboards`
--
ALTER TABLE `leaderboards`
  ADD CONSTRAINT `leaderboards_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `leagues`
--
ALTER TABLE `leagues`
  ADD CONSTRAINT `leagues_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `league_audit_log`
--
ALTER TABLE `league_audit_log`
  ADD CONSTRAINT `league_audit_log_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`),
  ADD CONSTRAINT `league_audit_log_ibfk_3` FOREIGN KEY (`season_id`) REFERENCES `league_seasons` (`season_id`),
  ADD CONSTRAINT `league_audit_log_ibfk_4` FOREIGN KEY (`membership_id`) REFERENCES `league_memberships` (`membership_id`);

--
-- Constraints for table `league_history`
--
ALTER TABLE `league_history`
  ADD CONSTRAINT `league_history_ibfk_1` FOREIGN KEY (`season_id`) REFERENCES `league_seasons` (`season_id`),
  ADD CONSTRAINT `league_history_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `league_history_ibfk_3` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`),
  ADD CONSTRAINT `league_history_ibfk_4` FOREIGN KEY (`verified_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `league_memberships`
--
ALTER TABLE `league_memberships`
  ADD CONSTRAINT `fk_league_memberships_user_league` FOREIGN KEY (`user_league_id`) REFERENCES `user_leagues` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `league_memberships_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `league_memberships_ibfk_2` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`),
  ADD CONSTRAINT `league_memberships_ibfk_3` FOREIGN KEY (`season_id`) REFERENCES `league_seasons` (`season_id`),
  ADD CONSTRAINT `league_memberships_ibfk_4` FOREIGN KEY (`approved_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `league_seasons`
--
ALTER TABLE `league_seasons`
  ADD CONSTRAINT `league_seasons_ibfk_1` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`),
  ADD CONSTRAINT `league_seasons_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `messages`
--
ALTER TABLE `messages`
  ADD CONSTRAINT `messages_ibfk_1` FOREIGN KEY (`sender_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `messages_ibfk_2` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `paymentverifications`
--
ALTER TABLE `paymentverifications`
  ADD CONSTRAINT `paymentverifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `paymentverifications_ibfk_2` FOREIGN KEY (`verified_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `season_history`
--
ALTER TABLE `season_history`
  ADD CONSTRAINT `season_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `season_history_ibfk_2` FOREIGN KEY (`season_id`) REFERENCES `seasons` (`season_id`) ON DELETE CASCADE;

--
-- Constraints for table `transactions`
--
ALTER TABLE `transactions`
  ADD CONSTRAINT `transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `transactions_ibfk_3` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`),
  ADD CONSTRAINT `transactions_ibfk_4` FOREIGN KEY (`related_user_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `fk_current_league` FOREIGN KEY (`current_league_id`) REFERENCES `leagues` (`league_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_user_current_league` FOREIGN KEY (`current_league_id`) REFERENCES `leagues` (`league_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_user_current_season` FOREIGN KEY (`current_season_id`) REFERENCES `seasons` (`season_id`) ON DELETE SET NULL;

--
-- Constraints for table `user_achievements`
--
ALTER TABLE `user_achievements`
  ADD CONSTRAINT `user_achievements_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `user_achievements_ibfk_2` FOREIGN KEY (`achievement_id`) REFERENCES `achievements` (`achievement_id`);

--
-- Constraints for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD CONSTRAINT `user_activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `user_friends`
--
ALTER TABLE `user_friends`
  ADD CONSTRAINT `user_friends_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `user_friends_ibfk_2` FOREIGN KEY (`friend_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `user_leagues`
--
ALTER TABLE `user_leagues`
  ADD CONSTRAINT `fk_user_leagues_league` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_user_leagues_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user_league_stats`
--
ALTER TABLE `user_league_stats`
  ADD CONSTRAINT `fk_user_league_stats_league` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_user_league_stats_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
