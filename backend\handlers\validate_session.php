<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get Authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    
    if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
        throw new Exception('No valid authorization token provided');
    }
    
    $token = substr($authHeader, 7); // Remove 'Bearer ' prefix
    
    if (empty($token)) {
        throw new Exception('Empty token provided');
    }
    
    // Validate token in database
    $stmt = $conn->prepare("
        SELECT us.user_id, us.expires_at, u.username, u.email, u.full_name, u.role, u.balance, 
               COALESCE((SELECT SUM(points) FROM user_achievements WHERE user_id = u.user_id), 0) as points
        FROM user_sessions us 
        JOIN users u ON us.user_id = u.user_id 
        WHERE us.token = ? AND us.expires_at > NOW()
    ");
    $stmt->execute([$token]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        throw new Exception('Invalid or expired session token');
    }
    
    // Update last active timestamp
    $stmt = $conn->prepare("UPDATE users SET last_active = NOW() WHERE user_id = ?");
    $stmt->execute([$session['user_id']]);
    
    // Return user data
    echo json_encode([
        'success' => true,
        'message' => 'Session valid',
        'user' => [
            'id' => $session['user_id'],
            'username' => $session['username'],
            'email' => $session['email'],
            'fullName' => $session['full_name'],
            'role' => $session['role'],
            'balance' => floatval($session['balance']),
            'points' => intval($session['points'])
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
