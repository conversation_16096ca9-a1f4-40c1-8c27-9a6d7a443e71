<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Checking currency-related tables...\n";
    
    // Check for currency tables
    $tables = $conn->query("SHOW TABLES LIKE '%currenc%'")->fetchAll();
    echo "Currency tables found:\n";
    foreach ($tables as $table) {
        echo "- " . $table[0] . "\n";
    }
    
    // Check for exchange rate tables
    $tables = $conn->query("SHOW TABLES LIKE '%exchange%'")->fetchAll();
    echo "\nExchange rate tables found:\n";
    foreach ($tables as $table) {
        echo "- " . $table[0] . "\n";
    }
    
    // Try to check if currencies table exists and has data
    try {
        $result = $conn->query("SELECT COUNT(*) as count FROM currencies")->fetch();
        echo "\nCurrencies table has " . $result['count'] . " records\n";
    } catch (Exception $e) {
        echo "\nCurrencies table does not exist or error: " . $e->getMessage() . "\n";
    }
    
    // Try to check if exchange_rates table exists and has data
    try {
        $result = $conn->query("SELECT COUNT(*) as count FROM exchange_rates")->fetch();
        echo "Exchange_rates table has " . $result['count'] . " records\n";
    } catch (Exception $e) {
        echo "Exchange_rates table does not exist or error: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
