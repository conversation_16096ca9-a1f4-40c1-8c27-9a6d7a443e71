<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

/**
 * Convert FanCoin amount to specified currency
 */
function convertFanCoinToCurrency($conn, $fanCoinAmount, $currencyId) {
    try {
        // Get the latest exchange rate for the currency
        $stmt = $conn->prepare("
            SELECT 
                c.currency_code,
                c.currency_symbol,
                c.currency_name,
                er.rate_to_fancoin
            FROM currencies c
            LEFT JOIN exchange_rates er ON c.id = er.currency_id
            WHERE c.id = :currency_id AND c.is_active = 1
            ORDER BY er.updated_at DESC
            LIMIT 1
        ");
        
        $stmt->execute(['currency_id' => $currencyId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            return [
                'success' => false,
                'message' => 'Currency not found or inactive'
            ];
        }
        
        if (!$result['rate_to_fancoin']) {
            return [
                'success' => false,
                'message' => 'No exchange rate available for this currency'
            ];
        }
        
        $convertedAmount = $fanCoinAmount * $result['rate_to_fancoin'];
        
        return [
            'success' => true,
            'original_amount' => (float)$fanCoinAmount,
            'converted_amount' => (float)$convertedAmount,
            'currency_code' => $result['currency_code'],
            'currency_symbol' => $result['currency_symbol'],
            'currency_name' => $result['currency_name'],
            'exchange_rate' => (float)$result['rate_to_fancoin'],
            'formatted_amount' => $result['currency_symbol'] . number_format($convertedAmount, 2),
            'conversion_text' => number_format($fanCoinAmount, 2) . ' FanCoin = ' . 
                               $result['currency_symbol'] . number_format($convertedAmount, 2) . ' ' . $result['currency_code']
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Conversion error: ' . $e->getMessage()
        ];
    }
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }
    
    // Handle both GET and POST requests
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $fanCoinAmount = isset($_GET['amount']) ? (float)$_GET['amount'] : null;
        $currencyId = isset($_GET['currency_id']) ? (int)$_GET['currency_id'] : null;
        $currencyCode = isset($_GET['currency_code']) ? strtoupper(trim($_GET['currency_code'])) : null;
    } else {
        $input = json_decode(file_get_contents("php://input"), true);
        $fanCoinAmount = isset($input['amount']) ? (float)$input['amount'] : null;
        $currencyId = isset($input['currency_id']) ? (int)$input['currency_id'] : null;
        $currencyCode = isset($input['currency_code']) ? strtoupper(trim($input['currency_code'])) : null;
    }
    
    // Validate required parameters
    if ($fanCoinAmount === null || $fanCoinAmount < 0) {
        jsonResponse(400, "Valid FanCoin amount is required");
    }
    
    if (!$currencyId && !$currencyCode) {
        jsonResponse(400, "Currency ID or currency code is required");
    }
    
    // If currency code provided, get currency ID
    if (!$currencyId && $currencyCode) {
        $stmt = $conn->prepare("SELECT id FROM currencies WHERE currency_code = :currency_code AND is_active = 1");
        $stmt->execute(['currency_code' => $currencyCode]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            jsonResponse(404, "Currency code not found or inactive");
        }
        
        $currencyId = $result['id'];
    }
    
    // Perform the conversion
    $conversionResult = convertFanCoinToCurrency($conn, $fanCoinAmount, $currencyId);
    
    if (!$conversionResult['success']) {
        jsonResponse(400, $conversionResult['message']);
    }
    
    jsonResponse(200, "Currency conversion successful", $conversionResult);
    
} catch (PDOException $e) {
    error_log("Database error in convert_currency.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    error_log("General error in convert_currency.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred during currency conversion");
}
?>
