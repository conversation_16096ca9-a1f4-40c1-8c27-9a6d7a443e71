<?php
// Test the actual user_send_otp.php handler
header("Content-Type: application/json; charset=UTF-8");

// Simulate the exact request that the frontend is making
$_POST = [];
$input = json_encode([
    'userId' => 13,
    'email' => '<EMAIL>'
]);

// Capture the input
file_put_contents('php://input', $input);

echo "🧪 Testing user_send_otp.php handler...\n";
echo "Input: $input\n\n";

// Capture output
ob_start();

try {
    // Include the actual handler
    include 'handlers/user_send_otp.php';
    
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "✅ Handler executed successfully!\n";
    echo "Output: $output\n";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Error in handler: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    ob_end_clean();
    echo "❌ Fatal error in handler: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}
?>
