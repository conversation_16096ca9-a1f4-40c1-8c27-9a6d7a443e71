# FanBet247 Currency Display System

## Overview
The Currency Display System provides a comprehensive set of React components and utilities for displaying FanCoin amounts in users' preferred currencies throughout the FanBet247 application.

## Architecture

### Context-Based State Management
- **CurrencyContext**: Centralized currency state management with caching
- **Integration**: Seamlessly integrates with existing UserContext and ErrorContext
- **Performance**: Implements localStorage caching to reduce API calls

### Component Hierarchy
```
CurrencyProvider (Context)
├── CurrencyAmount (Base component)
├── CurrencyBalance (User balance display)
├── CurrencyBetAmount (Bet amount display)
├── CurrencyCompact (Table/list display)
├── CurrencyInput (Input with conversion preview)
├── CurrencyComparison (Before/after amounts)
├── CurrencyTooltip (Hover information)
├── CurrencySelector (Settings component)
├── CurrencyQuickSelector (Header/sidebar)
└── CurrencyInfo (Rate information)
```

## Components

### Core Components

#### CurrencyAmount
**Purpose**: Base component for displaying FanCoin amounts in user's preferred currency

**Props**:
- `amount` (number): FanCoin amount to display
- `showBoth` (boolean): Show both converted amount and FanCoin (default: true)
- `showFanCoinOnly` (boolean): Force FanCoin-only display (default: false)
- `size` (string): 'small', 'medium', 'large' (default: 'medium')
- `loading` (boolean): Show loading state (default: false)
- `className` (string): Additional CSS classes

**Usage**:
```jsx
import { CurrencyAmount } from '../components/Currency';

<CurrencyAmount amount={100} showBoth={true} size="medium" />
// Output: "R1,800.00 (100 FanCoin)" for ZAR user
```

#### CurrencyBalance
**Purpose**: Specialized component for displaying user balance with emphasis

**Usage**:
```jsx
import { CurrencyBalance } from '../components/Currency';

<CurrencyBalance amount={userData.points} size="large" />
```

#### CurrencyBetAmount
**Purpose**: Specialized component for displaying bet amounts

**Usage**:
```jsx
import { CurrencyBetAmount } from '../components/Currency';

<CurrencyBetAmount amount={bet.amount} size="medium" />
```

#### CurrencyInput
**Purpose**: Input field with real-time currency conversion preview

**Props**:
- `value` (string): Current input value
- `onChange` (function): Change handler
- `placeholder` (string): Input placeholder
- `showConversion` (boolean): Show conversion preview (default: true)

**Usage**:
```jsx
import { CurrencyInput } from '../components/Currency';

<CurrencyInput 
    value={betAmount}
    onChange={(e) => setBetAmount(e.target.value)}
    placeholder="Enter bet amount"
    showConversion={true}
/>
```

### Selector Components

#### CurrencySelector
**Purpose**: Full currency selection interface for user settings

**Props**:
- `userId` (number): User ID for updating preference
- `onCurrencyChange` (function): Callback when currency changes
- `showPreview` (boolean): Show conversion examples (default: true)
- `disabled` (boolean): Disable selector (default: false)

**Usage**:
```jsx
import { CurrencySelector } from '../components/Currency';

<CurrencySelector 
    userId={userId}
    onCurrencyChange={(newCurrency) => console.log('Changed to:', newCurrency)}
    showPreview={true}
/>
```

#### CurrencyQuickSelector
**Purpose**: Compact currency selector for headers/sidebars

**Usage**:
```jsx
import { CurrencyQuickSelector } from '../components/Currency';

<CurrencyQuickSelector userId={userId} className="header-selector" />
```

## Integration Examples

### UserLayout Integration
```jsx
// Sidebar balance
<CurrencyBalance 
    amount={userData.points} 
    size="small" 
    className="sidebar-balance"
/>

// Header currency selector
<CurrencyQuickSelector 
    userId={localStorage.getItem('userId')} 
    className="header-currency-selector"
/>
```

### Dashboard Integration
```jsx
// Main balance display
<CurrencyBalance 
    amount={user.balance} 
    size="large" 
    className="dashboard-balance"
/>

// Bet amounts in lists
<CurrencyBetAmount 
    amount={bet.amount} 
    size="small" 
/>
```

### Form Integration
```jsx
// Bet amount input
<CurrencyInput 
    value={betAmount}
    onChange={(e) => setBetAmount(e.target.value)}
    showConversion={true}
/>
```

## Context Usage

### useCurrency Hook
```jsx
import { useCurrency } from '../contexts/CurrencyContext';

function MyComponent() {
    const { 
        currencies,
        userCurrency,
        convertToUserCurrency,
        formatAmountForDisplay,
        loading,
        error
    } = useCurrency();
    
    // Convert amount
    const conversion = convertToUserCurrency(100);
    
    // Format for display
    const formatted = formatAmountForDisplay(100, true);
    
    return <div>{formatted}</div>;
}
```

### Available Context Functions
- `convertToUserCurrency(amount)`: Convert FanCoin to user currency
- `formatAmountForDisplay(amount, showBoth)`: Format amount for display
- `getCurrencyById(id)`: Get currency by ID
- `getCurrencyByCode(code)`: Get currency by code
- `refreshCurrencyData()`: Refresh currency data from API
- `updateUserCurrency(currency)`: Update user's currency preference

## Styling

### CSS Classes
- `.currency-amount`: Base currency amount styling
- `.currency-amount.small/medium/large`: Size variants
- `.currency-balance`: Balance-specific styling
- `.currency-bet-amount`: Bet amount styling
- `.currency-compact`: Compact display styling
- `.currency-input-wrapper`: Input component wrapper
- `.currency-selector`: Full selector styling
- `.currency-quick-selector`: Quick selector styling

### Responsive Design
- Mobile-optimized layouts
- Collapsible secondary amounts on small screens
- Touch-friendly selectors

### Dark Mode Support
- Automatic dark mode detection
- Consistent color schemes
- Proper contrast ratios

## Performance Optimizations

### Caching Strategy
- **localStorage**: Cache currency data for 30 minutes
- **Context**: Minimize re-renders with useMemo and useCallback
- **API**: Batch currency requests where possible

### Loading States
- Skeleton loading for currency amounts
- Progressive loading for selectors
- Graceful fallbacks for network errors

### Error Handling
- Fallback to USD for failed currency loads
- User-friendly error messages
- Retry mechanisms for network failures

## Testing

### Component Testing
```jsx
import { render, screen } from '@testing-library/react';
import { CurrencyProvider } from '../contexts/CurrencyContext';
import { CurrencyAmount } from '../components/Currency';

test('displays currency amount correctly', () => {
    render(
        <CurrencyProvider>
            <CurrencyAmount amount={100} />
        </CurrencyProvider>
    );
    
    expect(screen.getByText(/100/)).toBeInTheDocument();
});
```

### Integration Testing
- Test currency context with different user preferences
- Verify API integration with mock responses
- Test error handling and fallback scenarios

## Migration Guide

### Updating Existing Components
1. **Import currency components**:
   ```jsx
   import { CurrencyAmount } from '../components/Currency';
   ```

2. **Replace hardcoded amounts**:
   ```jsx
   // Before
   <span>{amount} FC</span>
   
   // After
   <CurrencyAmount amount={amount} />
   ```

3. **Update balance displays**:
   ```jsx
   // Before
   <span>₦{balance.toLocaleString()}</span>
   
   // After
   <CurrencyBalance amount={balance} />
   ```

### Common Patterns
- **Lists/Tables**: Use `CurrencyCompact` for space-efficient display
- **Forms**: Use `CurrencyInput` for amount inputs with conversion preview
- **Headers**: Use `CurrencyQuickSelector` for currency switching
- **Settings**: Use `CurrencySelector` for full currency management

## Best Practices

### Component Selection
- Use `CurrencyBalance` for user balance displays
- Use `CurrencyBetAmount` for bet-related amounts
- Use `CurrencyCompact` in tables and lists
- Use `CurrencyAmount` for general purpose displays

### Performance
- Avoid unnecessary re-renders by memoizing props
- Use loading states for better user experience
- Implement proper error boundaries

### Accessibility
- Provide meaningful alt text and titles
- Ensure proper keyboard navigation
- Maintain sufficient color contrast

### Internationalization
- Support RTL languages in future updates
- Consider locale-specific number formatting
- Plan for additional currency symbols

This Currency Display System provides a robust, scalable foundation for multi-currency support in FanBet247 while maintaining excellent user experience and performance.
