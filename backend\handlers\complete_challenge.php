<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

$conn = getDBConnection();
$conn->beginTransaction();

try {
    // Get input parameters
    $challenge_id = $_POST['challenge_id'];
    $team_a_score = $_POST['team_a_score'];
    $team_b_score = $_POST['team_b_score'];
    $winner = $_POST['winner'];
    $loser = isset($_POST['loser']) ? $_POST['loser'] : null;
    $new_status = $_POST['new_status'];

    // Validate winner/loser combination
    if ($winner !== 'draw' && empty($loser)) {
        throw new Exception('Loser must be specified when winner is not a draw');
    }

    if ($winner !== 'draw' && $winner === $loser) {
        throw new Exception('Winner and loser cannot be the same team');
    }

    // Verify challenge exists and is closed
    $checkChallenge = $conn->prepare("
        SELECT challenge_id, team_a, team_b, odds_team_a, odds_team_b, odds_draw, odds_lost 
        FROM challenges 
        WHERE challenge_id = :challenge_id AND status = 'Closed'
    ");
    $checkChallenge->execute([':challenge_id' => $challenge_id]);
    $challenge = $checkChallenge->fetch(PDO::FETCH_ASSOC);

    if (!$challenge) {
        throw new Exception('Challenge not found or not in Closed status');
    }

    // Get all bets for this challenge
    $getBets = $conn->prepare("
        SELECT 
            b.bet_id,
            b.user1_id, 
            b.user2_id,
            u1.username as user1_name,
            u2.username as user2_name,
            b.bet_choice_user1,
            b.bet_choice_user2,
            b.amount_user1,
            b.amount_user2,
            b.odds_user1,
            b.odds_user2,
            b.potential_return_win_user1,
            b.potential_return_draw_user1,
            b.potential_return_loss_user1,
            b.potential_return_win_user2,
            b.potential_return_draw_user2,
            b.potential_return_loss_user2
        FROM bets b
        JOIN users u1 ON b.user1_id = u1.user_id
        JOIN users u2 ON b.user2_id = u2.user_id
        WHERE b.challenge_id = :challenge_id 
        AND b.bet_status = 'joined'
    ");
    $getBets->execute([':challenge_id' => $challenge_id]);
    $bets = $getBets->fetchAll(PDO::FETCH_ASSOC);

    // Process each bet
    foreach ($bets as $bet) {
        // Determine outcomes and winnings
        $user1_outcome = '';
        $user2_outcome = '';
        $user1_winnings = 0;
        $user2_winnings = 0;

        if ($winner === 'draw') {
            // Handle draw outcome
            $user1_outcome = ($bet['bet_choice_user1'] === 'draw') ? 'win' : 'loss';
            $user2_outcome = ($bet['bet_choice_user2'] === 'draw') ? 'win' : 'loss';
            $user1_winnings = ($user1_outcome === 'win') ? 
                $bet['potential_return_draw_user1'] : 
                $bet['amount_user1'] * $challenge['odds_lost'];
            $user2_winnings = ($user2_outcome === 'win') ? 
                $bet['potential_return_draw_user2'] : 
                $bet['amount_user2'] * $challenge['odds_lost'];
        } else {
            // Handle win/loss outcome
            $user1_outcome = ($bet['bet_choice_user1'] === $winner) ? 'win' : 'loss';
            $user2_outcome = ($bet['bet_choice_user2'] === $winner) ? 'win' : 'loss';
            $user1_winnings = ($user1_outcome === 'win') ? 
                $bet['potential_return_win_user1'] : 
                $bet['amount_user1'] * $challenge['odds_lost'];
            $user2_winnings = ($user2_outcome === 'win') ? 
                $bet['potential_return_win_user2'] : 
                $bet['amount_user2'] * $challenge['odds_lost'];
        }

        // Process user 1
        processUserResults(
            $conn,
            $bet['user1_id'],
            $user1_outcome,
            $user1_winnings,
            $bet['amount_user1'],
            $bet['bet_id'],
            $challenge_id
        );

        // Process user 2
        processUserResults(
            $conn,
            $bet['user2_id'],
            $user2_outcome,
            $user2_winnings,
            $bet['amount_user2'],
            $bet['bet_id'],
            $challenge_id
        );

        // Update bet status and outcome
        $updateBet = $conn->prepare("
            UPDATE bets 
            SET bet_status = 'completed',
                outcome = :winner,
                user1_outcome = :user1_outcome,
                user2_outcome = :user2_outcome,
                final_winnings_user1 = :winnings_user1,
                final_winnings_user2 = :winnings_user2
            WHERE bet_id = :bet_id
        ");
        $updateBet->execute([
            ':winner' => $winner,
            ':user1_outcome' => $user1_outcome,
            ':user2_outcome' => $user2_outcome,
            ':winnings_user1' => $user1_winnings,
            ':winnings_user2' => $user2_winnings,
            ':bet_id' => $bet['bet_id']
        ]);
    }

    // Update challenge
    $updateChallenge = $conn->prepare("
        UPDATE challenges 
        SET status = :new_status,
            result = :winner,
            team_a_score = :team_a_score,
            team_b_score = :team_b_score,
            completed_at = CURRENT_TIMESTAMP
        WHERE challenge_id = :challenge_id
    ");
    
    $updateChallenge->execute([
        ':new_status' => $new_status,
        ':winner' => $winner,
        ':team_a_score' => $team_a_score,
        ':team_b_score' => $team_b_score,
        ':challenge_id' => $challenge_id
    ]);

    $conn->commit();
    echo json_encode([
        'success' => true, 
        'message' => 'Challenge settled successfully!'
    ]);

} catch (Exception $e) {
    $conn->rollBack();
    error_log("Error in complete_challenge.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function processUserResults($conn, $user_id, $outcome, $winnings, $bet_amount, $bet_id, $challenge_id) {
    // Calculate points based on outcome
    $points_earned = 0;
    $wdl_field = '';
    
    switch ($outcome) {
        case 'win':
            $points_earned = 3;
            $wdl_field = 'wins';
            break;
        case 'draw':
            $points_earned = 1;
            $wdl_field = 'draws';
            break;
        case 'loss':
            $points_earned = 0;
            $wdl_field = 'losses';
            break;
        default:
            throw new Exception("Invalid outcome: $outcome");
    }

    // Update users table WDL stats and balance
    $updateUserStats = $conn->prepare("
        UPDATE users
        SET `$wdl_field` = `$wdl_field` + 1,
            balance = balance + :winnings,
            total_bets = total_bets + 1,
            points = points + :points_earned,
            total_points = total_points + :points_earned,
            last_bet_date = CURRENT_TIMESTAMP
        WHERE user_id = :user_id
    ");
    $updateUserStats->execute([
        ':winnings' => $winnings,
        ':points_earned' => $points_earned,
        ':user_id' => $user_id
    ]);

    // Get user's active leagues
    $getLeagues = $conn->prepare("
        SELECT league_id 
        FROM league_memberships
        WHERE user_id = :user_id 
        AND status = 'active'
    ");
    $getLeagues->execute([':user_id' => $user_id]);
    $leagues = $getLeagues->fetchAll(PDO::FETCH_COLUMN);

    // Update league stats if user is in any leagues
    if (!empty($leagues)) {
        foreach ($leagues as $league_id) {
            // Update league memberships
            $updateLeagueMembership = $conn->prepare("
                UPDATE league_memberships
                SET `$wdl_field` = `$wdl_field` + 1,
                    current_points = current_points + :points_earned,
                    total_bets = total_bets + 1,
                    last_bet_date = CURRENT_TIMESTAMP
                WHERE user_id = :user_id
                AND league_id = :league_id
                AND status = 'active'
            ");
            $updateLeagueMembership->execute([
                ':points_earned' => $points_earned,
                ':user_id' => $user_id,
                ':league_id' => $league_id
            ]);

            // Update user league stats
            $updateLeagueStats = $conn->prepare("
                INSERT INTO user_league_stats 
                    (user_id, league_id, wins, draws, losses, points)
                VALUES 
                    (:user_id, :league_id,
                     CASE WHEN :wdl_field = 'wins' THEN 1 ELSE 0 END,
                     CASE WHEN :wdl_field = 'draws' THEN 1 ELSE 0 END,
                     CASE WHEN :wdl_field = 'losses' THEN 1 ELSE 0 END,
                     :points_earned)
                ON DUPLICATE KEY UPDATE
                    wins = wins + CASE WHEN :wdl_field2 = 'wins' THEN 1 ELSE 0 END,
                    draws = draws + CASE WHEN :wdl_field3 = 'draws' THEN 1 ELSE 0 END,
                    losses = losses + CASE WHEN :wdl_field4 = 'losses' THEN 1 ELSE 0 END,
                    points = points + :points_earned2
            ");
            
            $updateLeagueStats->execute([
                ':user_id' => $user_id,
                ':league_id' => $league_id,
                ':wdl_field' => $wdl_field,
                ':wdl_field2' => $wdl_field,
                ':wdl_field3' => $wdl_field,
                ':wdl_field4' => $wdl_field,
                ':points_earned' => $points_earned,
                ':points_earned2' => $points_earned
            ]);
        }
    }

    // Record transaction
    $insertTransaction = $conn->prepare("
        INSERT INTO transactions (
            user_id, 
            amount, 
            type, 
            status, 
            related_bet_id,
            related_challenge_id,
            description
        ) VALUES (
            :user_id,
            :amount,
            'bet_settlement',
            'completed',
            :bet_id,
            :challenge_id,
            :description
        )
    ");
    $insertTransaction->execute([
        ':user_id' => $user_id,
        ':amount' => $winnings,
        ':bet_id' => $bet_id,
        ':challenge_id' => $challenge_id,
        ':description' => "Bet outcome: " . ucfirst($outcome) . " - Amount: " . ($winnings >= 0 ? '+' : '') . $winnings
    ]);
}
