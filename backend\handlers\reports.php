<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Get query parameters
    $reportType = isset($_GET['type']) ? htmlspecialchars(strip_tags($_GET['type'])) : 'overview';
    $startDate = isset($_GET['startDate']) ? htmlspecialchars(strip_tags($_GET['startDate'])) : date('Y-m-01'); // First day of current month
    $endDate = isset($_GET['endDate']) ? htmlspecialchars(strip_tags($_GET['endDate'])) : date('Y-m-d'); // Today
    $format = isset($_GET['format']) ? htmlspecialchars(strip_tags($_GET['format'])) : 'json';

    try {
        $reportData = [];

        switch ($reportType) {
            case 'overview':
                $reportData = generateOverviewReport($conn, $startDate, $endDate);
                break;
            case 'financial':
                $reportData = generateFinancialReport($conn, $startDate, $endDate);
                break;
            case 'user_activity':
                $reportData = generateUserActivityReport($conn, $startDate, $endDate);
                break;
            case 'betting':
                $reportData = generateBettingReport($conn, $startDate, $endDate);
                break;
            case 'system':
                $reportData = generateSystemReport($conn, $startDate, $endDate);
                break;
            case 'challenges':
                $reportData = generateChallengesReport($conn, $startDate, $endDate);
                break;
            default:
                throw new Exception("Invalid report type");
        }

        if ($format === 'csv') {
            exportToCSV($reportData, $reportType);
        } else {
            http_response_code(200);
            echo json_encode([
                "success" => true,
                "data" => $reportData,
                "report_type" => $reportType,
                "date_range" => [
                    "start" => $startDate,
                    "end" => $endDate
                ]
            ]);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Failed to generate report: " . $e->getMessage()
        ]);
    }

} else {
    http_response_code(405);
    echo json_encode([
        "success" => false,
        "message" => "Method not allowed"
    ]);
}

function generateOverviewReport($conn, $startDate, $endDate) {
    $data = [];

    // Total users
    $query = "SELECT COUNT(*) as total_users FROM users WHERE role = 'user'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $data['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];

    // New users in date range
    $query = "SELECT COUNT(*) as new_users FROM users WHERE role = 'user' AND created_at BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['new_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['new_users'];

    // Active users (users who placed bets in date range)
    $query = "SELECT COUNT(DISTINCT u.user_id) as active_users
              FROM users u
              INNER JOIN bets b ON (u.user_id = b.user1_id OR u.user_id = b.user2_id)
              WHERE u.role = 'user' AND b.created_at BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['active_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['active_users'];

    // Total bets
    $query = "SELECT COUNT(*) as total_bets FROM bets WHERE created_at BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['total_bets'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_bets'];

    // Total bet amount
    $query = "SELECT SUM(amount_user1 + COALESCE(amount_user2, 0)) as total_bet_amount FROM bets WHERE created_at BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['total_bet_amount'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_bet_amount'] ?: 0;

    // Total challenges
    $query = "SELECT COUNT(*) as total_challenges FROM challenges WHERE challenge_date BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['total_challenges'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_challenges'];

    // Active challenges
    $query = "SELECT COUNT(*) as active_challenges FROM challenges WHERE status = 'Open'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $data['active_challenges'] = $stmt->fetch(PDO::FETCH_ASSOC)['active_challenges'];

    // Completed challenges
    $query = "SELECT COUNT(*) as completed_challenges FROM challenges WHERE status = 'Settled' AND challenge_date BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['completed_challenges'] = $stmt->fetch(PDO::FETCH_ASSOC)['completed_challenges'];

    // Total leagues
    $query = "SELECT COUNT(*) as total_leagues FROM leagues";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $data['total_leagues'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_leagues'];

    // Active league memberships
    $query = "SELECT COUNT(*) as active_memberships FROM league_memberships WHERE status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $data['active_memberships'] = $stmt->fetch(PDO::FETCH_ASSOC)['active_memberships'];

    // Total transactions
    $query = "SELECT COUNT(*) as total_transactions FROM transactions WHERE created_at BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['total_transactions'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_transactions'];

    // Total transaction volume
    $query = "SELECT SUM(amount) as total_transaction_volume FROM transactions WHERE created_at BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['total_transaction_volume'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_transaction_volume'] ?: 0;

    // Credit requests
    $query = "SELECT COUNT(*) as total_credit_requests FROM credit_requests WHERE created_at BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['total_credit_requests'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_credit_requests'];

    // Pending credit requests
    $query = "SELECT COUNT(*) as pending_credit_requests FROM credit_requests WHERE status = 'pending'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $data['pending_credit_requests'] = $stmt->fetch(PDO::FETCH_ASSOC)['pending_credit_requests'];

    return $data;
}

function generateFinancialReport($conn, $startDate, $endDate) {
    $data = [];

    // Transaction summary by type
    $query = "SELECT 
                type,
                COUNT(*) as count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount
              FROM transactions 
              WHERE created_at BETWEEN :start AND :end 
              GROUP BY type
              ORDER BY total_amount DESC";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['transaction_summary'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Daily transaction volume
    $query = "SELECT 
                DATE(created_at) as date,
                COUNT(*) as transaction_count,
                SUM(amount) as daily_volume
              FROM transactions 
              WHERE created_at BETWEEN :start AND :end 
              GROUP BY DATE(created_at)
              ORDER BY date";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['daily_volume'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Top users by transaction volume
    $query = "SELECT 
                u.username,
                u.full_name,
                COUNT(t.transaction_id) as transaction_count,
                SUM(t.amount) as total_volume
              FROM users u
              INNER JOIN transactions t ON u.user_id = t.user_id
              WHERE t.created_at BETWEEN :start AND :end
              GROUP BY u.user_id, u.username, u.full_name
              ORDER BY total_volume DESC
              LIMIT 10";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['top_users'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $data;
}

function generateUserActivityReport($conn, $startDate, $endDate) {
    $data = [];

    // Daily user registrations
    $query = "SELECT 
                DATE(created_at) as date,
                COUNT(*) as new_users
              FROM users 
              WHERE role = 'user' AND created_at BETWEEN :start AND :end 
              GROUP BY DATE(created_at)
              ORDER BY date";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['daily_registrations'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // User activity by type
    $query = "SELECT 
                activity_type,
                COUNT(*) as count
              FROM user_activity_log 
              WHERE created_at BETWEEN :start AND :end 
              GROUP BY activity_type
              ORDER BY count DESC";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['activity_types'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Most active users
    $query = "SELECT 
                u.username,
                u.full_name,
                COUNT(ual.log_id) as activity_count,
                MAX(ual.created_at) as last_activity
              FROM users u
              INNER JOIN user_activity_log ual ON u.user_id = ual.user_id
              WHERE ual.created_at BETWEEN :start AND :end
              GROUP BY u.user_id, u.username, u.full_name
              ORDER BY activity_count DESC
              LIMIT 10";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['most_active_users'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $data;
}

function generateBettingReport($conn, $startDate, $endDate) {
    $data = [];

    // Betting statistics
    $query = "SELECT 
                COUNT(*) as total_bets,
                SUM(CASE WHEN bet_status = 'completed' THEN 1 ELSE 0 END) as completed_bets,
                SUM(CASE WHEN bet_status = 'open' THEN 1 ELSE 0 END) as open_bets,
                AVG(amount) as avg_bet_amount,
                SUM(amount) as total_bet_volume
              FROM bets 
              WHERE created_at BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['betting_stats'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // Daily betting activity
    $query = "SELECT 
                DATE(created_at) as date,
                COUNT(*) as bet_count,
                SUM(amount) as daily_volume
              FROM bets 
              WHERE created_at BETWEEN :start AND :end 
              GROUP BY DATE(created_at)
              ORDER BY date";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['daily_betting'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Top bettors
    $query = "SELECT 
                u.username,
                u.full_name,
                COUNT(b.bet_id) as bet_count,
                SUM(b.amount) as total_bet_amount
              FROM users u
              INNER JOIN bets b ON (u.user_id = b.user1_id OR u.user_id = b.user2_id)
              WHERE b.created_at BETWEEN :start AND :end
              GROUP BY u.user_id, u.username, u.full_name
              ORDER BY total_bet_amount DESC
              LIMIT 10";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['top_bettors'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $data;
}

function generateSystemReport($conn, $startDate, $endDate) {
    $data = [];

    // System overview
    $data['system_overview'] = [
        'report_period' => ['start' => $startDate, 'end' => $endDate],
        'generated_at' => date('Y-m-d H:i:s')
    ];

    // Database statistics
    $query = "SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'user') as total_users,
        (SELECT COUNT(*) FROM users WHERE role = 'admin') as total_admins,
        (SELECT COUNT(*) FROM bets) as total_bets,
        (SELECT COUNT(*) FROM challenges) as total_challenges,
        (SELECT COUNT(*) FROM leagues) as total_leagues,
        (SELECT COUNT(*) FROM transactions) as total_transactions,
        (SELECT COUNT(*) FROM credit_requests) as total_credit_requests";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $data['database_stats'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // System activity in period
    $query = "SELECT
        (SELECT COUNT(*) FROM users WHERE created_at BETWEEN :start AND :end) as new_users,
        (SELECT COUNT(*) FROM bets WHERE created_at BETWEEN :start AND :end) as new_bets,
        (SELECT COUNT(*) FROM challenges WHERE challenge_date BETWEEN :start AND :end) as new_challenges,
        (SELECT COUNT(*) FROM transactions WHERE created_at BETWEEN :start AND :end) as new_transactions,
        (SELECT COUNT(*) FROM credit_requests WHERE created_at BETWEEN :start AND :end) as new_credit_requests";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['period_activity'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // Email/notification statistics (if email log table exists)
    try {
        $query = "SELECT COUNT(*) as total_emails FROM email_logs WHERE created_at BETWEEN :start AND :end";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':start', $startDate);
        $stmt->bindParam(':end', $endDate);
        $stmt->execute();
        $data['email_stats'] = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $data['email_stats'] = ['total_emails' => 'N/A - Email logging not configured'];
    }

    // System health indicators
    $query = "SELECT
        (SELECT COUNT(*) FROM bets WHERE bet_status = 'open') as open_bets,
        (SELECT COUNT(*) FROM challenges WHERE status = 'Open') as open_challenges,
        (SELECT COUNT(*) FROM credit_requests WHERE status = 'pending') as pending_credit_requests,
        (SELECT COUNT(*) FROM league_memberships WHERE status = 'pending') as pending_memberships";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $data['system_health'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // Revenue/financial summary
    $query = "SELECT
        SUM(CASE WHEN type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
        SUM(CASE WHEN type = 'withdrawal' THEN amount ELSE 0 END) as total_withdrawals,
        SUM(CASE WHEN type = 'bet' THEN amount ELSE 0 END) as total_bet_volume,
        COUNT(CASE WHEN type = 'deposit' THEN 1 END) as deposit_count,
        COUNT(CASE WHEN type = 'withdrawal' THEN 1 END) as withdrawal_count
        FROM transactions WHERE created_at BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['financial_summary'] = $stmt->fetch(PDO::FETCH_ASSOC);

    return $data;
}

function generateChallengesReport($conn, $startDate, $endDate) {
    $data = [];

    // Challenge statistics
    $query = "SELECT
        COUNT(*) as total_challenges,
        SUM(CASE WHEN status = 'Open' THEN 1 ELSE 0 END) as open_challenges,
        SUM(CASE WHEN status = 'Closed' THEN 1 ELSE 0 END) as closed_challenges,
        SUM(CASE WHEN status = 'Settled' THEN 1 ELSE 0 END) as settled_challenges,
        AVG(CASE WHEN odds_team_a IS NOT NULL THEN odds_team_a ELSE 0 END) as avg_team_a_odds,
        AVG(CASE WHEN odds_team_b IS NOT NULL THEN odds_team_b ELSE 0 END) as avg_team_b_odds,
        AVG(CASE WHEN odds_draw IS NOT NULL THEN odds_draw ELSE 0 END) as avg_draw_odds
        FROM challenges WHERE challenge_date BETWEEN :start AND :end";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['challenge_stats'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // Challenge results distribution
    $query = "SELECT
        result,
        COUNT(*) as count,
        ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM challenges WHERE result IS NOT NULL AND challenge_date BETWEEN :start AND :end)), 2) as percentage
        FROM challenges
        WHERE result IS NOT NULL AND challenge_date BETWEEN :start AND :end
        GROUP BY result";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['result_distribution'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Most popular teams
    $query = "SELECT
        team_name,
        appearances,
        wins,
        ROUND((wins * 100.0 / appearances), 2) as win_percentage
        FROM (
            SELECT team_a as team_name, COUNT(*) as appearances,
                   SUM(CASE WHEN result = 'team_a_win' THEN 1 ELSE 0 END) as wins
            FROM challenges WHERE challenge_date BETWEEN :start AND :end
            GROUP BY team_a
            UNION ALL
            SELECT team_b as team_name, COUNT(*) as appearances,
                   SUM(CASE WHEN result = 'team_b_win' THEN 1 ELSE 0 END) as wins
            FROM challenges WHERE challenge_date BETWEEN :start AND :end
            GROUP BY team_b
        ) team_stats
        GROUP BY team_name
        HAVING appearances > 0
        ORDER BY appearances DESC, win_percentage DESC
        LIMIT 10";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['popular_teams'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Daily challenge activity
    $query = "SELECT
        DATE(challenge_date) as date,
        COUNT(*) as challenges_created,
        SUM(CASE WHEN status = 'Settled' THEN 1 ELSE 0 END) as challenges_settled
        FROM challenges
        WHERE challenge_date BETWEEN :start AND :end
        GROUP BY DATE(challenge_date)
        ORDER BY date";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':start', $startDate);
    $stmt->bindParam(':end', $endDate);
    $stmt->execute();
    $data['daily_activity'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $data;
}

function exportToCSV($data, $reportType) {
    $filename = $reportType . '_report_' . date('Y-m-d') . '.csv';
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // Write CSV headers and data based on report type
    switch ($reportType) {
        case 'financial':
            if (isset($data['transaction_summary'])) {
                fputcsv($output, ['Transaction Type', 'Count', 'Total Amount', 'Average Amount']);
                foreach ($data['transaction_summary'] as $row) {
                    fputcsv($output, [$row['type'], $row['count'], $row['total_amount'], $row['avg_amount']]);
                }
            }
            break;
        // Add more CSV export cases as needed
    }
    
    fclose($output);
    exit;
}
?>
