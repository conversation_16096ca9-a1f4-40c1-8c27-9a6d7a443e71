[{"C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js": "1", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js": "2", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js": "3", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js": "4", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js": "5", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js": "6", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js": "7", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js": "8", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js": "9", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js": "10", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js": "11", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js": "12", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js": "13", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js": "14", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js": "15", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js": "16", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js": "17", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js": "18", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js": "19", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js": "20", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NewWelcomePage.js": "21", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js": "22", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js": "23", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js": "24", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js": "25", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js": "26", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js": "27", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js": "28", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js": "29", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js": "30", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js": "31", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js": "32", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js": "33", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js": "34", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js": "35", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js": "36", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js": "37", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js": "38", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js": "39", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js": "40", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js": "41", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js": "42", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js": "43", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js": "44", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js": "45", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js": "46", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js": "47", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js": "48", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js": "49", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js": "50", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js": "51", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js": "52", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js": "53", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js": "54", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettingsFixed.js": "55", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js": "56", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WithdrawalHistory.js": "57", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserPaymentMethods.js": "58", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js": "59", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js": "60", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js": "61", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js": "62", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js": "63", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js": "64", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js": "65", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js": "66", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js": "67", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js": "68", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js": "69", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js": "70", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js": "71", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js": "72", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js": "73", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js": "74", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js": "75", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js": "76", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js": "77", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\faviconUtils.js": "78", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js": "79", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js": "80", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js": "81", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdrawal.js": "82", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\teamLogoUtils.js": "83", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js": "84", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengeCard.js": "85", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\welcomeService.js": "86", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\LiveMatchCard.js": "87", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js": "88", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js": "89", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\User2FASetup.js": "90", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js": "91", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js": "92", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js": "93", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js": "94", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js": "95", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js": "96", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js": "97", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js": "98", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js": "99", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js": "100", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js": "101", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js": "102", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\BetTable.js": "103", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\BetDetailsModal.js": "104", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Login.js": "105", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\index.js": "106", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\User2FAVerification.js": "107", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\UserOTPVerification.js": "108", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminSecurityManagement.js": "109", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\homepageService.js": "110", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ModernHomePage.js": "111", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\index.js": "112", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\Header.js": "113", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\HeroSection.js": "114", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\LeagueCard.js": "115", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\TopLeaguesSection.js": "116", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useHomepageData.js": "117", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\homepageUtils.js": "118", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\BetActivityItem.js": "119", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\ChallengeCard.js": "120", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\RecentBetsSection.js": "121", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\LiveChallengesSection.js": "122", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\BlogSection.js": "123", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\BlogCard.js": "124", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\Footer.js": "125", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestHomePage.js": "126", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\HeaderTest.js": "127", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\SimpleRecentBets.js": "128"}, {"size": 1593, "mtime": 1739215325917, "results": "129", "hashOfConfig": "130"}, {"size": 12964, "mtime": 1754121180630, "results": "131", "hashOfConfig": "130"}, {"size": 362, "mtime": 1725527312699, "results": "132", "hashOfConfig": "130"}, {"size": 2857, "mtime": 1754121048960, "results": "133", "hashOfConfig": "130"}, {"size": 1583, "mtime": 1738380484748, "results": "134", "hashOfConfig": "130"}, {"size": 1958, "mtime": 1738907546846, "results": "135", "hashOfConfig": "130"}, {"size": 9434, "mtime": 1752539743613, "results": "136", "hashOfConfig": "130"}, {"size": 1431, "mtime": 1747472215947, "results": "137", "hashOfConfig": "130"}, {"size": 19847, "mtime": 1752842621765, "results": "138", "hashOfConfig": "130"}, {"size": 13936, "mtime": 1749750686297, "results": "139", "hashOfConfig": "130"}, {"size": 2137, "mtime": 1754113461185, "results": "140", "hashOfConfig": "130"}, {"size": 11778, "mtime": 1754133289601, "results": "141", "hashOfConfig": "130"}, {"size": 18129, "mtime": 1753984024064, "results": "142", "hashOfConfig": "130"}, {"size": 18418, "mtime": 1754113733620, "results": "143", "hashOfConfig": "130"}, {"size": 11989, "mtime": 1752846823662, "results": "144", "hashOfConfig": "130"}, {"size": 43308, "mtime": 1749118478833, "results": "145", "hashOfConfig": "130"}, {"size": 40463, "mtime": 1754121227351, "results": "146", "hashOfConfig": "130"}, {"size": 45923, "mtime": 1754114130814, "results": "147", "hashOfConfig": "130"}, {"size": 25276, "mtime": 1754123809015, "results": "148", "hashOfConfig": "130"}, {"size": 66041, "mtime": 1753984014495, "results": "149", "hashOfConfig": "130"}, {"size": 29538, "mtime": 1751554737757, "results": "150", "hashOfConfig": "130"}, {"size": 15481, "mtime": 1751290367000, "results": "151", "hashOfConfig": "130"}, {"size": 3867, "mtime": 1749105548034, "results": "152", "hashOfConfig": "130"}, {"size": 12332, "mtime": 1749106493490, "results": "153", "hashOfConfig": "130"}, {"size": 17158, "mtime": 1749113919875, "results": "154", "hashOfConfig": "130"}, {"size": 21913, "mtime": 1751279289062, "results": "155", "hashOfConfig": "130"}, {"size": 26689, "mtime": 1754122829999, "results": "156", "hashOfConfig": "130"}, {"size": 35666, "mtime": 1749736717528, "results": "157", "hashOfConfig": "130"}, {"size": 6470, "mtime": 1751306633077, "results": "158", "hashOfConfig": "130"}, {"size": 398, "mtime": 1725625029363, "results": "159", "hashOfConfig": "130"}, {"size": 19566, "mtime": 1754114670736, "results": "160", "hashOfConfig": "130"}, {"size": 17866, "mtime": 1751287911119, "results": "161", "hashOfConfig": "130"}, {"size": 35088, "mtime": 1754113752350, "results": "162", "hashOfConfig": "130"}, {"size": 484, "mtime": 1747774257496, "results": "163", "hashOfConfig": "130"}, {"size": 8993, "mtime": 1754114609755, "results": "164", "hashOfConfig": "130"}, {"size": 14033, "mtime": 1754122829807, "results": "165", "hashOfConfig": "130"}, {"size": 10574, "mtime": 1754113328316, "results": "166", "hashOfConfig": "130"}, {"size": 30541, "mtime": 1754114828572, "results": "167", "hashOfConfig": "130"}, {"size": 37464, "mtime": 1754121227352, "results": "168", "hashOfConfig": "130"}, {"size": 22327, "mtime": 1751555598368, "results": "169", "hashOfConfig": "130"}, {"size": 31885, "mtime": 1754123788039, "results": "170", "hashOfConfig": "130"}, {"size": 13011, "mtime": 1754068376010, "results": "171", "hashOfConfig": "130"}, {"size": 20681, "mtime": 1752860280130, "results": "172", "hashOfConfig": "130"}, {"size": 15498, "mtime": 1754114705214, "results": "173", "hashOfConfig": "130"}, {"size": 12101, "mtime": 1751557520626, "results": "174", "hashOfConfig": "130"}, {"size": 12871, "mtime": 1751557753202, "results": "175", "hashOfConfig": "130"}, {"size": 4784, "mtime": 1754113295715, "results": "176", "hashOfConfig": "130"}, {"size": 8917, "mtime": 1738228976181, "results": "177", "hashOfConfig": "130"}, {"size": 27667, "mtime": 1754113317697, "results": "178", "hashOfConfig": "130"}, {"size": 28050, "mtime": 1754121180672, "results": "179", "hashOfConfig": "130"}, {"size": 1242, "mtime": 1732832820214, "results": "180", "hashOfConfig": "130"}, {"size": 31215, "mtime": 1754122829806, "results": "181", "hashOfConfig": "130"}, {"size": 205, "mtime": 1732832805260, "results": "182", "hashOfConfig": "130"}, {"size": 82, "mtime": 1751559119212, "results": "183", "hashOfConfig": "130"}, {"size": 14862, "mtime": 1752614436945, "results": "184", "hashOfConfig": "130"}, {"size": 8290, "mtime": 1751193168091, "results": "185", "hashOfConfig": "130"}, {"size": 9989, "mtime": 1751559185145, "results": "186", "hashOfConfig": "130"}, {"size": 13724, "mtime": 1751558806092, "results": "187", "hashOfConfig": "130"}, {"size": 11530, "mtime": 1754121180650, "results": "188", "hashOfConfig": "130"}, {"size": 24442, "mtime": 1751297418539, "results": "189", "hashOfConfig": "130"}, {"size": 23731, "mtime": 1754064115114, "results": "190", "hashOfConfig": "130"}, {"size": 11957, "mtime": 1752650999140, "results": "191", "hashOfConfig": "130"}, {"size": 3303, "mtime": 1752650999134, "results": "192", "hashOfConfig": "130"}, {"size": 4274, "mtime": 1752650999123, "results": "193", "hashOfConfig": "130"}, {"size": 5587, "mtime": 1752650999133, "results": "194", "hashOfConfig": "130"}, {"size": 7198, "mtime": 1752685011234, "results": "195", "hashOfConfig": "130"}, {"size": 10884, "mtime": 1752650999136, "results": "196", "hashOfConfig": "130"}, {"size": 5672, "mtime": 1754068508316, "results": "197", "hashOfConfig": "130"}, {"size": 14221, "mtime": 1752650999140, "results": "198", "hashOfConfig": "130"}, {"size": 16886, "mtime": 1751555732120, "results": "199", "hashOfConfig": "130"}, {"size": 16539, "mtime": 1754113771126, "results": "200", "hashOfConfig": "130"}, {"size": 1352, "mtime": 1738907631772, "results": "201", "hashOfConfig": "130"}, {"size": 4062, "mtime": 1754314478889, "results": "202", "hashOfConfig": "130"}, {"size": 3211, "mtime": 1747478622718, "results": "203", "hashOfConfig": "130"}, {"size": 7783, "mtime": 1753968174561, "results": "204", "hashOfConfig": "130"}, {"size": 591, "mtime": 1754121180478, "results": "205", "hashOfConfig": "130"}, {"size": 4889, "mtime": 1739089917990, "results": "206", "hashOfConfig": "130"}, {"size": 4290, "mtime": 1753963111871, "results": "207", "hashOfConfig": "130"}, {"size": 856, "mtime": 1738005002533, "results": "208", "hashOfConfig": "130"}, {"size": 4026, "mtime": 1749114060143, "results": "209", "hashOfConfig": "130"}, {"size": 1833, "mtime": 1749750397933, "results": "210", "hashOfConfig": "130"}, {"size": 11589, "mtime": 1751558672709, "results": "211", "hashOfConfig": "130"}, {"size": 3558, "mtime": 1752614436910, "results": "212", "hashOfConfig": "130"}, {"size": 597, "mtime": 1738005020143, "results": "213", "hashOfConfig": "130"}, {"size": 5565, "mtime": 1751543546856, "results": "214", "hashOfConfig": "130"}, {"size": 12020, "mtime": 1752696720649, "results": "215", "hashOfConfig": "130"}, {"size": 5268, "mtime": 1751543490786, "results": "216", "hashOfConfig": "130"}, {"size": 3300, "mtime": 1753995676730, "results": "217", "hashOfConfig": "130"}, {"size": 5823, "mtime": 1751190205858, "results": "218", "hashOfConfig": "130"}, {"size": 12316, "mtime": 1752614437074, "results": "219", "hashOfConfig": "130"}, {"size": 317, "mtime": 1749231241721, "results": "220", "hashOfConfig": "130"}, {"size": 977, "mtime": 1749746063985, "results": "221", "hashOfConfig": "130"}, {"size": 5619, "mtime": 1751190125112, "results": "222", "hashOfConfig": "130"}, {"size": 4644, "mtime": 1751190097589, "results": "223", "hashOfConfig": "130"}, {"size": 5569, "mtime": 1751190153517, "results": "224", "hashOfConfig": "130"}, {"size": 7030, "mtime": 1754113175266, "results": "225", "hashOfConfig": "130"}, {"size": 15311, "mtime": 1751555473327, "results": "226", "hashOfConfig": "130"}, {"size": 19477, "mtime": 1749236876615, "results": "227", "hashOfConfig": "130"}, {"size": 12103, "mtime": 1749736717523, "results": "228", "hashOfConfig": "130"}, {"size": 22414, "mtime": 1749237247953, "results": "229", "hashOfConfig": "130"}, {"size": 8985, "mtime": 1751206912079, "results": "230", "hashOfConfig": "130"}, {"size": 7542, "mtime": 1751190440918, "results": "231", "hashOfConfig": "130"}, {"size": 5973, "mtime": 1752659201446, "results": "232", "hashOfConfig": "130"}, {"size": 5746, "mtime": 1752657647122, "results": "233", "hashOfConfig": "130"}, {"size": 7648, "mtime": 1752849103913, "results": "234", "hashOfConfig": "130"}, {"size": 227, "mtime": 1752860281340, "results": "235", "hashOfConfig": "130"}, {"size": 11465, "mtime": 1752861749207, "results": "236", "hashOfConfig": "130"}, {"size": 11083, "mtime": 1752860280133, "results": "237", "hashOfConfig": "130"}, {"size": 25112, "mtime": 1754122827566, "results": "238", "hashOfConfig": "130"}, {"size": 3960, "mtime": 1754114175815, "results": "239", "hashOfConfig": "130"}, {"size": 628, "mtime": 1754058661243, "results": "240", "hashOfConfig": "130"}, {"size": 515, "mtime": 1754056901120, "results": "241", "hashOfConfig": "130"}, {"size": 1058, "mtime": 1754056245147, "results": "242", "hashOfConfig": "130"}, {"size": 6532, "mtime": 1753996927416, "results": "243", "hashOfConfig": "130"}, {"size": 4577, "mtime": 1754034714249, "results": "244", "hashOfConfig": "130"}, {"size": 10183, "mtime": 1754048521573, "results": "245", "hashOfConfig": "130"}, {"size": 5489, "mtime": 1753995608657, "results": "246", "hashOfConfig": "130"}, {"size": 6581, "mtime": 1753995653822, "results": "247", "hashOfConfig": "130"}, {"size": 4906, "mtime": 1754057148754, "results": "248", "hashOfConfig": "130"}, {"size": 7401, "mtime": 1754034882037, "results": "249", "hashOfConfig": "130"}, {"size": 10997, "mtime": 1754056957260, "results": "250", "hashOfConfig": "130"}, {"size": 10762, "mtime": 1754046522179, "results": "251", "hashOfConfig": "130"}, {"size": 7928, "mtime": 1754035817042, "results": "252", "hashOfConfig": "130"}, {"size": 4852, "mtime": 1754035839784, "results": "253", "hashOfConfig": "130"}, {"size": 9010, "mtime": 1754036003521, "results": "254", "hashOfConfig": "130"}, {"size": 2200, "mtime": 1754039475009, "results": "255", "hashOfConfig": "130"}, {"size": 3460, "mtime": 1754041622648, "results": "256", "hashOfConfig": "130"}, {"size": 7032, "mtime": 1754058841803, "results": "257", "hashOfConfig": "130"}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10x7tp5", {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js", ["642", "643"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js", ["644"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js", ["645"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js", ["646", "647", "648"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js", ["649", "650"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js", ["651", "652"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js", ["653", "654", "655", "656", "657", "658", "659"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js", ["660", "661"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js", ["662", "663", "664", "665", "666"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js", ["667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js", ["678"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js", ["679", "680", "681"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NewWelcomePage.js", ["682", "683", "684", "685", "686", "687", "688", "689"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js", ["690", "691"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js", ["692", "693"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js", ["694", "695"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js", ["696"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js", ["697"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js", ["698", "699", "700"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js", ["701"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js", ["702"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js", ["703", "704", "705", "706"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js", ["707", "708", "709", "710"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js", ["711"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js", ["712"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js", ["713", "714", "715"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js", ["716"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js", ["717"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js", ["718", "719", "720"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js", ["721"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js", ["722", "723", "724", "725", "726"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettingsFixed.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js", ["727", "728", "729"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WithdrawalHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserPaymentMethods.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js", ["730"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js", ["731", "732", "733", "734", "735"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js", ["736", "737", "738", "739", "740"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js", ["741"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js", ["742"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js", ["743"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js", ["744", "745"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js", ["746", "747"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js", ["748", "749", "750", "751"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\faviconUtils.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdrawal.js", ["752", "753", "754", "755"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\teamLogoUtils.js", ["756"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengeCard.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\welcomeService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\LiveMatchCard.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js", ["757", "758"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\User2FASetup.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js", ["759"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js", ["760", "761"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js", ["762", "763", "764", "765"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js", ["766", "767", "768"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js", ["769"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\BetTable.js", ["770"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\BetDetailsModal.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Login.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\User2FAVerification.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\UserOTPVerification.js", ["771"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminSecurityManagement.js", ["772", "773"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\homepageService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ModernHomePage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\HeroSection.js", ["774"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\LeagueCard.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\TopLeaguesSection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useHomepageData.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\homepageUtils.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\BetActivityItem.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\ChallengeCard.js", ["775", "776"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\RecentBetsSection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\LiveChallengesSection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\BlogSection.js", ["777", "778"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\BlogCard.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\Footer.js", ["779", "780", "781", "782"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestHomePage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\HeaderTest.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Homepage\\SimpleRecentBets.js", [], [], {"ruleId": "783", "severity": 1, "message": "784", "line": 20, "column": 8, "nodeType": "785", "messageId": "786", "endLine": 20, "endColumn": 20}, {"ruleId": "783", "severity": 1, "message": "787", "line": 78, "column": 8, "nodeType": "785", "messageId": "786", "endLine": 78, "endColumn": 23}, {"ruleId": "788", "severity": 1, "message": "789", "line": 31, "column": 6, "nodeType": "790", "endLine": 31, "endColumn": 8, "suggestions": "791"}, {"ruleId": "788", "severity": 1, "message": "792", "line": 51, "column": 8, "nodeType": "790", "endLine": 51, "endColumn": 10, "suggestions": "793"}, {"ruleId": "788", "severity": 1, "message": "794", "line": 55, "column": 9, "nodeType": "795", "endLine": 55, "endColumn": 62}, {"ruleId": "788", "severity": 1, "message": "796", "line": 104, "column": 6, "nodeType": "790", "endLine": 104, "endColumn": 60, "suggestions": "797"}, {"ruleId": "788", "severity": 1, "message": "798", "line": 170, "column": 6, "nodeType": "790", "endLine": 170, "endColumn": 113, "suggestions": "799"}, {"ruleId": "783", "severity": 1, "message": "800", "line": 6, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 6, "endColumn": 11}, {"ruleId": "788", "severity": 1, "message": "801", "line": 39, "column": 8, "nodeType": "790", "endLine": 39, "endColumn": 16, "suggestions": "802"}, {"ruleId": "783", "severity": 1, "message": "803", "line": 20, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 20, "endColumn": 20}, {"ruleId": "783", "severity": 1, "message": "804", "line": 20, "column": 22, "nodeType": "785", "messageId": "786", "endLine": 20, "endColumn": 35}, {"ruleId": "783", "severity": 1, "message": "805", "line": 6, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 6, "endColumn": 21}, {"ruleId": "783", "severity": 1, "message": "806", "line": 6, "column": 23, "nodeType": "785", "messageId": "786", "endLine": 6, "endColumn": 38}, {"ruleId": "783", "severity": 1, "message": "807", "line": 6, "column": 40, "nodeType": "785", "messageId": "786", "endLine": 6, "endColumn": 50}, {"ruleId": "783", "severity": 1, "message": "808", "line": 6, "column": 52, "nodeType": "785", "messageId": "786", "endLine": 6, "endColumn": 61}, {"ruleId": "783", "severity": 1, "message": "809", "line": 9, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 9, "endColumn": 22}, {"ruleId": "783", "severity": 1, "message": "810", "line": 31, "column": 13, "nodeType": "785", "messageId": "786", "endLine": 31, "endColumn": 20}, {"ruleId": "788", "severity": 1, "message": "811", "line": 48, "column": 8, "nodeType": "790", "endLine": 48, "endColumn": 10, "suggestions": "812"}, {"ruleId": "783", "severity": 1, "message": "813", "line": 3, "column": 20, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 28}, {"ruleId": "788", "severity": 1, "message": "814", "line": 39, "column": 8, "nodeType": "790", "endLine": 39, "endColumn": 42, "suggestions": "815"}, {"ruleId": "783", "severity": 1, "message": "813", "line": 5, "column": 20, "nodeType": "785", "messageId": "786", "endLine": 5, "endColumn": 28}, {"ruleId": "783", "severity": 1, "message": "816", "line": 5, "column": 37, "nodeType": "785", "messageId": "786", "endLine": 5, "endColumn": 43}, {"ruleId": "783", "severity": 1, "message": "817", "line": 5, "column": 45, "nodeType": "785", "messageId": "786", "endLine": 5, "endColumn": 52}, {"ruleId": "783", "severity": 1, "message": "818", "line": 14, "column": 21, "nodeType": "785", "messageId": "786", "endLine": 14, "endColumn": 31}, {"ruleId": "788", "severity": 1, "message": "819", "line": 41, "column": 8, "nodeType": "790", "endLine": 41, "endColumn": 41, "suggestions": "820"}, {"ruleId": "783", "severity": 1, "message": "821", "line": 9, "column": 3, "nodeType": "785", "messageId": "786", "endLine": 9, "endColumn": 16}, {"ruleId": "783", "severity": 1, "message": "822", "line": 11, "column": 3, "nodeType": "785", "messageId": "786", "endLine": 11, "endColumn": 17}, {"ruleId": "783", "severity": 1, "message": "823", "line": 12, "column": 3, "nodeType": "785", "messageId": "786", "endLine": 12, "endColumn": 13}, {"ruleId": "783", "severity": 1, "message": "824", "line": 13, "column": 3, "nodeType": "785", "messageId": "786", "endLine": 13, "endColumn": 16}, {"ruleId": "783", "severity": 1, "message": "825", "line": 14, "column": 3, "nodeType": "785", "messageId": "786", "endLine": 14, "endColumn": 8}, {"ruleId": "783", "severity": 1, "message": "826", "line": 15, "column": 3, "nodeType": "785", "messageId": "786", "endLine": 15, "endColumn": 16}, {"ruleId": "783", "severity": 1, "message": "827", "line": 16, "column": 3, "nodeType": "785", "messageId": "786", "endLine": 16, "endColumn": 10}, {"ruleId": "783", "severity": 1, "message": "828", "line": 17, "column": 3, "nodeType": "785", "messageId": "786", "endLine": 17, "endColumn": 9}, {"ruleId": "783", "severity": 1, "message": "829", "line": 21, "column": 16, "nodeType": "785", "messageId": "786", "endLine": 21, "endColumn": 19}, {"ruleId": "783", "severity": 1, "message": "830", "line": 93, "column": 12, "nodeType": "785", "messageId": "786", "endLine": 93, "endColumn": 30}, {"ruleId": "788", "severity": 1, "message": "831", "line": 128, "column": 8, "nodeType": "790", "endLine": 128, "endColumn": 10, "suggestions": "832"}, {"ruleId": "833", "severity": 1, "message": "834", "line": 472, "column": 9, "nodeType": "835", "messageId": "836", "endLine": 472, "endColumn": 32}, {"ruleId": "783", "severity": 1, "message": "837", "line": 4, "column": 121, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 132}, {"ruleId": "783", "severity": 1, "message": "838", "line": 4, "column": 149, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 161}, {"ruleId": "783", "severity": 1, "message": "839", "line": 11, "column": 11, "nodeType": "785", "messageId": "786", "endLine": 11, "endColumn": 19}, {"ruleId": "783", "severity": 1, "message": "840", "line": 5, "column": 8, "nodeType": "785", "messageId": "786", "endLine": 5, "endColumn": 21}, {"ruleId": "783", "severity": 1, "message": "841", "line": 16, "column": 12, "nodeType": "785", "messageId": "786", "endLine": 16, "endColumn": 17}, {"ruleId": "788", "severity": 1, "message": "842", "line": 41, "column": 8, "nodeType": "790", "endLine": 41, "endColumn": 10, "suggestions": "843"}, {"ruleId": "783", "severity": 1, "message": "844", "line": 101, "column": 11, "nodeType": "785", "messageId": "786", "endLine": 101, "endColumn": 25}, {"ruleId": "845", "severity": 1, "message": "846", "line": 532, "column": 33, "nodeType": "847", "endLine": 532, "endColumn": 69}, {"ruleId": "845", "severity": 1, "message": "846", "line": 535, "column": 33, "nodeType": "847", "endLine": 535, "endColumn": 69}, {"ruleId": "845", "severity": 1, "message": "846", "line": 538, "column": 33, "nodeType": "847", "endLine": 538, "endColumn": 69}, {"ruleId": "845", "severity": 1, "message": "846", "line": 541, "column": 33, "nodeType": "847", "endLine": 541, "endColumn": 69}, {"ruleId": "788", "severity": 1, "message": "848", "line": 23, "column": 8, "nodeType": "790", "endLine": 23, "endColumn": 19, "suggestions": "849"}, {"ruleId": "788", "severity": 1, "message": "850", "line": 32, "column": 8, "nodeType": "790", "endLine": 32, "endColumn": 30, "suggestions": "851"}, {"ruleId": "783", "severity": 1, "message": "852", "line": 3, "column": 35, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 41}, {"ruleId": "783", "severity": 1, "message": "853", "line": 5, "column": 25, "nodeType": "785", "messageId": "786", "endLine": 5, "endColumn": 39}, {"ruleId": "783", "severity": 1, "message": "854", "line": 3, "column": 40, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 46}, {"ruleId": "788", "severity": 1, "message": "855", "line": 29, "column": 8, "nodeType": "790", "endLine": 29, "endColumn": 10, "suggestions": "856"}, {"ruleId": "783", "severity": 1, "message": "857", "line": 3, "column": 34, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 41}, {"ruleId": "788", "severity": 1, "message": "814", "line": 31, "column": 8, "nodeType": "790", "endLine": 31, "endColumn": 17, "suggestions": "858"}, {"ruleId": "783", "severity": 1, "message": "859", "line": 5, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 5, "endColumn": 15}, {"ruleId": "783", "severity": 1, "message": "821", "line": 6, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 6, "endColumn": 18}, {"ruleId": "788", "severity": 1, "message": "860", "line": 29, "column": 8, "nodeType": "790", "endLine": 29, "endColumn": 31, "suggestions": "861"}, {"ruleId": "783", "severity": 1, "message": "862", "line": 5, "column": 7, "nodeType": "785", "messageId": "786", "endLine": 5, "endColumn": 19}, {"ruleId": "788", "severity": 1, "message": "863", "line": 42, "column": 8, "nodeType": "790", "endLine": 42, "endColumn": 10, "suggestions": "864"}, {"ruleId": "783", "severity": 1, "message": "865", "line": 12, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 12, "endColumn": 20}, {"ruleId": "783", "severity": 1, "message": "866", "line": 17, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 17, "endColumn": 12}, {"ruleId": "783", "severity": 1, "message": "867", "line": 18, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 18, "endColumn": 17}, {"ruleId": "783", "severity": 1, "message": "857", "line": 19, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 19, "endColumn": 12}, {"ruleId": "783", "severity": 1, "message": "868", "line": 1, "column": 60, "nodeType": "785", "messageId": "786", "endLine": 1, "endColumn": 66}, {"ruleId": "783", "severity": 1, "message": "869", "line": 28, "column": 12, "nodeType": "785", "messageId": "786", "endLine": 28, "endColumn": 26}, {"ruleId": "783", "severity": 1, "message": "870", "line": 28, "column": 28, "nodeType": "785", "messageId": "786", "endLine": 28, "endColumn": 45}, {"ruleId": "788", "severity": 1, "message": "871", "line": 83, "column": 8, "nodeType": "790", "endLine": 83, "endColumn": 10, "suggestions": "872"}, {"ruleId": "783", "severity": 1, "message": "809", "line": 4, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 22}, {"ruleId": "788", "severity": 1, "message": "873", "line": 137, "column": 8, "nodeType": "790", "endLine": 137, "endColumn": 18, "suggestions": "874"}, {"ruleId": "783", "severity": 1, "message": "837", "line": 3, "column": 19, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 30}, {"ruleId": "783", "severity": 1, "message": "875", "line": 14, "column": 12, "nodeType": "785", "messageId": "786", "endLine": 14, "endColumn": 24}, {"ruleId": "783", "severity": 1, "message": "876", "line": 89, "column": 11, "nodeType": "785", "messageId": "786", "endLine": 89, "endColumn": 23}, {"ruleId": "783", "severity": 1, "message": "877", "line": 103, "column": 11, "nodeType": "785", "messageId": "786", "endLine": 103, "endColumn": 22}, {"ruleId": "788", "severity": 1, "message": "814", "line": 44, "column": 8, "nodeType": "790", "endLine": 44, "endColumn": 21, "suggestions": "878"}, {"ruleId": "783", "severity": 1, "message": "879", "line": 6, "column": 8, "nodeType": "785", "messageId": "786", "endLine": 6, "endColumn": 23}, {"ruleId": "783", "severity": 1, "message": "880", "line": 118, "column": 9, "nodeType": "785", "messageId": "786", "endLine": 118, "endColumn": 25}, {"ruleId": "783", "severity": 1, "message": "881", "line": 152, "column": 9, "nodeType": "785", "messageId": "786", "endLine": 152, "endColumn": 22}, {"ruleId": "783", "severity": 1, "message": "882", "line": 4, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 17}, {"ruleId": "783", "severity": 1, "message": "883", "line": 13, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 13, "endColumn": 17}, {"ruleId": "783", "severity": 1, "message": "841", "line": 14, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 14, "endColumn": 15}, {"ruleId": "783", "severity": 1, "message": "881", "line": 115, "column": 9, "nodeType": "785", "messageId": "786", "endLine": 115, "endColumn": 22}, {"ruleId": "783", "severity": 1, "message": "884", "line": 132, "column": 9, "nodeType": "785", "messageId": "786", "endLine": 132, "endColumn": 19}, {"ruleId": "783", "severity": 1, "message": "885", "line": 145, "column": 9, "nodeType": "785", "messageId": "786", "endLine": 145, "endColumn": 22}, {"ruleId": "783", "severity": 1, "message": "886", "line": 10, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 10, "endColumn": 23}, {"ruleId": "783", "severity": 1, "message": "887", "line": 10, "column": 25, "nodeType": "785", "messageId": "786", "endLine": 10, "endColumn": 41}, {"ruleId": "783", "severity": 1, "message": "888", "line": 18, "column": 25, "nodeType": "785", "messageId": "786", "endLine": 18, "endColumn": 46}, {"ruleId": "788", "severity": 1, "message": "889", "line": 19, "column": 8, "nodeType": "790", "endLine": 19, "endColumn": 10, "suggestions": "890"}, {"ruleId": "783", "severity": 1, "message": "891", "line": 4, "column": 45, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 64}, {"ruleId": "783", "severity": 1, "message": "892", "line": 4, "column": 66, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 79}, {"ruleId": "783", "severity": 1, "message": "893", "line": 4, "column": 111, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 123}, {"ruleId": "788", "severity": 1, "message": "894", "line": 29, "column": 8, "nodeType": "790", "endLine": 29, "endColumn": 10, "suggestions": "895"}, {"ruleId": "783", "severity": 1, "message": "896", "line": 256, "column": 11, "nodeType": "785", "messageId": "786", "endLine": 256, "endColumn": 26}, {"ruleId": "783", "severity": 1, "message": "809", "line": 4, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 22}, {"ruleId": "783", "severity": 1, "message": "897", "line": 7, "column": 14, "nodeType": "785", "messageId": "786", "endLine": 7, "endColumn": 20}, {"ruleId": "783", "severity": 1, "message": "867", "line": 7, "column": 41, "nodeType": "785", "messageId": "786", "endLine": 7, "endColumn": 53}, {"ruleId": "783", "severity": 1, "message": "898", "line": 8, "column": 46, "nodeType": "785", "messageId": "786", "endLine": 8, "endColumn": 52}, {"ruleId": "783", "severity": 1, "message": "899", "line": 315, "column": 11, "nodeType": "785", "messageId": "786", "endLine": 315, "endColumn": 27}, {"ruleId": "788", "severity": 1, "message": "900", "line": 24, "column": 8, "nodeType": "790", "endLine": 24, "endColumn": 33, "suggestions": "901"}, {"ruleId": "783", "severity": 1, "message": "897", "line": 5, "column": 57, "nodeType": "785", "messageId": "786", "endLine": 5, "endColumn": 63}, {"ruleId": "788", "severity": 1, "message": "902", "line": 24, "column": 8, "nodeType": "790", "endLine": 24, "endColumn": 33, "suggestions": "903"}, {"ruleId": "783", "severity": 1, "message": "904", "line": 121, "column": 19, "nodeType": "785", "messageId": "786", "endLine": 121, "endColumn": 28}, {"ruleId": "783", "severity": 1, "message": "905", "line": 136, "column": 19, "nodeType": "785", "messageId": "786", "endLine": 136, "endColumn": 22}, {"ruleId": "783", "severity": 1, "message": "906", "line": 3, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 14}, {"ruleId": "783", "severity": 1, "message": "907", "line": 93, "column": 11, "nodeType": "785", "messageId": "786", "endLine": 93, "endColumn": 23}, {"ruleId": "783", "severity": 1, "message": "908", "line": 4, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 14}, {"ruleId": "783", "severity": 1, "message": "909", "line": 6, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 6, "endColumn": 10}, {"ruleId": "783", "severity": 1, "message": "823", "line": 7, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 7, "endColumn": 15}, {"ruleId": "783", "severity": 1, "message": "910", "line": 8, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 8, "endColumn": 16}, {"ruleId": "788", "severity": 1, "message": "911", "line": 35, "column": 6, "nodeType": "790", "endLine": 35, "endColumn": 88, "suggestions": "912"}, {"ruleId": "913", "severity": 1, "message": "914", "line": 171, "column": 47, "nodeType": "915", "messageId": "916", "endLine": 171, "endColumn": 49}, {"ruleId": "783", "severity": 1, "message": "917", "line": 174, "column": 9, "nodeType": "785", "messageId": "786", "endLine": 174, "endColumn": 33}, {"ruleId": "913", "severity": 1, "message": "914", "line": 175, "column": 59, "nodeType": "915", "messageId": "916", "endLine": 175, "endColumn": 61}, {"ruleId": "918", "severity": 1, "message": "919", "line": 114, "column": 1, "nodeType": "920", "endLine": 120, "endColumn": 3}, {"ruleId": "921", "severity": 1, "message": "922", "line": 93, "column": 39, "nodeType": "923", "messageId": "924", "endLine": 93, "endColumn": 86}, {"ruleId": "783", "severity": 1, "message": "925", "line": 177, "column": 15, "nodeType": "785", "messageId": "786", "endLine": 177, "endColumn": 30}, {"ruleId": "783", "severity": 1, "message": "926", "line": 3, "column": 41, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 62}, {"ruleId": "783", "severity": 1, "message": "926", "line": 3, "column": 40, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 61}, {"ruleId": "788", "severity": 1, "message": "927", "line": 28, "column": 8, "nodeType": "790", "endLine": 28, "endColumn": 24, "suggestions": "928"}, {"ruleId": "783", "severity": 1, "message": "857", "line": 3, "column": 51, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 58}, {"ruleId": "783", "severity": 1, "message": "909", "line": 3, "column": 102, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 107}, {"ruleId": "783", "severity": 1, "message": "929", "line": 18, "column": 12, "nodeType": "785", "messageId": "786", "endLine": 18, "endColumn": 21}, {"ruleId": "788", "severity": 1, "message": "930", "line": 23, "column": 8, "nodeType": "790", "endLine": 23, "endColumn": 17, "suggestions": "931"}, {"ruleId": "783", "severity": 1, "message": "932", "line": 3, "column": 33, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 38}, {"ruleId": "783", "severity": 1, "message": "933", "line": 14, "column": 12, "nodeType": "785", "messageId": "786", "endLine": 14, "endColumn": 21}, {"ruleId": "788", "severity": 1, "message": "934", "line": 29, "column": 8, "nodeType": "790", "endLine": 29, "endColumn": 10, "suggestions": "935"}, {"ruleId": "783", "severity": 1, "message": "936", "line": 144, "column": 36, "nodeType": "785", "messageId": "786", "endLine": 144, "endColumn": 48}, {"ruleId": "783", "severity": 1, "message": "937", "line": 1, "column": 17, "nodeType": "785", "messageId": "786", "endLine": 1, "endColumn": 25}, {"ruleId": "788", "severity": 1, "message": "927", "line": 35, "column": 8, "nodeType": "790", "endLine": 35, "endColumn": 24, "suggestions": "938"}, {"ruleId": "788", "severity": 1, "message": "939", "line": 67, "column": 8, "nodeType": "790", "endLine": 67, "endColumn": 41, "suggestions": "940"}, {"ruleId": "788", "severity": 1, "message": "941", "line": 89, "column": 46, "nodeType": "785", "endLine": 89, "endColumn": 53}, {"ruleId": "783", "severity": 1, "message": "906", "line": 2, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 2, "endColumn": 14}, {"ruleId": "783", "severity": 1, "message": "942", "line": 3, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 3, "endColumn": 29}, {"ruleId": "783", "severity": 1, "message": "943", "line": 20, "column": 5, "nodeType": "785", "messageId": "786", "endLine": 20, "endColumn": 15}, {"ruleId": "783", "severity": 1, "message": "944", "line": 4, "column": 10, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 28}, {"ruleId": "783", "severity": 1, "message": "945", "line": 4, "column": 30, "nodeType": "785", "messageId": "786", "endLine": 4, "endColumn": 42}, {"ruleId": "845", "severity": 1, "message": "846", "line": 41, "column": 15, "nodeType": "847", "endLine": 41, "endColumn": 73}, {"ruleId": "845", "severity": 1, "message": "846", "line": 46, "column": 15, "nodeType": "847", "endLine": 46, "endColumn": 72}, {"ruleId": "845", "severity": 1, "message": "846", "line": 51, "column": 15, "nodeType": "847", "endLine": 51, "endColumn": 74}, {"ruleId": "845", "severity": 1, "message": "846", "line": 56, "column": 15, "nodeType": "847", "endLine": 56, "endColumn": 72}, "no-unused-vars", "'TestHomePage' is defined but never used.", "Identifier", "unusedVar", "'LeagueSelection' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeError'. Either include it or remove the dependency array.", "ArrayExpression", ["946"], "React Hook useCallback has a missing dependency: 'CACHE_DURATION'. Either include it or remove the dependency array.", ["947"], "The 'publicRoutes' array makes the dependencies of useCallback Hook (at line 144) change on every render. To fix this, wrap the initialization of 'publicRoutes' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'setUserData'. Either include it or remove the dependency array.", ["948"], "React Hook useEffect has a missing dependency: 'publicRoutes'. Either include it or remove the dependency array.", ["949"], "'FaUser' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchUserBets', 'fetchUserDetails', and 'fetchUserTransactions'. Either include them or remove the dependency array.", ["950"], "'challenges' is assigned a value but never used.", "'setChallenges' is assigned a value but never used.", "'userService' is defined but never used.", "'currencyService' is defined but never used.", "'apiService' is defined but never used.", "'endpoints' is defined but never used.", "'API_BASE_URL' is defined but never used.", "'execute' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTeams'. Either include it or remove the dependency array.", ["951"], "'FaFilter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchLeaderboard'. Either include it or remove the dependency array.", ["952"], "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'setSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllBets'. Either include it or remove the dependency array.", ["953"], "'FaCalendarAlt' is defined but never used.", "'FaFootballBall' is defined but never used.", "'FaChartBar' is defined but never used.", "'FaExchangeAlt' is defined but never used.", "'FaEye' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaClock' is defined but never used.", "'FaBell' is defined but never used.", "'Bar' is defined but never used.", "'teamPopularityData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["954"], "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "'FaChartLine' is defined but never used.", "'FaUserShield' is defined but never used.", "'navigate' is assigned a value but never used.", "'LiveMatchCard' is defined but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchWelcomeData'. Either include it or remove the dependency array.", ["955"], "'handleBetClick' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fetchConversations'. Either include it or remove the dependency array.", ["956"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'markMessagesAsRead'. Either include them or remove the dependency array.", ["957"], "'FaSave' is defined but never used.", "'refreshFavicon' is defined but never used.", "'FaCopy' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["958"], "'FaTimes' is defined but never used.", ["959"], "'FaDownload' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReport'. Either include it or remove the dependency array.", ["960"], "'API_BASE_URL' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'checkChallengeStatus'. Either include it or remove the dependency array.", ["961"], "'FaMoneyBillWave' is defined but never used.", "'FaUsers' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'useRef' is defined but never used.", "'payoutPreviews' is assigned a value but never used.", "'setPayoutPreviews' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'fetchLeagueDetails'. Either include it or remove the dependency array.", ["962"], "React Hook useEffect has a missing dependency: 'fetchUserData'. Either include it or remove the dependency array.", ["963"], "'loadingUsers' is assigned a value but never used.", "'currentUsers' is assigned a value but never used.", "'getTeamLogo' is assigned a value but never used.", ["964"], "'BetDetailsModal' is defined but never used.", "'renderPagination' is assigned a value but never used.", "'calculateOdds' is assigned a value but never used.", "'FaCoins' is defined but never used.", "'loading' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", "'getUserStatus' is assigned a value but never used.", "'paymentMethod' is assigned a value but never used.", "'setPaymentMethod' is assigned a value but never used.", "'convertToUserCurrency' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPendingRequests'. Either include it or remove the dependency array.", ["965"], "'FaExclamationCircle' is defined but never used.", "'FaUserFriends' is defined but never used.", "'FaUserCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFriends'. Either include it or remove the dependency array.", ["966"], "'handleChallenge' is assigned a value but never used.", "'FaStar' is defined but never used.", "'FaFire' is defined but never used.", "'renderNavigation' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["967"], "React Hook useEffect has a missing dependency: 'fetchCreditRequests'. Either include it or remove the dependency array.", ["968"], "'matchDate' is assigned a value but never used.", "'now' is assigned a value but never used.", "'Link' is defined but never used.", "'getBetChoice' is assigned a value but never used.", "'FaGamepad' is defined but never used.", "'FaCog' is defined but never used.", "'FaMoneyBill' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateAmounts'. Either include it or remove the dependency array.", ["969"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'getSelectedPaymentMethod' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'attempt'.", "ArrowFunctionExpression", "unsafeRefs", "'originalExecute' is assigned a value but never used.", "'FaExclamationTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendInitialOTP'. Either include it or remove the dependency array.", ["970"], "'adminInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPreferences'. Either include it or remove the dependency array.", ["971"], "'FaKey' is defined but never used.", "'secretKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initiate2FASetup'. Either include it or remove the dependency array.", ["972"], "'userCurrency' is assigned a value but never used.", "'useState' is defined but never used.", ["973"], "React Hook useEffect has a missing dependency: 'filterUsers'. Either include it or remove the dependency array.", ["974"], "The ref value 'componentRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'componentRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'formatTimeRemaining' is defined but never used.", "'match_date' is assigned a value but never used.", "'formatRelativeTime' is defined but never used.", "'truncateText' is defined but never used.", {"desc": "975", "fix": "976"}, {"desc": "977", "fix": "978"}, {"desc": "979", "fix": "980"}, {"desc": "981", "fix": "982"}, {"desc": "983", "fix": "984"}, {"desc": "985", "fix": "986"}, {"desc": "987", "fix": "988"}, {"desc": "989", "fix": "990"}, {"desc": "991", "fix": "992"}, {"desc": "993", "fix": "994"}, {"desc": "995", "fix": "996"}, {"desc": "997", "fix": "998"}, {"desc": "999", "fix": "1000"}, {"desc": "1001", "fix": "1002"}, {"desc": "1003", "fix": "1004"}, {"desc": "1005", "fix": "1006"}, {"desc": "1007", "fix": "1008"}, {"desc": "1009", "fix": "1010"}, {"desc": "1011", "fix": "1012"}, {"desc": "1013", "fix": "1014"}, {"desc": "1015", "fix": "1016"}, {"desc": "1017", "fix": "1018"}, {"desc": "1019", "fix": "1020"}, {"desc": "1021", "fix": "1022"}, {"desc": "1023", "fix": "1024"}, {"desc": "1025", "fix": "1026"}, {"desc": "1027", "fix": "1028"}, {"desc": "1023", "fix": "1029"}, {"desc": "1030", "fix": "1031"}, "Update the dependencies array to be: [removeError]", {"range": "1032", "text": "1033"}, "Update the dependencies array to be: [CACHE_DURATION]", {"range": "1034", "text": "1035"}, "Update the dependencies array to be: [isAuthenticated, userId, setUserData, navigate, location.pathname]", {"range": "1036", "text": "1037"}, "Update the dependencies array to be: [isAuthenticated, userId, navigate, location.pathname, fetchUserData, fetchNotifications, fetchSidebarLogo, publicRoutes]", {"range": "1038", "text": "1039"}, "Update the dependencies array to be: [fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", {"range": "1040", "text": "1041"}, "Update the dependencies array to be: [fetchTeams]", {"range": "1042", "text": "1043"}, "Update the dependencies array to be: [pagination.current_page, filters, fetchLeaderboard]", {"range": "1044", "text": "1045"}, "Update the dependencies array to be: [pagination.currentPage, filters, fetchAllBets]", {"range": "1046", "text": "1047"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1048", "text": "1049"}, "Update the dependencies array to be: [fetchWelcomeData]", {"range": "1050", "text": "1051"}, "Update the dependencies array to be: [activeTab, fetchConversations]", {"range": "1052", "text": "1053"}, "Update the dependencies array to be: [fetchMessages, markMessagesAsRead, selectedConversation]", {"range": "1054", "text": "1055"}, "Update the dependencies array to be: [fetchAdminData]", {"range": "1056", "text": "1057"}, "Update the dependencies array to be: [fetchLeaderboard, filters]", {"range": "1058", "text": "1059"}, "Update the dependencies array to be: [reportType, dateRange, fetchReport]", {"range": "1060", "text": "1061"}, "Update the dependencies array to be: [checkChallengeStatus]", {"range": "1062", "text": "1063"}, "Update the dependencies array to be: [fetchLeagueDetails]", {"range": "1064", "text": "1065"}, "Update the dependencies array to be: [fetchUserData, navigate]", {"range": "1066", "text": "1067"}, "Update the dependencies array to be: [currentPage, fetchLeaderboard]", {"range": "1068", "text": "1069"}, "Update the dependencies array to be: [fetchPendingRequests]", {"range": "1070", "text": "1071"}, "Update the dependencies array to be: [fetchFriends]", {"range": "1072", "text": "1073"}, "Update the dependencies array to be: [navigate, currentUserId, fetchInitialData]", {"range": "1074", "text": "1075"}, "Update the dependencies array to be: [navigate, currentUserId, fetchCreditRequests]", {"range": "1076", "text": "1077"}, "Update the dependencies array to be: [withdrawalForm.amount_fancoins, withdrawalForm.target_currency_id, exchangeRates, calculateAmounts]", {"range": "1078", "text": "1079"}, "Update the dependencies array to be: [initialOtpSent, sendInitialOTP]", {"range": "1080", "text": "1081"}, "Update the dependencies array to be: [adminId, fetchPreferences]", {"range": "1082", "text": "1083"}, "Update the dependencies array to be: [initiate2FASetup]", {"range": "1084", "text": "1085"}, {"range": "1086", "text": "1081"}, "Update the dependencies array to be: [users, searchTerm, filterStatus, filterUsers]", {"range": "1087", "text": "1088"}, [985, 987], "[removeError]", [1873, 1875], "[CACHE_DURATION]", [3811, 3865], "[isAuthenticated, userId, setUserData, navigate, location.pathname]", [5937, 6044], "[isAuthenticated, userId, navigate, location.pathname, fetchUserData, fetchNotifications, fetchSidebarLogo, publicRoutes]", [1053, 1061], "[fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", [1766, 1768], "[fetchTeams]", [1249, 1283], "[pagination.current_page, filters, fetchLeaderboard]", [1169, 1202], "[pagination.currentPage, filters, fetchAllBets]", [4088, 4090], "[fetchDashboardData]", [1544, 1546], "[fetchWelcomeData]", [1071, 1082], "[activeTab, fetchConversations]", [1429, 1451], "[fetch<PERSON>essages, markMessagesAsRead, selectedConversation]", [1277, 1279], "[fetchAdminData]", [887, 896], "[fetchLeaderboard, filters]", [892, 915], "[reportType, dateRange, fetchReport]", [1554, 1556], "[checkChallengeStatus]", [3030, 3032], "[fetchLeagueDetails]", [6041, 6051], "[fetch<PERSON><PERSON><PERSON><PERSON>, navigate]", [1160, 1173], "[currentPage, fetchLeaderboard]", [696, 698], "[fetchPendingRequests]", [1331, 1333], "[fetchFriends]", [856, 881], "[navigate, currentUserId, fetchInitialData]", [891, 916], "[navigate, currentUserId, fetchCreditRequests]", [1028, 1110], "[withdrawalForm.amount_fancoins, withdrawalForm.target_currency_id, exchangeRates, calculateAmounts]", [1264, 1280], "[initialOtpSent, sendInitialOTP]", [856, 865], "[adminId, fetchPreferences]", [1137, 1139], "[initiate2FASetup]", [1205, 1221], [2078, 2111], "[users, searchTerm, filterStatus, filterUsers]"]