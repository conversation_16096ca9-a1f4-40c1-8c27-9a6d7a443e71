<?php return array(
    'root' => array(
        'name' => 'fanbet247/backend',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'bacon/bacon-qr-code' => array(
            'pretty_version' => 'v3.0.1',
            'version' => '3.0.1.0',
            'reference' => 'f9cc1f52b5a463062251d666761178dbdb6b544f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.6',
            'version' => '1.0.6.0',
            'reference' => '8dfd07c6d2cf31c8da90c53b83c026c7696dda90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'endroid/qr-code' => array(
            'pretty_version' => '6.0.8',
            'version' => '6.0.8.0',
            'reference' => '8102273afbcd5e3d95f1faaab2c5aa31e3637f61',
            'type' => 'library',
            'install_path' => __DIR__ . '/../endroid/qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fanbet247/backend' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'df1e7fde177501eee2037dd159cf04f5f301a512',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpmailer/phpmailer' => array(
            'pretty_version' => 'v6.10.0',
            'version' => '6.10.0.0',
            'reference' => 'bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmailer/phpmailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa' => array(
            'pretty_version' => 'v8.0.3',
            'version' => '8.0.3.0',
            'reference' => '6f8d87ebd5afbf7790bde1ffc7579c7c705e0fad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
