<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

try {
    // Verify database connection
    if (!$conn) {
        error_log("Database connection failed in leaderboard.php");
        throw new Exception("Database connection failed");
    }

    // Get pagination and filter parameters with validation
    $page = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT) ?: 1;
    $limit = filter_input(INPUT_GET, 'limit', FILTER_VALIDATE_INT) ?: 10;
    $offset = ($page - 1) * $limit;
    $sortBy = filter_input(INPUT_GET, 'sortBy', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?: 'points';
    $order = filter_input(INPUT_GET, 'order', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?: 'DESC';
    $timeFilter = filter_input(INPUT_GET, 'timeFilter', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?: 'all';
    $leagueFilter = filter_input(INPUT_GET, 'leagueFilter', FILTER_VALIDATE_INT);
    $search = filter_input(INPUT_GET, 'search', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?: '';

    // Validate sort column
    $allowedSortColumns = ['points', 'total_points', 'wins', 'losses', 'total_bets', 'balance', 'current_streak', 'highest_streak'];
    if (!in_array($sortBy, $allowedSortColumns)) {
        $sortBy = 'points';
    }

    // Validate order
    $order = ($order === 'ASC') ? 'ASC' : 'DESC';

    error_log("Leaderboard request - Page: $page, Limit: $limit, Offset: $offset, Search: '$search', League: $leagueFilter, Sort: $sortBy $order");

    // Build count query with filters
    $countQuery = "SELECT COUNT(*) as total FROM users u WHERE u.role = 'user'";
    $countParams = [];

    if ($leagueFilter) {
        $countQuery .= " AND u.current_league_id = :league_id";
        $countParams[':league_id'] = $leagueFilter;
    }

    if (!empty($search)) {
        $countQuery .= " AND (u.username LIKE :search OR u.full_name LIKE :search OR u.email LIKE :search)";
        $countParams[':search'] = "%$search%";
    }

    if ($timeFilter !== 'all') {
        switch ($timeFilter) {
            case 'week':
                $countQuery .= " AND u.last_active >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
                break;
            case 'month':
                $countQuery .= " AND u.last_active >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                break;
            case 'year':
                $countQuery .= " AND u.last_active >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
                break;
        }
    }

    $countStmt = $conn->prepare($countQuery);
    foreach ($countParams as $key => $value) {
        $countStmt->bindValue($key, $value);
    }

    if (!$countStmt->execute()) {
        error_log("Count query failed: " . print_r($countStmt->errorInfo(), true));
        throw new Exception("Failed to count total records");
    }

    $totalRecords = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    error_log("Total records found: $totalRecords");
    
    // Enhanced leaderboard query with detailed stats and filters
    $query = "SELECT
        u.user_id,
        u.username,
        u.full_name,
        COALESCE(u.points, 0) as points,
        COALESCE(u.total_points, 0) as total_points,
        COALESCE(u.wins, 0) as wins,
        COALESCE(u.draws, 0) as draws,
        COALESCE(u.losses, 0) as losses,
        COALESCE(u.total_bets, 0) as total_bets,
        COALESCE(u.balance, 0) as balance,
        COALESCE(u.current_streak, 0) as current_streak,
        COALESCE(u.highest_streak, 0) as highest_streak,
        u.created_at,
        u.last_active,
        l.name as league_name,
        CASE
            WHEN u.total_bets > 0 THEN ROUND((u.wins / u.total_bets) * 100, 2)
            ELSE 0
        END as win_percentage
    FROM users u
    LEFT JOIN leagues l ON u.current_league_id = l.league_id
    WHERE u.role = 'user'";

    $params = [];

    // Add league filter
    if ($leagueFilter) {
        $query .= " AND u.current_league_id = :league_id";
        $params[':league_id'] = $leagueFilter;
    }

    // Add search filter
    if (!empty($search)) {
        $query .= " AND (u.username LIKE :search OR u.full_name LIKE :search OR u.email LIKE :search)";
        $params[':search'] = "%$search%";
    }

    // Add time filter for activity
    if ($timeFilter !== 'all') {
        switch ($timeFilter) {
            case 'week':
                $query .= " AND u.last_active >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
                break;
            case 'month':
                $query .= " AND u.last_active >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                break;
            case 'year':
                $query .= " AND u.last_active >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
                break;
        }
    }

    // Add ordering
    $query .= " ORDER BY u.$sortBy $order";

    // Add pagination
    $query .= " LIMIT :limit OFFSET :offset";

    error_log("Executing leaderboard query: " . str_replace([':limit', ':offset'], [$limit, $offset], $query));
    
    $stmt = $conn->prepare($query);

    if (!$stmt) {
        error_log("Query preparation failed: " . print_r($conn->errorInfo(), true));
        throw new Exception("Failed to prepare leaderboard query");
    }

    // Bind parameters
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    if (!$stmt->execute()) {
        error_log("Query execution failed: " . print_r($stmt->errorInfo(), true));
        throw new Exception("Failed to execute leaderboard query");
    }

    $leaderboard = $stmt->fetchAll(PDO::FETCH_ASSOC);
    error_log("Found " . count($leaderboard) . " players for page " . $page);

    // Get leagues for filter dropdown
    $leaguesQuery = "SELECT league_id, name FROM leagues ORDER BY name";
    $leaguesStmt = $conn->prepare($leaguesQuery);
    $leaguesStmt->execute();
    $leagues = $leaguesStmt->fetchAll(PDO::FETCH_ASSOC);

    if ($leaderboard) {
        // Add ranking and format the data
        foreach ($leaderboard as $index => &$player) {
            $player['rank'] = $offset + $index + 1;
            // Ensure numeric values
            $player['points'] = (int)$player['points'];
            $player['total_points'] = (int)$player['total_points'];
            $player['current_streak'] = (int)$player['current_streak'];
            $player['highest_streak'] = (int)$player['highest_streak'];
            $player['total_bets'] = (int)$player['total_bets'];
            $player['wins'] = (int)$player['wins'];
            $player['draws'] = (int)$player['draws'];
            $player['losses'] = (int)$player['losses'];
            $player['balance'] = (float)$player['balance'];
            $player['win_percentage'] = (float)$player['win_percentage'];
        }

        echo json_encode([
            'success' => true,
            'message' => 'Leaderboard data retrieved successfully',
            'data' => [
                'leaderboard' => $leaderboard,
                'leagues' => $leagues,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($totalRecords / $limit),
                    'total_records' => $totalRecords,
                    'records_per_page' => $limit
                ]
            ]
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'No leaderboard data found',
            'data' => [
                'leaderboard' => [],
                'leagues' => $leagues,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => 0,
                    'total_records' => 0,
                    'records_per_page' => $limit
                ]
            ]
        ]);
    }
} catch (PDOException $e) {
    error_log("Database Error in leaderboard.php: " . $e->getMessage());
    error_log("Error code: " . $e->getCode());
    error_log("Error trace: " . $e->getTraceAsString());
    
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred: ' . $e->getMessage(),
        'error_code' => $e->getCode()
    ]);
} catch (Exception $e) {
    error_log("General Error in leaderboard.php: " . $e->getMessage());
    error_log("Error trace: " . $e->getTraceAsString());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred: ' . $e->getMessage()
    ]);
}
