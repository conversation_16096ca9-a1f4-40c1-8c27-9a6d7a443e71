/**
 * Quick Fix for API Configuration Issues
 * 
 * This script can be added to your index.html or run in the browser console
 * to debug and fix API configuration issues.
 */

(function() {
    'use strict';
    
    console.log('🔧 FanBet247 API Configuration Fix');
    console.log('===================================');
    
    // Check current configuration
    const currentHost = window.location.host;
    const currentProtocol = window.location.protocol;
    const currentPath = window.location.pathname;
    
    console.log('Current URL:', window.location.href);
    console.log('Host:', currentHost);
    console.log('Protocol:', currentProtocol);
    console.log('Path:', currentPath);
    
    // Detect the correct API base URL
    let apiBaseUrl;
    
    if (currentHost.includes('localhost') || currentHost.includes('127.0.0.1')) {
        // Development
        if (window.location.port === '3000') {
            apiBaseUrl = '/backend/handlers'; // React dev server with proxy
        } else {
            apiBaseUrl = '/FanBet247/backend/handlers'; // Local MAMP/XAMPP
        }
    } else {
        // Production - assume root deployment
        apiBaseUrl = '/backend/handlers';
    }
    
    console.log('Detected API Base URL:', apiBaseUrl);
    
    // Test API connectivity
    async function testAPIEndpoint(endpoint, name) {
        const url = apiBaseUrl + '/' + endpoint;
        console.log(`Testing ${name}: ${url}`);
        
        try {
            const response = await fetch(url);
            const status = response.status;
            const statusText = response.statusText;
            
            if (response.ok) {
                console.log(`✅ ${name}: ${status} ${statusText}`);
                return true;
            } else {
                console.log(`❌ ${name}: ${status} ${statusText}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ ${name}: ${error.message}`);
            return false;
        }
    }
    
    // Test multiple endpoints
    async function runAPITests() {
        console.log('\n🧪 Testing API Endpoints...');
        
        const tests = [
            { endpoint: 'homepage_data.php', name: 'Homepage Data' },
            { endpoint: 'get_currencies.php', name: 'Currencies' },
            { endpoint: 'get_site_config.php', name: 'Site Config' }
        ];
        
        const results = [];
        for (const test of tests) {
            const result = await testAPIEndpoint(test.endpoint, test.name);
            results.push({ ...test, success: result });
        }
        
        console.log('\n📊 Test Results:');
        results.forEach(result => {
            const icon = result.success ? '✅' : '❌';
            console.log(`${icon} ${result.name}: ${result.success ? 'Working' : 'Failed'}`);
        });
        
        return results;
    }
    
    // Fix axios configuration if it exists
    function fixAxiosConfig() {
        if (window.axios) {
            console.log('\n🔧 Fixing axios configuration...');
            
            // Update axios defaults
            window.axios.defaults.baseURL = apiBaseUrl;
            window.axios.defaults.timeout = 30000;
            window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            
            console.log('✅ Axios configuration updated');
            console.log('New base URL:', window.axios.defaults.baseURL);
        } else {
            console.log('⚠️ Axios not found in global scope');
        }
    }
    
    // Create a global API helper
    window.FanBet247API = {
        baseURL: apiBaseUrl,
        
        async get(endpoint) {
            const url = `${this.baseURL}/${endpoint}`;
            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error(`API Error (${endpoint}):`, error);
                throw error;
            }
        },
        
        async post(endpoint, data) {
            const url = `${this.baseURL}/${endpoint}`;
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(data)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error(`API Error (${endpoint}):`, error);
                throw error;
            }
        },
        
        // Test all endpoints
        async test() {
            return await runAPITests();
        }
    };
    
    // Auto-fix on load
    fixAxiosConfig();
    
    console.log('\n🎉 API Configuration Fix Applied!');
    console.log('📝 Usage:');
    console.log('  - Test APIs: FanBet247API.test()');
    console.log('  - Get data: FanBet247API.get("homepage_data.php")');
    console.log('  - Post data: FanBet247API.post("endpoint.php", {data})');
    console.log('  - Current base URL:', apiBaseUrl);
    
    // Auto-test after a short delay
    setTimeout(() => {
        console.log('\n🔍 Running automatic API test...');
        runAPITests().then(results => {
            const failedTests = results.filter(r => !r.success);
            if (failedTests.length === 0) {
                console.log('\n🎉 All API tests passed!');
            } else {
                console.log('\n⚠️ Some API tests failed. Check your backend deployment.');
                console.log('Failed tests:', failedTests.map(t => t.name));
            }
        });
    }, 1000);
    
})();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { FanBet247API: window.FanBet247API };
}
