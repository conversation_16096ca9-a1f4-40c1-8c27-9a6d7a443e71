<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!isset($_GET['userId'])) {
        throw new Exception('User ID is required');
    }
    
    $userId = $_GET['userId'];
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $offset = ($page - 1) * $limit;
    
    // Get total count for pagination
    $countQuery = "SELECT COUNT(*) as total FROM bets b WHERE b.user1_id = :userId";
    if (!empty($search)) {
        $countQuery .= " AND b.reference_number LIKE :search";
    }
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':userId', $userId, PDO::PARAM_INT);
    if (!empty($search)) {
        $searchParam = "%$search%";
        $countStmt->bindParam(':search', $searchParam, PDO::PARAM_STR);
    }
    $countStmt->execute();
    $totalCount = $countStmt->fetch()['total'];
    
    // Main query with pagination
    $query = "SELECT b.*, 
            u1.username as user1_username,
            u2.username as user2_username,
            c.match_type,
            c.team_a, 
            c.team_b, 
            c.odds_team_a, 
            c.odds_team_b,
            c.odds_draw,
            c.odds_lost,
            c.match_date,
            c.start_time,
            c.end_time,
            c.challenge_date,
            c.logo1 as team1_logo,
            c.logo2 as team2_logo,
            CASE 
                WHEN b.bet_choice_user1 = 'team_a_win' THEN b.amount_user1 * c.odds_lost
                WHEN b.bet_choice_user1 = 'team_b_win' THEN b.amount_user1 * c.odds_lost
                ELSE b.amount_user1 * c.odds_lost
            END as potential_loss_user1,
            CASE 
                WHEN b.bet_choice_user1 = 'draw' THEN b.amount_user1 * c.odds_draw
                ELSE 0
            END as potential_draw_win_user1
            FROM bets b
            JOIN users u1 ON b.user1_id = u1.user_id
            LEFT JOIN users u2 ON b.user2_id = u2.user_id
            JOIN challenges c ON b.challenge_id = c.challenge_id
            WHERE b.user1_id = :userId";
    if (!empty($search)) {
        $query .= " AND b.reference_number LIKE :search";
    }
    $query .= " ORDER BY c.match_date DESC LIMIT :offset, :limit";
            
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
    if (!empty($search)) {
        $searchParam = "%$search%";
        $stmt->bindParam(':search', $searchParam, PDO::PARAM_STR);
    }
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    $bets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Update status for display and format dates
    foreach ($bets as &$bet) {
        if ($bet['bet_status'] === 'open' && $bet['user2_id'] !== null) {
            $bet['bet_status'] = 'joined';
        }
        
        // Format match_date for display
        if (isset($bet['match_date'])) {
            $bet['match_date'] = date('Y-m-d H:i:s', strtotime($bet['match_date']));
        }
    }

    echo json_encode([
        'success' => true,
        'bets' => $bets,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => ceil($totalCount / $limit),
            'totalItems' => $totalCount,
            'itemsPerPage' => $limit
        ]
    ]);
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}