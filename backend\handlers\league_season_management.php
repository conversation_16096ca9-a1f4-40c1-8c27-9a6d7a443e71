<?php
header("Access-Control-Allow-Origin: *"); 
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';
$conn = getDBConnection();

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
    exit();
}

// GET: Retrieve seasons for a league
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        if (!isset($_GET['league_id'])) {
            jsonResponse(400, 'League ID is required');
        }

        $query = "SELECT s.*, 
                 (SELECT COUNT(*) FROM league_memberships WHERE league_id = s.league_id AND status = 'active') as total_participants
                 FROM league_seasons s 
                 WHERE s.league_id = ? 
                 ORDER BY s.created_at DESC";
        
        $stmt = $conn->prepare($query);
        $stmt->execute([$_GET['league_id']]);
        $seasons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        jsonResponse(200, 'Seasons fetched successfully', $seasons);
    } catch (PDOException $e) {
        error_log("Database error in league_season_management.php: " . $e->getMessage());
        jsonResponse(500, 'Database error occurred');
    }
}

// POST: Create a new season
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $data = json_decode(file_get_contents("php://input"), true);
        
        if (!isset($data['league_id']) || !isset($data['season_name']) || 
            !isset($data['start_date']) || !isset($data['prize_pool'])) {
            jsonResponse(400, 'Missing required fields');
        }

        // Calculate end date (90 days from start date)
        $start_date = new DateTime($data['start_date']);
        $end_date = clone $start_date;
        $end_date->modify('+90 days');

        $query = "INSERT INTO league_seasons 
                 (league_id, season_name, start_date, end_date, prize_pool, status, created_at) 
                 VALUES (?, ?, ?, ?, ?, 'upcoming', NOW())";
        
        $stmt = $conn->prepare($query);
        $stmt->execute([
            $data['league_id'],
            $data['season_name'],
            $data['start_date'],
            $end_date->format('Y-m-d H:i:s'),
            $data['prize_pool']
        ]);

        $season_id = $conn->lastInsertId();
        jsonResponse(201, 'Season created successfully', ['season_id' => $season_id]);
    } catch (PDOException $e) {
        error_log("Database error in league_season_management.php: " . $e->getMessage());
        jsonResponse(500, 'Failed to create season');
    }
}

// PUT: Update season status
if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    try {
        $data = json_decode(file_get_contents("php://input"), true);
        
        if (!isset($data['season_id']) || !isset($data['action'])) {
            jsonResponse(400, 'Season ID and action are required');
        }

        if ($data['action'] === 'end') {
            $query = "UPDATE league_seasons SET status = 'completed', updated_at = NOW() WHERE season_id = ?";
            $stmt = $conn->prepare($query);
            $stmt->execute([$data['season_id']]);

            if ($stmt->rowCount() > 0) {
                // Calculate final rankings and distribute rewards here
                calculateSeasonRankings($conn, $data['season_id']);
                jsonResponse(200, 'Season ended successfully');
            } else {
                jsonResponse(404, 'Season not found');
            }
        } else {
            jsonResponse(400, 'Invalid action');
        }
    } catch (PDOException $e) {
        error_log("Database error in league_season_management.php: " . $e->getMessage());
        jsonResponse(500, 'Failed to update season');
    }
}

// Helper function to calculate final rankings and distribute rewards
function calculateSeasonRankings($conn, $season_id) {
    try {
        // Get season details
        $stmt = $conn->prepare("SELECT league_id, prize_pool FROM league_seasons WHERE season_id = ?");
        $stmt->execute([$season_id]);
        $season = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get final rankings
        $query = "SELECT user_id, points FROM league_memberships 
                 WHERE league_id = ? AND status = 'active' 
                 ORDER BY points DESC";
        $stmt = $conn->prepare($query);
        $stmt->execute([$season['league_id']]);
        $rankings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate rewards
        $total_players = count($rankings);
        $prize_pool = $season['prize_pool'];
        $reward_distribution = [
            1 => 0.5,  // 50% for 1st place
            2 => 0.3,  // 30% for 2nd place
            3 => 0.2   // 20% for 3rd place
        ];

        // Record history and distribute rewards
        foreach ($rankings as $rank => $player) {
            $rank_position = $rank + 1;
            $reward = isset($reward_distribution[$rank_position]) 
                     ? $prize_pool * $reward_distribution[$rank_position] 
                     : 0;

            // Record in league_history
            $query = "INSERT INTO league_history 
                     (season_id, user_id, final_points, final_rank, rewards_earned) 
                     VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->execute([
                $season_id,
                $player['user_id'],
                $player['points'],
                $rank_position,
                $reward
            ]);

            // If there's a reward, add it to user's balance
            if ($reward > 0) {
                $query = "UPDATE users SET balance = balance + ? WHERE user_id = ?";
                $stmt = $conn->prepare($query);
                $stmt->execute([$reward, $player['user_id']]);
            }
        }
    } catch (PDOException $e) {
        error_log("Error calculating season rankings: " . $e->getMessage());
        throw $e;
    }
}
