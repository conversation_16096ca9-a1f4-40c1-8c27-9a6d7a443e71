<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../config/database.php';

try {
    $conn = getDBConnection();
    
    // Update SMTP settings with Hostinger configuration
    $stmt = $conn->prepare("
        UPDATE smtp_settings 
        SET host = :host, 
            port = :port, 
            username = :username, 
            password = :password, 
            encryption = :encryption, 
            from_email = :from_email, 
            from_name = :from_name, 
            is_active = :is_active,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = 1
    ");
    
    $host = 'mail.hostinger.com';
    $port = 465;
    $username = '<EMAIL>';
    $password = 'Money2025@Demo#';
    $encryption = 'ssl';
    $from_email = '<EMAIL>';
    $from_name = 'FanBet247';
    $is_active = 1;
    
    $stmt->bindParam(':host', $host);
    $stmt->bindParam(':port', $port);
    $stmt->bindParam(':username', $username);
    $stmt->bindParam(':password', $password);
    $stmt->bindParam(':encryption', $encryption);
    $stmt->bindParam(':from_email', $from_email);
    $stmt->bindParam(':from_name', $from_name);
    $stmt->bindParam(':is_active', $is_active);
    
    if ($stmt->execute()) {
        echo json_encode([
            "success" => true,
            "message" => "SMTP settings updated to Hostinger configuration successfully",
            "settings" => [
                "host" => $host,
                "port" => $port,
                "username" => $username,
                "encryption" => $encryption,
                "from_email" => $from_email,
                "from_name" => $from_name,
                "is_active" => $is_active
            ]
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Failed to update SMTP settings"
        ]);
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
}
?>
