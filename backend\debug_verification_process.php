<?php
require_once 'includes/db_connect.php';
require_once 'vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

echo "🔍 DEBUGGING VERIFICATION PROCESS\n";
echo "=================================\n\n";

try {
    $conn = getDBConnection();
    
    // Get babademo user data
    $userId = 4; // babademo user ID
    
    echo "👤 Checking user ID: $userId\n";
    
    // Get user data
    $stmt = $conn->prepare("SELECT * FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "❌ User not found!\n";
        exit;
    }
    
    echo "✅ User: {$user['username']}\n";
    echo "   TFA Enabled: " . ($user['tfa_enabled'] ? 'Yes' : 'No') . "\n";
    echo "   Auth Method: {$user['auth_method']}\n\n";
    
    // Get 2FA setup
    $stmt = $conn->prepare("SELECT * FROM user_2fa WHERE user_id = ?");
    $stmt->execute([$userId]);
    $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tfaSetup) {
        echo "❌ No 2FA setup found!\n";
        exit;
    }
    
    echo "🔑 2FA Setup:\n";
    echo "   Secret Key: {$tfaSetup['secret_key']}\n";
    echo "   Is Enabled: " . ($tfaSetup['is_enabled'] ? 'Yes' : 'No') . "\n";
    echo "   Setup Complete: " . ($tfaSetup['setup_completed'] ? 'Yes' : 'No') . "\n";
    echo "   Auth Type: {$tfaSetup['auth_type']}\n\n";
    
    // Test Google2FA with the actual secret from database
    $google2fa = new Google2FA();
    $secretKey = $tfaSetup['secret_key'];
    
    echo "📱 TESTING WITH DATABASE SECRET:\n";
    echo "   Secret: $secretKey\n";
    
    // Generate current code
    $currentCode = $google2fa->getCurrentOtp($secretKey);
    echo "   Current TOTP: $currentCode\n";
    echo "   Time: " . date('H:i:s') . "\n\n";
    
    // Test verification with different windows
    echo "🧪 VERIFICATION TESTS:\n";
    for ($window = 0; $window <= 4; $window++) {
        $isValid = $google2fa->verifyKey($secretKey, $currentCode, $window);
        echo "   Window $window: " . ($isValid ? "✅ VALID" : "❌ INVALID") . "\n";
    }
    
    echo "\n🔍 SIMULATING ACTUAL VERIFICATION HANDLER:\n";
    
    // Simulate the exact verification process from user_verify_2fa.php
    $verificationCode = $currentCode;
    
    echo "   Input code: $verificationCode\n";
    echo "   Code length: " . strlen($verificationCode) . "\n";
    echo "   Is numeric: " . (ctype_digit($verificationCode) ? 'Yes' : 'No') . "\n";
    
    if (strlen($verificationCode) !== 6 || !ctype_digit($verificationCode)) {
        echo "   ❌ Code format validation failed\n";
    } else {
        echo "   ✅ Code format validation passed\n";
        
        // Check if user has 2FA enabled
        if (!$user['tfa_enabled']) {
            echo "   ❌ User doesn't have 2FA enabled\n";
        } else {
            echo "   ✅ User has 2FA enabled\n";
            
            // Check if 2FA setup exists and is enabled
            if (!$tfaSetup || !$tfaSetup['is_enabled']) {
                echo "   ❌ 2FA setup not found or not enabled\n";
            } else {
                echo "   ✅ 2FA setup found and enabled\n";
                
                // Test verification with window
                $window = 2;
                $isValid = $google2fa->verifyKey($tfaSetup['secret_key'], $verificationCode, $window);
                
                echo "   🎯 Verification result (window $window): " . ($isValid ? "✅ SUCCESS" : "❌ FAILED") . "\n";
                
                if (!$isValid) {
                    echo "\n🔧 TROUBLESHOOTING:\n";
                    
                    // Try different time offsets
                    echo "   Testing different time offsets:\n";
                    $currentTime = time();
                    
                    for ($offset = -5; $offset <= 5; $offset++) {
                        $testTime = $currentTime + ($offset * 30);
                        $testCode = $google2fa->getCurrentOtp($secretKey, $testTime);
                        $testValid = $google2fa->verifyKey($secretKey, $testCode, 2);
                        
                        $timeStr = date('H:i:s', $testTime);
                        echo "     Offset {$offset} (${timeStr}): Code $testCode - " . ($testValid ? "✅" : "❌") . "\n";
                        
                        if ($testCode === $verificationCode) {
                            echo "       ^ This matches our test code!\n";
                        }
                    }
                    
                    // Check if secret key has any encoding issues
                    echo "\n   Secret key analysis:\n";
                    echo "     Length: " . strlen($secretKey) . "\n";
                    echo "     Characters: " . $secretKey . "\n";
                    echo "     Base32 valid: ";
                    
                    try {
                        $decoded = $google2fa->base32Decode($secretKey);
                        echo "✅ Yes\n";
                        echo "     Decoded length: " . strlen($decoded) . "\n";
                    } catch (Exception $e) {
                        echo "❌ No - " . $e->getMessage() . "\n";
                    }
                }
            }
        }
    }
    
    echo "\n📋 SUMMARY:\n";
    echo "===========\n";
    echo "User ID: $userId\n";
    echo "Username: {$user['username']}\n";
    echo "Secret Key: $secretKey\n";
    echo "Current Code: $currentCode\n";
    echo "Verification: " . ($isValid ? "✅ WORKING" : "❌ FAILING") . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
