<?php
/**
 * Fixed Admin Login Handler
 * Handles admin authentication with proper JSON parsing and error handling
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// CORS Headers
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Credentials: true");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, X-Admin-ID");

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Include database connection
include_once '../includes/db_connect.php';

/**
 * Validate admin credentials
 */
function validateAdminCredentials($identifier, $password) {
    try {
        $conn = getDBConnection();
        if (!$conn) {
            throw new PDOException("Database connection failed");
        }

        // Prepare statement to find admin by username or email
        $stmt = $conn->prepare("SELECT admin_id, username, password_hash, role, email FROM admins WHERE username = :identifier OR email = :identifier");
        $stmt->bindParam(':identifier', $identifier);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Verify password
            if (password_verify($password, $row['password_hash'])) {
                // Update last_login timestamp
                $updateStmt = $conn->prepare("UPDATE admins SET last_login = CURRENT_TIMESTAMP WHERE admin_id = :admin_id");
                $updateStmt->bindParam(':admin_id', $row['admin_id']);
                $updateStmt->execute();

                return [
                    'success' => true,
                    'admin_id' => $row['admin_id'],
                    'username' => $row['username'],
                    'email' => $row['email'],
                    'role' => $row['role']
                ];
            }
        }
        
        return ['success' => false, 'message' => 'Invalid credentials'];
        
    } catch (PDOException $e) {
        error_log("Admin Login Error: " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Generate session token for admin
 */
function generateAdminSession($adminId) {
    try {
        $conn = getDBConnection();
        $sessionToken = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
        
        // Store session in database (you may need to create this table)
        $stmt = $conn->prepare("INSERT INTO admin_sessions (admin_id, session_token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE session_token = VALUES(session_token), expires_at = VALUES(expires_at)");
        $stmt->execute([$adminId, $sessionToken, $expiresAt]);
        
        return $sessionToken;
    } catch (Exception $e) {
        error_log("Session generation error: " . $e->getMessage());
        return null;
    }
}

// Main login processing
try {
    // Test database connection first
    $conn = getDBConnection();
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Get raw input
    $rawInput = file_get_contents("php://input");
    
    // Log the raw input for debugging
    error_log("Admin Login - Raw input: " . $rawInput);
    
    // Try to decode JSON
    $data = json_decode($rawInput, true);
    
    // If JSON decode fails, try to get from POST
    if (!$data && !empty($_POST)) {
        $data = $_POST;
        error_log("Admin Login - Using POST data: " . print_r($_POST, true));
    }
    
    // If still no data, try to parse manually
    if (!$data && $rawInput) {
        // Try to fix common JSON issues
        $cleanInput = str_replace(['\\"', "\\'"], ['"', "'"], $rawInput);
        $data = json_decode($cleanInput, true);
        error_log("Admin Login - Cleaned input: " . $cleanInput);
    }

    // Validate input data
    if (!$data || !isset($data['identifier']) || !isset($data['password'])) {
        throw new Exception("Missing identifier or password. Received: " . print_r($data, true));
    }

    $identifier = trim($data['identifier']);
    $password = $data['password'];
    
    // Log the attempt
    error_log("Admin Login - Attempting login for: " . $identifier);

    // Validate credentials
    $result = validateAdminCredentials($identifier, $password);
    
    if ($result['success']) {
        // Generate session token
        $sessionToken = generateAdminSession($result['admin_id']);
        
        // Successful login response
        $response = [
            "success" => true,
            "message" => "Login successful",
            "admin_id" => $result['admin_id'],
            "username" => $result['username'],
            "email" => $result['email'],
            "role" => $result['role'],
            "session_token" => $sessionToken,
            "timestamp" => date('Y-m-d H:i:s')
        ];
        
        error_log("Admin Login - Success for: " . $identifier);
        echo json_encode($response);
        
    } else {
        // Failed login response
        $response = [
            "success" => false,
            "message" => "Invalid username/email or password"
        ];
        
        error_log("Admin Login - Failed for: " . $identifier);
        echo json_encode($response);
    }

} catch (Exception $e) {
    // Error response
    error_log("Admin Login Exception: " . $e->getMessage());
    error_log("Admin Login Stack Trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "An error occurred during login",
        "debug_info" => [
            "error" => $e->getMessage(),
            "file" => $e->getFile(),
            "line" => $e->getLine(),
            "raw_input" => $rawInput ?? 'No input',
            "post_data" => $_POST ?? 'No POST data',
            "timestamp" => date('Y-m-d H:i:s')
        ]
    ]);
}
?>
