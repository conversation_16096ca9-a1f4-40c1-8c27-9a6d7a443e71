# FanBet247 VPS Deployment Script
# This script helps deploy your local files to the VPS

param(
    [string]$VPSHost = "**************",
    [string]$VPSUser = "root",
    [string]$VPSPath = "/var/www/fanbet247.xyz",
    [switch]$TestOnly = $false,
    [switch]$BackupFirst = $true
)

Write-Host "🚀 FanBet247 VPS Deployment Script" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Check if we have the required tools
$requiredTools = @("scp", "ssh")
foreach ($tool in $requiredTools) {
    if (!(Get-Command $tool -ErrorAction SilentlyContinue)) {
        Write-Host "❌ Error: $tool is not available. Please install OpenSSH or use WSL." -ForegroundColor Red
        Write-Host "You can install OpenSSH on Windows via: Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******" -ForegroundColor Yellow
        exit 1
    }
}

# Function to execute SSH commands
function Invoke-SSHCommand {
    param([string]$Command)
    Write-Host "🔧 Executing: $Command" -ForegroundColor Yellow
    ssh -o StrictHostKeyChecking=no $VPSUser@$VPSHost $Command
}

# Function to copy files via SCP
function Copy-ToVPS {
    param([string]$LocalPath, [string]$RemotePath)
    Write-Host "📤 Copying $LocalPath to $RemotePath" -ForegroundColor Green
    scp -o StrictHostKeyChecking=no -r $LocalPath ${VPSUser}@${VPSHost}:$RemotePath
}

Write-Host "🔍 Checking VPS connection..." -ForegroundColor Blue
$connectionTest = ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 $VPSUser@$VPSHost "echo 'Connection successful'"
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Cannot connect to VPS. Please check your connection and credentials." -ForegroundColor Red
    exit 1
}
Write-Host "✅ VPS connection successful" -ForegroundColor Green

if ($TestOnly) {
    Write-Host "🧪 Test mode - checking VPS structure only" -ForegroundColor Yellow
    Invoke-SSHCommand "ls -la $VPSPath"
    Invoke-SSHCommand "ls -la $VPSPath/backend/handlers/ | head -10"
    exit 0
}

# Create backup if requested
if ($BackupFirst) {
    Write-Host "💾 Creating backup of current VPS files..." -ForegroundColor Blue
    $backupName = "fanbet247_backup_$(Get-Date -Format 'yyyy-MM-dd_HH-mm-ss')"
    Invoke-SSHCommand "cd /var/www && tar -czf ${backupName}.tar.gz fanbet247.xyz"
    Write-Host "✅ Backup created: ${backupName}.tar.gz" -ForegroundColor Green
}

# Ensure directory structure exists
Write-Host "📁 Ensuring directory structure..." -ForegroundColor Blue
Invoke-SSHCommand "mkdir -p $VPSPath"
Invoke-SSHCommand "mkdir -p $VPSPath/backend/handlers"
Invoke-SSHCommand "mkdir -p $VPSPath/backend/includes"
Invoke-SSHCommand "mkdir -p $VPSPath/uploads"
Invoke-SSHCommand "mkdir -p $VPSPath/static"

# Deploy critical files
Write-Host "🚀 Deploying files to VPS..." -ForegroundColor Blue

# Copy frontend build files
if (Test-Path "index.html") {
    Copy-ToVPS "index.html" "$VPSPath/"
    Copy-ToVPS "static" "$VPSPath/"
    Copy-ToVPS "manifest.json" "$VPSPath/"
    Copy-ToVPS "favicon.ico" "$VPSPath/"
    if (Test-Path "logo192.png") { Copy-ToVPS "logo192.png" "$VPSPath/" }
    if (Test-Path "logo512.png") { Copy-ToVPS "logo512.png" "$VPSPath/" }
    Write-Host "✅ Frontend files deployed" -ForegroundColor Green
} else {
    Write-Host "⚠️ Warning: index.html not found. Run 'npm run build' first." -ForegroundColor Yellow
}

# Copy backend files
if (Test-Path "backend") {
    Copy-ToVPS "backend" "$VPSPath/"
    Write-Host "✅ Backend files deployed" -ForegroundColor Green
} else {
    Write-Host "❌ Error: Backend directory not found" -ForegroundColor Red
}

# Copy configuration files
Copy-ToVPS ".htaccess" "$VPSPath/"
Copy-ToVPS "debug.php" "$VPSPath/"
Copy-ToVPS "vps_structure_checker.php" "$VPSPath/"

# Set proper permissions
Write-Host "🔐 Setting file permissions..." -ForegroundColor Blue
Invoke-SSHCommand "chown -R www-data:www-data $VPSPath"
Invoke-SSHCommand "find $VPSPath -type d -exec chmod 755 {} \;"
Invoke-SSHCommand "find $VPSPath -type f -exec chmod 644 {} \;"
Invoke-SSHCommand "chmod 755 $VPSPath/backend/handlers/*.php"
Invoke-SSHCommand "chmod 777 $VPSPath/uploads"

# Test Apache configuration
Write-Host "🔧 Testing Apache configuration..." -ForegroundColor Blue
Invoke-SSHCommand "apache2ctl configtest"

# Restart Apache if needed
$restartApache = Read-Host "Do you want to restart Apache? (y/N)"
if ($restartApache -eq "y" -or $restartApache -eq "Y") {
    Write-Host "🔄 Restarting Apache..." -ForegroundColor Blue
    Invoke-SSHCommand "systemctl restart apache2"
    Write-Host "✅ Apache restarted" -ForegroundColor Green
}

# Final verification
Write-Host "🔍 Running final verification..." -ForegroundColor Blue
Invoke-SSHCommand "ls -la $VPSPath/ | head -10"
Invoke-SSHCommand "ls -la $VPSPath/backend/handlers/ | head -5"

Write-Host ""
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Visit https://fanbet247.xyz/vps_structure_checker.php to verify deployment" -ForegroundColor White
Write-Host "2. Visit https://fanbet247.xyz/debug.php to check system status" -ForegroundColor White
Write-Host "3. Test your main site at https://fanbet247.xyz" -ForegroundColor White
Write-Host ""
Write-Host "🔧 If you encounter issues:" -ForegroundColor Yellow
Write-Host "- Check Apache error logs: ssh $VPSUser@$VPSHost 'tail -f /var/log/apache2/error.log'" -ForegroundColor White
Write-Host "- Check file permissions: ssh $VPSUser@$VPSHost 'ls -la $VPSPath'" -ForegroundColor White
Write-Host "- Verify .htaccess: ssh $VPSUser@$VPSHost 'cat $VPSPath/.htaccess'" -ForegroundColor White
