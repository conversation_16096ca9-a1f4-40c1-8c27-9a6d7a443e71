<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get request parameters
    $requestId = $_GET['request_id'] ?? null;
    $userId = $_GET['user_id'] ?? null;

    if (!$requestId || !$userId) {
        throw new Exception('Missing required parameters');
    }

    // Verify the request belongs to the user
    $stmt = $conn->prepare("
        SELECT proof_image 
        FROM credit_requests 
        WHERE request_id = :request_id 
        AND user_id = :user_id
    ");
    
    $stmt->execute([
        ':request_id' => $requestId,
        ':user_id' => $userId
    ]);
    
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$result) {
        throw new Exception('Proof image not found or unauthorized access');
    }

    $imagePath = '../../' . $result['proof_image'];
    
    if (!file_exists($imagePath)) {
        throw new Exception('Image file not found');
    }

    // Get image mime type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $imagePath);
    finfo_close($finfo);

    // Set proper content type header
    header("Content-Type: " . $mimeType);
    
    // Output the image
    readfile($imagePath);
    exit;

} catch (Exception $e) {
    header("Content-Type: application/json; charset=UTF-8");
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 