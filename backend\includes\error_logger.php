<?php
// Debug mode configuration
define('DEBUG_MODE', true); // Set to false in production

// Initialize error logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

// Create logs directory if it doesn't exist
if (!file_exists(__DIR__ . '/../logs')) {
    mkdir(__DIR__ . '/../logs', 0777, true);
}

// Custom error handler
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    $errorDetails = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => getErrorType($errno),
        'message' => $errstr,
        'file' => $errfile,
        'line' => $errline
    ];

    // Log to file
    error_log(json_encode($errorDetails) . "\n", 3, __DIR__ . '/../logs/php_errors.log');

    // Don't execute PHP's internal error handler
    return true;
}

// Get error type string
function getErrorType($errno) {
    switch ($errno) {
        case E_ERROR:
            return 'E_ERROR';
        case E_WARNING:
            return 'E_WARNING';
        case E_PARSE:
            return 'E_PARSE';
        case E_NOTICE:
            return 'E_NOTICE';
        case E_CORE_ERROR:
            return 'E_CORE_ERROR';
        case E_CORE_WARNING:
            return 'E_CORE_WARNING';
        case E_COMPILE_ERROR:
            return 'E_COMPILE_ERROR';
        case E_COMPILE_WARNING:
            return 'E_COMPILE_WARNING';
        case E_USER_ERROR:
            return 'E_USER_ERROR';
        case E_USER_WARNING:
            return 'E_USER_WARNING';
        case E_USER_NOTICE:
            return 'E_USER_NOTICE';
        case E_STRICT:
            return 'E_STRICT';
        case E_RECOVERABLE_ERROR:
            return 'E_RECOVERABLE_ERROR';
        case E_DEPRECATED:
            return 'E_DEPRECATED';
        case E_USER_DEPRECATED:
            return 'E_USER_DEPRECATED';
        default:
            return 'UNKNOWN';
    }
}

// Set custom error handler
set_error_handler('customErrorHandler');

// Custom exception handler
function customExceptionHandler($exception) {
    $errorDetails = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => get_class($exception),
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ];

    // Log to file
    error_log(json_encode($errorDetails) . "\n", 3, __DIR__ . '/../logs/php_exceptions.log');

    // If in debug mode, show detailed error
    if (DEBUG_MODE) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $exception->getMessage(),
            'debug_info' => $errorDetails
        ]);
    } else {
        // Generic error in production
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'An internal server error occurred'
        ]);
    }
}

// Set custom exception handler
set_exception_handler('customExceptionHandler');

// Shutdown function to catch fatal errors
function shutdownHandler() {
    $error = error_get_last();
    if ($error !== NULL && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
        $errorDetails = [
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => 'FATAL_ERROR',
            'message' => $error['message'],
            'file' => $error['file'],
            'line' => $error['line']
        ];

        // Log to file
        error_log(json_encode($errorDetails) . "\n", 3, __DIR__ . '/../logs/php_fatal_errors.log');

        // If in debug mode, show detailed error
        if (DEBUG_MODE) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'A fatal error occurred',
                'debug_info' => $errorDetails
            ]);
        } else {
            // Generic error in production
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'An internal server error occurred'
            ]);
        }
    }
}

// Register shutdown function
register_shutdown_function('shutdownHandler');
