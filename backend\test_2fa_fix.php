<?php
require_once 'vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

echo "🧪 Testing 2FA Fix\n";
echo "==================\n\n";

try {
    $google2fa = new Google2FA();
    
    // Generate a test secret
    $secretKey = $google2fa->generateSecretKey();
    echo "🔑 Test Secret Key: $secretKey\n";
    
    // Generate current TOTP code
    $currentCode = $google2fa->getCurrentOtp($secretKey);
    echo "📱 Current TOTP Code: $currentCode\n";
    echo "⏰ Current Time: " . date('Y-m-d H:i:s') . "\n";
    echo "⏰ Unix Timestamp: " . time() . "\n\n";
    
    // Test OLD method (setWindow + verifyKey)
    echo "🔍 Testing OLD method (setWindow + verifyKey):\n";
    $google2fa->setWindow(2);
    $isValidOld = $google2fa->verifyKey($secretKey, $currentCode);
    echo "   Result: " . ($isValidOld ? "✅ SUCCESS" : "❌ FAILED") . "\n\n";
    
    // Test NEW method (verifyKey with window parameter)
    echo "🔍 Testing NEW method (verifyKey with window parameter):\n";
    $window = 2;
    $isValidNew = $google2fa->verifyKey($secretKey, $currentCode, $window);
    echo "   Result: " . ($isValidNew ? "✅ SUCCESS" : "❌ FAILED") . "\n\n";
    
    // Test with different windows
    echo "🔍 Testing different window sizes:\n";
    for ($w = 0; $w <= 4; $w++) {
        $isValid = $google2fa->verifyKey($secretKey, $currentCode, $w);
        echo "   Window $w: " . ($isValid ? "✅" : "❌") . "\n";
    }
    
    echo "\n🎯 Conclusion:\n";
    if ($isValidNew) {
        echo "✅ NEW method works! The fix should resolve the 2FA verification issue.\n";
    } else {
        echo "❌ NEW method failed. There may be other issues.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
