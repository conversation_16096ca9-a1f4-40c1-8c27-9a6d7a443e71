<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define environment-specific database credentials
function getDatabaseConfig() {
    return [
        'host' => 'localhost',
        'db_name' => 'fanbet247',
        'username' => 'root',
        'password' => 'root'  // Update this with your actual database password
    ];
}

// Initialize database connection
function initializeDatabase() {
    $config = getDatabaseConfig();
    
    try {
        $conn = new PDO(
            "mysql:host={$config['host']};dbname={$config['db_name']}",
            $config['username'],
            $config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        );
        return $conn;
    } catch(PDOException $e) {
        error_log("Database Connection Error: " . $e->getMessage());
        return null;
    }
}

// Global connection variable
$conn = initializeDatabase();

// Function to get database connection
function getDBConnection() {
    global $conn;
    
    // If connection is null, try to reinitialize
    if ($conn === null) {
        $conn = initializeDatabase();
    }
    
    return $conn;
}

// Optional: Function to check connection status
function checkDatabaseConnection() {
    try {
        $conn = getDBConnection();
        if ($conn !== null) {
            $stmt = $conn->query("SELECT 1");
            return $stmt !== false;
        }
        return false;
    } catch(PDOException $e) {
        return false;
    }
}
?>