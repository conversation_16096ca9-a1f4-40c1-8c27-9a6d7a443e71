/**
 * Currency Selector Component Styles
 * 
 * Provides styling for currency selection components including
 * main selector, quick selector, and currency info display.
 */

/* Base Currency Selector */
.currency-selector {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.currency-selector.loading {
    opacity: 0.7;
    pointer-events: none;
}

.currency-selector.disabled {
    background: #f8f9fa;
    opacity: 0.6;
}

/* Selector Header */
.selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.selector-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.current-currency {
    font-size: 0.9rem;
    color: #6c757d;
    background: #e9ecef;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

/* Selector Wrapper */
.selector-wrapper {
    position: relative;
    margin-bottom: 1rem;
}

.currency-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #ced4da;
    border-radius: 8px;
    font-size: 1rem;
    background: #ffffff;
    color: #495057;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.currency-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.currency-select:disabled {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.updating-indicator {
    position: absolute;
    right: 2.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: #007bff;
}

/* Loading Placeholder */
.loading-placeholder {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    font-style: italic;
}

/* Conversion Preview */
.conversion-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.conversion-preview h4 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
}

.preview-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #ffffff;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 0.9rem;
}

.fancoin-amount {
    color: #007bff;
    font-weight: 600;
}

.arrow {
    color: #6c757d;
    margin: 0 0.25rem;
}

.converted-amount {
    color: #28a745;
    font-weight: 600;
}

/* Selector Messages */
.selector-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.selector-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.selector-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.selector-message.disabled {
    background: #e2e3e5;
    color: #6c757d;
    border: 1px solid #d6d8db;
    text-align: center;
    justify-content: center;
}

/* Selector Help */
.selector-help {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
}

.selector-help p {
    margin: 0;
    color: #1565c0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Currency Quick Selector */
.currency-quick-selector {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.quick-select {
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.875rem;
    background: #ffffff;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1em 1em;
    padding-right: 2rem;
    min-width: 80px;
}

.quick-select:hover {
    border-color: #007bff;
}

.quick-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.quick-select:disabled {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.updating-icon {
    color: #007bff;
    font-size: 0.875rem;
}

/* Currency Info */
.currency-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.info-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.currency-name {
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
}

.currency-code {
    color: #6c757d;
    font-size: 0.9rem;
}

.exchange-rate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.rate-label {
    color: #6c757d;
}

.rate-value {
    font-weight: 600;
    color: #28a745;
}

.rate-updated {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #6c757d;
}

.update-label {
    font-weight: 500;
}

.update-time {
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .currency-selector {
        padding: 1rem;
    }
    
    .selector-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .preview-grid {
        grid-template-columns: 1fr;
    }
    
    .preview-item {
        justify-content: space-between;
    }
    
    .exchange-rate,
    .rate-updated {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

@media (max-width: 480px) {
    .currency-selector {
        padding: 0.75rem;
    }
    
    .selector-label {
        font-size: 1rem;
    }
    
    .currency-select {
        padding: 0.625rem 0.875rem;
        font-size: 0.9rem;
    }
    
    .conversion-preview {
        padding: 0.75rem;
    }
    
    .selector-help {
        padding: 0.75rem;
    }
}
