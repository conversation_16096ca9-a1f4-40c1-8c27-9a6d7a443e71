<?php
// Execute the sidebar logo setting addition
try {
    $conn = new PDO(
        "mysql:host=localhost;dbname=fanbet247",
        'root',
        'root',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    if (!$conn) {
        die("Database connection failed");
    }
    
    echo "Adding sidebar_logo setting to general_settings table...\n";
    
    // Execute the SQL to add sidebar_logo setting
    $sql = "INSERT IGNORE INTO general_settings (setting_name, setting_value, description, created_at, updated_at) 
            VALUES ('sidebar_logo', '', 'Path to the sidebar logo for user dashboard', NOW(), NOW())";
    
    $conn->exec($sql);
    echo "✓ Sidebar logo setting added successfully\n";
    
    // Create upload directory if it doesn't exist
    $uploadDir = '../uploads/sidebar_logo/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
        echo "✓ Created sidebar logo upload directory\n";
    } else {
        echo "✓ Sidebar logo upload directory already exists\n";
    }
    
    echo "Setup completed successfully!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
