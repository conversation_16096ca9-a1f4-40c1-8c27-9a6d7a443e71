<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$status = $_POST['status'] ?? '';
$startDate = $_POST['startDate'] ?? '';
$endDate = $_POST['endDate'] ?? '';

try {
    $sql = "UPDATE bets b
            JOIN challenges c ON b.challenge_id = c.challenge_id
            SET b.bet_status = ?
            WHERE b.created_at BETWEEN ? AND ?
            AND c.end_time BETWEEN ? AND ?";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$status, $startDate, $endDate, $startDate, $endDate]);

    $updatedCount = $stmt->rowCount();

    echo json_encode([
        'success' => true,
        'message' => "Updated $updatedCount bets to status: $status",
        'updated_count' => $updatedCount
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error updating bet status: ' . $e->getMessage()
    ]);
}