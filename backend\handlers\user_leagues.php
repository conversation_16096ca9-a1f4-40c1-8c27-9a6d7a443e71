<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
    exit();
}

try {
    // Get user ID from request
    $userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
    if (!$userId) {
        jsonResponse(400, 'Invalid or missing user ID');
    }

    // Check if user exists
    $userQuery = "SELECT user_id FROM users WHERE user_id = ?";
    $stmt = $conn->prepare($userQuery);
    $stmt->execute([$userId]);
    if (!$stmt->fetch()) {
        jsonResponse(404, 'User not found');
    }

    // Get user's leagues with detailed information
    $query = "SELECT 
                lm.*, 
                l.name,
                l.status as league_status,
                l.min_bet_amount,
                l.max_bet_amount,
                ls.season_name,
                ls.start_date,
                ls.end_date
              FROM league_memberships lm
              JOIN leagues l ON lm.league_id = l.league_id
              LEFT JOIN league_seasons ls ON lm.season_id = ls.season_id
              WHERE lm.user_id = ?
              ORDER BY lm.join_date DESC";
    
    $stmt = $conn->prepare($query);
    $stmt->execute([$userId]);
    $leagues = $stmt->fetchAll(PDO::FETCH_ASSOC);

    jsonResponse(200, 'User leagues retrieved successfully', $leagues);
} catch (PDOException $e) {
    error_log("Database error in user_leagues.php: " . $e->getMessage());
    jsonResponse(500, 'Database error occurred: ' . $e->getMessage());
} catch (Exception $e) {
    error_log("General error in user_leagues.php: " . $e->getMessage());
    jsonResponse(500, 'An error occurred: ' . $e->getMessage());
}
