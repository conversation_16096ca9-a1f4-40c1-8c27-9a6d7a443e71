<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    $adminId = 1; // superadmin
    $newPassword = 'admin123';
    $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("UPDATE admins SET password_hash = ? WHERE admin_id = ?");
    $stmt->execute([$passwordHash, $adminId]);
    
    if ($stmt->rowCount() > 0) {
        echo "Superadmin password reset successfully!\n";
        echo "Username: superadmin\n";
        echo "Email: <EMAIL>\n";
        echo "New Password: $newPassword\n";
    } else {
        echo "Failed to reset password or admin not found.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
