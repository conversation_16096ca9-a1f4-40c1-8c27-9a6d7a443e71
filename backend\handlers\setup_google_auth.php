<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

include_once '../includes/db_connect.php';
require '../vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

try {
    $conn = getDBConnection();
    
    // Get request data
    $data = json_decode(file_get_contents("php://input"));
    
    // Validate required fields
    if (!isset($data->user_id)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "User ID is required"
        ]);
        exit;
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT user_id, username, email FROM users WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $data->user_id);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode([
            "success" => false,
            "message" => "User not found"
        ]);
        exit;
    }
    
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Check if Google Auth is already set up
    $stmt = $conn->prepare("
        SELECT id FROM user_2fa 
        WHERE user_id = :user_id AND auth_type = 'google_auth' AND is_enabled = 1
    ");
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Google Authenticator is already set up for this user"
        ]);
        exit;
    }
    
    // Generate new secret key
    $google2fa = new Google2FA();
    $secretKey = $google2fa->generateSecretKey();
    
    // Generate backup codes
    $backupCodes = [];
    for ($i = 0; $i < 8; $i++) {
        $backupCodes[] = bin2hex(random_bytes(4));
    }
    $backupCodesJson = json_encode($backupCodes);
    
    // Store secret key and backup codes
    $stmt = $conn->prepare("
        INSERT INTO user_2fa (user_id, secret_key, auth_type, backup_codes, is_enabled) 
        VALUES (:user_id, :secret_key, 'google_auth', :backup_codes, 0)
        ON DUPLICATE KEY UPDATE 
        secret_key = :secret_key, 
        backup_codes = :backup_codes, 
        is_enabled = 0
    ");
    
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->bindParam(':secret_key', $secretKey);
    $stmt->bindParam(':backup_codes', $backupCodesJson);
    
    if (!$stmt->execute()) {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Failed to set up Google Authenticator"
        ]);
        exit;
    }
    
    // Generate QR code URL
    $qrCodeUrl = $google2fa->getQRCodeUrl(
        'FanBet247',
        $user['email'],
        $secretKey
    );
    
    http_response_code(200);
    echo json_encode([
        "success" => true,
        "message" => "Google Authenticator setup initiated",
        "secretKey" => $secretKey,
        "qrCodeUrl" => $qrCodeUrl,
        "backupCodes" => $backupCodes
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Error: " . $e->getMessage()
    ]);
}
?>
