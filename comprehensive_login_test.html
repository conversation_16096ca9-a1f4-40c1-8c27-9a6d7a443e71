<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FanBet247 Comprehensive Login Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .test-card {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-card h3 { margin-top: 0; color: #495057; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background: linear-gradient(145deg, #3498db, #2980b9);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 12px rgba(0,0,0,0.2); }
        .btn.success { background: linear-gradient(145deg, #27ae60, #229954); }
        .btn.danger { background: linear-gradient(145deg, #e74c3c, #c0392b); }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .result.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .result.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .config-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.online { background: #28a745; }
        .status-indicator.offline { background: #dc3545; }
        .status-indicator.unknown { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 FanBet247 Comprehensive Login Test</h1>
        <p style="text-align: center; color: #7f8c8d;">
            <strong>Generated:</strong> <span id="timestamp"></span> | 
            <strong>Environment:</strong> <span id="environment"></span>
        </p>

        <!-- Configuration Detection -->
        <h2>🔍 Configuration Detection</h2>
        <div class="config-info">
            <div><strong>Current URL:</strong> <span id="currentUrl"></span></div>
            <div><strong>Detected API Base URL:</strong> <span id="detectedApiUrl"></span></div>
            <div><strong>Environment:</strong> <span id="envType"></span></div>
            <div><strong>Connection Status:</strong> <span class="status-indicator" id="connectionStatus"></span><span id="connectionText"></span></div>
        </div>

        <!-- Test Grid -->
        <div class="test-grid">
            <!-- Admin Login Test -->
            <div class="test-card">
                <h3>👤 Admin Login Test</h3>
                <div class="form-group">
                    <label>Username/Email:</label>
                    <input type="text" id="adminIdentifier" value="admin" placeholder="admin">
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" id="adminPassword" value="loving12" placeholder="loving12">
                </div>
                <div class="form-group">
                    <label>Test Method:</label>
                    <select id="adminTestMethod">
                        <option value="fetch">Fetch API</option>
                        <option value="axios">Axios (if available)</option>
                        <option value="xhr">XMLHttpRequest</option>
                    </select>
                </div>
                <button class="btn" onclick="testAdminLogin()">🔐 Test Admin Login</button>
                <button class="btn success" onclick="testAdminEndpoint()">📡 Test Endpoint</button>
                <div id="adminResult" class="result" style="display: none;"></div>
            </div>

            <!-- User Login Test -->
            <div class="test-card">
                <h3>👥 User Login Test</h3>
                <div class="form-group">
                    <label>Username:</label>
                    <input type="text" id="userUsername" value="testuser" placeholder="testuser">
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" id="userPassword" value="password123" placeholder="password123">
                </div>
                <div class="form-group">
                    <label>Test Method:</label>
                    <select id="userTestMethod">
                        <option value="fetch">Fetch API</option>
                        <option value="axios">Axios (if available)</option>
                        <option value="xhr">XMLHttpRequest</option>
                    </select>
                </div>
                <button class="btn" onclick="testUserLogin()">🔐 Test User Login</button>
                <button class="btn success" onclick="testUserEndpoint()">📡 Test Endpoint</button>
                <div id="userResult" class="result" style="display: none;"></div>
            </div>

            <!-- API Connectivity Test -->
            <div class="test-card">
                <h3>🌐 API Connectivity Test</h3>
                <button class="btn" onclick="testAllEndpoints()">🧪 Test All Endpoints</button>
                <button class="btn success" onclick="testCORS()">🔗 Test CORS</button>
                <button class="btn danger" onclick="testNetworkDiagnostics()">🔍 Network Diagnostics</button>
                <div id="connectivityResult" class="result" style="display: none;"></div>
            </div>

            <!-- Configuration Test -->
            <div class="test-card">
                <h3>⚙️ Configuration Test</h3>
                <button class="btn" onclick="testConfiguration()">🔧 Test Config</button>
                <button class="btn success" onclick="testEnvironment()">🌍 Test Environment</button>
                <button class="btn danger" onclick="downloadReport()">📋 Download Report</button>
                <div id="configResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <!-- Comprehensive Results -->
        <h2>📊 Test Results Summary</h2>
        <div id="summaryResults" class="result info" style="display: none;"></div>
    </div>

    <script>
        // Configuration Detection
        const currentUrl = window.location.href;
        const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        const isProduction = !isDevelopment;
        
        // Detect API Base URL (same logic as config.js)
        function detectApiBaseUrl() {
            const projectPath = '';
            if (isDevelopment && window.location.port === '3000') {
                return '/backend/handlers';
            }
            return '/backend/handlers';
        }

        const apiBaseUrl = detectApiBaseUrl();

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('timestamp').textContent = new Date().toLocaleString();
            document.getElementById('currentUrl').textContent = currentUrl;
            document.getElementById('detectedApiUrl').textContent = apiBaseUrl;
            document.getElementById('environment').textContent = isDevelopment ? 'Development' : 'Production';
            document.getElementById('envType').textContent = isDevelopment ? 'Development' : 'Production';
            
            // Test initial connection
            testInitialConnection();
        });

        // Test initial connection
        async function testInitialConnection() {
            try {
                const response = await fetch('/debug.php?action=ping');
                if (response.ok) {
                    document.getElementById('connectionStatus').className = 'status-indicator online';
                    document.getElementById('connectionText').textContent = 'Online';
                } else {
                    document.getElementById('connectionStatus').className = 'status-indicator offline';
                    document.getElementById('connectionText').textContent = 'Offline';
                }
            } catch (error) {
                document.getElementById('connectionStatus').className = 'status-indicator offline';
                document.getElementById('connectionText').textContent = 'Connection Failed';
            }
        }

        // Generic API call function
        async function makeApiCall(url, method = 'GET', data = null, requestMethod = 'fetch') {
            const startTime = Date.now();
            let result = {
                success: false,
                data: null,
                error: null,
                timing: 0,
                method: requestMethod,
                url: url
            };

            try {
                let response;
                
                if (requestMethod === 'fetch') {
                    const options = {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    };
                    
                    if (data && method !== 'GET') {
                        options.body = JSON.stringify(data);
                    }
                    
                    response = await fetch(url, options);
                    result.data = await response.text();
                    
                    try {
                        result.data = JSON.parse(result.data);
                    } catch (e) {
                        // Keep as text if not JSON
                    }
                    
                    result.success = response.ok;
                    result.status = response.status;
                    result.statusText = response.statusText;
                    
                } else if (requestMethod === 'xhr') {
                    return new Promise((resolve) => {
                        const xhr = new XMLHttpRequest();
                        xhr.open(method, url, true);
                        xhr.setRequestHeader('Content-Type', 'application/json');
                        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                        
                        xhr.onreadystatechange = function() {
                            if (xhr.readyState === 4) {
                                result.timing = Date.now() - startTime;
                                result.status = xhr.status;
                                result.statusText = xhr.statusText;
                                result.success = xhr.status >= 200 && xhr.status < 300;
                                
                                try {
                                    result.data = JSON.parse(xhr.responseText);
                                } catch (e) {
                                    result.data = xhr.responseText;
                                }
                                
                                resolve(result);
                            }
                        };
                        
                        xhr.onerror = function() {
                            result.timing = Date.now() - startTime;
                            result.error = 'Network error';
                            resolve(result);
                        };
                        
                        if (data && method !== 'GET') {
                            xhr.send(JSON.stringify(data));
                        } else {
                            xhr.send();
                        }
                    });
                }
                
            } catch (error) {
                result.error = error.message;
            }
            
            result.timing = Date.now() - startTime;
            return result;
        }

        // Test Admin Login
        async function testAdminLogin() {
            const identifier = document.getElementById('adminIdentifier').value;
            const password = document.getElementById('adminPassword').value;
            const method = document.getElementById('adminTestMethod').value;
            const resultDiv = document.getElementById('adminResult');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Testing admin login...';
            
            const loginData = { identifier, password };
            const result = await makeApiCall(`${apiBaseUrl}/admin_login_handler.php`, 'POST', loginData, method);
            
            let output = `Admin Login Test Results:\n`;
            output += `Method: ${result.method}\n`;
            output += `URL: ${result.url}\n`;
            output += `Status: ${result.status} ${result.statusText}\n`;
            output += `Timing: ${result.timing}ms\n`;
            output += `Success: ${result.success}\n\n`;
            
            if (result.error) {
                output += `Error: ${result.error}\n\n`;
                resultDiv.className = 'result error';
            } else {
                output += `Response:\n${JSON.stringify(result.data, null, 2)}\n`;
                resultDiv.className = result.success ? 'result success' : 'result error';
            }
            
            resultDiv.textContent = output;
        }

        // Test User Login
        async function testUserLogin() {
            const username = document.getElementById('userUsername').value;
            const password = document.getElementById('userPassword').value;
            const method = document.getElementById('userTestMethod').value;
            const resultDiv = document.getElementById('userResult');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Testing user login...';
            
            const loginData = { username, password };
            const result = await makeApiCall(`${apiBaseUrl}/login.php`, 'POST', loginData, method);
            
            let output = `User Login Test Results:\n`;
            output += `Method: ${result.method}\n`;
            output += `URL: ${result.url}\n`;
            output += `Status: ${result.status} ${result.statusText}\n`;
            output += `Timing: ${result.timing}ms\n`;
            output += `Success: ${result.success}\n\n`;
            
            if (result.error) {
                output += `Error: ${result.error}\n\n`;
                resultDiv.className = 'result error';
            } else {
                output += `Response:\n${JSON.stringify(result.data, null, 2)}\n`;
                resultDiv.className = result.success ? 'result success' : 'result error';
            }
            
            resultDiv.textContent = output;
        }

        // Test Admin Endpoint
        async function testAdminEndpoint() {
            const resultDiv = document.getElementById('adminResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Testing admin endpoint accessibility...';
            
            const result = await makeApiCall(`${apiBaseUrl}/admin_login_handler.php`, 'GET');
            
            let output = `Admin Endpoint Test:\n`;
            output += `URL: ${result.url}\n`;
            output += `Status: ${result.status} ${result.statusText}\n`;
            output += `Accessible: ${result.success}\n`;
            output += `Response: ${typeof result.data === 'string' ? result.data.substring(0, 200) + '...' : JSON.stringify(result.data)}\n`;
            
            resultDiv.className = result.success ? 'result success' : 'result error';
            resultDiv.textContent = output;
        }

        // Test User Endpoint
        async function testUserEndpoint() {
            const resultDiv = document.getElementById('userResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Testing user endpoint accessibility...';
            
            const result = await makeApiCall(`${apiBaseUrl}/login.php`, 'GET');
            
            let output = `User Endpoint Test:\n`;
            output += `URL: ${result.url}\n`;
            output += `Status: ${result.status} ${result.statusText}\n`;
            output += `Accessible: ${result.success}\n`;
            output += `Response: ${typeof result.data === 'string' ? result.data.substring(0, 200) + '...' : JSON.stringify(result.data)}\n`;
            
            resultDiv.className = result.success ? 'result success' : 'result error';
            resultDiv.textContent = output;
        }

        // Test All Endpoints
        async function testAllEndpoints() {
            const resultDiv = document.getElementById('connectivityResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Testing all endpoints...';
            
            const endpoints = [
                'homepage_data.php',
                'get_currencies.php',
                'get_site_config.php',
                'admin_login_handler.php',
                'login.php'
            ];
            
            let output = 'Endpoint Connectivity Test:\n\n';
            
            for (const endpoint of endpoints) {
                const result = await makeApiCall(`${apiBaseUrl}/${endpoint}`, 'GET');
                const status = result.success ? '✅' : '❌';
                output += `${status} ${endpoint}: ${result.status} (${result.timing}ms)\n`;
            }
            
            resultDiv.className = 'result info';
            resultDiv.textContent = output;
        }

        // Test CORS
        async function testCORS() {
            const resultDiv = document.getElementById('connectivityResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Testing CORS configuration...';
            
            let output = 'CORS Test Results:\n\n';
            
            try {
                const response = await fetch(`${apiBaseUrl}/admin_login_handler.php`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                output += `OPTIONS Request: ${response.status} ${response.statusText}\n`;
                output += `CORS Headers:\n`;
                
                for (const [key, value] of response.headers.entries()) {
                    if (key.toLowerCase().includes('access-control')) {
                        output += `  ${key}: ${value}\n`;
                    }
                }
                
                resultDiv.className = 'result success';
            } catch (error) {
                output += `CORS Test Failed: ${error.message}\n`;
                resultDiv.className = 'result error';
            }
            
            resultDiv.textContent = output;
        }

        // Test Network Diagnostics
        async function testNetworkDiagnostics() {
            const resultDiv = document.getElementById('connectivityResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 Running network diagnostics...';
            
            let output = 'Network Diagnostics:\n\n';
            
            // Test basic connectivity
            output += `Current Domain: ${window.location.hostname}\n`;
            output += `Current Port: ${window.location.port || (window.location.protocol === 'https:' ? '443' : '80')}\n`;
            output += `Protocol: ${window.location.protocol}\n`;
            output += `User Agent: ${navigator.userAgent}\n\n`;
            
            // Test DNS resolution
            try {
                const start = Date.now();
                await fetch('/favicon.ico', { method: 'HEAD' });
                output += `DNS Resolution: ${Date.now() - start}ms\n`;
            } catch (error) {
                output += `DNS Resolution: Failed - ${error.message}\n`;
            }
            
            // Test backend accessibility
            try {
                const start = Date.now();
                const response = await fetch('/debug.php?action=ping');
                output += `Backend Ping: ${Date.now() - start}ms (${response.status})\n`;
            } catch (error) {
                output += `Backend Ping: Failed - ${error.message}\n`;
            }
            
            resultDiv.className = 'result info';
            resultDiv.textContent = output;
        }

        // Test Configuration
        async function testConfiguration() {
            const resultDiv = document.getElementById('configResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            
            let output = 'Configuration Test:\n\n';
            output += `Detected API Base URL: ${apiBaseUrl}\n`;
            output += `Current URL: ${currentUrl}\n`;
            output += `Environment: ${isDevelopment ? 'Development' : 'Production'}\n`;
            output += `Port: ${window.location.port}\n`;
            output += `Protocol: ${window.location.protocol}\n\n`;
            
            // Test if axios is available
            output += `Axios Available: ${typeof axios !== 'undefined'}\n`;
            output += `Fetch Available: ${typeof fetch !== 'undefined'}\n`;
            output += `XMLHttpRequest Available: ${typeof XMLHttpRequest !== 'undefined'}\n\n`;
            
            // Test localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                output += `LocalStorage: Working\n`;
            } catch (error) {
                output += `LocalStorage: Failed - ${error.message}\n`;
            }
            
            resultDiv.textContent = output;
        }

        // Test Environment
        async function testEnvironment() {
            const resultDiv = document.getElementById('configResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            
            let output = 'Environment Test:\n\n';
            
            // Test server response
            try {
                const response = await fetch('/debug.php');
                const text = await response.text();
                output += `Server Response: ${response.status} ${response.statusText}\n`;
                output += `Content Length: ${text.length} characters\n`;
                output += `Content Type: ${response.headers.get('content-type')}\n\n`;
            } catch (error) {
                output += `Server Test Failed: ${error.message}\n\n`;
            }
            
            // Test API structure
            const testUrls = [
                '/backend/handlers/',
                '/backend/includes/',
                '/static/',
                '/uploads/'
            ];
            
            output += 'Directory Structure Test:\n';
            for (const url of testUrls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    output += `${url}: ${response.status}\n`;
                } catch (error) {
                    output += `${url}: Failed\n`;
                }
            }
            
            resultDiv.textContent = output;
        }

        // Download Report
        function downloadReport() {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const report = document.documentElement.outerHTML;
            
            const blob = new Blob([report], { type: 'text/html' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `fanbet247_login_test_report_${timestamp}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
