# FanBet247 Database Analysis Report
Generated on: 2025-07-18 15:06:53

## 🔍 Database Analysis Overview
This report provides comprehensive analysis of the FanBet247 database structure.
Focus: Authentication system tables and relationships

## 📊 Database Information
**Database Name:** fanbet247
**MySQL Version:** 5.7.24
**Character Set:** utf8
**Total Tables:** 52

## 📋 All Database Tables
| Table Name | Type | Rows | Size |
|------------|------|------|------|
| `achievements` | 📊 Data | 7 | 0.02 MB |
| `admin_2fa` | 🔐 Auth | 1 | 0.03 MB |
| `admin_auth_logs` | 🔐 Auth | 138 | 0.08 MB |
| `admin_auth_settings` | 📊 Data | 10 | 0.03 MB |
| `admin_auth_status` | 📊 Data | 2 |  MB |
| `admin_login_attempts` | 📊 Data | 0 | 0.05 MB |
| `admin_otp` | 🔐 Auth | 1 | 0.05 MB |
| `admin_recovery_codes` | 📊 Data | 0 | 0.06 MB |
| `admin_recovery_tokens` | 📊 Data | 0 | 0.06 MB |
| `adminactions` | 📊 Data | 6 | 0.03 MB |
| `admins` | 🔐 Auth | 2 | 0.08 MB |
| `bets` | 📊 Data | 19 | 0.14 MB |
| `challenges` | 📊 Data | 24 | 0.05 MB |
| `credit_requests` | 📊 Data | 9 | 0.06 MB |
| `currencies` | 📊 Data | 8 | 0.06 MB |
| `exchange_rates` | 📊 Data | 8 | 0.06 MB |
| `general_settings` | 📊 Data | 12 | 0.03 MB |
| `leaderboards` | 📊 Data | 0 | 0.05 MB |
| `league_audit_log` | 📊 Data | 0 | 0.08 MB |
| `league_history` | 📊 Data | 0 | 0.08 MB |
| `league_memberships` | 📊 Data | 7 | 0.13 MB |
| `league_memberships_backup` | 📊 Data | 6 | 0.02 MB |
| `league_seasons` | 📊 Data | 4 | 0.05 MB |
| `leagues` | 📊 Data | 11 | 0.06 MB |
| `messages` | 📊 Data | 34 | 0.06 MB |
| `payment_methods` | 📊 Data | 6 | 0.02 MB |
| `paymentverifications` | 📊 Data | 0 | 0.05 MB |
| `season_history` | 📊 Data | 0 | 0.05 MB |
| `seasons` | 📊 Data | 0 | 0.02 MB |
| `security_settings` | 📊 Data | 9 | 0.03 MB |
| `smtp_settings` | 🔐 Auth | 1 | 0.02 MB |
| `system_settings` | 📊 Data | 0 | 0.02 MB |
| `teams` | 📊 Data | 13 | 0.02 MB |
| `transactions` | 📊 Data | 117 | 0.09 MB |
| `user_2fa` | 🔐 Auth | 2 | 0.03 MB |
| `user_achievements` | 📊 Data | 0 | 0.03 MB |
| `user_activity_log` | 📊 Data | 25 | 0.03 MB |
| `user_auth_logs` | 🔐 Auth | 26 | 0.06 MB |
| `user_auth_settings` | 📊 Data | 1 | 0.03 MB |
| `user_friends` | 📊 Data | 6 | 0.05 MB |
| `user_league_stats` | 📊 Data | 7 | 0.06 MB |
| `user_league_stats_backup` | 📊 Data | 0 | 0.02 MB |
| `user_leagues` | 📊 Data | 7 | 0.05 MB |
| `user_leagues_backup` | 📊 Data | 6 | 0.02 MB |
| `user_login_attempts` | 📊 Data | 0 | 0.06 MB |
| `user_otp` | 🔐 Auth | 8 | 0.03 MB |
| `user_payment_methods` | 📊 Data | 0 | 0.05 MB |
| `user_sessions` | 🔐 Auth | 26 | 0.03 MB |
| `users` | 🔐 Auth | 14 | 0.16 MB |
| `withdrawal_limits` | 📊 Data | 3 | 0.03 MB |
| `withdrawal_notifications` | 📊 Data | 0 | 0.06 MB |
| `withdrawal_requests` | 📊 Data | 0 | 0.09 MB |

## 🔐 Authentication Tables Analysis
### 📊 Table: `users`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `user_id` | int(11) | NO | PRI |  | auto_increment |
| `username` | varchar(50) | NO | UNI |  |  |
| `full_name` | varchar(255) | NO |  |  |  |
| `password_hash` | varchar(255) | NO |  |  |  |
| `email` | varchar(100) | NO | UNI |  |  |
| `favorite_team` | varchar(100) | YES |  |  |  |
| `balance` | decimal(10,2) | YES |  | 0.00 |  |
| `created_at` | timestamp | NO |  | CURRENT_TIMESTAMP |  |
| `updated_at` | timestamp | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| `points` | int(11) | YES |  | 0 |  |
| `current_league_id` | int(11) | YES | MUL |  |  |
| `total_points` | int(11) | YES |  | 0 |  |
| `current_streak` | int(11) | YES |  | 0 |  |
| `highest_streak` | int(11) | YES |  | 0 |  |
| `current_season_id` | int(11) | YES | MUL |  |  |
| `last_active` | timestamp | YES | MUL |  |  |
| `role` | enum('user','admin') | NO |  | user |  |
| `wins` | int(11) | YES |  | 0 |  |
| `draws` | int(11) | YES |  | 0 |  |
| `losses` | int(11) | YES |  | 0 |  |
| `total_bets` | int(11) | YES |  | 0 |  |
| `last_bet_date` | timestamp | YES |  |  |  |
| `status` | enum('active','suspended','banned') | YES |  | active |  |
| `preferred_currency_id` | int(11) | YES | MUL | 1 |  |
| `user_level` | varchar(50) | YES |  | standard |  |
| `otp_enabled` | tinyint(1) | YES |  | 0 |  |
| `tfa_enabled` | tinyint(1) | YES |  | 0 |  |
| `auth_method` | enum('password_only','password_otp','password_2fa','password_otp_2fa') | YES |  | password_only |  |

**Row Count:** 14

**Sample Data (Top 3 rows, anonymized):**
| user_id | username | full_name | password_hash | email | favorite_team | balance | created_at | updated_at | points | current_league_id | total_points | current_streak | highest_streak | current_season_id | last_active | role | wins | draws | losses | total_bets | last_bet_date | status | preferred_currency_id | user_level | otp_enabled | tfa_enabled | auth_method |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | user*** | James Bellengi | ***hashed*** | user***@example.com | Manchester United | 5923206.00 | 2024-09-23 17:25:21 | 2025-06-07 10:01:25 | 9 | 7 | 9 | 0 | 0 | NULL | NULL | admin | 1 | 0 | 0 | 0 | NULL | suspended | 1 | standard | 123*** | 0 | password_only |
| 2 | user*** | Lil Wayne | ***hashed*** | user***@example.com | Everton | 5666.20 | 2024-09-23 17:44:57 | 2025-07-15 22:58:29 | 3 | 11 | 3 | 0 | 0 | NULL | 2025-07-15 22:58:29 | user | 1 | 0 | 4 | 3 | 2025-02-09 09:40:56 | active | 1 | standard | 123*** | 0 | password_only |
| 3 | user*** | James Link | ***hashed*** | user***@example.com | Manchester United | 8103.20 | 2024-09-24 19:28:36 | 2025-06-07 10:01:40 | 0 | 11 | 0 | 0 | 0 | NULL | NULL | user | 0 | 0 | 1 | 1 | 2025-02-09 08:31:04 | banned | 1 | standard | 123*** | 0 | password_only |

**Foreign Keys:**
- `current_league_id` → `leagues.league_id`
- `current_league_id` → `leagues.league_id`
- `current_season_id` → `seasons.season_id`
- `preferred_currency_id` → `currencies.id`
- `preferred_currency_id` → `currencies.id`
- `preferred_currency_id` → `currencies.id`

### 📊 Table: `user_otp`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `id` | int(11) | NO | PRI |  | auto_increment |
| `user_id` | int(11) | NO | MUL |  |  |
| `otp` | varchar(6) | NO |  |  |  |
| `expiry` | datetime | NO |  |  |  |
| `attempts` | int(11) | YES |  | 0 |  |
| `used` | tinyint(1) | YES |  | 0 |  |
| `created_at` | timestamp | NO |  | CURRENT_TIMESTAMP |  |

**Row Count:** 8

**Sample Data (Top 3 rows, anonymized):**
| id | user_id | otp | expiry | attempts | used | created_at |
|---|---|---|---|---|---|---|
| 1 | 11 | 123*** | 2025-06-30 22:41:28 | 0 | 1 | 2025-07-01 00:31:28 |
| 3 | 1 | 123*** | 2025-07-01 15:44:37 | 0 | 0 | 2025-07-01 17:39:37 |
| 6 | 4 | 123*** | 2025-07-01 15:46:35 | 0 | 1 | 2025-07-01 17:41:35 |

**Foreign Keys:**
- `user_id` → `users.user_id`

### 📊 Table: `user_auth_logs`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `id` | int(11) | NO | PRI |  | auto_increment |
| `user_id` | int(11) | NO | MUL |  |  |
| `auth_type` | enum('login','otp','2fa','logout','settings') | NO | MUL |  |  |
| `action` | varchar(50) | NO |  |  |  |
| `details` | json | YES |  |  |  |
| `ip_address` | varchar(45) | YES |  |  |  |
| `user_agent` | text | YES |  |  |  |
| `created_at` | timestamp | NO | MUL | CURRENT_TIMESTAMP |  |

**Row Count:** 26

**Sample Data (Top 3 rows, anonymized):**
| id | user_id | auth_type | action | details | ip_address | user_agent | created_at |
|---|---|---|---|---|---|---|---|
| 1 | 11 | otp | otp_enabled | {"test": true, "otp_enabled": true} | 127.0.0.1 | Test Script | 2025-06-30 23:54:29 |
| 2 | 11 | otp | otp_disabled | {"test": true, "otp_enabled": false} | 127.0.0.1 | Test Script | 2025-06-30 23:54:29 |
| 3 | 11 | otp | otp_enabled | {"otp_enabled": true, "new_auth_method": "passw... | ::1 | unknown | 2025-06-30 23:55:03 |

**Foreign Keys:**
- `user_id` → `users.user_id`

### 📊 Table: `user_2fa`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `id` | int(11) | NO | PRI |  | auto_increment |
| `user_id` | int(11) | NO | UNI |  |  |
| `secret_key` | varchar(255) | YES |  |  |  |
| `is_enabled` | tinyint(1) | NO |  | 0 |  |
| `auth_type` | enum('email_otp','google_auth') | NO |  | email_otp |  |
| `backup_codes` | text | YES |  |  |  |
| `setup_completed` | tinyint(1) | NO |  | 0 |  |
| `last_used` | timestamp | YES |  |  |  |
| `created_at` | timestamp | NO |  | CURRENT_TIMESTAMP |  |
| `updated_at` | timestamp | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

**Row Count:** 2

**Sample Data (Top 3 rows, anonymized):**
| id | user_id | secret_key | is_enabled | auth_type | backup_codes | setup_completed | last_used | created_at | updated_at |
|---|---|---|---|---|---|---|---|---|---|
| 1 | 11 | ***hidden*** | 0 | google_auth | ***hidden*** | 0 | NULL | 2025-07-01 00:15:18 | 2025-07-01 00:32:54 |
| 6 | 4 | ***hidden*** | 0 | google_auth | ***hidden*** | 0 | NULL | 2025-07-01 17:07:46 | 2025-07-01 17:07:46 |

**Foreign Keys:**
- `user_id` → `users.user_id`

### 📊 Table: `user_sessions`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `id` | int(11) | NO | PRI |  | auto_increment |
| `user_id` | int(11) | NO | MUL |  |  |
| `token` | varchar(255) | NO |  |  |  |
| `expires_at` | timestamp | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| `created_at` | timestamp | NO |  | CURRENT_TIMESTAMP |  |

**Row Count:** 26

**Sample Data (Top 3 rows, anonymized):**
| id | user_id | token | expires_at | created_at |
|---|---|---|---|---|
| 1 | 5 | 9a338cfbd9a2865e6bfb2cd1a46a022696d3bc61610c17f... | 2025-07-16 02:21:43 | 2025-07-15 02:21:43 |
| 2 | 5 | 3e05e72694d51c4fc86e775b099a01fd7fd5fe2b0ec2821... | 2025-07-16 02:23:18 | 2025-07-15 02:23:18 |
| 3 | 4 | 56a63e5477f4f33248a16dc7ef6c0e7dd9fa002fcc5ca84... | 2025-07-16 22:56:55 | 2025-07-15 22:56:55 |

**Foreign Keys:**
- `user_id` → `users.user_id`

### 📊 Table: `smtp_settings`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `id` | int(11) | NO | PRI |  | auto_increment |
| `host` | varchar(255) | NO |  | smtp.gmail.com |  |
| `port` | int(11) | NO |  | 587 |  |
| `username` | varchar(255) | NO |  |  |  |
| `password` | varchar(255) | NO |  |  |  |
| `encryption` | enum('none','ssl','tls') | NO |  | tls |  |
| `from_email` | varchar(255) | NO |  | <EMAIL> |  |
| `from_name` | varchar(255) | NO |  | FanBet247 |  |
| `is_active` | tinyint(1) | NO |  | 0 |  |
| `created_at` | timestamp | NO |  | CURRENT_TIMESTAMP |  |
| `updated_at` | timestamp | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

**Row Count:** 1

**Sample Data (Top 3 rows, anonymized):**
| id | host | port | username | password | encryption | from_email | from_name | is_active | created_at | updated_at |
|---|---|---|---|---|---|---|---|---|---|---|
| 1 | smtp.hostinger.com | 465 | user*** | ***hashed*** | ssl | user***@example.com | FanBet247 | 1 | 2025-06-05 09:22:43 | 2025-07-18 16:46:12 |

### 📊 Table: `admins`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `admin_id` | int(11) | NO | PRI |  | auto_increment |
| `username` | varchar(50) | NO | UNI |  |  |
| `password_hash` | varchar(255) | NO |  |  |  |
| `email` | varchar(100) | NO | UNI |  |  |
| `role` | enum('super_admin','moderator','support') | NO |  |  |  |
| `last_login` | timestamp | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| `created_at` | timestamp | NO |  | CURRENT_TIMESTAMP |  |
| `updated_at` | timestamp | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| `two_factor_enabled` | tinyint(1) | NO |  | 0 |  |
| `auth_method` | enum('password_only','otp','2fa') | NO |  | password_only |  |
| `last_2fa_setup` | timestamp | YES |  |  |  |
| `account_locked_until` | datetime | YES |  |  |  |
| `failed_login_attempts` | int(11) | NO |  | 0 |  |

**Row Count:** 2

**Sample Data (Top 3 rows, anonymized):**
| admin_id | username | password_hash | email | role | last_login | created_at | updated_at | two_factor_enabled | auth_method | last_2fa_setup | account_locked_until | failed_login_attempts |
|---|---|---|---|---|---|---|---|---|---|---|---|---|
| 1 | user*** | ***hashed*** | user***@example.com | super_admin | 2025-07-16 08:58:38 | 2024-09-06 10:47:14 | 2025-07-16 08:58:38 | 1 | 2fa | 2025-06-06 21:18:49 | NULL | 0 |
| 2 | user*** | ***hashed*** | user***@example.com | super_admin | 2025-06-05 00:32:52 | 2024-09-06 22:07:14 | 2025-06-05 00:32:52 | 0 | password_only | NULL | NULL | 0 |

### 📊 Table: `admin_2fa`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `id` | int(11) | NO | PRI |  | auto_increment |
| `admin_id` | int(11) | NO | UNI |  |  |
| `secret_key` | varchar(255) | YES |  |  |  |
| `is_enabled` | tinyint(1) | NO |  | 0 |  |
| `auth_type` | enum('email_otp','google_auth') | NO |  | email_otp |  |
| `backup_codes` | text | YES |  |  |  |
| `setup_completed` | tinyint(1) | NO |  | 0 |  |
| `last_used` | timestamp | YES |  |  |  |
| `created_at` | timestamp | NO |  | CURRENT_TIMESTAMP |  |
| `updated_at` | timestamp | NO |  | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

**Row Count:** 1

**Sample Data (Top 3 rows, anonymized):**
| id | admin_id | secret_key | is_enabled | auth_type | backup_codes | setup_completed | last_used | created_at | updated_at |
|---|---|---|---|---|---|---|---|---|---|
| 1 | 1 | ***hidden*** | 1 | google_auth | ***hidden*** | 1 | 2025-07-16 08:58:38 | 2025-06-06 20:10:32 | 2025-07-16 08:58:38 |

**Foreign Keys:**
- `admin_id` → `admins.admin_id`

### 📊 Table: `admin_otp`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `id` | int(11) | NO | PRI |  | auto_increment |
| `admin_id` | int(11) | NO | MUL |  |  |
| `otp` | varchar(10) | NO |  |  |  |
| `expires_at` | datetime | NO | MUL |  |  |
| `attempts` | int(11) | NO |  | 0 |  |
| `locked_until` | datetime | YES |  |  |  |
| `used` | tinyint(1) | NO |  | 0 |  |
| `created_at` | timestamp | NO |  | CURRENT_TIMESTAMP |  |

**Row Count:** 1

**Sample Data (Top 3 rows, anonymized):**
| id | admin_id | otp | expires_at | attempts | locked_until | used | created_at |
|---|---|---|---|---|---|---|---|
| 24 | 1 | 123*** | 2025-06-06 19:13:45 | 0 | NULL | 1 | 2025-06-06 21:08:45 |

**Foreign Keys:**
- `admin_id` → `admins.admin_id`

### 📊 Table: `admin_auth_logs`
**Structure:**
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| `id` | int(11) | NO | PRI |  | auto_increment |
| `admin_id` | int(11) | NO | MUL |  |  |
| `auth_type` | enum('password','otp','2fa','backup_code','recovery','config','security','test','system') | NO |  |  |  |
| `action` | varchar(50) | NO |  |  |  |
| `ip_address` | varchar(45) | YES |  |  |  |
| `user_agent` | text | YES |  |  |  |
| `details` | text | YES |  |  |  |
| `log_level` | enum('INFO','WARNING','ERROR','CRITICAL') | NO |  | INFO |  |
| `created_at` | timestamp | NO | MUL | CURRENT_TIMESTAMP |  |

**Row Count:** 138

**Sample Data (Top 3 rows, anonymized):**
| id | admin_id | auth_type | action | ip_address | user_agent | details | log_level | created_at |
|---|---|---|---|---|---|---|---|---|
| 1 | 1 | security | incident | ::1 | unknown | {"incident_type":"unauthorized_otp_request","de... | WARNING | 2025-06-06 20:09:47 |
| 2 | 1 | otp | otp_sent | ::1 | unknown | {"success":true,"timestamp":"2025-06-06 18:10:1... | INFO | 2025-06-06 20:10:11 |
| 3 | 1 | 2fa | 2fa_setup | ::1 | unknown | {"setup_initiated":true,"auth_type":"google_auth"} | INFO | 2025-06-06 20:10:32 |

**Foreign Keys:**
- `admin_id` → `admins.admin_id`

## 📧 SMTP Configuration Analysis
**Active SMTP Configuration:**
- Host: smtp.hostinger.com
- Port: 465
- Username: <EMAIL>
- Password: ***configured***
- Encryption: ssl
- From Email: <EMAIL>
- From Name: FanBet247
- Status: Active
- **Configuration Status:** ✅ Complete

## 👥 User Authentication Data Analysis
**Total Users:** 14
**Active Users:** 10

**Authentication Methods:**
- password_only: 11 users (78.6%)
- password_otp: 3 users (21.4%)
- **OTP Enabled:** 3 users (21.4%)
- **2FA Enabled:** 0 users (0%)

## 🔗 Table Relationships
**Foreign Key Relationships:**
| From Table | From Column | To Table | To Column |
|------------|-------------|----------|-----------|
| `adminactions` | `admin_id` | `admins` | `admin_id` |
| `admin_2fa` | `admin_id` | `admins` | `admin_id` |
| `admin_auth_logs` | `admin_id` | `admins` | `admin_id` |
| `admin_login_attempts` | `admin_id` | `admins` | `admin_id` |
| `admin_otp` | `admin_id` | `admins` | `admin_id` |
| `admin_recovery_codes` | `admin_id` | `admins` | `admin_id` |
| `admin_recovery_tokens` | `admin_id` | `admins` | `admin_id` |
| `bets` | `team1_id` | `teams` | `id` |
| `bets` | `team2_id` | `teams` | `id` |
| `bets` | `user1_id` | `users` | `user_id` |
| `bets` | `user2_id` | `users` | `user_id` |
| `challenges` | `admin_id` | `admins` | `admin_id` |
| `credit_requests` | `approved_by` | `users` | `user_id` |
| `credit_requests` | `payment_method_id` | `payment_methods` | `id` |
| `credit_requests` | `user_id` | `users` | `user_id` |
| `exchange_rates` | `currency_id` | `currencies` | `id` |
| `exchange_rates` | `updated_by_admin_id` | `admins` | `admin_id` |
| `leaderboards` | `user_id` | `users` | `user_id` |
| `leagues` | `created_by` | `admins` | `admin_id` |
| `league_audit_log` | `admin_id` | `admins` | `admin_id` |
| `league_audit_log` | `membership_id` | `league_memberships` | `membership_id` |
| `league_audit_log` | `season_id` | `league_seasons` | `season_id` |
| `league_history` | `league_id` | `leagues` | `league_id` |
| `league_history` | `season_id` | `league_seasons` | `season_id` |
| `league_history` | `user_id` | `users` | `user_id` |
| `league_history` | `verified_by` | `admins` | `admin_id` |
| `league_memberships` | `approved_by` | `admins` | `admin_id` |
| `league_memberships` | `league_id` | `leagues` | `league_id` |
| `league_memberships` | `season_id` | `league_seasons` | `season_id` |
| `league_memberships` | `user_id` | `users` | `user_id` |
| `league_memberships` | `user_league_id` | `user_leagues` | `id` |
| `league_seasons` | `created_by` | `admins` | `admin_id` |
| `league_seasons` | `league_id` | `leagues` | `league_id` |
| `messages` | `recipient_id` | `users` | `user_id` |
| `messages` | `sender_id` | `users` | `user_id` |
| `paymentverifications` | `user_id` | `users` | `user_id` |
| `paymentverifications` | `verified_by` | `admins` | `admin_id` |
| `season_history` | `season_id` | `seasons` | `season_id` |
| `season_history` | `user_id` | `users` | `user_id` |
| `transactions` | `admin_id` | `admins` | `admin_id` |
| `transactions` | `related_challenge_id` | `challenges` | `challenge_id` |
| `transactions` | `related_user_id` | `users` | `user_id` |
| `transactions` | `user_id` | `users` | `user_id` |
| `users` | `current_league_id` | `leagues` | `league_id` |
| `users` | `current_league_id` | `leagues` | `league_id` |
| `users` | `current_season_id` | `seasons` | `season_id` |
| `users` | `preferred_currency_id` | `currencies` | `id` |
| `users` | `preferred_currency_id` | `currencies` | `id` |
| `users` | `preferred_currency_id` | `currencies` | `id` |
| `user_2fa` | `user_id` | `users` | `user_id` |
| `user_achievements` | `achievement_id` | `achievements` | `achievement_id` |
| `user_achievements` | `user_id` | `users` | `user_id` |
| `user_activity_log` | `user_id` | `users` | `user_id` |
| `user_auth_logs` | `user_id` | `users` | `user_id` |
| `user_auth_settings` | `user_id` | `users` | `user_id` |
| `user_friends` | `friend_id` | `users` | `user_id` |
| `user_friends` | `user_id` | `users` | `user_id` |
| `user_leagues` | `league_id` | `leagues` | `league_id` |
| `user_leagues` | `user_id` | `users` | `user_id` |
| `user_league_stats` | `league_id` | `leagues` | `league_id` |
| `user_league_stats` | `user_id` | `users` | `user_id` |
| `user_login_attempts` | `user_id` | `users` | `user_id` |
| `user_otp` | `user_id` | `users` | `user_id` |
| `user_payment_methods` | `user_id` | `users` | `user_id` |
| `user_sessions` | `user_id` | `users` | `user_id` |
| `withdrawal_notifications` | `user_id` | `users` | `user_id` |
| `withdrawal_notifications` | `withdrawal_id` | `withdrawal_requests` | `withdrawal_id` |
| `withdrawal_requests` | `payment_method_id` | `user_payment_methods` | `payment_method_id` |
| `withdrawal_requests` | `user_id` | `users` | `user_id` |

## 💡 Recommendations
✅ **All systems appear to be properly configured!**


## ✅ Analysis Complete
Report saved as: database_analysis_2025-07-18_15-06-53.md
