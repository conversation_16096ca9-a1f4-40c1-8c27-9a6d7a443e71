<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

try {
    $db = getDBConnection();

    if (!$db) {
        echo json_encode([
            'success' => false,
            'message' => 'Database connection failed'
        ]);
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $user_id = $input['user_id'] ?? null;
        $action = $input['action'] ?? 'ban';
        
        if (!$user_id) {
            echo json_encode([
                'success' => false,
                'message' => 'User ID is required'
            ]);
            exit;
        }
        
        // Check if user exists
        $check_query = "SELECT user_id, username FROM users WHERE user_id = :user_id";
        $stmt = $db->prepare($check_query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            echo json_encode([
                'success' => false,
                'message' => 'User not found'
            ]);
            exit;
        }
        
        // Status column should already exist based on database analysis
        // Adding error logging for debugging
        error_log("Ban User Request - User ID: $user_id, Action: $action");
        
        // Update user status to banned
        $update_query = "UPDATE users SET status = 'banned', updated_at = CURRENT_TIMESTAMP WHERE user_id = :user_id";

        $stmt = $db->prepare($update_query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);

        if ($stmt->execute()) {
            // Log the action for audit purposes
            error_log("User banned successfully - User ID: $user_id, Username: {$user['username']}");

            echo json_encode([
                'success' => true,
                'message' => "User {$user['username']} has been banned successfully"
            ]);
        } else {
            error_log("Failed to ban user - User ID: $user_id, Error: " . implode(', ', $stmt->errorInfo()));

            echo json_encode([
                'success' => false,
                'message' => 'Failed to ban user: Database update failed'
            ]);
        }
        
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
