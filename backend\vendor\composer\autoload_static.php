<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitbe14072eb65fe1eca6c06ee4c3696e51
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'PragmaRX\\Google2FA\\' => 19,
            '<PERSON>gonIE\\ConstantTime\\' => 23,
            '<PERSON><PERSON><PERSON><PERSON><PERSON>\\PHPMailer\\' => 20,
        ),
        'F' => 
        array (
            'FanBet247\\' => 10,
        ),
        'E' => 
        array (
            'Endroid\\QrCode\\' => 15,
        ),
        'D' => 
        array (
            'DASPRiD\\Enum\\' => 13,
        ),
        'B' => 
        array (
            'BaconQrCode\\' => 12,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PragmaRX\\Google2FA\\' => 
        array (
            0 => __DIR__ . '/..' . '/pragmarx/google2fa/src',
        ),
        'ParagonIE\\ConstantTime\\' => 
        array (
            0 => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src',
        ),
        '<PERSON><PERSON><PERSON><PERSON><PERSON>\\PHP<PERSON>ailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
        'FanBet247\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
        'Endroid\\QrCode\\' => 
        array (
            0 => __DIR__ . '/..' . '/endroid/qr-code/src',
        ),
        'DASPRiD\\Enum\\' => 
        array (
            0 => __DIR__ . '/..' . '/dasprid/enum/src',
        ),
        'BaconQrCode\\' => 
        array (
            0 => __DIR__ . '/..' . '/bacon/bacon-qr-code/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitbe14072eb65fe1eca6c06ee4c3696e51::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitbe14072eb65fe1eca6c06ee4c3696e51::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitbe14072eb65fe1eca6c06ee4c3696e51::$classMap;

        }, null, ClassLoader::class);
    }
}
