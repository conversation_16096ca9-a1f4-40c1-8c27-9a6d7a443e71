<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }
    
    // Check if we want only active currencies or all currencies
    $activeOnly = isset($_GET['active_only']) ? filter_var($_GET['active_only'], FILTER_VALIDATE_BOOLEAN) : true;
    
    // Build query based on active_only parameter
    $query = "SELECT 
                c.id,
                c.currency_code,
                c.currency_name,
                c.currency_symbol,
                c.is_active,
                c.created_at,
                c.updated_at
              FROM currencies c";
    
    if ($activeOnly) {
        $query .= " WHERE c.is_active = 1";
    }
    
    $query .= " ORDER BY c.currency_code ASC";
    
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $currencies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format the response data
    $formattedCurrencies = [];
    foreach ($currencies as $currency) {
        $formattedCurrencies[] = [
            'id' => (int)$currency['id'],
            'currency_code' => $currency['currency_code'],
            'currency_name' => $currency['currency_name'],
            'currency_symbol' => $currency['currency_symbol'],
            'is_active' => (bool)$currency['is_active'],
            'display_name' => $currency['currency_symbol'] . ' ' . $currency['currency_code'] . ' - ' . $currency['currency_name'],
            'created_at' => $currency['created_at'],
            'updated_at' => $currency['updated_at']
        ];
    }
    
    // Get total count for pagination info
    $countQuery = "SELECT COUNT(*) as total FROM currencies";
    if ($activeOnly) {
        $countQuery .= " WHERE is_active = 1";
    }
    
    $countStmt = $conn->prepare($countQuery);
    $countStmt->execute();
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    jsonResponse(200, "Currencies fetched successfully", [
        'currencies' => $formattedCurrencies,
        'total_count' => (int)$totalCount,
        'active_only' => $activeOnly
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in get_currencies.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    error_log("General error in get_currencies.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred while fetching currencies");
}
?>
