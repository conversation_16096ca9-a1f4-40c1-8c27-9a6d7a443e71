<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Access-Control-Allow-Credentials: true");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

include_once '../includes/db_connect.php';

$conn = getDBConnection();

// Debug connection
if (!$conn) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Handle GET requests
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $userId = $_GET['user_id'] ?? null;
    $type = $_GET['type'] ?? null;
    $otherUserId = $_GET['other_user_id'] ?? null;

    if (!$userId) {
        echo json_encode(['success' => false, 'message' => 'User ID is required']);
        exit;
    }

    // Add this new condition to handle unread count requests
    if ($type === 'unread_count') {
        try {
            $query = "SELECT COUNT(*) as unread_count 
                     FROM messages 
                     WHERE recipient_id = :user_id 
                     AND is_read = 0 
                     AND deleted_by_recipient = 0";
            
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':user_id', $userId);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'unread_count' => (int)$result['unread_count']]);
            exit;
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Database error occurred']);
            exit;
        }
    }

    // Fetch conversations (inbox or sent)
    if ($type === 'inbox' || $type === 'sent') {
        try {
            $query = "
                SELECT DISTINCT 
                    u.user_id,
                    u.username,
                    m.content as last_message,
                    m.created_at as last_message_time,
                    m.is_read
                FROM messages m
                JOIN users u ON " . ($type === 'inbox' 
                    ? "u.user_id = m.sender_id AND m.recipient_id = :user_id"
                    : "u.user_id = m.recipient_id AND m.sender_id = :user_id"
                ) . "
                WHERE m.id IN (
                    SELECT MAX(id)
                    FROM messages
                    WHERE " . ($type === 'inbox'
                        ? "recipient_id = :user_id"
                        : "sender_id = :user_id"
                    ) . "
                    GROUP BY " . ($type === 'inbox' ? "sender_id" : "recipient_id") . "
                )
                ORDER BY m.created_at DESC";

            $stmt = $conn->prepare($query);
            $stmt->bindParam(':user_id', $userId);
            $stmt->execute();
            $conversations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode(['success' => true, 'conversations' => $conversations]);
            exit;
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Database error occurred']);
            exit;
        }
    }

    // Fetch conversation messages
    if ($type === 'conversation' && $otherUserId) {
        try {
            $query = "
                SELECT 
                    m.*,
                    sender.username as sender_username,
                    sender.favorite_team as sender_team,
                    recipient.username as recipient_username,
                    recipient.favorite_team as recipient_team,
                    t1.logo as sender_team_logo,
                    t2.logo as recipient_team_logo,
                    t1.name as sender_team_name,
                    t2.name as recipient_team_name,
                    CASE 
                        WHEN m.sender_id = :user_id THEN 'sent'
                        ELSE 'received'
                    END as message_type
                FROM messages m
                JOIN users sender ON m.sender_id = sender.user_id
                JOIN users recipient ON m.recipient_id = recipient.user_id
                LEFT JOIN teams t1 ON sender.favorite_team = t1.name
                LEFT JOIN teams t2 ON recipient.favorite_team = t2.name
                WHERE (
                    (m.sender_id = :user_id AND m.recipient_id = :other_user_id)
                    OR 
                    (m.sender_id = :other_user_id AND m.recipient_id = :user_id)
                )
                ORDER BY m.created_at ASC";

            $stmt = $conn->prepare($query);
            $stmt->bindParam(':user_id', $userId);
            $stmt->bindParam(':other_user_id', $otherUserId);
            $stmt->execute();
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Mark messages as read
            $updateQuery = "
                UPDATE messages 
                SET is_read = TRUE 
                WHERE recipient_id = :user_id 
                AND sender_id = :other_user_id 
                AND is_read = FALSE";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bindParam(':user_id', $userId);
            $updateStmt->bindParam(':other_user_id', $otherUserId);
            $updateStmt->execute();

            echo json_encode(['success' => true, 'messages' => $messages]);
            exit;
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Database error occurred']);
            exit;
        }
    }
}

// Handle POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents("php://input"));

    if (!isset($data->sender_id) || !isset($data->recipient_id) || !isset($data->content)) {
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
        exit;
    }

    try {
        $query = "INSERT INTO messages (sender_id, recipient_id, content) 
                  VALUES (:sender_id, :recipient_id, :content)";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':sender_id', $data->sender_id);
        $stmt->bindParam(':recipient_id', $data->recipient_id);
        $stmt->bindParam(':content', $data->content);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Message sent successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to send message']);
        }
    } catch (PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    }

    // Add this new condition to handle marking messages as read
    if (isset($data->action) && $data->action === 'mark_read') {
        try {
            $query = "UPDATE messages 
                     SET is_read = 1 
                     WHERE recipient_id = :recipient_id 
                     AND sender_id = :sender_id 
                     AND is_read = 0";
            
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':recipient_id', $data->recipient_id);
            $stmt->bindParam(':sender_id', $data->sender_id);
            $stmt->execute();
            
            echo json_encode(['success' => true]);
            exit;
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Database error occurred']);
            exit;
        }
    }
}
