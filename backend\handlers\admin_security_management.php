<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once __DIR__ . '/../includes/db_connect.php';
$conn = getDBConnection();

function sendResponse($status, $message, $data = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status === 200,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

// Simple admin check (matching existing pattern)
function isAdmin() {
    // For now, return admin ID 1. In production, implement proper admin authentication
    return 1;
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendResponse(405, "Only GET method is allowed");
    }

    // Verify admin (using same pattern as other admin endpoints)
    $adminId = isAdmin();
    if (!$adminId) {
        sendResponse(403, "Admin privileges required");
    }

    // Fetch comprehensive user security data
    $stmt = $conn->prepare("
        SELECT
            u.user_id,
            u.username,
            u.email,
            u.role,
            u.status,
            u.created_at,
            u.last_active,
            u.otp_enabled,
            u.tfa_enabled,
            u.auth_method,
            t.secret_key as tfa_secret,
            t.is_enabled as tfa_setup_enabled,
            t.setup_completed as tfa_setup_completed,
            t.created_at as tfa_created_at
        FROM users u
        LEFT JOIN user_2fa t ON u.user_id = t.user_id
        ORDER BY u.created_at DESC
    ");
    
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process and enhance user data
    $processedUsers = [];
    foreach ($users as $user) {
        $processedUser = [
            'user_id' => (int)$user['user_id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role' => $user['role'],
            'status' => $user['status'],
            'created_at' => $user['created_at'],
            'last_login' => $user['last_active'],
            'failed_login_attempts' => 0, // Not available in current schema
            'otp_enabled' => (bool)$user['otp_enabled'],
            'tfa_enabled' => (bool)$user['tfa_enabled'],
            'auth_method' => $user['auth_method'] ?: 'password_only',
            
            // Security setup details
            'otp_setup' => [
                'has_secret' => (bool)$user['otp_enabled'],
                'is_enabled' => (bool)$user['otp_enabled'],
                'created_at' => null
            ],
            
            'tfa_setup' => [
                'has_secret' => !empty($user['tfa_secret']),
                'is_enabled' => (bool)$user['tfa_setup_enabled'],
                'setup_completed' => (bool)$user['tfa_setup_completed'],
                'created_at' => $user['tfa_created_at']
            ],
            
            // Security analysis
            'security_level' => calculateSecurityLevel($user),
            'security_issues' => identifySecurityIssues($user),
            'last_security_change' => getLastSecurityChange($conn, $user['user_id'])
        ];
        
        $processedUsers[] = $processedUser;
    }
    
    // Get security statistics
    $stats = [
        'total_users' => count($processedUsers),
        'otp_enabled' => count(array_filter($processedUsers, fn($u) => $u['otp_enabled'])),
        'tfa_enabled' => count(array_filter($processedUsers, fn($u) => $u['tfa_enabled'])),
        'security_issues' => count(array_filter($processedUsers, fn($u) => !empty($u['security_issues']))),
        'high_security' => count(array_filter($processedUsers, fn($u) => $u['security_level'] === 'high')),
        'medium_security' => count(array_filter($processedUsers, fn($u) => $u['security_level'] === 'medium')),
        'low_security' => count(array_filter($processedUsers, fn($u) => $u['security_level'] === 'low'))
    ];
    
    // Log admin access (simplified)
    error_log("Admin $adminId accessed security management dashboard");

    // Return data in the format expected by frontend
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => 'Security data retrieved successfully',
        'users' => $processedUsers,
        'stats' => $stats
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch security data: ' . $e->getMessage()
    ]);
}

function calculateSecurityLevel($user) {
    if ($user['tfa_enabled']) {
        return 'high';
    } elseif ($user['otp_enabled']) {
        return 'medium';
    } else {
        return 'low';
    }
}

function identifySecurityIssues($user) {
    $issues = [];
    
    // Check for suspended/banned accounts
    if ($user['status'] === 'suspended') {
        $issues[] = 'Account suspended';
    } elseif ($user['status'] === 'banned') {
        $issues[] = 'Account banned';
    }
    
    // Check for inactive accounts (no activity in 90 days)
    if ($user['last_active']) {
        $lastActive = new DateTime($user['last_active']);
        $now = new DateTime();
        $daysSinceActive = $now->diff($lastActive)->days;

        if ($daysSinceActive > 90) {
            $issues[] = 'Inactive account (last active: ' . $daysSinceActive . ' days ago)';
        }
    } else {
        $issues[] = 'Never been active';
    }
    
    // Check for incomplete security setup
    if ($user['tfa_enabled'] && empty($user['tfa_secret'])) {
        $issues[] = '2FA enabled but no secret key';
    }
    
    return $issues;
}

function getLastSecurityChange($conn, $userId) {
    // Simplified - just return null for now since we don't have the logs table
    return null;
}

// Simplified logging function
function logAdminAction($message) {
    error_log("Admin Security Management: " . $message);
}
?>
