<?php
/**
 * SMTP Email Testing Script for FanBet247
 * 
 * This script tests the SMTP email functionality using the existing
 * smtp_settings configuration from the database.
 * 
 * Purpose: Verify email delivery capability for OTP system
 * Test Email: <EMAIL>
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'includes/db_connect.php';

// Check if PHPMailer is available
if (file_exists('vendor/autoload.php')) {
    require 'vendor/autoload.php';
} else {
    die("❌ PHPMailer not found. Please install PHPMailer via Composer.\n");
}

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class SMTPTester {
    private $conn;
    private $logFile;
    private $testEmail = '<EMAIL>';
    
    public function __construct() {
        $this->conn = getDBConnection();
        $this->logFile = 'smtp_test_' . date('Y-m-d_H-i-s') . '.log';
        $this->log("🧪 SMTP Email Testing Started at " . date('Y-m-d H:i:s'));
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
        echo $logMessage;
    }
    
    public function runTests() {
        $this->log("=" . str_repeat("=", 60));
        $this->log("🔧 FanBet247 SMTP Email Testing System");
        $this->log("=" . str_repeat("=", 60));
        
        // Test 1: Database Connection
        $this->testDatabaseConnection();
        
        // Test 2: SMTP Settings Retrieval
        $smtpSettings = $this->testSMTPSettingsRetrieval();
        
        if (!$smtpSettings) {
            $this->log("❌ Cannot proceed without SMTP settings");
            return false;
        }
        
        // Test 3: PHPMailer Configuration
        $this->testPHPMailerConfiguration($smtpSettings);
        
        // Test 4: SMTP Connection Test
        $this->testSMTPConnection($smtpSettings);
        
        // Test 5: Send Test Email
        $this->sendTestEmail($smtpSettings);
        
        // Test 6: Test sendEmail() Function
        $this->testSendEmailFunction();
        
        // Test 7: OTP Email Template Test
        $this->testOTPEmailTemplate($smtpSettings);
        
        $this->log("=" . str_repeat("=", 60));
        $this->log("✅ SMTP Testing Completed");
        $this->log("📄 Log file saved as: " . $this->logFile);
        $this->log("=" . str_repeat("=", 60));
        
        return true;
    }
    
    private function testDatabaseConnection() {
        $this->log("\n🔍 Test 1: Database Connection");
        
        try {
            if ($this->conn) {
                $this->log("✅ Database connection successful");
                
                // Test query
                $stmt = $this->conn->prepare("SELECT DATABASE() as db_name");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $this->log("📊 Connected to database: " . $result['db_name']);
                
                return true;
            } else {
                $this->log("❌ Database connection failed");
                return false;
            }
        } catch (Exception $e) {
            $this->log("❌ Database error: " . $e->getMessage());
            return false;
        }
    }
    
    private function testSMTPSettingsRetrieval() {
        $this->log("\n🔍 Test 2: SMTP Settings Retrieval");
        
        try {
            // Check if smtp_settings table exists
            $stmt = $this->conn->prepare("SHOW TABLES LIKE 'smtp_settings'");
            $stmt->execute();
            
            if ($stmt->rowCount() == 0) {
                $this->log("❌ smtp_settings table does not exist");
                return false;
            }
            
            $this->log("✅ smtp_settings table exists");
            
            // Get active SMTP settings
            $stmt = $this->conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
            $stmt->execute();
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$settings) {
                $this->log("❌ No active SMTP settings found");
                return false;
            }
            
            $this->log("✅ Active SMTP settings retrieved");
            $this->log("📊 SMTP Configuration:");
            $this->log("   Host: " . ($settings['host'] ?? 'Not set'));
            $this->log("   Port: " . ($settings['port'] ?? 'Not set'));
            $this->log("   Username: " . ($settings['username'] ?? 'Not set'));
            $this->log("   Password: " . (isset($settings['password']) && !empty($settings['password']) ? str_repeat('*', strlen($settings['password'])) : 'Not set'));
            $this->log("   Encryption: " . ($settings['encryption'] ?? 'Not set'));
            $this->log("   From Email: " . ($settings['from_email'] ?? 'Not set'));
            $this->log("   From Name: " . ($settings['from_name'] ?? 'Not set'));
            
            return $settings;
            
        } catch (Exception $e) {
            $this->log("❌ Error retrieving SMTP settings: " . $e->getMessage());
            return false;
        }
    }
    
    private function testPHPMailerConfiguration($settings) {
        $this->log("\n🔍 Test 3: PHPMailer Configuration");
        
        try {
            $mail = new PHPMailer(true);
            $this->log("✅ PHPMailer instance created successfully");
            
            // Configure SMTP
            $mail->isSMTP();
            $mail->Host = $settings['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['username'];
            $mail->Password = $settings['password'];
            $mail->SMTPSecure = $settings['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $settings['port'];
            
            $this->log("✅ PHPMailer configured with SMTP settings");
            
            return true;
            
        } catch (Exception $e) {
            $this->log("❌ PHPMailer configuration error: " . $e->getMessage());
            return false;
        }
    }
    
    private function testSMTPConnection($settings) {
        $this->log("\n🔍 Test 4: SMTP Connection Test");
        
        try {
            $mail = new PHPMailer(true);
            
            // Enable verbose debug output
            $mail->SMTPDebug = SMTP::DEBUG_CONNECTION;
            $mail->Debugoutput = function($str, $level) {
                $this->log("🔧 SMTP Debug: " . trim($str));
            };
            
            // Configure SMTP
            $mail->isSMTP();
            $mail->Host = $settings['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['username'];
            $mail->Password = $settings['password'];
            $mail->SMTPSecure = $settings['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $settings['port'];
            
            // Test connection
            if ($mail->smtpConnect()) {
                $this->log("✅ SMTP connection successful");
                $mail->smtpClose();
                return true;
            } else {
                $this->log("❌ SMTP connection failed");
                return false;
            }
            
        } catch (Exception $e) {
            $this->log("❌ SMTP connection error: " . $e->getMessage());
            return false;
        }
    }
    
    private function sendTestEmail($settings) {
        $this->log("\n🔍 Test 5: Send Test Email");
        $this->log("📧 Sending test email to: " . $this->testEmail);
        
        try {
            $mail = new PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = $settings['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['username'];
            $mail->Password = $settings['password'];
            $mail->SMTPSecure = $settings['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $settings['port'];
            
            // Recipients
            $mail->setFrom($settings['from_email'], $settings['from_name']);
            $mail->addAddress($this->testEmail, 'Test Recipient');
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = 'FanBet247 SMTP Test - ' . date('Y-m-d H:i:s');
            $mail->Body = $this->getTestEmailHTML();
            $mail->AltBody = $this->getTestEmailText();
            
            $mail->send();
            $this->log("✅ Test email sent successfully!");
            $this->log("📧 Email sent to: " . $this->testEmail);
            $this->log("📝 Subject: " . $mail->Subject);
            
            return true;
            
        } catch (Exception $e) {
            $this->log("❌ Failed to send test email: " . $e->getMessage());
            return false;
        }
    }
    
    private function testSendEmailFunction() {
        $this->log("\n🔍 Test 6: sendEmail() Function Test");
        
        // Check if sendEmail function exists
        if (!function_exists('sendEmail')) {
            // Try to include the email functions file
            $emailFiles = [
                'includes/email_functions.php',
                'includes/functions.php',
                'handlers/email_handler.php'
            ];
            
            $found = false;
            foreach ($emailFiles as $file) {
                if (file_exists($file)) {
                    include_once $file;
                    $found = true;
                    $this->log("✅ Included email functions from: $file");
                    break;
                }
            }
            
            if (!$found) {
                $this->log("⚠️ sendEmail() function not found - will test direct PHPMailer only");
                return false;
            }
        }
        
        if (function_exists('sendEmail')) {
            try {
                $subject = 'FanBet247 sendEmail() Function Test - ' . date('Y-m-d H:i:s');
                $message = $this->getTestEmailHTML();
                
                $result = sendEmail($this->testEmail, $subject, $message, $this->conn);
                
                if ($result['success']) {
                    $this->log("✅ sendEmail() function test successful");
                    $this->log("📧 Email sent via sendEmail() function");
                } else {
                    $this->log("❌ sendEmail() function failed: " . $result['message']);
                }
                
                return $result['success'];
                
            } catch (Exception $e) {
                $this->log("❌ sendEmail() function error: " . $e->getMessage());
                return false;
            }
        }
        
        return false;
    }
    
    private function testOTPEmailTemplate($settings) {
        $this->log("\n🔍 Test 7: OTP Email Template Test");
        
        try {
            $mail = new PHPMailer(true);
            
            // Configure SMTP
            $mail->isSMTP();
            $mail->Host = $settings['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['username'];
            $mail->Password = $settings['password'];
            $mail->SMTPSecure = $settings['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $settings['port'];
            
            // Recipients
            $mail->setFrom($settings['from_email'], $settings['from_name']);
            $mail->addAddress($this->testEmail, 'Test User');
            
            // OTP Email Content
            $otpCode = '123456';
            $mail->isHTML(true);
            $mail->Subject = 'Your FanBet247 Verification Code';
            $mail->Body = $this->getOTPEmailHTML($otpCode);
            $mail->AltBody = $this->getOTPEmailText($otpCode);
            
            $mail->send();
            $this->log("✅ OTP email template test successful!");
            $this->log("📧 OTP email sent with code: $otpCode");
            
            return true;
            
        } catch (Exception $e) {
            $this->log("❌ OTP email template test failed: " . $e->getMessage());
            return false;
        }
    }
    
    private function getTestEmailHTML() {
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>FanBet247 SMTP Test</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h1 style="color: #2563eb;">FanBet247 SMTP Test</h1>
                <p>This is a test email to verify SMTP functionality.</p>
                <div style="background: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3>Test Details:</h3>
                    <ul>
                        <li><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</li>
                        <li><strong>Purpose:</strong> SMTP Configuration Verification</li>
                        <li><strong>System:</strong> FanBet247 OTP Email System</li>
                    </ul>
                </div>
                <p>If you received this email, the SMTP configuration is working correctly!</p>
                <hr style="margin: 30px 0;">
                <p style="font-size: 12px; color: #666;">
                    This is an automated test email from FanBet247.<br>
                    Please do not reply to this email.
                </p>
            </div>
        </body>
        </html>';
    }
    
    private function getTestEmailText() {
        return "FanBet247 SMTP Test\n\n" .
               "This is a test email to verify SMTP functionality.\n\n" .
               "Test Details:\n" .
               "- Test Time: " . date('Y-m-d H:i:s') . "\n" .
               "- Purpose: SMTP Configuration Verification\n" .
               "- System: FanBet247 OTP Email System\n\n" .
               "If you received this email, the SMTP configuration is working correctly!\n\n" .
               "This is an automated test email from FanBet247.\n" .
               "Please do not reply to this email.";
    }
    
    private function getOTPEmailHTML($otpCode) {
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Your FanBet247 Verification Code</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h1 style="color: #2563eb;">FanBet247</h1>
                <h2>Your Verification Code</h2>
                <p>Use the following code to complete your login:</p>
                <div style="background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 5px; margin: 20px 0;">
                    <h1 style="margin: 0; font-size: 32px; letter-spacing: 5px;">' . $otpCode . '</h1>
                </div>
                <p><strong>Important:</strong></p>
                <ul>
                    <li>This code expires in 5 minutes</li>
                    <li>Do not share this code with anyone</li>
                    <li>If you did not request this code, please ignore this email</li>
                </ul>
                <hr style="margin: 30px 0;">
                <p style="font-size: 12px; color: #666;">
                    This is an automated email from FanBet247.<br>
                    Please do not reply to this email.
                </p>
            </div>
        </body>
        </html>';
    }
    
    private function getOTPEmailText($otpCode) {
        return "FanBet247 - Your Verification Code\n\n" .
               "Use the following code to complete your login:\n\n" .
               "Verification Code: $otpCode\n\n" .
               "Important:\n" .
               "- This code expires in 5 minutes\n" .
               "- Do not share this code with anyone\n" .
               "- If you did not request this code, please ignore this email\n\n" .
               "This is an automated email from FanBet247.\n" .
               "Please do not reply to this email.";
    }
}

// Run the tests
echo "🚀 Starting SMTP Email Testing...\n\n";

$tester = new SMTPTester();
$success = $tester->runTests();

if ($success) {
    echo "\n🎉 All tests completed! Check the log file for detailed results.\n";
} else {
    echo "\n⚠️ Some tests failed. Check the log file for details.\n";
}
?>
