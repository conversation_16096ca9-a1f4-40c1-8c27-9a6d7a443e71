<?php
// Debug script to capture and analyze actual API requests
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log all incoming requests
$logFile = 'debug_2fa_requests.log';

// Get request method and data
$method = $_SERVER['REQUEST_METHOD'];
$uri = $_SERVER['REQUEST_URI'];
$timestamp = date('Y-m-d H:i:s');

// Get raw input
$rawInput = file_get_contents('php://input');
$headers = getallheaders();

// Parse JSON if present
$jsonData = null;
if (!empty($rawInput)) {
    $jsonData = json_decode($rawInput, true);
}

// Create detailed log entry
$logEntry = [
    'timestamp' => $timestamp,
    'method' => $method,
    'uri' => $uri,
    'headers' => $headers,
    'raw_input' => $rawInput,
    'parsed_json' => $jsonData,
    'get_params' => $_GET,
    'post_params' => $_POST,
    'server_info' => [
        'php_version' => PHP_VERSION,
        'server_time' => time(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
    ]
];

// Write to log file
file_put_contents($logFile, json_encode($logEntry, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND | LOCK_EX);

// If this is a 2FA verification request, process it with detailed logging
if (strpos($uri, 'user_verify_2fa.php') !== false) {
    echo "🔍 2FA VERIFICATION REQUEST INTERCEPTED\n";
    echo "=====================================\n\n";
    
    echo "📅 Timestamp: $timestamp\n";
    echo "🌐 Method: $method\n";
    echo "📍 URI: $uri\n";
    echo "📦 Raw Input: $rawInput\n";
    echo "🔧 Parsed JSON: " . json_encode($jsonData, JSON_PRETTY_PRINT) . "\n\n";
    
    if ($jsonData) {
        echo "🔍 PARAMETER ANALYSIS:\n";
        echo "   userId: " . ($jsonData['userId'] ?? 'MISSING') . "\n";
        echo "   verification_code: " . ($jsonData['verification_code'] ?? 'MISSING') . "\n";
        echo "   is_backup_code: " . ($jsonData['is_backup_code'] ?? 'false') . "\n\n";
        
        // Test the verification with the exact same parameters
        if (isset($jsonData['userId']) && isset($jsonData['verification_code'])) {
            require_once 'includes/db_connect.php';
            require_once 'vendor/autoload.php';
            
            use PragmaRX\Google2FA\Google2FA;
            
            try {
                $conn = getDBConnection();
                $userId = $jsonData['userId'];
                $verificationCode = $jsonData['verification_code'];
                
                echo "🧪 TESTING WITH EXACT SAME PARAMETERS:\n";
                echo "   User ID: $userId\n";
                echo "   Code: $verificationCode\n";
                
                // Get user data
                $stmt = $conn->prepare("SELECT * FROM users WHERE user_id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$user) {
                    echo "   ❌ User not found\n";
                } else {
                    echo "   ✅ User found: {$user['username']}\n";
                    echo "   TFA Enabled: " . ($user['tfa_enabled'] ? 'Yes' : 'No') . "\n";
                    
                    // Get 2FA setup
                    $stmt = $conn->prepare("SELECT * FROM user_2fa WHERE user_id = ?");
                    $stmt->execute([$userId]);
                    $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$tfaSetup) {
                        echo "   ❌ 2FA setup not found\n";
                    } else {
                        echo "   ✅ 2FA setup found\n";
                        echo "   Secret: {$tfaSetup['secret_key']}\n";
                        echo "   Enabled: " . ($tfaSetup['is_enabled'] ? 'Yes' : 'No') . "\n";
                        
                        // Test verification
                        $google2fa = new Google2FA();
                        $window = 2;
                        $isValid = $google2fa->verifyKey($tfaSetup['secret_key'], $verificationCode, $window);
                        
                        echo "   🎯 Verification Result: " . ($isValid ? "✅ SUCCESS" : "❌ FAILED") . "\n";
                        
                        // Generate current code for comparison
                        $currentCode = $google2fa->getCurrentOtp($tfaSetup['secret_key']);
                        echo "   📱 Current Valid Code: $currentCode\n";
                        echo "   ⏰ Server Time: " . date('H:i:s') . "\n";
                        
                        if (!$isValid) {
                            echo "\n🔧 TROUBLESHOOTING:\n";
                            
                            // Test with different windows
                            for ($w = 0; $w <= 8; $w++) {
                                $testValid = $google2fa->verifyKey($tfaSetup['secret_key'], $verificationCode, $w);
                                echo "     Window $w: " . ($testValid ? "✅" : "❌") . "\n";
                            }
                            
                            // Test time offsets
                            echo "\n   Time offset analysis:\n";
                            $currentTime = time();
                            for ($offset = -10; $offset <= 10; $offset++) {
                                $testTime = $currentTime + ($offset * 30);
                                $testCode = $google2fa->getCurrentOtp($tfaSetup['secret_key'], $testTime);
                                if ($testCode === $verificationCode) {
                                    $timeStr = date('H:i:s', $testTime);
                                    echo "     ✅ Code matches time offset $offset (${timeStr})\n";
                                }
                            }
                        }
                    }
                }
                
            } catch (Exception $e) {
                echo "   ❌ Error: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n\n";
}

// Continue with normal processing
?>
