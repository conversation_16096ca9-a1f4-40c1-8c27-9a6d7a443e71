<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

// Handle GET requests for fetching friend requests
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $userId = $_GET['user_id'] ?? null;

    if (!$userId) {
        echo json_encode(['success' => false, 'message' => 'User ID required']);
        exit;
    }

    try {
        // Get pending friend requests (both sent and received)
        $query = "SELECT 
                    u.user_id as id,
                    u.username,
                    u.favorite_team,
                    uf.created_at as request_date,
                    CASE 
                        WHEN uf.user_id = :user_id THEN 'sent'
                        ELSE 'received'
                    END as request_type,
                    (
                        SELECT COALESCE(ROUND(
                            COUNT(CASE WHEN outcome = 'win' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 
                            2
                        ), 0)
                        FROM bets 
                        WHERE user1_id = u.user_id OR user2_id = u.user_id
                    ) as win_rate
                FROM user_friends uf
                JOIN users u ON 
                    CASE 
                        WHEN uf.user_id = :user_id THEN u.user_id = uf.friend_id
                        ELSE u.user_id = uf.user_id
                    END
                WHERE (uf.friend_id = :user_id OR uf.user_id = :user_id)
                AND uf.status = 'pending'";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Add debug information
        error_log("Friend requests for user $userId: " . json_encode($requests));

        echo json_encode([
            'success' => true,
            'requests' => $requests
        ]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

// Handle POST requests for friend request actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents("php://input"));
    
    if (!isset($data->action) || !isset($data->user_id) || !isset($data->friend_id)) {
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
        exit;
    }

    try {
        switch ($data->action) {
            case 'send':
                // Check if request already exists
                $checkQuery = "SELECT * FROM user_friends 
                             WHERE (user_id = :user_id AND friend_id = :friend_id)
                             OR (user_id = :friend_id AND friend_id = :user_id)";
                $checkStmt = $conn->prepare($checkQuery);
                $checkStmt->bindParam(':user_id', $data->user_id);
                $checkStmt->bindParam(':friend_id', $data->friend_id);
                $checkStmt->execute();
                
                if ($checkStmt->rowCount() > 0) {
                    echo json_encode(['success' => false, 'message' => 'Friend request already exists']);
                    exit;
                }

                $query = "INSERT INTO user_friends (user_id, friend_id, status) 
                         VALUES (:user_id, :friend_id, 'pending')";
                break;

            case 'accept':
                $query = "UPDATE user_friends 
                         SET status = 'accepted' 
                         WHERE user_id = :friend_id AND friend_id = :user_id 
                         AND status = 'pending'";
                break;

            case 'reject':
                $query = "DELETE FROM user_friends 
                         WHERE user_id = :friend_id AND friend_id = :user_id 
                         AND status = 'pending'";
                break;

            case 'cancel':
                $query = "DELETE FROM user_friends 
                         WHERE user_id = :user_id AND friend_id = :friend_id 
                         AND status = 'pending'";
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
                exit;
        }
            
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $data->user_id);
        $stmt->bindParam(':friend_id', $data->friend_id);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Action completed successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to complete action']);
        }
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}
