{"ast": null, "code": "// Enhanced Dynamic API Configuration - Works on ANY domain without hardcoding\nconst isDevelopment=process.env.NODE_ENV==='development'||window.location.hostname==='localhost'||window.location.hostname==='127.0.0.1';// Smart project path detection\nconst getProjectPath=()=>{// Priority 1: Environment variable override\nif(process.env.REACT_APP_PROJECT_PATH){return process.env.REACT_APP_PROJECT_PATH;}// Priority 2: Development with React dev server (port 3000)\nif(isDevelopment&&window.location.port==='3000'){// Use proxy - no project path needed\nreturn'';}// Priority 3: Auto-detect from current URL for production\nconst currentPath=window.location.pathname;const pathParts=currentPath.split('/').filter(part=>part);// If URL has path segments and first segment is not a known route\nconst knownRoutes=['admin','user','login','register','dashboard','api','backend'];if(pathParts.length>0&&!knownRoutes.includes(pathParts[0])){// First segment is likely the project folder\nreturn'/'+pathParts[0];}// Priority 4: Check if we're in a subfolder by testing common patterns\n// This handles cases where the app is deployed in a subfolder\nconst possiblePaths=['/FanBet247','/fanbet247','/app','/betting'];for(const path of possiblePaths){if(currentPath.startsWith(path)){return path;}}// Default: Root domain deployment (no project folder)\nreturn'';};// Dynamic API URL with intelligent detection\nconst getApiBaseUrl=()=>{const projectPath=getProjectPath();// Development with React dev server - use proxy\nif(isDevelopment&&window.location.port==='3000'){return'/backend/handlers';// Proxy routes to localhost/FanBet247/backend/handlers\n}// Production: domain.com or domain.com/projectfolder\n// Ensure we always have a valid URL, never undefined\nconst baseUrl=`${projectPath}/backend/handlers`;// Fallback for production if projectPath is empty\nif(!projectPath||projectPath===''){return'/backend/handlers';}return baseUrl;};export const API_BASE_URL=getApiBaseUrl();export const PROJECT_PATH=getProjectPath();// Enhanced asset URL helper for images, uploads, etc.\nexport const getAssetUrl=path=>{const projectPath=getProjectPath();const cleanPath=path.startsWith('/')?path:`/${path}`;// Development with proxy\nif(isDevelopment&&window.location.port==='3000'){return`/backend${cleanPath}`;// Proxy handles routing\n}// Production: use project path + backend + asset path\nreturn`${projectPath}/backend${cleanPath}`;};// Get full backend URL (for cases where you need the complete URL)\nexport const getBackendUrl=()=>{const projectPath=getProjectPath();if(isDevelopment&&window.location.port==='3000'){return`${window.location.origin}/backend`;}return`${window.location.origin}${projectPath}/backend`;};// Debug logging (only in development)\nif(isDevelopment){console.log('🔧 Enhanced Dynamic API Configuration:');console.log('Environment:',process.env.NODE_ENV);console.log('Current URL:',window.location.href);console.log('Detected Project Path:',PROJECT_PATH);console.log('Final API Base URL:',API_BASE_URL);console.log('Backend URL:',getBackendUrl());console.log('');console.log('📝 This will work on ANY domain:');console.log('   - Development: localhost:3000 → proxy to localhost/FanBet247/backend/handlers');console.log('   - Production Root: yourdomain.com → yourdomain.com/backend/handlers');console.log('   - Production Subfolder: yourdomain.com/myapp → yourdomain.com/myapp/backend/handlers');console.log('   - Subdomain: api.yourdomain.com → api.yourdomain.com/backend/handlers');}", "map": {"version": 3, "names": ["isDevelopment", "process", "env", "NODE_ENV", "window", "location", "hostname", "getProjectPath", "REACT_APP_PROJECT_PATH", "port", "currentPath", "pathname", "pathParts", "split", "filter", "part", "knownRoutes", "length", "includes", "possiblePaths", "path", "startsWith", "getApiBaseUrl", "projectPath", "baseUrl", "API_BASE_URL", "PROJECT_PATH", "getAssetUrl", "cleanPath", "getBackendUrl", "origin", "console", "log", "href"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/config.js"], "sourcesContent": ["// Enhanced Dynamic API Configuration - Works on ANY domain without hardcoding\nconst isDevelopment = process.env.NODE_ENV === 'development' ||\n                     window.location.hostname === 'localhost' ||\n                     window.location.hostname === '127.0.0.1';\n\n// Smart project path detection\nconst getProjectPath = () => {\n    // Priority 1: Environment variable override\n    if (process.env.REACT_APP_PROJECT_PATH) {\n        return process.env.REACT_APP_PROJECT_PATH;\n    }\n\n    // Priority 2: Development with React dev server (port 3000)\n    if (isDevelopment && window.location.port === '3000') {\n        // Use proxy - no project path needed\n        return '';\n    }\n\n    // Priority 3: Auto-detect from current URL for production\n    const currentPath = window.location.pathname;\n    const pathParts = currentPath.split('/').filter(part => part);\n\n    // If URL has path segments and first segment is not a known route\n    const knownRoutes = ['admin', 'user', 'login', 'register', 'dashboard', 'api', 'backend'];\n    if (pathParts.length > 0 && !knownRoutes.includes(pathParts[0])) {\n        // First segment is likely the project folder\n        return '/' + pathParts[0];\n    }\n\n    // Priority 4: Check if we're in a subfolder by testing common patterns\n    // This handles cases where the app is deployed in a subfolder\n    const possiblePaths = ['/FanBet247', '/fanbet247', '/app', '/betting'];\n    for (const path of possiblePaths) {\n        if (currentPath.startsWith(path)) {\n            return path;\n        }\n    }\n\n    // Default: Root domain deployment (no project folder)\n    return '';\n};\n\n// Dynamic API URL with intelligent detection\nconst getApiBaseUrl = () => {\n    const projectPath = getProjectPath();\n\n    // Development with React dev server - use proxy\n    if (isDevelopment && window.location.port === '3000') {\n        return '/backend/handlers'; // Proxy routes to localhost/FanBet247/backend/handlers\n    }\n\n    // Production: domain.com or domain.com/projectfolder\n    // Ensure we always have a valid URL, never undefined\n    const baseUrl = `${projectPath}/backend/handlers`;\n\n    // Fallback for production if projectPath is empty\n    if (!projectPath || projectPath === '') {\n        return '/backend/handlers';\n    }\n\n    return baseUrl;\n};\n\nexport const API_BASE_URL = getApiBaseUrl();\nexport const PROJECT_PATH = getProjectPath();\n\n// Enhanced asset URL helper for images, uploads, etc.\nexport const getAssetUrl = (path) => {\n    const projectPath = getProjectPath();\n    const cleanPath = path.startsWith('/') ? path : `/${path}`;\n\n    // Development with proxy\n    if (isDevelopment && window.location.port === '3000') {\n        return `/backend${cleanPath}`; // Proxy handles routing\n    }\n\n    // Production: use project path + backend + asset path\n    return `${projectPath}/backend${cleanPath}`;\n};\n\n// Get full backend URL (for cases where you need the complete URL)\nexport const getBackendUrl = () => {\n    const projectPath = getProjectPath();\n\n    if (isDevelopment && window.location.port === '3000') {\n        return `${window.location.origin}/backend`;\n    }\n\n    return `${window.location.origin}${projectPath}/backend`;\n};\n\n// Debug logging (only in development)\nif (isDevelopment) {\n    console.log('🔧 Enhanced Dynamic API Configuration:');\n    console.log('Environment:', process.env.NODE_ENV);\n    console.log('Current URL:', window.location.href);\n    console.log('Detected Project Path:', PROJECT_PATH);\n    console.log('Final API Base URL:', API_BASE_URL);\n    console.log('Backend URL:', getBackendUrl());\n    console.log('');\n    console.log('📝 This will work on ANY domain:');\n    console.log('   - Development: localhost:3000 → proxy to localhost/FanBet247/backend/handlers');\n    console.log('   - Production Root: yourdomain.com → yourdomain.com/backend/handlers');\n    console.log('   - Production Subfolder: yourdomain.com/myapp → yourdomain.com/myapp/backend/handlers');\n    console.log('   - Subdomain: api.yourdomain.com → api.yourdomain.com/backend/handlers');\n}"], "mappings": "AAAA;AACA,KAAM,CAAAA,aAAa,CAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,EACvCC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAK,WAAW,EACxCF,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAK,WAAW,CAE7D;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CACzB;AACA,GAAIN,OAAO,CAACC,GAAG,CAACM,sBAAsB,CAAE,CACpC,MAAO,CAAAP,OAAO,CAACC,GAAG,CAACM,sBAAsB,CAC7C,CAEA;AACA,GAAIR,aAAa,EAAII,MAAM,CAACC,QAAQ,CAACI,IAAI,GAAK,MAAM,CAAE,CAClD;AACA,MAAO,EAAE,CACb,CAEA;AACA,KAAM,CAAAC,WAAW,CAAGN,MAAM,CAACC,QAAQ,CAACM,QAAQ,CAC5C,KAAM,CAAAC,SAAS,CAAGF,WAAW,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,IAAI,EAAIA,IAAI,CAAC,CAE7D;AACA,KAAM,CAAAC,WAAW,CAAG,CAAC,OAAO,CAAE,MAAM,CAAE,OAAO,CAAE,UAAU,CAAE,WAAW,CAAE,KAAK,CAAE,SAAS,CAAC,CACzF,GAAIJ,SAAS,CAACK,MAAM,CAAG,CAAC,EAAI,CAACD,WAAW,CAACE,QAAQ,CAACN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAE,CAC7D;AACA,MAAO,GAAG,CAAGA,SAAS,CAAC,CAAC,CAAC,CAC7B,CAEA;AACA;AACA,KAAM,CAAAO,aAAa,CAAG,CAAC,YAAY,CAAE,YAAY,CAAE,MAAM,CAAE,UAAU,CAAC,CACtE,IAAK,KAAM,CAAAC,IAAI,GAAI,CAAAD,aAAa,CAAE,CAC9B,GAAIT,WAAW,CAACW,UAAU,CAACD,IAAI,CAAC,CAAE,CAC9B,MAAO,CAAAA,IAAI,CACf,CACJ,CAEA;AACA,MAAO,EAAE,CACb,CAAC,CAED;AACA,KAAM,CAAAE,aAAa,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,WAAW,CAAGhB,cAAc,CAAC,CAAC,CAEpC;AACA,GAAIP,aAAa,EAAII,MAAM,CAACC,QAAQ,CAACI,IAAI,GAAK,MAAM,CAAE,CAClD,MAAO,mBAAmB,CAAE;AAChC,CAEA;AACA;AACA,KAAM,CAAAe,OAAO,CAAG,GAAGD,WAAW,mBAAmB,CAEjD;AACA,GAAI,CAACA,WAAW,EAAIA,WAAW,GAAK,EAAE,CAAE,CACpC,MAAO,mBAAmB,CAC9B,CAEA,MAAO,CAAAC,OAAO,CAClB,CAAC,CAED,MAAO,MAAM,CAAAC,YAAY,CAAGH,aAAa,CAAC,CAAC,CAC3C,MAAO,MAAM,CAAAI,YAAY,CAAGnB,cAAc,CAAC,CAAC,CAE5C;AACA,MAAO,MAAM,CAAAoB,WAAW,CAAIP,IAAI,EAAK,CACjC,KAAM,CAAAG,WAAW,CAAGhB,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAqB,SAAS,CAAGR,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,CAAGD,IAAI,CAAG,IAAIA,IAAI,EAAE,CAE1D;AACA,GAAIpB,aAAa,EAAII,MAAM,CAACC,QAAQ,CAACI,IAAI,GAAK,MAAM,CAAE,CAClD,MAAO,WAAWmB,SAAS,EAAE,CAAE;AACnC,CAEA;AACA,MAAO,GAAGL,WAAW,WAAWK,SAAS,EAAE,CAC/C,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAN,WAAW,CAAGhB,cAAc,CAAC,CAAC,CAEpC,GAAIP,aAAa,EAAII,MAAM,CAACC,QAAQ,CAACI,IAAI,GAAK,MAAM,CAAE,CAClD,MAAO,GAAGL,MAAM,CAACC,QAAQ,CAACyB,MAAM,UAAU,CAC9C,CAEA,MAAO,GAAG1B,MAAM,CAACC,QAAQ,CAACyB,MAAM,GAAGP,WAAW,UAAU,CAC5D,CAAC,CAED;AACA,GAAIvB,aAAa,CAAE,CACf+B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CACrDD,OAAO,CAACC,GAAG,CAAC,cAAc,CAAE/B,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC,CACjD4B,OAAO,CAACC,GAAG,CAAC,cAAc,CAAE5B,MAAM,CAACC,QAAQ,CAAC4B,IAAI,CAAC,CACjDF,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEN,YAAY,CAAC,CACnDK,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEP,YAAY,CAAC,CAChDM,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEH,aAAa,CAAC,CAAC,CAAC,CAC5CE,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC,CACfD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/CD,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC,CAC/FD,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC,CACrFD,OAAO,CAACC,GAAG,CAAC,yFAAyF,CAAC,CACtGD,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC,CAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}