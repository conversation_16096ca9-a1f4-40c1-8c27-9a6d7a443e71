<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
    exit();
}

try {
    // Fetch and sanitize inputs
    $leagueId = isset($_GET['league_id']) ? intval($_GET['league_id']) : 0;
    $userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

    if (!$leagueId) {
        jsonResponse(400, 'League ID is required');
    }

    // Get league details along with active season details
    $leagueQuery = "SELECT 
        l.*,
        ls.season_name,
        ls.start_date,
        ls.end_date,
        DATEDIFF(ls.end_date, CURRENT_DATE()) as days_remaining,
        (SELECT COUNT(*) FROM league_memberships WHERE league_id = l.league_id AND status = 'active') as total_members
    FROM leagues l
    LEFT JOIN league_seasons ls ON l.league_id = ls.league_id AND ls.status = 'active'
    WHERE l.league_id = ?";

    $stmt = $conn->prepare($leagueQuery);
    if (!$stmt->execute([$leagueId])) {
        jsonResponse(500, 'Failed to fetch league details');
    }
    $leagueDetails = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$leagueDetails) {
        jsonResponse(404, 'League not found');
    }

    // Add media URLs if available
    if (!empty($leagueDetails['icon_path'])) {
        $leagueDetails['league_icon'] = '/backend/uploads/leagues/icons/' . $leagueDetails['icon_path'];
    }
    if (!empty($leagueDetails['banner_path'])) {
        $leagueDetails['league_banner'] = '/backend/uploads/leagues/banners/' . $leagueDetails['banner_path'];
    }

    // Leaderboard query using aggregation to ensure one row per user
    $leaderboardQuery = "SELECT 
        u.user_id,
        u.username,
        u.full_name,
        COALESCE(u.points, 0) AS points,
        MIN(m.join_date) AS join_date,
        SUM(m.wins) AS wins,
        SUM(m.draws) AS draws,
        SUM(m.losses) AS losses,
        MAX(m.deposit_amount) AS deposit_amount,
        SUM(m.total_bets) AS total_bets,
        COALESCE(MAX(s.streak), 0) AS current_streak
    FROM league_memberships m
    INNER JOIN users u ON m.user_id = u.user_id
    LEFT JOIN user_league_stats s ON u.user_id = s.user_id AND s.league_id = ?
    WHERE m.league_id = ? AND m.status = 'active'
    GROUP BY u.user_id, u.username, u.full_name, u.points
    ORDER BY points DESC, wins DESC, join_date ASC";

    $stmt = $conn->prepare($leaderboardQuery);
    if (!$stmt->execute([$leagueId, $leagueId])) {
        jsonResponse(500, 'Failed to fetch leaderboard details');
    }
    $leaderboard = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add rank positions to the leaderboard
    foreach ($leaderboard as $index => &$player) {
        $player['rank_position'] = $index + 1;
        // Format numeric values
        $player['deposit_amount'] = floatval($player['deposit_amount']);
        $player['points'] = intval($player['points']);
        $player['wins'] = intval($player['wins']);
        $player['draws'] = intval($player['draws']);
        $player['losses'] = intval($player['losses']);
        $player['total_bets'] = intval($player['total_bets']);
        $player['current_streak'] = intval($player['current_streak']);
    }
    unset($player); // Break reference

    // If a user_id is provided, verify that the user is an active member of the league
    $userPosition = null;
    if ($userId) {
        $membershipQuery = "SELECT * FROM league_memberships 
                            WHERE league_id = ? 
                              AND user_id = ? 
                              AND status = 'active' 
                            LIMIT 1";
        $stmt = $conn->prepare($membershipQuery);
        if (!$stmt->execute([$leagueId, $userId])) {
            jsonResponse(500, 'Failed to verify league membership for the user');
        }
        $membership = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$membership) {
            jsonResponse(404, 'User is not an active member of the league');
        }

        // Find the user in the leaderboard array
        foreach ($leaderboard as $player) {
            if ($player['user_id'] == $userId) {
                $userPosition = [
                    'rank' => $player['rank_position'],
                    'points' => $player['points'],
                    'wins' => $player['wins'],
                    'draws' => $player['draws'],
                    'losses' => $player['losses'],
                    'current_streak' => $player['current_streak'],
                    'total_bets' => $player['total_bets'],
                    'deposit_amount' => $player['deposit_amount']
                ];
                break;
            }
        }
    }

    // Format the response data
    $responseData = [
        'league' => [
            'id' => (int)$leagueDetails['league_id'],
            'name' => $leagueDetails['name'],
            'description' => $leagueDetails['description'],
            'min_bet_amount' => (float)$leagueDetails['min_bet_amount'],
            'max_bet_amount' => (float)$leagueDetails['max_bet_amount'],
            'league_icon' => $leagueDetails['league_icon'] ?? null,
            'league_banner' => $leagueDetails['league_banner'] ?? null,
            'total_members' => (int)$leagueDetails['total_members'],
            'season_name' => $leagueDetails['season_name'] ?? null,
            'days_remaining' => (int)$leagueDetails['days_remaining']
        ],
        'leaderboard' => $leaderboard,
        'user_position' => $userPosition
    ];

    jsonResponse(200, 'League details retrieved successfully', $responseData);

} catch (PDOException $e) {
    error_log("Database error in league_details.php: " . $e->getMessage());
    jsonResponse(500, 'A database error occurred. Please try again later.');
} catch (Exception $e) {
    error_log("General error in league_details.php: " . $e->getMessage());
    jsonResponse(500, 'An error occurred. Please try again later.');
}
