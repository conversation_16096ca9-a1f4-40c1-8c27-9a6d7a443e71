<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$query = $_GET['query'] ?? '';

if (empty($query)) {
    echo json_encode(['success' => false, 'message' => 'Search query required', 'users' => []]);
    exit;
}

$searchQuery = "%$query%";
$sql = "SELECT user_id, username, favorite_team FROM users WHERE username LIKE :query";
$stmt = $conn->prepare($sql);
$stmt->bindParam(':query', $searchQuery);
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode(['success' => true, 'users' => $users]);
