<?php
/**
 * Withdrawal Requests API
 * Handles user withdrawal requests and admin approval workflow
 */

require_once '../config/database.php';
require_once '../config/cors.php';

header('Content-Type: application/json');

// Get request method and user authentication
$method = $_SERVER['REQUEST_METHOD'];
$user_id = $_SESSION['user_id'] ?? null;
$admin_id = $_SESSION['admin_id'] ?? null;

try {
    switch ($method) {
        case 'GET':
            if ($admin_id) {
                handleGetAllWithdrawals($pdo, $admin_id);
            } elseif ($user_id) {
                handleGetUserWithdrawals($pdo, $user_id);
            } else {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Authentication required']);
            }
            break;
        case 'POST':
            if ($user_id) {
                handleCreateWithdrawal($pdo, $user_id);
            } else {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'User authentication required']);
            }
            break;
        case 'PUT':
            if ($admin_id) {
                handleUpdateWithdrawal($pdo, $admin_id);
            } else {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Admin authentication required']);
            }
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleGetUserWithdrawals($pdo, $user_id) {
    $stmt = $pdo->prepare("
        SELECT 
            wr.withdrawal_id,
            wr.amount_fancoins,
            wr.amount_currency,
            wr.target_currency_id,
            c.currency_code,
            c.currency_symbol,
            wr.exchange_rate,
            wr.withdrawal_fee,
            wr.net_amount,
            wr.reference_number,
            wr.status,
            wr.admin_notes,
            wr.processed_at,
            wr.created_at,
            upm.method_type,
            upm.method_name
        FROM withdrawal_requests wr
        JOIN user_payment_methods upm ON wr.payment_method_id = upm.payment_method_id
        JOIN currencies c ON wr.target_currency_id = c.currency_id
        WHERE wr.user_id = ?
        ORDER BY wr.created_at DESC
    ");
    
    $stmt->execute([$user_id]);
    $withdrawals = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'data' => $withdrawals
    ]);
}

function handleGetAllWithdrawals($pdo, $admin_id) {
    $status = $_GET['status'] ?? 'all';
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    $where_clause = "";
    $params = [];
    
    if ($status !== 'all') {
        $where_clause = "WHERE wr.status = ?";
        $params[] = $status;
    }
    
    $stmt = $pdo->prepare("
        SELECT 
            wr.withdrawal_id,
            wr.user_id,
            u.username,
            wr.amount_fancoins,
            wr.amount_currency,
            wr.target_currency_id,
            c.currency_code,
            c.currency_symbol,
            wr.exchange_rate,
            wr.withdrawal_fee,
            wr.net_amount,
            wr.reference_number,
            wr.status,
            wr.admin_notes,
            wr.processed_by,
            wr.processed_at,
            wr.created_at,
            upm.method_type,
            upm.method_name,
            upm.account_details
        FROM withdrawal_requests wr
        JOIN users u ON wr.user_id = u.user_id
        JOIN user_payment_methods upm ON wr.payment_method_id = upm.payment_method_id
        JOIN currencies c ON wr.target_currency_id = c.currency_id
        $where_clause
        ORDER BY wr.created_at DESC
        LIMIT ? OFFSET ?
    ");
    
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $withdrawals = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Decode account details for each withdrawal
    foreach ($withdrawals as &$withdrawal) {
        $withdrawal['account_details'] = json_decode($withdrawal['account_details'], true);
    }
    
    // Get total count
    $count_stmt = $pdo->prepare("SELECT COUNT(*) FROM withdrawal_requests wr " . $where_clause);
    if ($status !== 'all') {
        $count_stmt->execute([$status]);
    } else {
        $count_stmt->execute();
    }
    $total = $count_stmt->fetchColumn();
    
    echo json_encode([
        'success' => true,
        'data' => $withdrawals,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function handleCreateWithdrawal($pdo, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $required_fields = ['payment_method_id', 'amount_fancoins', 'target_currency_id'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "Field '$field' is required"]);
            return;
        }
    }
    
    $pdo->beginTransaction();
    
    try {
        // Verify payment method belongs to user and is verified
        $stmt = $pdo->prepare("
            SELECT payment_method_id, is_verified, status 
            FROM user_payment_methods 
            WHERE payment_method_id = ? AND user_id = ?
        ");
        $stmt->execute([$input['payment_method_id'], $user_id]);
        $payment_method = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$payment_method) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Payment method not found']);
            return;
        }
        
        if (!$payment_method['is_verified'] || $payment_method['status'] !== 'active') {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Payment method must be verified and active']);
            return;
        }
        
        // Get user balance
        $stmt = $pdo->prepare("SELECT balance FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $user_balance = $stmt->fetchColumn();
        
        if ($user_balance < $input['amount_fancoins']) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Insufficient balance']);
            return;
        }
        
        // Get exchange rate
        $stmt = $pdo->prepare("SELECT rate_to_fancoin FROM exchange_rates WHERE currency_id = ?");
        $stmt->execute([$input['target_currency_id']]);
        $exchange_rate = $stmt->fetchColumn();
        
        if (!$exchange_rate) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid target currency']);
            return;
        }
        
        // Calculate amounts
        $amount_currency = $input['amount_fancoins'] / $exchange_rate;
        $withdrawal_fee = $input['amount_fancoins'] * 0.02; // 2% fee
        $net_amount = $amount_currency - ($withdrawal_fee / $exchange_rate);
        
        // Generate reference number
        $reference_number = 'WD' . strtoupper(substr(uniqid(), -8));
        
        // Create withdrawal request
        $stmt = $pdo->prepare("
            INSERT INTO withdrawal_requests 
            (user_id, payment_method_id, amount_fancoins, amount_currency, target_currency_id, 
             exchange_rate, withdrawal_fee, net_amount, reference_number, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
        ");
        
        $stmt->execute([
            $user_id,
            $input['payment_method_id'],
            $input['amount_fancoins'],
            $amount_currency,
            $input['target_currency_id'],
            $exchange_rate,
            $withdrawal_fee,
            $net_amount,
            $reference_number
        ]);
        
        $withdrawal_id = $pdo->lastInsertId();
        
        // Deduct balance from user
        $stmt = $pdo->prepare("UPDATE users SET balance = balance - ? WHERE user_id = ?");
        $stmt->execute([$input['amount_fancoins'], $user_id]);
        
        // Create transaction record
        $stmt = $pdo->prepare("
            INSERT INTO transactions 
            (user_id, transaction_type, amount, description, reference_number) 
            VALUES (?, 'withdrawal', ?, ?, ?)
        ");
        
        $stmt->execute([
            $user_id,
            -$input['amount_fancoins'],
            "Withdrawal request: $reference_number",
            $reference_number
        ]);
        
        // Create notification
        createWithdrawalNotification($pdo, $withdrawal_id, $user_id, 'request_received', 
            'Withdrawal Request Received', 
            "Your withdrawal request $reference_number has been received and is pending approval."
        );
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request created successfully',
            'withdrawal_id' => $withdrawal_id,
            'reference_number' => $reference_number
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function handleUpdateWithdrawal($pdo, $admin_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['withdrawal_id']) || !isset($input['status'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Withdrawal ID and status are required']);
        return;
    }
    
    $valid_statuses = ['approved', 'rejected', 'processing', 'completed', 'cancelled'];
    if (!in_array($input['status'], $valid_statuses)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        return;
    }
    
    $pdo->beginTransaction();
    
    try {
        // Get withdrawal details
        $stmt = $pdo->prepare("
            SELECT wr.*, u.username 
            FROM withdrawal_requests wr 
            JOIN users u ON wr.user_id = u.user_id 
            WHERE wr.withdrawal_id = ?
        ");
        $stmt->execute([$input['withdrawal_id']]);
        $withdrawal = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$withdrawal) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Withdrawal not found']);
            return;
        }
        
        // Update withdrawal status
        $stmt = $pdo->prepare("
            UPDATE withdrawal_requests 
            SET status = ?, admin_notes = ?, processed_by = ?, processed_at = NOW() 
            WHERE withdrawal_id = ?
        ");
        
        $stmt->execute([
            $input['status'],
            $input['admin_notes'] ?? null,
            $admin_id,
            $input['withdrawal_id']
        ]);
        
        // Handle status-specific actions
        if ($input['status'] === 'rejected' || $input['status'] === 'cancelled') {
            // Refund the amount to user balance
            $stmt = $pdo->prepare("UPDATE users SET balance = balance + ? WHERE user_id = ?");
            $stmt->execute([$withdrawal['amount_fancoins'], $withdrawal['user_id']]);
            
            // Create refund transaction
            $stmt = $pdo->prepare("
                INSERT INTO transactions 
                (user_id, transaction_type, amount, description, reference_number) 
                VALUES (?, 'refund', ?, ?, ?)
            ");
            
            $stmt->execute([
                $withdrawal['user_id'],
                $withdrawal['amount_fancoins'],
                "Withdrawal refund: {$withdrawal['reference_number']}",
                $withdrawal['reference_number']
            ]);
        }
        
        // Create notification
        $notification_messages = [
            'approved' => 'Your withdrawal request has been approved and is being processed.',
            'rejected' => 'Your withdrawal request has been rejected. Amount refunded to your account.',
            'processing' => 'Your withdrawal is currently being processed.',
            'completed' => 'Your withdrawal has been completed successfully.',
            'cancelled' => 'Your withdrawal request has been cancelled. Amount refunded to your account.'
        ];
        
        createWithdrawalNotification($pdo, $input['withdrawal_id'], $withdrawal['user_id'], 
            $input['status'], 
            'Withdrawal Status Update', 
            $notification_messages[$input['status']]
        );
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal status updated successfully'
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function createWithdrawalNotification($pdo, $withdrawal_id, $user_id, $type, $title, $message) {
    $stmt = $pdo->prepare("
        INSERT INTO withdrawal_notifications 
        (withdrawal_id, user_id, notification_type, title, message) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([$withdrawal_id, $user_id, $type, $title, $message]);
}
?>
