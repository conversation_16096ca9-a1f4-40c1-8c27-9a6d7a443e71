<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FanBet247 Currency System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .api-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .loading {
            color: #6c757d;
            font-style: italic;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .conversion-example {
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 FanBet247 Currency System Test Suite</h1>
    <p>This page tests the currency system APIs and functionality in a browser environment.</p>

    <div class="test-container">
        <h2 class="test-header">📊 API Endpoint Tests</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2 class="test-header">💰 Currency Conversion Examples</h2>
        <div id="conversion-examples"></div>
        <button onclick="testConversions()">Test Conversions</button>
    </div>

    <div class="test-container">
        <h2 class="test-header">🔄 Interactive Currency Converter</h2>
        <div>
            <label for="amount">FanCoin Amount:</label>
            <input type="number" id="amount" value="100" min="0" step="0.01">
            
            <label for="currency">Target Currency:</label>
            <select id="currency">
                <option value="1">USD - US Dollar</option>
                <option value="2">ZAR - South African Rand</option>
                <option value="3">EUR - Euro</option>
                <option value="4">GBP - British Pound</option>
                <option value="5">CAD - Canadian Dollar</option>
                <option value="6">AUD - Australian Dollar</option>
            </select>
            
            <button onclick="convertCurrency()">Convert</button>
        </div>
        <div id="conversion-result"></div>
    </div>

    <script>
        const API_BASE = '/backend/handlers';
        
        function addResult(container, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('conversion-examples').innerHTML = '';
            document.getElementById('conversion-result').innerHTML = '';
        }

        async function testAPI(endpoint, description) {
            const container = document.getElementById('test-results');
            const testDiv = document.createElement('div');
            testDiv.className = 'api-test';
            testDiv.innerHTML = `<strong>${description}</strong><br><span class="loading">Testing...</span>`;
            container.appendChild(testDiv);

            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}/${endpoint}`);
                const endTime = Date.now();
                const data = await response.json();
                
                if (data.success) {
                    testDiv.innerHTML = `
                        <strong>✅ ${description}</strong><br>
                        <small>Response time: ${endTime - startTime}ms</small><br>
                        <small>Status: ${data.status || 'Success'}</small>
                    `;
                    testDiv.className = 'api-test success';
                } else {
                    testDiv.innerHTML = `
                        <strong>❌ ${description}</strong><br>
                        <small>Error: ${data.message || 'Unknown error'}</small>
                    `;
                    testDiv.className = 'api-test error';
                }
            } catch (error) {
                testDiv.innerHTML = `
                    <strong>💥 ${description}</strong><br>
                    <small>Network Error: ${error.message}</small>
                `;
                testDiv.className = 'api-test error';
            }
        }

        async function runAllTests() {
            clearResults();
            
            await testAPI('get_currencies.php', 'Get Currencies API');
            await testAPI('get_exchange_rates.php', 'Get Exchange Rates API');
            await testAPI('convert_currency.php?amount=100&currency_id=1', 'Convert 100 FC to USD');
            await testAPI('convert_currency.php?amount=100&currency_id=2', 'Convert 100 FC to ZAR');
            await testAPI('convert_currency.php?amount=50&currency_id=3', 'Convert 50 FC to EUR');
            
            // Test error handling
            await testAPI('convert_currency.php?amount=100&currency_id=999', 'Invalid Currency Test (should fail)');
        }

        async function testConversions() {
            const container = document.getElementById('conversion-examples');
            container.innerHTML = '<p class="loading">Loading conversion examples...</p>';
            
            try {
                const amounts = [10, 50, 100, 500, 1000];
                const currencies = [
                    {id: 1, code: 'USD', symbol: '$'},
                    {id: 2, code: 'ZAR', symbol: 'R'},
                    {id: 3, code: 'EUR', symbol: '€'},
                    {id: 4, code: 'GBP', symbol: '£'},
                    {id: 5, code: 'CAD', symbol: 'C$'},
                    {id: 6, code: 'AUD', symbol: 'A$'}
                ];
                
                container.innerHTML = '';
                
                for (const amount of amounts) {
                    const exampleDiv = document.createElement('div');
                    exampleDiv.className = 'conversion-example';
                    exampleDiv.innerHTML = `<strong>${amount} FanCoin conversions:</strong><br>`;
                    
                    for (const currency of currencies) {
                        try {
                            const response = await fetch(`${API_BASE}/convert_currency.php?amount=${amount}&currency_id=${currency.id}`);
                            const data = await response.json();
                            
                            if (data.success) {
                                exampleDiv.innerHTML += `${currency.code}: ${data.formatted_amount || currency.symbol + data.converted_amount}<br>`;
                            } else {
                                exampleDiv.innerHTML += `${currency.code}: Error<br>`;
                            }
                        } catch (error) {
                            exampleDiv.innerHTML += `${currency.code}: Network Error<br>`;
                        }
                    }
                    
                    container.appendChild(exampleDiv);
                }
            } catch (error) {
                container.innerHTML = `<div class="error">Error loading conversions: ${error.message}</div>`;
            }
        }

        async function convertCurrency() {
            const amount = document.getElementById('amount').value;
            const currencyId = document.getElementById('currency').value;
            const resultDiv = document.getElementById('conversion-result');
            
            resultDiv.innerHTML = '<p class="loading">Converting...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/convert_currency.php?amount=${amount}&currency_id=${currencyId}`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="conversion-example">
                            <strong>Conversion Result:</strong><br>
                            ${amount} FanCoin = ${data.formatted_amount || data.converted_amount}<br>
                            <small>Exchange rate: 1 FanCoin = ${data.rate || 'N/A'}</small>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">Conversion failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Network error: ${error.message}</div>`;
            }
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult(document.getElementById('test-results'), 
                    '🚀 Currency System Test Page Loaded Successfully', 'success');
                addResult(document.getElementById('test-results'), 
                    'Click "Run All Tests" to test the API endpoints', 'info');
            }, 500);
        });
    </script>
</body>
</html>
