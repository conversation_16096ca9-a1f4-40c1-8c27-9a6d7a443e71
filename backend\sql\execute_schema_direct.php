<?php
/**
 * Direct SQL Schema Execution
 * Executes the admin authentication schema directly
 */

require_once '../includes/db_connect.php';

header('Content-Type: application/json');

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    echo "Starting database setup...\n";
    
    // Create admin_auth_settings table
    $sql1 = "CREATE TABLE IF NOT EXISTS admin_auth_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_name VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $conn->exec($sql1);
    echo "✅ Created admin_auth_settings table\n";
    
    // Create admin_2fa table
    $sql2 = "CREATE TABLE IF NOT EXISTS admin_2fa (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        secret_key VARCHAR(255),
        is_enabled BOOLEAN NOT NULL DEFAULT 0,
        auth_type ENUM('email_otp', 'google_auth') NOT NULL DEFAULT 'email_otp',
        backup_codes TEXT,
        setup_completed BOOLEAN NOT NULL DEFAULT 0,
        last_used TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
        UNIQUE KEY unique_admin_2fa (admin_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $conn->exec($sql2);
    echo "✅ Created admin_2fa table\n";
    
    // Create admin_otp table
    $sql3 = "CREATE TABLE IF NOT EXISTS admin_otp (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        otp VARCHAR(10) NOT NULL,
        expires_at DATETIME NOT NULL,
        attempts INT NOT NULL DEFAULT 0,
        locked_until DATETIME NULL,
        used BOOLEAN NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
        INDEX idx_admin_otp_admin_id (admin_id),
        INDEX idx_admin_otp_expires (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $conn->exec($sql3);
    echo "✅ Created admin_otp table\n";
    
    // Create admin_auth_logs table
    $sql4 = "CREATE TABLE IF NOT EXISTS admin_auth_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        auth_type ENUM('password', 'otp', '2fa', 'backup_code', 'recovery', 'config', 'security', 'test', 'system') NOT NULL,
        action VARCHAR(50) NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        details TEXT,
        log_level ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL DEFAULT 'INFO',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
        INDEX idx_admin_auth_logs_admin_id (admin_id),
        INDEX idx_admin_auth_logs_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $conn->exec($sql4);
    echo "✅ Created admin_auth_logs table\n";
    
    // Create admin_login_attempts table
    $sql5 = "CREATE TABLE IF NOT EXISTS admin_login_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT,
        ip_address VARCHAR(45) NOT NULL,
        attempt_type ENUM('password', 'otp', '2fa', 'login', 'critical_incident') NOT NULL,
        attempts INT NOT NULL DEFAULT 1,
        locked_until DATETIME NULL,
        last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
        INDEX idx_admin_login_attempts_ip (ip_address),
        INDEX idx_admin_login_attempts_admin_id (admin_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $conn->exec($sql5);
    echo "✅ Created admin_login_attempts table\n";
    
    // Create admin_recovery_codes table
    $sql6 = "CREATE TABLE IF NOT EXISTS admin_recovery_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        recovery_code VARCHAR(20) NOT NULL,
        expires_at DATETIME NOT NULL,
        used BOOLEAN NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
        INDEX idx_admin_recovery_admin_id (admin_id),
        INDEX idx_admin_recovery_code (recovery_code),
        INDEX idx_admin_recovery_expires (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $conn->exec($sql6);
    echo "✅ Created admin_recovery_codes table\n";
    
    // Create admin_recovery_tokens table
    $sql7 = "CREATE TABLE IF NOT EXISTS admin_recovery_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        access_token VARCHAR(255) NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(admin_id) ON DELETE CASCADE,
        UNIQUE KEY unique_admin_token (admin_id),
        INDEX idx_admin_recovery_token (access_token),
        INDEX idx_admin_recovery_token_expires (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $conn->exec($sql7);
    echo "✅ Created admin_recovery_tokens table\n";
    
    // Add columns to admins table
    try {
        $conn->exec("ALTER TABLE admins ADD COLUMN two_factor_enabled BOOLEAN NOT NULL DEFAULT 0");
        echo "✅ Added two_factor_enabled column to admins table\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️ Column two_factor_enabled already exists\n";
        } else {
            throw $e;
        }
    }
    
    try {
        $conn->exec("ALTER TABLE admins ADD COLUMN auth_method ENUM('password_only', 'otp', '2fa') NOT NULL DEFAULT 'password_only'");
        echo "✅ Added auth_method column to admins table\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️ Column auth_method already exists\n";
        } else {
            throw $e;
        }
    }
    
    try {
        $conn->exec("ALTER TABLE admins ADD COLUMN last_2fa_setup TIMESTAMP NULL");
        echo "✅ Added last_2fa_setup column to admins table\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️ Column last_2fa_setup already exists\n";
        } else {
            throw $e;
        }
    }
    
    try {
        $conn->exec("ALTER TABLE admins ADD COLUMN account_locked_until DATETIME NULL");
        echo "✅ Added account_locked_until column to admins table\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️ Column account_locked_until already exists\n";
        } else {
            throw $e;
        }
    }
    
    try {
        $conn->exec("ALTER TABLE admins ADD COLUMN failed_login_attempts INT NOT NULL DEFAULT 0");
        echo "✅ Added failed_login_attempts column to admins table\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️ Column failed_login_attempts already exists\n";
        } else {
            throw $e;
        }
    }
    
    // Insert default settings
    $defaultSettings = [
        ['admin_auth_method', 'password_only', 'Admin authentication method: password_only, otp, 2fa'],
        ['admin_otp_enabled', 'false', 'Enable OTP authentication for admins'],
        ['admin_2fa_enabled', 'false', 'Enable 2FA authentication for admins'],
        ['admin_otp_expiry_time', '300', 'Admin OTP expiry time in seconds (default: 5 minutes)'],
        ['admin_max_otp_attempts', '3', 'Maximum OTP verification attempts before lockout'],
        ['admin_max_login_attempts', '5', 'Maximum login attempts before account lockout'],
        ['admin_lockout_time', '1800', 'Admin account lockout time in seconds (default: 30 minutes)'],
        ['admin_require_2fa_for', 'login,password_change', 'Actions requiring 2FA verification (comma-separated)'],
        ['admin_backup_codes_count', '10', 'Number of backup codes to generate for 2FA'],
        ['admin_session_timeout', '3600', 'Admin session timeout in seconds (default: 1 hour)']
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO admin_auth_settings (setting_name, setting_value, description) 
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE 
        setting_value = VALUES(setting_value),
        description = VALUES(description),
        updated_at = CURRENT_TIMESTAMP
    ");
    
    foreach ($defaultSettings as $setting) {
        $stmt->execute($setting);
    }
    echo "✅ Inserted default authentication settings\n";
    
    // Create view
    $viewSql = "CREATE OR REPLACE VIEW admin_auth_status AS
    SELECT 
        a.admin_id,
        a.username,
        a.email,
        a.role,
        a.auth_method,
        a.two_factor_enabled,
        a.account_locked_until,
        a.failed_login_attempts,
        a2fa.is_enabled as has_2fa_setup,
        a2fa.auth_type as preferred_2fa_type,
        a2fa.setup_completed as is_2fa_setup_complete,
        CASE 
            WHEN a.account_locked_until IS NOT NULL AND a.account_locked_until > NOW() THEN 'locked'
            WHEN a.two_factor_enabled = 1 AND a2fa.setup_completed = 1 THEN 'secure'
            WHEN a.auth_method = 'password_only' THEN 'basic'
            ELSE 'setup_required'
        END as security_status
    FROM admins a
    LEFT JOIN admin_2fa a2fa ON a.admin_id = a2fa.admin_id";
    
    $conn->exec($viewSql);
    echo "✅ Created admin_auth_status view\n";
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "All admin authentication tables and settings have been created.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
