<?php
/**
 * Get Admin Authentication Settings
 * Returns current admin authentication configuration
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Check if admin_auth_settings table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'admin_auth_settings'");
    if ($stmt->rowCount() === 0) {
        // Table doesn't exist, return default settings
        echo json_encode([
            'success' => true,
            'settings' => [
                'admin_auth_method' => 'password_only',
                'admin_otp_enabled' => 'false',
                'admin_2fa_enabled' => 'false',
                'admin_otp_expiry_time' => '300',
                'admin_max_otp_attempts' => '3',
                'admin_max_login_attempts' => '5',
                'admin_lockout_time' => '1800',
                'admin_require_2fa_for' => 'login,password_change',
                'admin_backup_codes_count' => '10',
                'admin_session_timeout' => '3600'
            ],
            'table_exists' => false,
            'message' => 'Using default settings - admin authentication tables not yet created'
        ]);
        exit;
    }
    
    // Get all admin authentication settings
    $stmt = $conn->query("SELECT setting_name, setting_value, description FROM admin_auth_settings ORDER BY setting_name");
    $settings = [];
    $descriptions = [];
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_name']] = $row['setting_value'];
        $descriptions[$row['setting_name']] = $row['description'];
    }
    
    // If no settings found, insert defaults
    if (empty($settings)) {
        $defaultSettings = [
            ['admin_auth_method', 'password_only', 'Admin authentication method: password_only, otp, 2fa'],
            ['admin_otp_enabled', 'false', 'Enable OTP authentication for admins'],
            ['admin_2fa_enabled', 'false', 'Enable 2FA authentication for admins'],
            ['admin_otp_expiry_time', '300', 'Admin OTP expiry time in seconds (default: 5 minutes)'],
            ['admin_max_otp_attempts', '3', 'Maximum OTP verification attempts before lockout'],
            ['admin_max_login_attempts', '5', 'Maximum login attempts before account lockout'],
            ['admin_lockout_time', '1800', 'Admin account lockout time in seconds (default: 30 minutes)'],
            ['admin_require_2fa_for', 'login,password_change', 'Actions requiring 2FA verification (comma-separated)'],
            ['admin_backup_codes_count', '10', 'Number of backup codes to generate for 2FA'],
            ['admin_session_timeout', '3600', 'Admin session timeout in seconds (default: 1 hour)']
        ];
        
        $insertStmt = $conn->prepare("INSERT INTO admin_auth_settings (setting_name, setting_value, description) VALUES (?, ?, ?)");
        
        foreach ($defaultSettings as $setting) {
            $insertStmt->execute($setting);
            $settings[$setting[0]] = $setting[1];
            $descriptions[$setting[0]] = $setting[2];
        }
    }
    
    // Get SMTP configuration status (required for OTP)
    $smtpConfigured = false;
    try {
        $stmt = $conn->query("SELECT is_active FROM smtp_settings WHERE is_active = 'true' OR is_active = '1' OR is_active = 1 LIMIT 1");
        $smtpConfigured = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        // SMTP table might not exist
        $smtpConfigured = false;
    }
    
    // Get current admin count and their authentication status
    $adminStats = [];
    try {
        $stmt = $conn->query("
            SELECT 
                COUNT(*) as total_admins,
                SUM(CASE WHEN auth_method = 'password_only' THEN 1 ELSE 0 END) as password_only,
                SUM(CASE WHEN auth_method = 'otp' THEN 1 ELSE 0 END) as otp_enabled,
                SUM(CASE WHEN auth_method = '2fa' THEN 1 ELSE 0 END) as tfa_enabled,
                SUM(CASE WHEN two_factor_enabled = 1 THEN 1 ELSE 0 END) as has_2fa_setup
            FROM admins
        ");
        $adminStats = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Admins table might not have new columns yet
        $stmt = $conn->query("SELECT COUNT(*) as total_admins FROM admins");
        $adminStats = $stmt->fetch(PDO::FETCH_ASSOC);
        $adminStats['password_only'] = $adminStats['total_admins'];
        $adminStats['otp_enabled'] = 0;
        $adminStats['tfa_enabled'] = 0;
        $adminStats['has_2fa_setup'] = 0;
    }
    
    echo json_encode([
        'success' => true,
        'settings' => $settings,
        'descriptions' => $descriptions,
        'smtp_configured' => $smtpConfigured,
        'admin_stats' => $adminStats,
        'table_exists' => true,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to retrieve admin authentication settings',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
