<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get favicon setting from database
    $stmt = $conn->prepare("SELECT setting_value FROM general_settings WHERE setting_name = 'site_favicon'");
    $stmt->execute();
    
    $faviconPath = null;
    if ($stmt->rowCount() > 0) {
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $faviconPath = $row['setting_value'];
    }
    
    // Check if favicon file exists
    $faviconExists = false;
    $fullPath = null;
    
    if ($faviconPath) {
        $fullPath = "../{$faviconPath}";
        $faviconExists = file_exists($fullPath);
    }
    
    // Also check the default public favicon
    $publicFaviconExists = file_exists('../../frontend/public/favicon.ico');
    
    echo json_encode([
        'success' => true,
        'favicon_path' => $faviconPath,
        'favicon_exists' => $faviconExists,
        'public_favicon_exists' => $publicFaviconExists,
        'favicon_url' => $faviconPath ? "/backend/{$faviconPath}" : '/favicon.ico',
        'public_favicon_url' => '/favicon.ico'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
