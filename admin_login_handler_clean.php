<?php
/**
 * Clean Admin Login Handler
 * Fixed version with proper syntax and error handling
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// CORS Headers
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Credentials: true");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, X-Admin-ID");

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Include database connection
include_once '../includes/db_connect.php';

function validateAdminCredentials($identifier, $password) {
    try {
        $conn = getDBConnection();
        if (!$conn) {
            throw new PDOException("Database connection failed");
        }

        $stmt = $conn->prepare("SELECT admin_id, username, password_hash, role, email FROM admins WHERE username = :identifier OR email = :identifier");
        $stmt->bindParam(':identifier', $identifier);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (password_verify($password, $row['password_hash'])) {
                // Update last_login timestamp
                $updateStmt = $conn->prepare("UPDATE admins SET last_login = CURRENT_TIMESTAMP WHERE admin_id = :admin_id");
                $updateStmt->bindParam(':admin_id', $row['admin_id']);
                $updateStmt->execute();

                return [
                    'success' => true,
                    'admin_id' => $row['admin_id'],
                    'username' => $row['username'],
                    'email' => $row['email'],
                    'role' => $row['role']
                ];
            }
        }
        
        return ['success' => false, 'message' => 'Invalid credentials'];
        
    } catch (PDOException $e) {
        error_log("Admin Login Error: " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function generateAdminSession($adminId) {
    try {
        $conn = getDBConnection();
        $sessionToken = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
        
        // Simple session storage (you can enhance this)
        return $sessionToken;
    } catch (Exception $e) {
        error_log("Session generation error: " . $e->getMessage());
        return null;
    }
}

// Main login processing
try {
    // Test database connection first
    $conn = getDBConnection();
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Get raw input
    $rawInput = file_get_contents("php://input");
    
    // Log the raw input for debugging
    error_log("Admin Login - Raw input: " . $rawInput);
    
    // Try to decode JSON
    $data = json_decode($rawInput, true);
    
    // If JSON decode fails, try to get from POST
    if (!$data && !empty($_POST)) {
        $data = $_POST;
        error_log("Admin Login - Using POST data: " . print_r($_POST, true));
    }

    // Validate input data
    if (!$data || !isset($data['identifier']) || !isset($data['password'])) {
        throw new Exception("Missing identifier or password. Received: " . print_r($data, true));
    }

    $identifier = trim($data['identifier']);
    $password = $data['password'];
    
    // Log the attempt
    error_log("Admin Login - Attempting login for: " . $identifier);

    // Validate credentials
    $result = validateAdminCredentials($identifier, $password);
    
    if ($result['success']) {
        // Generate session token
        $sessionToken = generateAdminSession($result['admin_id']);
        
        // Successful login response
        $response = [
            "success" => true,
            "message" => "Login successful",
            "admin_id" => $result['admin_id'],
            "username" => $result['username'],
            "email" => $result['email'],
            "role" => $result['role'],
            "session_token" => $sessionToken,
            "timestamp" => date('Y-m-d H:i:s')
        ];
        
        error_log("Admin Login - Success for: " . $identifier);
        echo json_encode($response);
        
    } else {
        // Failed login response
        $response = [
            "success" => false,
            "message" => "Invalid username/email or password"
        ];
        
        error_log("Admin Login - Failed for: " . $identifier);
        echo json_encode($response);
    }

} catch (Exception $e) {
    // Error response
    error_log("Admin Login Exception: " . $e->getMessage());
    error_log("Admin Login Stack Trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "An error occurred during login",
        "debug_info" => [
            "error" => $e->getMessage(),
            "file" => $e->getFile(),
            "line" => $e->getLine(),
            "timestamp" => date('Y-m-d H:i:s')
        ]
    ]);
}
?>
