<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$userId = $_GET['userId'] ?? null;

if (!$userId) {
    echo json_encode(['success' => false, 'message' => 'User ID required']);
    exit;
}

try {
    // Fetch all bets for the user without limit
    $query = "SELECT 
        b.bet_id,
        b.challenge_id,
        b.bet_status,
        b.created_at,
        b.unique_code,
        b.bet_choice_user1,
        b.bet_choice_user2,
        b.amount_user1,
        b.amount_user2,
        b.potential_return_win_user1,
        b.potential_return_draw_user1,
        b.potential_return_loss_user1,
        b.potential_return_win_user2,
        b.potential_return_draw_user2,
        b.potential_return_loss_user2,
        c.team_a,
        c.team_b,
        c.odds_team_a,
        c.odds_team_b,
        u1.username as user1_name,
        u2.username as user2_name
    FROM bets b
    JOIN challenges c ON b.challenge_id = c.challenge_id
    JOIN users u1 ON b.user1_id = u1.user_id
    LEFT JOIN users u2 ON b.user2_id = u2.user_id
    WHERE b.user1_id = :userId OR b.user2_id = :userId
    ORDER BY b.created_at DESC";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(':userId', $userId);
    $stmt->execute();
    $bets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'bets' => $bets
    ]);
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch bets'
    ]);
}
