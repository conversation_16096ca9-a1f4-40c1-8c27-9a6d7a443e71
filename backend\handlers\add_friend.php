<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$data = json_decode(file_get_contents("php://input"));

if (!isset($data->user_id) || !isset($data->friend_id)) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

try {
    // First check if a friendship already exists
    $checkQuery = "SELECT * FROM user_friends 
                  WHERE (user_id = :user_id AND friend_id = :friend_id)
                  OR (user_id = :friend_id AND friend_id = :user_id)";
    
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bindParam(':user_id', $data->user_id);
    $checkStmt->bindParam(':friend_id', $data->friend_id);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() > 0) {
        $existingFriendship = $checkStmt->fetch(PDO::FETCH_ASSOC);
        if ($existingFriendship['status'] === 'pending') {
            echo json_encode(['success' => false, 'message' => 'Friend request already pending']);
        } else if ($existingFriendship['status'] === 'accepted') {
            echo json_encode(['success' => false, 'message' => 'Already friends']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Previous request exists']);
        }
        exit;
    }

    // If no existing friendship, create new friend request
    $query = "INSERT INTO user_friends (user_id, friend_id, status) 
              VALUES (:user_id, :friend_id, 'pending')";
    
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':user_id', $data->user_id);
    $stmt->bindParam(':friend_id', $data->friend_id);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true, 
            'message' => 'Friend request sent successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Failed to send friend request'
        ]);
    }
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
