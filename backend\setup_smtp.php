<?php
// Setup SMTP configuration for OTP email functionality
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Check if smtp_settings table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'smtp_settings'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        // Create smtp_settings table
        $createTable = "
        CREATE TABLE smtp_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            smtp_host VARCHAR(255) NOT NULL,
            smtp_port INT NOT NULL,
            smtp_username VARCHAR(255) NOT NULL,
            smtp_password VARCHAR(255) NOT NULL,
            smtp_encryption VARCHAR(10) DEFAULT 'tls',
            from_email VARCHAR(255) NOT NULL,
            from_name VARCHAR(255) NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        
        $conn->exec($createTable);
        echo "✅ Created smtp_settings table\n";
    } else {
        echo "✅ smtp_settings table already exists\n";
    }
    
    // Check if there are any SMTP settings
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM smtp_settings WHERE is_active = 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        // Insert default SMTP settings (using Gmail as example)
        $stmt = $conn->prepare("
            INSERT INTO smtp_settings 
            (smtp_host, smtp_port, smtp_username, smtp_password, smtp_encryption, from_email, from_name, is_active) 
            VALUES 
            ('smtp.gmail.com', 587, '<EMAIL>', 'your-app-password', 'tls', '<EMAIL>', 'FanBet247', 1)
        ");
        $stmt->execute();
        
        echo "✅ Inserted default SMTP settings\n";
        echo "⚠️  IMPORTANT: You need to update the SMTP credentials:\n";
        echo "   1. Update smtp_username with your actual email\n";
        echo "   2. Update smtp_password with your app password\n";
        echo "   3. Or configure with your preferred email service\n\n";
        
        echo "📧 Common SMTP configurations:\n";
        echo "   Gmail: smtp.gmail.com:587 (TLS)\n";
        echo "   Outlook: smtp-mail.outlook.com:587 (STARTTLS)\n";
        echo "   Yahoo: smtp.mail.yahoo.com:587 (TLS)\n";
        echo "   SendGrid: smtp.sendgrid.net:587 (TLS)\n\n";
    } else {
        echo "✅ SMTP settings already configured\n";
    }
    
    // Display current SMTP settings
    $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $smtp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($smtp) {
        echo "\n📊 Current SMTP Configuration:\n";
        echo "   Host: " . $smtp['smtp_host'] . "\n";
        echo "   Port: " . $smtp['smtp_port'] . "\n";
        echo "   Username: " . $smtp['smtp_username'] . "\n";
        echo "   Password: " . str_repeat('*', strlen($smtp['smtp_password'])) . "\n";
        echo "   Encryption: " . $smtp['smtp_encryption'] . "\n";
        echo "   From Email: " . $smtp['from_email'] . "\n";
        echo "   From Name: " . $smtp['from_name'] . "\n";
        echo "   Status: " . ($smtp['is_active'] ? 'Active' : 'Inactive') . "\n";
    }
    
    echo "\n🔧 To enable OTP email functionality:\n";
    echo "1. Configure SMTP settings in the database\n";
    echo "2. Test email sending with a real email service\n";
    echo "3. Enable OTP for users in the settings page\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
