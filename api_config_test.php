<?php
/**
 * API Configuration Test
 * Tests the API configuration and connectivity
 */

header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Get the action parameter
$action = $_GET['action'] ?? 'info';

switch ($action) {
    case 'info':
        echo json_encode([
            'status' => 'success',
            'message' => 'API Configuration Test',
            'data' => [
                'server_info' => [
                    'php_version' => PHP_VERSION,
                    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
                    'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown',
                    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
                    'http_host' => $_SERVER['HTTP_HOST'] ?? 'Unknown',
                    'https' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off',
                    'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
                    'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'Unknown'
                ],
                'file_structure' => [
                    'backend_dir' => is_dir('../backend'),
                    'handlers_dir' => is_dir('../backend/handlers'),
                    'includes_dir' => is_dir('../backend/includes'),
                    'admin_login_handler' => file_exists('../backend/handlers/admin_login_handler.php'),
                    'user_login_handler' => file_exists('../backend/handlers/login.php'),
                    'db_connect' => file_exists('../backend/includes/db_connect.php')
                ],
                'environment' => [
                    'current_directory' => getcwd(),
                    'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'Unknown',
                    'query_string' => $_SERVER['QUERY_STRING'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
                ]
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        break;

    case 'test_db':
        try {
            include_once '../backend/includes/db_connect.php';
            $conn = getDBConnection();
            
            if ($conn) {
                $stmt = $conn->query("SELECT COUNT(*) as admin_count FROM admins");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Database connection successful',
                    'data' => [
                        'admin_count' => $result['admin_count'],
                        'connection_type' => get_class($conn)
                    ]
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Database connection failed'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage()
            ]);
        }
        break;

    case 'test_admin_endpoint':
        $url = '/backend/handlers/admin_login_handler.php';
        $fullPath = $_SERVER['DOCUMENT_ROOT'] . $url;
        
        echo json_encode([
            'status' => 'success',
            'message' => 'Admin endpoint test',
            'data' => [
                'url' => $url,
                'full_path' => $fullPath,
                'file_exists' => file_exists($fullPath),
                'file_readable' => file_exists($fullPath) && is_readable($fullPath),
                'file_size' => file_exists($fullPath) ? filesize($fullPath) : 0,
                'file_modified' => file_exists($fullPath) ? date('Y-m-d H:i:s', filemtime($fullPath)) : null
            ]
        ]);
        break;

    case 'test_user_endpoint':
        $url = '/backend/handlers/login.php';
        $fullPath = $_SERVER['DOCUMENT_ROOT'] . $url;
        
        echo json_encode([
            'status' => 'success',
            'message' => 'User endpoint test',
            'data' => [
                'url' => $url,
                'full_path' => $fullPath,
                'file_exists' => file_exists($fullPath),
                'file_readable' => file_exists($fullPath) && is_readable($fullPath),
                'file_size' => file_exists($fullPath) ? filesize($fullPath) : 0,
                'file_modified' => file_exists($fullPath) ? date('Y-m-d H:i:s', filemtime($fullPath)) : null
            ]
        ]);
        break;

    case 'test_cors':
        $origin = $_SERVER['HTTP_ORIGIN'] ?? 'Unknown';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'Unknown';
        $headers = getallheaders();
        
        echo json_encode([
            'status' => 'success',
            'message' => 'CORS test',
            'data' => [
                'origin' => $origin,
                'method' => $method,
                'headers' => $headers,
                'cors_headers_sent' => [
                    'Access-Control-Allow-Origin' => '*',
                    'Access-Control-Allow-Methods' => 'GET, POST, OPTIONS',
                    'Access-Control-Allow-Headers' => 'Content-Type, X-Requested-With'
                ]
            ]
        ]);
        break;

    case 'ping':
        echo json_encode([
            'status' => 'success',
            'message' => 'Pong! API is reachable',
            'timestamp' => date('Y-m-d H:i:s'),
            'server_time' => time()
        ]);
        break;

    default:
        echo json_encode([
            'status' => 'error',
            'message' => 'Unknown action',
            'available_actions' => ['info', 'test_db', 'test_admin_endpoint', 'test_user_endpoint', 'test_cors', 'ping']
        ]);
        break;
}
?>
