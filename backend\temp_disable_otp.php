<?php
// Temporary script to disable <PERSON>TP for testuser123
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Disable OTP for testuser123
    $stmt = $conn->prepare("UPDATE users SET otp_enabled = 0, auth_method = 'password_only' WHERE username = 'testuser123'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "✅ Successfully disabled OTP for testuser123\n";
        echo "   - otp_enabled set to 0\n";
        echo "   - auth_method set to 'password_only'\n";
        echo "   - You can now log in with just username and password\n";
    } else {
        echo "❌ No user found with username 'testuser123'\n";
    }
    
    // Verify the change
    $stmt = $conn->prepare("SELECT username, otp_enabled, tfa_enabled, auth_method FROM users WHERE username = 'testuser123'");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "\n📊 Current settings for testuser123:\n";
        echo "   - Username: " . $user['username'] . "\n";
        echo "   - OTP Enabled: " . ($user['otp_enabled'] ? 'Yes' : 'No') . "\n";
        echo "   - 2FA Enabled: " . ($user['tfa_enabled'] ? 'Yes' : 'No') . "\n";
        echo "   - Auth Method: " . $user['auth_method'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
