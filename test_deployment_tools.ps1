# Test Deployment Tools Availability
Write-Host "🔍 Testing Deployment Tools Availability" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Test SSH
Write-Host "`n🔧 Testing SSH..." -ForegroundColor Blue
try {
    $sshPath = Get-Command ssh -ErrorAction Stop
    Write-Host "✅ SSH found at: $($sshPath.Source)" -ForegroundColor Green
    
    # Test SSH connection (with timeout)
    Write-Host "🌐 Testing VPS connection..." -ForegroundColor Yellow
    $sshTest = Start-Process -FilePath "ssh" -ArgumentList "-o","ConnectTimeout=5","-o","StrictHostKeyChecking=no","root@**************","echo 'test'" -Wait -PassThru -WindowStyle Hidden
    if ($sshTest.ExitCode -eq 0) {
        Write-Host "✅ VPS connection successful" -ForegroundColor Green
    } else {
        Write-Host "❌ VPS connection failed (Exit code: $($sshTest.ExitCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ SSH not found" -ForegroundColor Red
}

# Test SCP
Write-Host "`n📤 Testing SCP..." -ForegroundColor Blue
try {
    $scpPath = Get-Command scp -ErrorAction Stop
    Write-Host "✅ SCP found at: $($scpPath.Source)" -ForegroundColor Green
} catch {
    Write-Host "❌ SCP not found" -ForegroundColor Red
}

# Test PowerShell version
Write-Host "`n💻 PowerShell Information:" -ForegroundColor Blue
Write-Host "Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
Write-Host "Edition: $($PSVersionTable.PSEdition)" -ForegroundColor White

# Test file existence
Write-Host "`n📁 Checking Local Files:" -ForegroundColor Blue
$requiredFiles = @(
    "index.html",
    ".htaccess", 
    "debug.php",
    "vps_structure_checker.php",
    "backend\handlers\homepage_data.php"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (missing)" -ForegroundColor Red
    }
}

# Check directory structure
Write-Host "`n📂 Directory Structure:" -ForegroundColor Blue
$directories = @("backend", "backend\handlers", "backend\includes", "static")
foreach ($dir in $directories) {
    if (Test-Path $dir -PathType Container) {
        $fileCount = (Get-ChildItem $dir -File).Count
        Write-Host "✅ $dir ($fileCount files)" -ForegroundColor Green
    } else {
        Write-Host "❌ $dir (missing)" -ForegroundColor Red
    }
}

# Alternative deployment methods
Write-Host "`n🛠️ Alternative Deployment Options:" -ForegroundColor Cyan

# Check for WSL
try {
    $wslTest = wsl --list --quiet 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ WSL available - You can use: wsl ./deploy_to_vps.sh" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ WSL not available" -ForegroundColor Yellow
}

# Check for Git Bash
$gitBashPaths = @(
    "${env:ProgramFiles}\Git\bin\bash.exe",
    "${env:ProgramFiles(x86)}\Git\bin\bash.exe",
    "${env:LOCALAPPDATA}\Programs\Git\bin\bash.exe"
)

foreach ($path in $gitBashPaths) {
    if (Test-Path $path) {
        Write-Host "✅ Git Bash found at: $path" -ForegroundColor Green
        Write-Host "   You can use: `"$path`" ./deploy_to_vps.sh" -ForegroundColor White
        break
    }
}

Write-Host "`n📋 Manual Deployment Instructions:" -ForegroundColor Cyan
Write-Host "If automated tools don't work, you can:" -ForegroundColor White
Write-Host "1. Use WinSCP, FileZilla, or similar SFTP client" -ForegroundColor White
Write-Host "2. Upload files to: /var/www/fanbet247.xyz/" -ForegroundColor White
Write-Host "3. Set permissions via SSH or web panel" -ForegroundColor White

Write-Host "`n🎯 Next Steps:" -ForegroundColor Cyan
if ((Get-Command ssh -ErrorAction SilentlyContinue) -and (Get-Command scp -ErrorAction SilentlyContinue)) {
    Write-Host "✅ SSH tools available - You can run the deployment script" -ForegroundColor Green
    Write-Host "Run: .\deploy_to_vps.ps1" -ForegroundColor White
} else {
    Write-Host "⚠️ SSH tools not available - Use manual deployment" -ForegroundColor Yellow
    Write-Host "See VPS_DEPLOYMENT_GUIDE.md for detailed instructions" -ForegroundColor White
}

Write-Host "`n🔗 Test URLs after deployment:" -ForegroundColor Cyan
Write-Host "https://fanbet247.xyz/vps_structure_checker.php" -ForegroundColor White
Write-Host "https://fanbet247.xyz/debug.php" -ForegroundColor White
Write-Host "https://fanbet247.xyz" -ForegroundColor White
