<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get request data
    $data = json_decode(file_get_contents("php://input"));
    
    // Validate required fields
    if (!isset($data->email) || !isset($data->otp)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Email and OTP are required"
        ]);
        exit;
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT user_id FROM users WHERE email = :email");
    $stmt->bindParam(':email', $data->email);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode([
            "success" => false,
            "message" => "User not found"
        ]);
        exit;
    }
    
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get max OTP attempts
    $stmt = $conn->prepare("SELECT setting_value FROM security_settings WHERE setting_name = 'max_otp_attempts'");
    $stmt->execute();
    $maxAttempts = (int)$stmt->fetch(PDO::FETCH_ASSOC)['setting_value'];
    
    // Get lockout time
    $stmt = $conn->prepare("SELECT setting_value FROM security_settings WHERE setting_name = 'lockout_time'");
    $stmt->execute();
    $lockoutTime = (int)$stmt->fetch(PDO::FETCH_ASSOC)['setting_value'];
    
    // Check if user is locked out
    $stmt = $conn->prepare("
        SELECT locked_until FROM user_otp 
        WHERE user_id = :user_id AND locked_until > NOW()
    ");
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $lockedUntil = $stmt->fetch(PDO::FETCH_ASSOC)['locked_until'];
        http_response_code(403);
        echo json_encode([
            "success" => false,
            "message" => "Account is locked. Try again after " . $lockedUntil
        ]);
        exit;
    }
    
    // Check OTP
    $stmt = $conn->prepare("
        SELECT id, otp, expires_at, attempts FROM user_otp 
        WHERE user_id = :user_id AND expires_at > NOW()
    ");
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "OTP expired or not found"
        ]);
        exit;
    }
    
    $otpData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Check if OTP matches
    if ($otpData['otp'] !== $data->otp) {
        // Increment attempts
        $attempts = $otpData['attempts'] + 1;
        
        if ($attempts >= $maxAttempts) {
            // Lock account
            $lockedUntil = date('Y-m-d H:i:s', time() + $lockoutTime);
            $stmt = $conn->prepare("
                UPDATE user_otp 
                SET attempts = :attempts, locked_until = :locked_until 
                WHERE id = :id
            ");
            $stmt->bindParam(':attempts', $attempts);
            $stmt->bindParam(':locked_until', $lockedUntil);
            $stmt->bindParam(':id', $otpData['id']);
            $stmt->execute();
            
            http_response_code(403);
            echo json_encode([
                "success" => false,
                "message" => "Too many failed attempts. Account locked for " . ($lockoutTime / 60) . " minutes"
            ]);
        } else {
            // Update attempts
            $stmt = $conn->prepare("UPDATE user_otp SET attempts = :attempts WHERE id = :id");
            $stmt->bindParam(':attempts', $attempts);
            $stmt->bindParam(':id', $otpData['id']);
            $stmt->execute();
            
            http_response_code(400);
            echo json_encode([
                "success" => false,
                "message" => "Invalid OTP. " . ($maxAttempts - $attempts) . " attempts remaining"
            ]);
        }
        exit;
    }
    
    // OTP is valid, clear it
    $stmt = $conn->prepare("DELETE FROM user_otp WHERE id = :id");
    $stmt->bindParam(':id', $otpData['id']);
    $stmt->execute();
    
    // Get user details
    $stmt = $conn->prepare("SELECT user_id, username FROM users WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->execute();
    $userData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Start session
    session_start();
    $_SESSION['user_id'] = $userData['user_id'];
    $_SESSION['username'] = $userData['username'];
    
    http_response_code(200);
    echo json_encode([
        "success" => true,
        "message" => "OTP verified successfully",
        "userId" => $userData['user_id'],
        "username" => $userData['username']
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
}
?>
