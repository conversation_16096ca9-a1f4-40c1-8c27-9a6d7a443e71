<?php
/**
 * Enable 2FA for Testing
 * Temporarily enable 2FA to test functionality
 */

header('Content-Type: text/plain');

require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    echo "Enabling 2FA for testing...\n";

    // Enable 2FA globally
    $stmt = $conn->prepare("
        UPDATE admin_auth_settings 
        SET setting_value = 'true' 
        WHERE setting_name = 'admin_2fa_enabled'
    ");
    $stmt->execute();
    
    echo "✅ 2FA enabled globally\n";

    // Verify the setting
    $stmt = $conn->prepare("
        SELECT setting_value 
        FROM admin_auth_settings 
        WHERE setting_name = 'admin_2fa_enabled'
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Current 2FA setting: " . $result['setting_value'] . "\n";

    // Test 2FA setup endpoint
    echo "\nTesting 2FA setup endpoint...\n";

    // Get the first admin for testing
    $stmt = $conn->query("SELECT admin_id, username, email FROM admins LIMIT 1");
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("No admin found for testing");
    }

    echo "Testing 2FA setup for admin: {$admin['username']} ({$admin['email']})\n";

    // Test 2FA setup
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/FanBet247/backend/handlers/admin_setup_2fa.php?adminId={$admin['admin_id']}");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Response Code: $httpCode\n";
    
    $responseData = json_decode($response, true);
    
    if ($responseData && $responseData['success']) {
        echo "✅ 2FA setup endpoint working!\n";
        echo "Secret key generated: " . substr($responseData['secret_key'], 0, 10) . "...\n";
        echo "QR code URL generated: " . (isset($responseData['qr_code_url']) ? 'Yes' : 'No') . "\n";
        echo "Backup codes count: " . count($responseData['backup_codes'] ?? []) . "\n";
        
    } else {
        echo "❌ 2FA setup failed\n";
        echo "Error: " . ($responseData['message'] ?? 'Unknown error') . "\n";
        echo "Response: $response\n";
    }

    echo "\n🎉 Test completed! Both OTP and 2FA are now enabled for testing.\n";
    echo "You can now:\n";
    echo "1. Test OTP authentication in the Security Settings\n";
    echo "2. Test 2FA setup in the 2FA Settings page\n";
    echo "3. Test the complete authentication flow\n";

} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
}
?>
