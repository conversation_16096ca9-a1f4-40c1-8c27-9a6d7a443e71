<?php
require_once __DIR__ . '/../includes/db_connect.php';

try {
    echo "Testing database connection...\n";
    $conn = getDBConnection();
    echo "✅ Database connection successful!\n\n";
    
    // Check if user_2fa table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'user_2fa'");
    if ($stmt->rowCount() > 0) {
        echo "✅ user_2fa table already exists\n";
    } else {
        echo "❌ user_2fa table does not exist, creating...\n";
        
        // Create user_2fa table
        $sql = "CREATE TABLE IF NOT EXISTS user_2fa (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            secret_key VARCHAR(255),
            is_enabled BOOLEAN NOT NULL DEFAULT 0,
            auth_type ENUM('email_otp', 'google_auth') NOT NULL DEFAULT 'email_otp',
            backup_codes TEXT,
            setup_completed BOOLEAN NOT NULL DEFAULT 0,
            last_used TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_2fa (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $conn->exec($sql);
        echo "✅ user_2fa table created\n";
    }
    
    // Check if user_otp table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'user_otp'");
    if ($stmt->rowCount() > 0) {
        echo "✅ user_otp table already exists\n";
    } else {
        echo "❌ user_otp table does not exist, creating...\n";
        
        $sql = "CREATE TABLE IF NOT EXISTS user_otp (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            otp VARCHAR(10) NOT NULL,
            expires_at DATETIME NOT NULL,
            attempts INT NOT NULL DEFAULT 0,
            locked_until DATETIME NULL,
            used BOOLEAN NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_otp_user_id (user_id),
            INDEX idx_user_otp_expires (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $conn->exec($sql);
        echo "✅ user_otp table created\n";
    }
    
    // Check if user_login_attempts table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'user_login_attempts'");
    if ($stmt->rowCount() > 0) {
        echo "✅ user_login_attempts table already exists\n";
    } else {
        echo "❌ user_login_attempts table does not exist, creating...\n";
        
        $sql = "CREATE TABLE IF NOT EXISTS user_login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_type ENUM('login', 'otp', '2fa') NOT NULL DEFAULT 'login',
            attempts INT NOT NULL DEFAULT 1,
            locked_until DATETIME NULL,
            last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_attempt (user_id, attempt_type),
            INDEX idx_user_attempts_ip (ip_address),
            INDEX idx_user_attempts_locked (locked_until)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $conn->exec($sql);
        echo "✅ user_login_attempts table created\n";
    }
    
    // Check if user_auth_logs table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'user_auth_logs'");
    if ($stmt->rowCount() > 0) {
        echo "✅ user_auth_logs table already exists\n";
    } else {
        echo "❌ user_auth_logs table does not exist, creating...\n";
        
        $sql = "CREATE TABLE IF NOT EXISTS user_auth_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            auth_type ENUM('login', 'otp', '2fa', 'logout', 'settings') NOT NULL,
            action VARCHAR(50) NOT NULL,
            details JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_auth_logs_user_id (user_id),
            INDEX idx_user_auth_logs_type (auth_type),
            INDEX idx_user_auth_logs_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $conn->exec($sql);
        echo "✅ user_auth_logs table created\n";
    }
    
    // Check if user_auth_settings table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'user_auth_settings'");
    if ($stmt->rowCount() > 0) {
        echo "✅ user_auth_settings table already exists\n";
    } else {
        echo "❌ user_auth_settings table does not exist, creating...\n";
        
        $sql = "CREATE TABLE IF NOT EXISTS user_auth_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            setting_name VARCHAR(100) NOT NULL,
            setting_value TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_setting (user_id, setting_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $conn->exec($sql);
        echo "✅ user_auth_settings table created\n";
    }
    
    // Check if users table has the new columns
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $newColumns = [
        'otp_enabled' => 'BOOLEAN DEFAULT 0',
        'tfa_enabled' => 'BOOLEAN DEFAULT 0', 
        'auth_method' => "ENUM('password_only', 'password_otp', 'password_2fa', 'password_otp_2fa') DEFAULT 'password_only'"
    ];
    
    foreach ($newColumns as $column => $definition) {
        if (!in_array($column, $columns)) {
            echo "❌ Column '$column' missing from users table, adding...\n";
            $sql = "ALTER TABLE users ADD COLUMN $column $definition";
            $conn->exec($sql);
            echo "✅ Column '$column' added to users table\n";
        } else {
            echo "✅ Column '$column' already exists in users table\n";
        }
    }
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "All user authentication tables are ready.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
