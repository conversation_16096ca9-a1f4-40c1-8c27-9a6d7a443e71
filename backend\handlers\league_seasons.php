<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode(['success' => $status === 200, 'message' => $message, 'data' => $data]);
    exit();
}

try {
    // Get database connection
    $conn = getDBConnection();

    // Get user from token or session
    session_start();
    $userId = null;
    
    if (isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
        $stmt = $conn->prepare("SELECT user_id FROM user_sessions WHERE token = ? AND expires_at > NOW()");
        $stmt->execute([$token]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result) {
            $userId = $result['user_id'];
        }
    }

    if (!$userId) {
        jsonResponse(401, 'User not authenticated');
    }

    // Get active seasons with league info
    $query = "SELECT 
                s.season_id,
                s.season_name,
                s.start_date,
                s.end_date,
                s.status,
                l.name as league_name,
                l.league_id,
                DATEDIFF(s.end_date, CURRENT_DATE()) as days_remaining,
                (SELECT COUNT(*) FROM league_memberships WHERE season_id = s.season_id) as member_count
            FROM seasons s
            JOIN leagues l ON s.league_id = l.league_id
            WHERE s.status = 'active'
            ORDER BY s.created_at DESC";

    $stmt = $conn->prepare($query);
    $stmt->execute();
    $seasons = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format dates and add additional info
    foreach ($seasons as &$season) {
        $season['start_date'] = date('Y-m-d', strtotime($season['start_date']));
        $season['end_date'] = date('Y-m-d', strtotime($season['end_date']));
        $season['is_member'] = false;

        // Check if user is a member of this season
        $memberQuery = "SELECT 1 FROM league_memberships 
                       WHERE user_id = ? AND season_id = ? AND status = 'active'";
        $memberStmt = $conn->prepare($memberQuery);
        $memberStmt->execute([$userId, $season['season_id']]);
        if ($memberStmt->fetch()) {
            $season['is_member'] = true;
        }
    }

    jsonResponse(200, 'Success', [
        'seasons' => $seasons
    ]);

} catch (Exception $e) {
    error_log("Error in league_seasons.php: " . $e->getMessage());
    jsonResponse(500, 'An error occurred while fetching seasons');
}
