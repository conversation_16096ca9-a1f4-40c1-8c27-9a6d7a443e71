<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get user account settings
        
        $userId = $_GET['userId'] ?? null;
        
        if (!$userId) {
            throw new Exception("User ID is required");
        }
        
        // Verify user exists
        $stmt = $conn->prepare("SELECT user_id FROM users WHERE user_id = ?");
        $stmt->execute([$userId]);
        if (!$stmt->fetch()) {
            throw new Exception("User not found");
        }
        
        // Get user's account settings
        $stmt = $conn->prepare("
            SELECT setting_name, setting_value 
            FROM user_auth_settings 
            WHERE user_id = ? AND setting_name IN ('email_notifications', 'security_alerts', 'login_notifications')
        ");
        $stmt->execute([$userId]);
        $settingsRows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Default settings
        $defaultSettings = [
            'email_notifications' => true,
            'security_alerts' => true,
            'login_notifications' => true
        ];
        
        // Merge with user's settings
        $settings = $defaultSettings;
        foreach ($settingsRows as $row) {
            $settings[$row['setting_name']] = $row['setting_value'] === '1' || $row['setting_value'] === 'true';
        }
        
        echo json_encode([
            'success' => true,
            'settings' => $settings,
            'message' => 'Account settings loaded successfully'
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Update account settings
        
        $input = json_decode(file_get_contents('php://input'), true);
        $userId = $input['userId'] ?? null;
        $setting = $input['setting'] ?? '';
        $value = $input['value'] ?? false;
        
        if (!$userId) {
            throw new Exception("User ID is required");
        }
        
        if (empty($setting)) {
            throw new Exception("Setting name is required");
        }
        
        // Validate setting name
        $allowedSettings = ['email_notifications', 'security_alerts', 'login_notifications'];
        if (!in_array($setting, $allowedSettings)) {
            throw new Exception("Invalid setting name");
        }
        
        // Verify user exists
        $stmt = $conn->prepare("SELECT user_id FROM users WHERE user_id = ?");
        $stmt->execute([$userId]);
        if (!$stmt->fetch()) {
            throw new Exception("User not found");
        }
        
        $conn->beginTransaction();
        
        // Update or insert setting
        $stmt = $conn->prepare("
            INSERT INTO user_auth_settings (user_id, setting_name, setting_value) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value),
            updated_at = NOW()
        ");
        $stmt->execute([$userId, $setting, $value ? '1' : '0']);
        
        // Log the change
        $stmt = $conn->prepare("
            INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, 'settings', 'setting_updated', ?, ?, ?)
        ");
        $stmt->execute([
            $userId,
            json_encode([
                'setting_name' => $setting,
                'setting_value' => $value
            ]),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Setting updated successfully'
        ]);
    }
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
