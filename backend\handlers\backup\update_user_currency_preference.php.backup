<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, PUT, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';
include_once '../includes/currency_utils.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }
    
    // Only allow POST and PUT methods
    if (!in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT'])) {
        jsonResponse(405, "Method not allowed");
    }
    
    // Get request data
    $input = json_decode(file_get_contents("php://input"), true);
    
    if (!$input) {
        jsonResponse(400, "Invalid JSON data");
    }
    
    // Validate required fields
    $userId = isset($input['user_id']) ? (int)$input['user_id'] : null;
    $currencyId = isset($input['currency_id']) ? (int)$input['currency_id'] : null;
    
    if (!$userId) {
        jsonResponse(400, "User ID is required");
    }
    
    if (!$currencyId) {
        jsonResponse(400, "Currency ID is required");
    }
    
    // Verify user exists
    $stmt = $conn->prepare("SELECT user_id, username, preferred_currency_id FROM users WHERE user_id = :user_id");
    $stmt->execute(['user_id' => $userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        jsonResponse(404, "User not found");
    }
    
    // Check if the new currency is the same as current
    if ((int)$user['preferred_currency_id'] === $currencyId) {
        jsonResponse(200, "Currency preference is already set to the selected currency", [
            'user_id' => $userId,
            'currency_id' => $currencyId,
            'no_change' => true
        ]);
    }
    
    // Verify currency exists and is active
    $stmt = $conn->prepare("
        SELECT 
            c.id,
            c.currency_code,
            c.currency_name,
            c.currency_symbol,
            c.is_active,
            er.rate_to_fancoin
        FROM currencies c
        LEFT JOIN exchange_rates er ON c.id = er.currency_id
        WHERE c.id = :currency_id
        ORDER BY er.updated_at DESC
        LIMIT 1
    ");
    
    $stmt->execute(['currency_id' => $currencyId]);
    $currency = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$currency) {
        jsonResponse(404, "Currency not found");
    }
    
    if (!$currency['is_active']) {
        jsonResponse(400, "Selected currency is not active");
    }
    
    // Get old currency info for logging
    $stmt = $conn->prepare("
        SELECT 
            c.currency_code,
            c.currency_name,
            c.currency_symbol
        FROM currencies c
        WHERE c.id = :currency_id
    ");
    
    $stmt->execute(['currency_id' => $user['preferred_currency_id']]);
    $oldCurrency = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Update user's currency preference
    $stmt = $conn->prepare("UPDATE users SET preferred_currency_id = :currency_id WHERE user_id = :user_id");
    $stmt->execute([
        'currency_id' => $currencyId,
        'user_id' => $userId
    ]);
    
    if ($stmt->rowCount() === 0) {
        jsonResponse(500, "Failed to update currency preference");
    }
    
    // Log the change
    error_log("User {$user['username']} (ID: {$userId}) changed currency preference from {$oldCurrency['currency_code']} to {$currency['currency_code']}");
    
    // Prepare response data
    $responseData = [
        'user_id' => $userId,
        'username' => $user['username'],
        'old_currency' => [
            'id' => (int)$user['preferred_currency_id'],
            'currency_code' => $oldCurrency['currency_code'],
            'currency_name' => $oldCurrency['currency_name'],
            'currency_symbol' => $oldCurrency['currency_symbol']
        ],
        'new_currency' => [
            'id' => (int)$currency['id'],
            'currency_code' => $currency['currency_code'],
            'currency_name' => $currency['currency_name'],
            'currency_symbol' => $currency['currency_symbol'],
            'rate_to_fancoin' => $currency['rate_to_fancoin'] ? (float)$currency['rate_to_fancoin'] : null,
            'formatted_rate' => $currency['rate_to_fancoin'] ? 
                $currency['currency_symbol'] . number_format($currency['rate_to_fancoin'], 4) : 
                'No rate available',
            'display_name' => $currency['currency_symbol'] . ' ' . $currency['currency_code'] . ' - ' . $currency['currency_name']
        ],
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    // Add sample conversion examples if rate is available
    if ($currency['rate_to_fancoin']) {
        $sampleAmounts = [10, 50, 100, 500];
        $sampleConversions = [];
        
        foreach ($sampleAmounts as $amount) {
            $converted = $amount * $currency['rate_to_fancoin'];
            $sampleConversions[] = [
                'fancoin_amount' => $amount,
                'converted_amount' => (float)$converted,
                'formatted' => $currency['currency_symbol'] . number_format($converted, 2)
            ];
        }
        
        $responseData['sample_conversions'] = $sampleConversions;
    }
    
    jsonResponse(200, "Currency preference updated successfully", $responseData);
    
} catch (PDOException $e) {
    error_log("Database error in update_user_currency_preference.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    error_log("General error in update_user_currency_preference.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred while updating currency preference");
}
?>
