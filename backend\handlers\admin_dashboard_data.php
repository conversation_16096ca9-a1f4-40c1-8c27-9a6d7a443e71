<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Total Users
    $stmt = $conn->query("SELECT COUNT(*) as totalUsers FROM users");
    $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['totalUsers'];

    // Total Bets
    $stmt = $conn->query("SELECT COUNT(*) as totalBets FROM bets");
    $totalBets = $stmt->fetch(PDO::FETCH_ASSOC)['totalBets'];

    // Winning Bets
    $stmt = $conn->query("SELECT COUNT(*) as winningBets FROM bets WHERE outcome = 'win'");
    $winningBets = $stmt->fetch(PDO::FETCH_ASSOC)['winningBets'];

    // Active Challenges
    $stmt = $conn->query("SELECT COUNT(*) as activeChallenges FROM challenges WHERE status = 'Open'");
    $activeChallenges = $stmt->fetch(PDO::FETCH_ASSOC)['activeChallenges'];

    // Enhanced Recent Activity Query - Get actual user activities
    $stmt = $conn->query("
        (SELECT 
            'bet' as activity_type,
            u.user_id,
            u.username,
            u.balance,
            b.amount_user1 as amount,
            b.created_at as activity_time,
            c.team_a,
            c.team_b,
            'placed a bet' as action_description,
            b.potential_return_win_user1 as potential_return
        FROM bets b
        JOIN users u ON b.user1_id = u.user_id
        JOIN challenges c ON b.challenge_id = c.challenge_id
        ORDER BY b.created_at DESC
        LIMIT 10)
        
        UNION ALL
        
        (SELECT 
            'registration' as activity_type,
            u.user_id,
            u.username,
            u.balance,
            0 as amount,
            u.created_at as activity_time,
            NULL as team_a,
            NULL as team_b,
            'registered' as action_description,
            0 as potential_return
        FROM users u
        WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY u.created_at DESC
        LIMIT 5)
        
        UNION ALL
        
        (SELECT 
            'transaction' as activity_type,
            u.user_id,
            u.username,
            u.balance,
            t.amount,
            t.created_at as activity_time,
            NULL as team_a,
            NULL as team_b,
            CASE 
                WHEN t.type = 'admin_credit' THEN 'received credit'
                WHEN t.type = 'withdrawal' THEN 'made withdrawal'
                WHEN t.type = 'win' THEN 'won a bet'
                WHEN t.type = 'bet' THEN 'placed a bet'
                WHEN t.type = 'deposit' THEN 'made deposit'
                WHEN t.type = 'transfer_received' THEN 'received transfer'
                WHEN t.type = 'transfer_sent' THEN 'sent transfer'
                ELSE t.type
            END as action_description,
            t.amount as potential_return
        FROM transactions t
        JOIN users u ON t.user_id = u.user_id
        WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY t.created_at DESC
        LIMIT 10)
        
        ORDER BY activity_time DESC
        LIMIT 8
    ");
    $recentActivity = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Recent Challenges Query
$stmt = $conn->query("
SELECT 
    c.*,
    COUNT(b.bet_id) as total_bets
FROM challenges c
LEFT JOIN bets b ON c.challenge_id = b.challenge_id
WHERE c.status = 'Open'
GROUP BY c.challenge_id
ORDER BY c.challenge_date DESC
LIMIT 6
");
$recentChallenges = $stmt->fetchAll(PDO::FETCH_ASSOC);


    // Enhanced Recent Bets Query
    $stmt = $conn->query("
        SELECT 
            b.bet_id,
            u1.username as user1_name,
            u2.username as user2_name,
            b.amount_user1,
            b.amount_user2,
            b.potential_return_win_user1,
            b.potential_return_win_user2,
            c.team_a,
            c.team_b,
            b.created_at,
            b.bet_status
        FROM bets b
        JOIN users u1 ON b.user1_id = u1.user_id
        LEFT JOIN users u2 ON b.user2_id = u2.user_id
        JOIN challenges c ON b.challenge_id = c.challenge_id
        ORDER BY b.created_at DESC
        LIMIT 5
    ");
    $recentBets = $stmt->fetchAll(PDO::FETCH_ASSOC);
      $stmt = $conn->query("
          SELECT 
              teams.*,
              (SELECT COUNT(*) FROM users WHERE users.favorite_team = teams.name) as user_count
          FROM teams
          ORDER BY user_count DESC
          LIMIT 6
      ");
      $teamStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

      $dashboardData = [
          'success' => true,
          'totalUsers' => (int)$totalUsers,
          'totalBets' => (int)$totalBets,
          'activeChallenges' => (int)$activeChallenges,
          'totalWins' => number_format($winningBets * 100, 2),
          'recentActivity' => $recentActivity,
          'recentBets' => $recentBets,
          'teamStats' => $teamStats
      ];

      echo json_encode($dashboardData);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
?>