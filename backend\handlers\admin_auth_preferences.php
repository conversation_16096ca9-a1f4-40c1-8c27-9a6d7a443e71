<?php
/**
 * Admin Authentication Preferences
 * Handles individual admin authentication method preferences
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    $adminId = $_GET['adminId'] ?? null;
    if (!$adminId) {
        throw new Exception("Admin ID required");
    }
    
    // Verify admin exists
    $stmt = $conn->prepare("SELECT admin_id, username, email, role FROM admins WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Admin not found");
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get admin authentication preferences
        
        // Get admin's current auth settings
        $stmt = $conn->prepare("
            SELECT 
                auth_method, 
                two_factor_enabled, 
                account_locked_until, 
                failed_login_attempts,
                last_2fa_setup
            FROM admins 
            WHERE admin_id = ?
        ");
        $stmt->execute([$adminId]);
        $adminAuth = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get 2FA setup status
        $stmt = $conn->prepare("
            SELECT 
                auth_type, 
                is_enabled, 
                setup_completed, 
                last_used 
            FROM admin_2fa 
            WHERE admin_id = ?
        ");
        $stmt->execute([$adminId]);
        $twoFactorSetup = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get global admin auth settings
        $stmt = $conn->query("SELECT setting_name, setting_value FROM admin_auth_settings");
        $globalSettings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $globalSettings[$row['setting_name']] = $row['setting_value'];
        }
        
        echo json_encode([
            'success' => true,
            'admin' => $admin,
            'auth_settings' => $adminAuth ?: [
                'auth_method' => 'password_only',
                'two_factor_enabled' => false,
                'account_locked_until' => null,
                'failed_login_attempts' => 0,
                'last_2fa_setup' => null
            ],
            'two_factor_setup' => $twoFactorSetup ?: [
                'auth_type' => null,
                'is_enabled' => false,
                'setup_completed' => false,
                'last_used' => null
            ],
            'global_settings' => $globalSettings,
            'can_use_otp' => $globalSettings['admin_otp_enabled'] === 'true',
            'can_use_2fa' => $globalSettings['admin_2fa_enabled'] === 'true'
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'PUT') {
        // Update admin authentication preferences
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception("Invalid JSON input");
        }
        
        $authMethod = $input['auth_method'] ?? null;
        $enableTwoFactor = $input['enable_two_factor'] ?? false;
        
        // Validate auth method
        $allowedMethods = ['password_only', 'otp', '2fa'];
        if ($authMethod && !in_array($authMethod, $allowedMethods)) {
            throw new Exception("Invalid authentication method");
        }
        
        // Check global settings
        $stmt = $conn->query("SELECT setting_name, setting_value FROM admin_auth_settings");
        $globalSettings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $globalSettings[$row['setting_name']] = $row['setting_value'];
        }
        
        // Validate against global settings
        if ($authMethod === 'otp' && $globalSettings['admin_otp_enabled'] !== 'true') {
            throw new Exception("OTP authentication is not enabled globally");
        }
        
        if ($authMethod === '2fa' && $globalSettings['admin_2fa_enabled'] !== 'true') {
            throw new Exception("2FA authentication is not enabled globally");
        }
        
        $conn->beginTransaction();
        
        // Update admin auth method
        if ($authMethod) {
            $stmt = $conn->prepare("
                UPDATE admins 
                SET auth_method = ?, two_factor_enabled = ?, updated_at = NOW() 
                WHERE admin_id = ?
            ");
            $stmt->execute([$authMethod, $enableTwoFactor ? 1 : 0, $adminId]);
        }
        
        // If disabling 2FA, clean up 2FA setup
        if ($authMethod !== '2fa' && !$enableTwoFactor) {
            $stmt = $conn->prepare("
                UPDATE admin_2fa 
                SET is_enabled = 0 
                WHERE admin_id = ?
            ");
            $stmt->execute([$adminId]);
        }
        
        // Log the change
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, 'password', 'preferences_updated', ?, ?, ?)
        ");
        $stmt->execute([
            $adminId,
            json_encode(['new_auth_method' => $authMethod, 'two_factor_enabled' => $enableTwoFactor]),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Authentication preferences updated successfully',
            'auth_method' => $authMethod,
            'two_factor_enabled' => $enableTwoFactor
        ]);
    }
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to handle admin authentication preferences',
        'error' => $e->getMessage()
    ]);
}
?>
