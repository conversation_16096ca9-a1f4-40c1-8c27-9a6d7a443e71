<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $user_id = $_GET['user_id'] ?? null;
        
        if (!$user_id) {
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            exit;
        }

        // Check if transactions table exists, if not create mock data from bets
        $check_table = "SHOW TABLES LIKE 'transactions'";
        $table_exists = $db->query($check_table)->rowCount() > 0;
        
        if ($table_exists) {
            // Get actual transactions
            $query = "SELECT * FROM transactions 
                     WHERE user_id = :user_id 
                     ORDER BY created_at DESC 
                     LIMIT 20";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            // Create mock transactions from bet data
            $query = "SELECT 
                        b.bet_id as transaction_id,
                        CASE 
                            WHEN b.winner_user_id = :user_id THEN 'win'
                            WHEN b.bet_status = 'completed' AND b.winner_user_id != :user_id THEN 'loss'
                            ELSE 'bet_placed'
                        END as type,
                        CASE 
                            WHEN b.winner_user_id = :user_id THEN 
                                CASE WHEN b.user1_id = :user_id THEN b.amount_user1 ELSE b.amount_user2 END
                            WHEN b.bet_status = 'completed' AND b.winner_user_id != :user_id THEN 
                                -1 * CASE WHEN b.user1_id = :user_id THEN b.amount_user1 ELSE b.amount_user2 END
                            ELSE 0
                        END as amount,
                        b.bet_status as status,
                        b.created_at
                      FROM bets b
                      WHERE b.user1_id = :user_id OR b.user2_id = :user_id
                      ORDER BY b.created_at DESC
                      LIMIT 20";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        echo json_encode([
            'success' => true,
            'transactions' => $transactions
        ]);
        
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
