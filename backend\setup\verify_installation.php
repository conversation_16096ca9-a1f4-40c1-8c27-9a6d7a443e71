<?php
/**
 * Admin Authentication System Installation Verification
 * Verifies that all components are properly installed and configured
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

require_once '../includes/db_connect.php';

class InstallationVerifier {
    private $conn;
    private $checks = [];

    public function __construct() {
        $this->conn = getDBConnection();
    }

    /**
     * Run all verification checks
     */
    public function verify() {
        $this->checkDatabaseConnection();
        $this->checkRequiredTables();
        $this->checkTableStructures();
        $this->checkDefaultSettings();
        $this->checkDependencies();
        $this->checkFilePermissions();
        $this->checkSMTPConfiguration();
        $this->checkPHPExtensions();

        return [
            'success' => $this->allChecksPassed(),
            'checks' => $this->checks,
            'summary' => $this->getSummary(),
            'next_steps' => $this->getNextSteps(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Check database connection
     */
    private function checkDatabaseConnection() {
        try {
            if ($this->conn) {
                $stmt = $this->conn->query("SELECT 1");
                $this->addCheck('Database', 'Connection', true, 'Database connection successful');
            } else {
                $this->addCheck('Database', 'Connection', false, 'Failed to connect to database');
            }
        } catch (Exception $e) {
            $this->addCheck('Database', 'Connection', false, $e->getMessage());
        }
    }

    /**
     * Check required tables exist
     */
    private function checkRequiredTables() {
        $requiredTables = [
            'admin_auth_settings' => 'Admin authentication settings',
            'admin_2fa' => 'Admin 2FA configuration',
            'admin_otp' => 'Admin OTP tokens',
            'admin_auth_logs' => 'Admin authentication logs',
            'admin_login_attempts' => 'Admin login attempt tracking',
            'admin_recovery_codes' => 'Admin recovery codes',
            'admin_recovery_tokens' => 'Admin recovery tokens'
        ];

        foreach ($requiredTables as $table => $description) {
            try {
                $stmt = $this->conn->query("SHOW TABLES LIKE '$table'");
                $exists = $stmt->rowCount() > 0;
                
                if ($exists) {
                    $this->addCheck('Tables', $table, true, $description . ' table exists');
                } else {
                    $this->addCheck('Tables', $table, false, $description . ' table missing');
                }
            } catch (PDOException $e) {
                $this->addCheck('Tables', $table, false, 'Error checking table: ' . $e->getMessage());
            }
        }
    }

    /**
     * Check table structures
     */
    private function checkTableStructures() {
        // Check admin table enhancements
        $adminColumns = ['auth_method', 'two_factor_enabled', 'account_locked_until'];
        
        foreach ($adminColumns as $column) {
            try {
                $stmt = $this->conn->query("SHOW COLUMNS FROM admins LIKE '$column'");
                $exists = $stmt->rowCount() > 0;
                
                if ($exists) {
                    $this->addCheck('Structure', "admins.$column", true, "Column $column exists in admins table");
                } else {
                    $this->addCheck('Structure', "admins.$column", false, "Column $column missing from admins table");
                }
            } catch (PDOException $e) {
                $this->addCheck('Structure', "admins.$column", false, 'Error checking column: ' . $e->getMessage());
            }
        }

        // Check view exists
        try {
            $stmt = $this->conn->query("SELECT * FROM admin_auth_status LIMIT 1");
            $this->addCheck('Structure', 'admin_auth_status_view', true, 'Admin auth status view exists');
        } catch (PDOException $e) {
            $this->addCheck('Structure', 'admin_auth_status_view', false, 'Admin auth status view missing');
        }
    }

    /**
     * Check default settings
     */
    private function checkDefaultSettings() {
        try {
            $stmt = $this->conn->query("SELECT COUNT(*) as count FROM admin_auth_settings");
            $settingsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($settingsCount >= 10) {
                $this->addCheck('Settings', 'default_settings', true, "Default settings loaded ($settingsCount settings)");
            } else {
                $this->addCheck('Settings', 'default_settings', false, "Insufficient default settings ($settingsCount found, expected 10+)");
            }

            // Check specific critical settings
            $criticalSettings = ['admin_auth_method', 'admin_otp_enabled', 'admin_2fa_enabled'];
            
            foreach ($criticalSettings as $setting) {
                $stmt = $this->conn->prepare("SELECT setting_value FROM admin_auth_settings WHERE setting_name = ?");
                $stmt->execute([$setting]);
                $value = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($value) {
                    $this->addCheck('Settings', $setting, true, "Setting $setting exists with value: " . $value['setting_value']);
                } else {
                    $this->addCheck('Settings', $setting, false, "Critical setting $setting missing");
                }
            }

        } catch (PDOException $e) {
            $this->addCheck('Settings', 'default_settings', false, 'Error checking settings: ' . $e->getMessage());
        }
    }

    /**
     * Check dependencies
     */
    private function checkDependencies() {
        // Check Composer dependencies
        $composerFile = '../vendor/autoload.php';
        if (file_exists($composerFile)) {
            $this->addCheck('Dependencies', 'composer', true, 'Composer dependencies installed');
            
            // Check specific packages
            $requiredPackages = [
                'PHPMailer\\PHPMailer\\PHPMailer' => 'PHPMailer for email functionality',
                'PragmaRX\\Google2FA\\Google2FA' => 'Google2FA for 2FA functionality'
            ];
            
            foreach ($requiredPackages as $class => $description) {
                if (class_exists($class)) {
                    $this->addCheck('Dependencies', $class, true, $description . ' available');
                } else {
                    $this->addCheck('Dependencies', $class, false, $description . ' missing');
                }
            }
        } else {
            $this->addCheck('Dependencies', 'composer', false, 'Composer dependencies not installed');
        }
    }

    /**
     * Check file permissions
     */
    private function checkFilePermissions() {
        $criticalFiles = [
            '../includes/rate_limiter.php' => 'Rate limiter class',
            '../includes/audit_logger.php' => 'Audit logger class',
            '../includes/error_handler.php' => 'Error handler class',
            '../handlers/admin_send_otp.php' => 'OTP sender handler',
            '../handlers/admin_verify_otp.php' => 'OTP verification handler',
            '../handlers/admin_setup_2fa.php' => '2FA setup handler',
            '../handlers/admin_verify_2fa.php' => '2FA verification handler'
        ];

        foreach ($criticalFiles as $file => $description) {
            if (file_exists($file)) {
                if (is_readable($file)) {
                    $this->addCheck('Files', basename($file), true, $description . ' exists and readable');
                } else {
                    $this->addCheck('Files', basename($file), false, $description . ' exists but not readable');
                }
            } else {
                $this->addCheck('Files', basename($file), false, $description . ' missing');
            }
        }
    }

    /**
     * Check SMTP configuration
     */
    private function checkSMTPConfiguration() {
        try {
            $stmt = $this->conn->query("SHOW TABLES LIKE 'smtp_settings'");
            $smtpTableExists = $stmt->rowCount() > 0;
            
            if ($smtpTableExists) {
                $stmt = $this->conn->query("SELECT * FROM smtp_settings WHERE is_active = 1 LIMIT 1");
                $smtpConfig = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($smtpConfig) {
                    $this->addCheck('SMTP', 'configuration', true, 'SMTP is configured and active');
                    
                    // Check required SMTP fields
                    $requiredFields = ['host', 'port', 'username', 'password'];
                    foreach ($requiredFields as $field) {
                        if (!empty($smtpConfig[$field])) {
                            $this->addCheck('SMTP', $field, true, "SMTP $field configured");
                        } else {
                            $this->addCheck('SMTP', $field, false, "SMTP $field missing");
                        }
                    }
                } else {
                    $this->addCheck('SMTP', 'configuration', false, 'SMTP table exists but no active configuration');
                }
            } else {
                $this->addCheck('SMTP', 'table', false, 'SMTP settings table missing');
            }
        } catch (PDOException $e) {
            $this->addCheck('SMTP', 'check', false, 'Error checking SMTP: ' . $e->getMessage());
        }
    }

    /**
     * Check PHP extensions
     */
    private function checkPHPExtensions() {
        $requiredExtensions = [
            'pdo' => 'PDO for database connectivity',
            'pdo_mysql' => 'MySQL PDO driver',
            'openssl' => 'OpenSSL for encryption',
            'json' => 'JSON for data handling',
            'mbstring' => 'Multibyte string handling',
            'curl' => 'cURL for HTTP requests'
        ];

        foreach ($requiredExtensions as $extension => $description) {
            if (extension_loaded($extension)) {
                $this->addCheck('PHP Extensions', $extension, true, $description . ' loaded');
            } else {
                $this->addCheck('PHP Extensions', $extension, false, $description . ' missing');
            }
        }

        // Check PHP version
        $phpVersion = PHP_VERSION;
        $minVersion = '7.4.0';
        
        if (version_compare($phpVersion, $minVersion, '>=')) {
            $this->addCheck('PHP', 'version', true, "PHP version $phpVersion (>= $minVersion)");
        } else {
            $this->addCheck('PHP', 'version', false, "PHP version $phpVersion (< $minVersion required)");
        }
    }

    /**
     * Add a check result
     */
    private function addCheck($category, $item, $passed, $message) {
        $this->checks[] = [
            'category' => $category,
            'item' => $item,
            'passed' => $passed,
            'message' => $message
        ];
    }

    /**
     * Check if all checks passed
     */
    private function allChecksPassed() {
        foreach ($this->checks as $check) {
            if (!$check['passed']) {
                return false;
            }
        }
        return true;
    }

    /**
     * Get summary of checks
     */
    private function getSummary() {
        $categories = [];
        foreach ($this->checks as $check) {
            $category = $check['category'];
            if (!isset($categories[$category])) {
                $categories[$category] = ['passed' => 0, 'failed' => 0];
            }
            
            if ($check['passed']) {
                $categories[$category]['passed']++;
            } else {
                $categories[$category]['failed']++;
            }
        }

        $totalPassed = array_sum(array_column($this->checks, 'passed'));
        $totalChecks = count($this->checks);

        return [
            'categories' => $categories,
            'total_passed' => $totalPassed,
            'total_checks' => $totalChecks,
            'success_rate' => round(($totalPassed / $totalChecks) * 100, 1)
        ];
    }

    /**
     * Get next steps based on check results
     */
    private function getNextSteps() {
        $steps = [];
        $failedChecks = array_filter($this->checks, function($check) {
            return !$check['passed'];
        });

        if (empty($failedChecks)) {
            $steps[] = "✅ All checks passed! Your admin authentication system is ready.";
            $steps[] = "🔧 You can now configure authentication settings in the admin panel.";
            $steps[] = "📧 Make sure to test OTP email delivery if you plan to use OTP authentication.";
            $steps[] = "🔐 Test 2FA setup with Google Authenticator if you plan to use 2FA.";
        } else {
            $steps[] = "❌ Some checks failed. Please address the following issues:";
            
            foreach ($failedChecks as $check) {
                $steps[] = "• {$check['category']} - {$check['item']}: {$check['message']}";
            }
            
            $steps[] = "";
            $steps[] = "🔧 Common solutions:";
            $steps[] = "• Run the database setup script: /backend/sql/setup_admin_auth_tables.php";
            $steps[] = "• Install Composer dependencies: composer install";
            $steps[] = "• Configure SMTP settings in the admin panel";
            $steps[] = "• Check file permissions and PHP extensions";
        }

        return $steps;
    }
}

// Run verification if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $verifier = new InstallationVerifier();
        $result = $verifier->verify();
        
        echo json_encode($result, JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
    }
}
?>
