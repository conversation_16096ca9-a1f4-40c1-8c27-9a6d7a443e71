<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Access-Control-Allow-Methods: DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header("Content-Type: application/json");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $id = mysqli_real_escape_string($conn, $_GET['id']);

    $query = "DELETE FROM teams WHERE id = $id";
    
    if (mysqli_query($conn, $query)) {
        echo json_encode(["success" => true, "message" => "Team deleted successfully"]);
    } else {
        echo json_encode(["success" => false, "message" => "Error deleting team: " . mysqli_error($conn)]);
    }
}
