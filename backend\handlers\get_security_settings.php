<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Database connection
    $conn = new PDO(
        "mysql:host=localhost;dbname=fanbet247",
        'root',
        'root',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Create security_settings table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS security_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_name VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    $conn->exec($createTableSQL);

    // Insert default security settings if table is empty
    $checkStmt = $conn->query("SELECT COUNT(*) as count FROM security_settings");
    $count = $checkStmt->fetch()['count'];

    if ($count == 0) {
        $defaultSettings = [
            ['enable_2fa', 'false', 'Enable two-factor authentication for users'],
            ['allowed_auth_methods', 'email_otp,google_auth', 'Allowed authentication methods (comma-separated)'],
            ['otp_expiry_time', '300', 'OTP expiry time in seconds'],
            ['max_otp_attempts', '3', 'Maximum number of OTP verification attempts before lockout'],
            ['lockout_time', '1800', 'Account lockout time in seconds after max failed attempts'],
            ['password_min_length', '8', 'Minimum password length requirement'],
            ['require_special_chars', 'true', 'Require special characters in passwords'],
            ['session_timeout', '3600', 'Session timeout in seconds'],
            ['max_login_attempts', '5', 'Maximum login attempts before account lockout']
        ];

        $insertStmt = $conn->prepare("INSERT INTO security_settings (setting_name, setting_value, description) VALUES (?, ?, ?)");
        foreach ($defaultSettings as $setting) {
            $insertStmt->execute($setting);
        }
    }
    
    $stmt = $conn->prepare("SELECT setting_name, setting_value, description FROM security_settings");
    $stmt->execute();

    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_name']] = [
            'value' => $row['setting_value'],
            'description' => $row['description']
        ];
    }

    // Get admin authentication settings if table exists
    $adminAuthSettings = [];
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'admin_auth_settings'");
        if ($stmt->rowCount() > 0) {
            $stmt = $conn->prepare("SELECT setting_name, setting_value, description FROM admin_auth_settings");
            $stmt->execute();

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $adminAuthSettings[$row['setting_name']] = [
                    'value' => $row['setting_value'],
                    'description' => $row['description']
                ];
            }
        }
    } catch (PDOException $e) {
        // Admin auth settings table doesn't exist yet
        $adminAuthSettings = [];
    }

    echo json_encode([
        "success" => true,
        "settings" => $settings,
        "admin_auth_settings" => $adminAuthSettings,
        "admin_auth_available" => !empty($adminAuthSettings)
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
}
?>
