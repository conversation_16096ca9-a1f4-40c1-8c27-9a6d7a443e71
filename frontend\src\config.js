// Enhanced Dynamic API Configuration - Works on ANY domain without hardcoding
const isDevelopment = process.env.NODE_ENV === 'development' ||
                     window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1';

// Smart project path detection
const getProjectPath = () => {
    // Priority 1: Environment variable override
    if (process.env.REACT_APP_PROJECT_PATH) {
        return process.env.REACT_APP_PROJECT_PATH;
    }

    // Priority 2: Development with React dev server (port 3000)
    if (isDevelopment && window.location.port === '3000') {
        // Use proxy - no project path needed
        return '';
    }

    // Priority 3: Auto-detect from current URL for production
    const currentPath = window.location.pathname;
    const pathParts = currentPath.split('/').filter(part => part);

    // If URL has path segments and first segment is not a known route
    const knownRoutes = ['admin', 'user', 'login', 'register', 'dashboard', 'api', 'backend'];
    if (pathParts.length > 0 && !knownRoutes.includes(pathParts[0])) {
        // First segment is likely the project folder
        return '/' + pathParts[0];
    }

    // Priority 4: Check if we're in a subfolder by testing common patterns
    // This handles cases where the app is deployed in a subfolder
    const possiblePaths = ['/FanBet247', '/fanbet247', '/app', '/betting'];
    for (const path of possiblePaths) {
        if (currentPath.startsWith(path)) {
            return path;
        }
    }

    // Default: Root domain deployment (no project folder)
    return '';
};

// Dynamic API URL with intelligent detection
const getApiBaseUrl = () => {
    const projectPath = getProjectPath();

    // Development with React dev server - use proxy
    if (isDevelopment && window.location.port === '3000') {
        return '/backend/handlers'; // Proxy routes to localhost/FanBet247/backend/handlers
    }

    // Production: domain.com or domain.com/projectfolder
    // Ensure we always have a valid URL, never undefined
    const baseUrl = `${projectPath}/backend/handlers`;

    // Fallback for production if projectPath is empty
    if (!projectPath || projectPath === '') {
        return '/backend/handlers';
    }

    return baseUrl;
};

export const API_BASE_URL = getApiBaseUrl();
export const PROJECT_PATH = getProjectPath();

// Enhanced asset URL helper for images, uploads, etc.
export const getAssetUrl = (path) => {
    const projectPath = getProjectPath();
    const cleanPath = path.startsWith('/') ? path : `/${path}`;

    // Development with proxy
    if (isDevelopment && window.location.port === '3000') {
        return `/backend${cleanPath}`; // Proxy handles routing
    }

    // Production: use project path + backend + asset path
    return `${projectPath}/backend${cleanPath}`;
};

// Get full backend URL (for cases where you need the complete URL)
export const getBackendUrl = () => {
    const projectPath = getProjectPath();

    if (isDevelopment && window.location.port === '3000') {
        return `${window.location.origin}/backend`;
    }

    return `${window.location.origin}${projectPath}/backend`;
};

// Debug logging (only in development)
if (isDevelopment) {
    console.log('🔧 Enhanced Dynamic API Configuration:');
    console.log('Environment:', process.env.NODE_ENV);
    console.log('Current URL:', window.location.href);
    console.log('Detected Project Path:', PROJECT_PATH);
    console.log('Final API Base URL:', API_BASE_URL);
    console.log('Backend URL:', getBackendUrl());
    console.log('');
    console.log('📝 This will work on ANY domain:');
    console.log('   - Development: localhost:3000 → proxy to localhost/FanBet247/backend/handlers');
    console.log('   - Production Root: yourdomain.com → yourdomain.com/backend/handlers');
    console.log('   - Production Subfolder: yourdomain.com/myapp → yourdomain.com/myapp/backend/handlers');
    console.log('   - Subdomain: api.yourdomain.com → api.yourdomain.com/backend/handlers');
}