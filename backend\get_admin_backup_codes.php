<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "Getting backup codes for superadmin (admin_id = 1)...\n";
    
    $stmt = $conn->prepare('SELECT backup_codes FROM admin_2fa WHERE admin_id = 1');
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result && $result['backup_codes']) {
        $codes = json_decode($result['backup_codes'], true);
        echo "Backup codes:\n";
        foreach ($codes as $i => $code) {
            echo ($i + 1) . ': ' . $code . "\n";
        }
    } else {
        echo "No backup codes found for superadmin\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
