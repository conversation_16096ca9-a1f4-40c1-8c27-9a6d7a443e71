# FanBet247 Multi-Currency System Installation Guide

## Overview
This guide will help you install the multi-currency system for FanBet247, which allows users to select their preferred currency during registration and view FanCoin amounts in their local currency.

## Features
- **Multi-currency support**: USD, ZAR, EUR, GBP, CAD, AUD (easily extensible)
- **FanCoin as universal currency**: All betting remains in FanCoin (1 FanCoin = 1 USD base)
- **User currency preferences**: Users select preferred currency during registration
- **Admin exchange rate management**: Admins can manually update exchange rates
- **Real-time currency conversion**: Display amounts in user's preferred currency
- **Backward compatibility**: Existing functionality remains unchanged

## Database Schema Changes

### New Tables Created:
1. **`currencies`** - Stores available currencies (USD, ZAR, EUR, etc.)
2. **`exchange_rates`** - Stores conversion rates from FanCoin to other currencies

### Modified Tables:
1. **`users`** - Added `preferred_currency_id` field for user currency preference

## Installation Steps

### Step 1: Backup Your Database
```bash
mysqldump -u username -p fanbet247 > fanbet247_backup_$(date +%Y%m%d_%H%M%S).sql
```

### Step 2: Install Currency System
Choose one of the following methods:

#### Method A: Automated Setup (Recommended)
```bash
# Navigate to your FanBet247 directory
cd /path/to/FanBet247

# Run the setup script
php backend/sql/setup_currency_system.php
```

Or via browser:
```
http://your-domain/backend/sql/setup_currency_system.php
```

#### Method B: Manual SQL Execution
```bash
mysql -u username -p fanbet247 < backend/sql/currency_system_schema.sql
```

### Step 3: Verify Installation
```bash
# Run verification script
php backend/sql/verify_currency_system.php
```

Or via browser:
```
http://your-domain/backend/sql/verify_currency_system.php
```

### Step 4: Check Installation Results
The verification script will confirm:
- ✓ Currency tables created successfully
- ✓ Default currencies added (USD, ZAR, EUR, GBP, CAD, AUD)
- ✓ Exchange rates configured
- ✓ Users table updated with currency preferences
- ✓ Foreign key relationships established

## Default Exchange Rates
The system installs with these initial rates (1 FanCoin =):
- **USD**: $1.00 (base rate)
- **ZAR**: R18.00
- **EUR**: €0.92
- **GBP**: £0.80
- **CAD**: C$1.35
- **AUD**: A$1.50

**Note**: These are example rates. Update them through the admin interface after installation.

## Database Structure Details

### Currencies Table
```sql
CREATE TABLE currencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    currency_code VARCHAR(3) NOT NULL UNIQUE,
    currency_name VARCHAR(50) NOT NULL,
    currency_symbol VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Exchange Rates Table
```sql
CREATE TABLE exchange_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    currency_id INT NOT NULL,
    rate_to_fancoin DECIMAL(10,4) NOT NULL,
    updated_by_admin_id INT NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    FOREIGN KEY (updated_by_admin_id) REFERENCES admins(admin_id)
);
```

### Users Table Modification
```sql
ALTER TABLE users 
ADD COLUMN preferred_currency_id INT DEFAULT 1,
ADD FOREIGN KEY (preferred_currency_id) REFERENCES currencies(id);
```

## Next Steps
After successful installation:

1. **Update Exchange Rates**: Use the admin interface to set current exchange rates
2. **Test Registration**: Verify currency selection works in user registration
3. **Test Currency Display**: Confirm FanCoin amounts show in user's preferred currency
4. **Configure Additional Currencies**: Add more currencies if needed

## Troubleshooting

### Common Issues:

1. **Foreign Key Constraint Errors**
   - Ensure all referenced tables exist (users, admins)
   - Check that admin_id exists in admins table

2. **Permission Errors**
   - Verify database user has CREATE, ALTER, INSERT privileges
   - Check file permissions for PHP scripts

3. **Currency Not Displaying**
   - Verify exchange rates are set for all active currencies
   - Check that users have preferred_currency_id set

### Rollback Instructions
If you need to rollback the changes:

```sql
-- Remove foreign key constraints
ALTER TABLE users DROP FOREIGN KEY users_ibfk_[constraint_number];
ALTER TABLE exchange_rates DROP FOREIGN KEY exchange_rates_ibfk_1;
ALTER TABLE exchange_rates DROP FOREIGN KEY exchange_rates_ibfk_2;

-- Remove added column
ALTER TABLE users DROP COLUMN preferred_currency_id;

-- Drop new tables
DROP TABLE exchange_rates;
DROP TABLE currencies;
```

## Support
If you encounter issues during installation:
1. Check the verification script output for specific errors
2. Review database error logs
3. Ensure all prerequisites are met
4. Contact system administrator if needed

## Security Notes
- Exchange rates can only be updated by authenticated admins
- All currency conversions are for display purposes only
- Internal calculations remain in FanCoin to maintain precision
- Foreign key constraints ensure data integrity
