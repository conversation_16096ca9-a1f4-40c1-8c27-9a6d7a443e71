<?php
/**
 * Comprehensive Error Handler for Admin Authentication
 * Handles errors, logging, and security responses
 */

class AdminErrorHandler {
    private $auditLogger;
    private $rateLimiter;
    private $conn;

    public function __construct($connection, $auditLogger = null, $rateLimiter = null) {
        $this->conn = $connection;
        $this->auditLogger = $auditLogger;
        $this->rateLimiter = $rateLimiter;
    }

    /**
     * Handle authentication errors with proper logging and rate limiting
     */
    public function handleAuthError($error, $context = []) {
        $errorType = $this->classifyError($error);
        $adminId = $context['admin_id'] ?? null;
        $action = $context['action'] ?? 'unknown';
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        // Log the error
        if ($this->auditLogger) {
            $this->auditLogger->logAuthEvent(
                $adminId,
                $context['auth_type'] ?? 'unknown',
                $action . '_failed',
                [
                    'error_type' => $errorType,
                    'error_message' => $error->getMessage(),
                    'context' => $context
                ],
                $this->getLogLevel($errorType)
            );
        }

        // Apply rate limiting if appropriate
        if ($this->rateLimiter && $this->shouldApplyRateLimit($errorType)) {
            $this->rateLimiter->recordAttempt(
                $adminId ?? $ipAddress,
                $action,
                $adminId ? 'admin' : 'ip',
                $adminId
            );
        }

        // Return appropriate error response
        return $this->formatErrorResponse($error, $errorType, $context);
    }

    /**
     * Handle security incidents
     */
    public function handleSecurityIncident($type, $description, $context = []) {
        $adminId = $context['admin_id'] ?? null;
        $severity = $this->getIncidentSeverity($type);

        if ($this->auditLogger) {
            $this->auditLogger->logSecurityIncident($type, $description, $adminId, $severity);
        }

        // Take immediate action for critical incidents
        if ($severity === 'CRITICAL') {
            $this->handleCriticalIncident($type, $context);
        }

        return [
            'incident_logged' => true,
            'severity' => $severity,
            'action_taken' => $severity === 'CRITICAL' ? 'immediate_lockout' : 'logged'
        ];
    }

    /**
     * Validate and sanitize input data
     */
    public function validateInput($data, $rules) {
        $errors = [];
        $sanitized = [];

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;

            // Check required fields
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[$field] = "Field {$field} is required";
                continue;
            }

            // Skip validation if field is not required and empty
            if (empty($value) && (!isset($rule['required']) || !$rule['required'])) {
                $sanitized[$field] = null;
                continue;
            }

            // Type validation
            if (isset($rule['type'])) {
                switch ($rule['type']) {
                    case 'email':
                        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[$field] = "Invalid email format";
                        }
                        break;
                    case 'numeric':
                        if (!is_numeric($value)) {
                            $errors[$field] = "Field {$field} must be numeric";
                        }
                        break;
                    case 'string':
                        if (!is_string($value)) {
                            $errors[$field] = "Field {$field} must be a string";
                        }
                        break;
                }
            }

            // Length validation
            if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                $errors[$field] = "Field {$field} must be at least {$rule['min_length']} characters";
            }

            if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                $errors[$field] = "Field {$field} must not exceed {$rule['max_length']} characters";
            }

            // Pattern validation
            if (isset($rule['pattern']) && !preg_match($rule['pattern'], $value)) {
                $errors[$field] = $rule['pattern_message'] ?? "Field {$field} format is invalid";
            }

            // Custom validation
            if (isset($rule['custom']) && is_callable($rule['custom'])) {
                $customResult = $rule['custom']($value);
                if ($customResult !== true) {
                    $errors[$field] = $customResult;
                }
            }

            // Sanitize the value
            $sanitized[$field] = $this->sanitizeValue($value, $rule);
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'data' => $sanitized
        ];
    }

    /**
     * Check if system is under attack
     */
    public function checkSystemHealth() {
        try {
            // Check for suspicious activity patterns
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as failed_attempts,
                    COUNT(DISTINCT ip_address) as unique_ips,
                    COUNT(DISTINCT admin_id) as affected_admins
                FROM admin_auth_logs 
                WHERE action LIKE '%_failed' 
                AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
            ");
            $stmt->execute();
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);

            $healthStatus = 'healthy';
            $alerts = [];

            // Check for brute force attacks
            if ($stats['failed_attempts'] > 50) {
                $healthStatus = 'under_attack';
                $alerts[] = 'High number of failed authentication attempts detected';
            }

            // Check for distributed attacks
            if ($stats['unique_ips'] > 20 && $stats['failed_attempts'] > 30) {
                $healthStatus = 'distributed_attack';
                $alerts[] = 'Distributed attack pattern detected';
            }

            // Check for account enumeration
            if ($stats['affected_admins'] > 10) {
                $healthStatus = 'enumeration_attack';
                $alerts[] = 'Possible account enumeration attack';
            }

            return [
                'status' => $healthStatus,
                'stats' => $stats,
                'alerts' => $alerts,
                'timestamp' => date('Y-m-d H:i:s')
            ];

        } catch (PDOException $e) {
            return [
                'status' => 'unknown',
                'error' => 'Failed to check system health',
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }

    /**
     * Classify error type
     */
    private function classifyError($error) {
        $message = strtolower($error->getMessage());

        if (strpos($message, 'rate limit') !== false || strpos($message, 'too many') !== false) {
            return 'rate_limit';
        }

        if (strpos($message, 'invalid') !== false || strpos($message, 'incorrect') !== false) {
            return 'invalid_input';
        }

        if (strpos($message, 'expired') !== false) {
            return 'expired_token';
        }

        if (strpos($message, 'locked') !== false || strpos($message, 'disabled') !== false) {
            return 'account_locked';
        }

        if (strpos($message, 'database') !== false || strpos($message, 'connection') !== false) {
            return 'system_error';
        }

        return 'unknown';
    }

    /**
     * Get log level for error type
     */
    private function getLogLevel($errorType) {
        switch ($errorType) {
            case 'rate_limit':
            case 'account_locked':
                return 'WARNING';
            case 'system_error':
                return 'ERROR';
            case 'invalid_input':
            case 'expired_token':
                return 'INFO';
            default:
                return 'WARNING';
        }
    }

    /**
     * Check if rate limiting should be applied
     */
    private function shouldApplyRateLimit($errorType) {
        return in_array($errorType, ['invalid_input', 'expired_token']);
    }

    /**
     * Format error response
     */
    private function formatErrorResponse($error, $errorType, $context) {
        $response = [
            'success' => false,
            'message' => $error->getMessage(),
            'error_type' => $errorType,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        // Add specific information based on error type
        switch ($errorType) {
            case 'rate_limit':
                if ($this->rateLimiter && isset($context['admin_id'])) {
                    $remaining = $this->rateLimiter->getLockoutTimeRemaining(
                        $context['admin_id'],
                        $context['action'] ?? 'login',
                        'admin'
                    );
                    $response['retry_after'] = $remaining;
                }
                break;

            case 'invalid_input':
                $response['help'] = 'Please check your input and try again';
                break;

            case 'expired_token':
                $response['help'] = 'Please request a new verification code';
                break;
        }

        return $response;
    }

    /**
     * Get incident severity
     */
    private function getIncidentSeverity($type) {
        $criticalTypes = ['sql_injection', 'xss_attempt', 'unauthorized_access'];
        $warningTypes = ['rate_limit_exceeded', 'invalid_token', 'failed_authentication'];

        if (in_array($type, $criticalTypes)) {
            return 'CRITICAL';
        } elseif (in_array($type, $warningTypes)) {
            return 'WARNING';
        } else {
            return 'INFO';
        }
    }

    /**
     * Handle critical security incidents
     */
    private function handleCriticalIncident($type, $context) {
        $adminId = $context['admin_id'] ?? null;
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        // Immediate lockout for critical incidents
        if ($adminId && $this->rateLimiter) {
            // Lock account for 24 hours
            $this->rateLimiter->recordAttempt($adminId, 'critical_incident', 'admin');
        }

        // Block IP for critical incidents
        // This would integrate with your firewall/security system
        $this->blockSuspiciousIP($ipAddress, $type);
    }

    /**
     * Block suspicious IP (placeholder for integration with security systems)
     */
    private function blockSuspiciousIP($ipAddress, $reason) {
        // This would integrate with your firewall, fail2ban, or cloud security service
        error_log("SECURITY ALERT: Blocking IP {$ipAddress} for {$reason}");
        
        // Example: Add to database blacklist
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO ip_blacklist (ip_address, reason, blocked_at) 
                VALUES (?, ?, NOW())
                ON DUPLICATE KEY UPDATE 
                reason = VALUES(reason), 
                blocked_at = NOW()
            ");
            $stmt->execute([$ipAddress, $reason]);
        } catch (PDOException $e) {
            error_log("Failed to blacklist IP: " . $e->getMessage());
        }
    }

    /**
     * Sanitize input value
     */
    private function sanitizeValue($value, $rule) {
        if (isset($rule['sanitize'])) {
            switch ($rule['sanitize']) {
                case 'email':
                    return filter_var($value, FILTER_SANITIZE_EMAIL);
                case 'string':
                    return htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
                case 'numeric':
                    return filter_var($value, FILTER_SANITIZE_NUMBER_INT);
                default:
                    return $value;
            }
        }

        return $value;
    }
}
?>
