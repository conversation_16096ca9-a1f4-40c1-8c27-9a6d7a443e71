<?php
/**
 * Verify Admin 2FA Code
 * Verifies Google Authenticator code or backup code for admin login
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';
require_once '../vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['admin_id']) || !isset($input['code'])) {
        throw new Exception("Admin ID and verification code are required");
    }
    
    $adminId = $input['admin_id'];
    $code = $input['code'];
    $isBackupCode = $input['is_backup_code'] ?? false;
    
    // Verify admin exists
    $stmt = $conn->prepare("SELECT admin_id, username, email, role FROM admins WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Admin not found");
    }
    
    // Get admin's 2FA setup
    $stmt = $conn->prepare("
        SELECT secret_key, backup_codes, is_enabled, setup_completed 
        FROM admin_2fa 
        WHERE admin_id = ? AND is_enabled = 1 AND setup_completed = 1
    ");
    $stmt->execute([$adminId]);
    $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tfaSetup) {
        throw new Exception("2FA is not set up for this admin account");
    }
    
    // Get rate limiting settings
    $stmt = $conn->query("SELECT setting_name, setting_value FROM admin_auth_settings WHERE setting_name IN ('admin_max_login_attempts', 'admin_lockout_time')");
    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_name']] = $row['setting_value'];
    }
    
    $maxAttempts = $settings['admin_max_login_attempts'] ?? 5;
    $lockoutTime = $settings['admin_lockout_time'] ?? 1800; // 30 minutes default
    
    // Check for rate limiting
    $stmt = $conn->prepare("
        SELECT attempts, locked_until, last_attempt 
        FROM admin_login_attempts 
        WHERE admin_id = ? AND attempt_type = '2fa' AND locked_until > NOW()
        ORDER BY last_attempt DESC 
        LIMIT 1
    ");
    $stmt->execute([$adminId]);
    $rateLimitCheck = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($rateLimitCheck) {
        throw new Exception("Account is temporarily locked due to too many failed 2FA attempts. Please try again later.");
    }
    
    $conn->beginTransaction();
    
    $isValid = false;
    $usedBackupCode = null;
    
    if ($isBackupCode) {
        // Verify backup code
        $backupCodes = json_decode($tfaSetup['backup_codes'], true);
        
        if (in_array(strtoupper($code), $backupCodes)) {
            $isValid = true;
            $usedBackupCode = strtoupper($code);
            
            // Remove used backup code
            $backupCodes = array_diff($backupCodes, [$usedBackupCode]);
            $updatedBackupCodes = json_encode(array_values($backupCodes));
            
            $stmt = $conn->prepare("UPDATE admin_2fa SET backup_codes = ? WHERE admin_id = ?");
            $stmt->execute([$updatedBackupCodes, $adminId]);
        }
    } else {
        // Verify Google Authenticator code
        $google2fa = new Google2FA();
        $isValid = $google2fa->verifyKey($tfaSetup['secret_key'], $code);
    }
    
    if (!$isValid) {
        // Handle failed attempt
        $stmt = $conn->prepare("
            INSERT INTO admin_login_attempts (admin_id, ip_address, attempt_type, attempts) 
            VALUES (?, ?, '2fa', 1)
            ON DUPLICATE KEY UPDATE 
            attempts = attempts + 1, 
            last_attempt = NOW()
        ");
        $stmt->execute([$adminId, $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
        
        // Check if we need to lock the account
        $stmt = $conn->prepare("
            SELECT attempts FROM admin_login_attempts 
            WHERE admin_id = ? AND attempt_type = '2fa' 
            ORDER BY last_attempt DESC 
            LIMIT 1
        ");
        $stmt->execute([$adminId]);
        $attempts = $stmt->fetch(PDO::FETCH_ASSOC)['attempts'] ?? 0;
        
        if ($attempts >= $maxAttempts) {
            $lockedUntil = date('Y-m-d H:i:s', time() + $lockoutTime);
            $stmt = $conn->prepare("
                UPDATE admin_login_attempts 
                SET locked_until = ? 
                WHERE admin_id = ? AND attempt_type = '2fa'
            ");
            $stmt->execute([$lockedUntil, $adminId]);
            
            // Log lockout
            $stmt = $conn->prepare("
                INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
                VALUES (?, '2fa', 'account_locked', ?, ?, ?)
            ");
            $stmt->execute([
                $adminId,
                json_encode(['reason' => 'max_2fa_attempts_exceeded', 'locked_until' => $lockedUntil]),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            $conn->commit();
            throw new Exception("Too many failed 2FA attempts. Account locked for " . ($lockoutTime / 60) . " minutes.");
        }
        
        // Log failed attempt
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, '2fa', 'login_failed', ?, ?, ?)
        ");
        $stmt->execute([
            $adminId,
            json_encode(['reason' => 'invalid_2fa_code', 'is_backup_code' => $isBackupCode, 'attempts' => $attempts]),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        
        $remainingAttempts = $maxAttempts - $attempts;
        throw new Exception("Invalid " . ($isBackupCode ? "backup code" : "2FA code") . ". You have $remainingAttempts attempt(s) remaining.");
    }
    
    // 2FA verification successful
    
    // Clear failed attempts
    $stmt = $conn->prepare("DELETE FROM admin_login_attempts WHERE admin_id = ? AND attempt_type = '2fa'");
    $stmt->execute([$adminId]);
    
    // Reset admin failed login attempts
    $stmt = $conn->prepare("UPDATE admins SET failed_login_attempts = 0, account_locked_until = NULL WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    
    // Update last login and 2FA usage
    $stmt = $conn->prepare("UPDATE admins SET last_login = NOW() WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    
    $stmt = $conn->prepare("UPDATE admin_2fa SET last_used = NOW() WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    
    // Log successful 2FA verification
    $stmt = $conn->prepare("
        INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, '2fa', '2fa_verified', ?, ?, ?)
    ");
    $stmt->execute([
        $adminId,
        json_encode(['is_backup_code' => $isBackupCode, 'used_backup_code' => $usedBackupCode]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    // Log successful login
    $stmt = $conn->prepare("
        INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, '2fa', 'login_success', ?, ?, ?)
    ");
    $stmt->execute([
        $adminId,
        json_encode(['auth_method' => '2fa', 'used_backup_code' => $isBackupCode]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    $conn->commit();
    
    // Generate session token
    $sessionToken = bin2hex(random_bytes(32));
    
    // Check remaining backup codes
    $remainingBackupCodes = count(json_decode($tfaSetup['backup_codes'], true));
    if ($isBackupCode) {
        $remainingBackupCodes--;
    }
    
    echo json_encode([
        'success' => true,
        'message' => '2FA verification successful',
        'admin_id' => $admin['admin_id'],
        'username' => $admin['username'],
        'role' => $admin['role'],
        'session_token' => $sessionToken,
        'auth_method' => '2fa',
        'used_backup_code' => $isBackupCode,
        'remaining_backup_codes' => $remainingBackupCodes,
        'backup_code_warning' => $remainingBackupCodes <= 2 ? 'You have few backup codes remaining. Consider regenerating them.' : null,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
