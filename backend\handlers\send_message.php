<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

$data = json_decode(file_get_contents("php://input"));

if (!isset($data->sender_id) || !isset($data->receiver_id) || !isset($data->message)) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

$conn = getDBConnection();

$query = "INSERT INTO messages (sender_id, recipient_id, content, created_at) 
          VALUES (:sender_id, :recipient_id, :message, NOW())";

try {
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':sender_id', $data->sender_id);
    $stmt->bindParam(':recipient_id', $data->receiver_id);
    $stmt->bindParam(':message', $data->message);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Message sent successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to send message']);
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
} 