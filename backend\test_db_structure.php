<?php
header('Content-Type: text/plain');
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    echo "✅ Database connection successful\n\n";
    
    // Check users table structure
    echo "📋 Checking users table structure:\n";
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasOtpEnabled = false;
    $hasTfaEnabled = false;
    $hasAuthMethod = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'otp_enabled') $hasOtpEnabled = true;
        if ($column['Field'] === 'tfa_enabled') $hasTfaEnabled = true;
        if ($column['Field'] === 'auth_method') $hasAuthMethod = true;
        echo "  - {$column['Field']} ({$column['Type']})\n";
    }
    
    echo "\n📊 Authentication columns status:\n";
    echo "  - otp_enabled: " . ($hasOtpEnabled ? "✅ EXISTS" : "❌ MISSING") . "\n";
    echo "  - tfa_enabled: " . ($hasTfaEnabled ? "✅ EXISTS" : "❌ MISSING") . "\n";
    echo "  - auth_method: " . ($hasAuthMethod ? "✅ EXISTS" : "❌ MISSING") . "\n";
    
    // Check if user_otp table exists
    echo "\n📋 Checking user_otp table:\n";
    $stmt = $conn->query("SHOW TABLES LIKE 'user_otp'");
    if ($stmt->rowCount() > 0) {
        echo "✅ user_otp table exists\n";
        $stmt = $conn->query("DESCRIBE user_otp");
        $otpColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($otpColumns as $column) {
            echo "  - {$column['Field']} ({$column['Type']})\n";
        }
    } else {
        echo "❌ user_otp table does not exist\n";
    }
    
    // Check if user_2fa table exists
    echo "\n📋 Checking user_2fa table:\n";
    $stmt = $conn->query("SHOW TABLES LIKE 'user_2fa'");
    if ($stmt->rowCount() > 0) {
        echo "✅ user_2fa table exists\n";
        $stmt = $conn->query("DESCRIBE user_2fa");
        $tfaColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($tfaColumns as $column) {
            echo "  - {$column['Field']} ({$column['Type']})\n";
        }
    } else {
        echo "❌ user_2fa table does not exist\n";
    }
    
    // Test a specific user
    echo "\n👤 Testing user ID 14 (dynamicuser456):\n";
    $stmt = $conn->prepare("SELECT user_id, username, email" . 
                          ($hasOtpEnabled ? ", otp_enabled" : "") .
                          ($hasTfaEnabled ? ", tfa_enabled" : "") .
                          ($hasAuthMethod ? ", auth_method" : "") .
                          " FROM users WHERE user_id = ?");
    $stmt->execute([14]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✅ User found:\n";
        foreach ($user as $key => $value) {
            echo "  - $key: $value\n";
        }
    } else {
        echo "❌ User not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
