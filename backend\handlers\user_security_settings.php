<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get user ID from query parameter
    $userId = $_GET['userId'] ?? null;
    
    if (!$userId) {
        throw new Exception("User ID is required");
    }
    
    // Verify user exists
    $stmt = $conn->prepare("SELECT user_id, username, email, otp_enabled, tfa_enabled, auth_method FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get user's 2FA setup if exists
        $stmt = $conn->prepare("
            SELECT secret_key, is_enabled, setup_completed, auth_type, last_used 
            FROM user_2fa 
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Prepare security settings
        $securitySettings = [
            'otp_enabled' => (bool)$user['otp_enabled'],
            'tfa_enabled' => (bool)$user['tfa_enabled'],
            'auth_method' => $user['auth_method'] ?? 'password_only'
        ];
        
        echo json_encode([
            'success' => true,
            'settings' => $securitySettings,
            'user_email' => $user['email'],
            'tfa_setup' => $tfaSetup,
            'message' => 'Security settings loaded successfully'
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Update security settings
        
        $input = json_decode(file_get_contents('php://input'), true);
        $setting = $input['setting'] ?? '';
        $value = $input['value'] ?? false;
        
        if (empty($setting)) {
            throw new Exception("Setting name is required");
        }
        
        $conn->beginTransaction();
        
        switch ($setting) {
            case 'otp_enabled':
                $stmt = $conn->prepare("UPDATE users SET otp_enabled = ? WHERE user_id = ?");
                $stmt->execute([$value ? 1 : 0, $userId]);
                
                // Update auth method
                if ($value) {
                    $newAuthMethod = $user['tfa_enabled'] ? 'password_otp_2fa' : 'password_otp';
                } else {
                    $newAuthMethod = $user['tfa_enabled'] ? 'password_2fa' : 'password_only';
                }
                
                $stmt = $conn->prepare("UPDATE users SET auth_method = ? WHERE user_id = ?");
                $stmt->execute([$newAuthMethod, $userId]);
                
                // Log the change
                $stmt = $conn->prepare("
                    INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
                    VALUES (?, 'otp', ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $userId,
                    $value ? 'otp_enabled' : 'otp_disabled',
                    json_encode(['otp_enabled' => $value]),
                    $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
                ]);
                
                break;
                
            case 'tfa_enabled':
                if ($value) {
                    throw new Exception("2FA cannot be enabled directly. Please use the 2FA setup process.");
                } else {
                    // Disable 2FA
                    $stmt = $conn->prepare("UPDATE user_2fa SET is_enabled = 0 WHERE user_id = ?");
                    $stmt->execute([$userId]);
                    
                    $stmt = $conn->prepare("UPDATE users SET tfa_enabled = 0 WHERE user_id = ?");
                    $stmt->execute([$userId]);
                    
                    // Update auth method
                    $newAuthMethod = $user['otp_enabled'] ? 'password_otp' : 'password_only';
                    $stmt = $conn->prepare("UPDATE users SET auth_method = ? WHERE user_id = ?");
                    $stmt->execute([$newAuthMethod, $userId]);
                    
                    // Log the change
                    $stmt = $conn->prepare("
                        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
                        VALUES (?, '2fa', 'tfa_disabled', ?, ?, ?)
                    ");
                    $stmt->execute([
                        $userId,
                        json_encode(['tfa_enabled' => false]),
                        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
                    ]);
                }
                break;
                
            default:
                throw new Exception("Invalid setting name");
        }
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Security setting updated successfully'
        ]);
    }
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
