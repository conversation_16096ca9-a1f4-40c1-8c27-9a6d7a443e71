<?php
/**
 * Enable OTP for Testing
 * Temporarily enable OTP to test functionality
 */

header('Content-Type: text/plain');

require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    echo "Enabling OTP for testing...\n";

    // Enable OTP globally
    $stmt = $conn->prepare("
        UPDATE admin_auth_settings 
        SET setting_value = 'true' 
        WHERE setting_name = 'admin_otp_enabled'
    ");
    $stmt->execute();
    
    echo "✅ OTP enabled globally\n";

    // Verify the setting
    $stmt = $conn->prepare("
        SELECT setting_value 
        FROM admin_auth_settings 
        WHERE setting_name = 'admin_otp_enabled'
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Current OTP setting: " . $result['setting_value'] . "\n";

    // Now test OTP sending again
    echo "\nTesting OTP email again...\n";

    // Get the first admin for testing
    $stmt = $conn->query("SELECT admin_id, username, email FROM admins LIMIT 1");
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("No admin found for testing");
    }

    echo "Testing OTP email for admin: {$admin['username']} ({$admin['email']})\n";

    // Test OTP generation and email sending
    $testData = [
        'admin_id' => $admin['admin_id']
    ];

    // Make a request to the OTP sender
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/FanBet247/backend/handlers/admin_send_otp.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($testData))
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Response Code: $httpCode\n";
    echo "Response: $response\n";

    $responseData = json_decode($response, true);
    
    if ($responseData && $responseData['success']) {
        echo "✅ OTP email sent successfully!\n";
        echo "Email sent to: " . ($responseData['email_masked'] ?? 'N/A') . "\n";
        
        // Check if OTP was stored in database
        $stmt = $conn->prepare("
            SELECT otp, expires_at, created_at 
            FROM admin_otp 
            WHERE admin_id = ? 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$admin['admin_id']]);
        $otpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($otpRecord) {
            echo "✅ OTP stored in database\n";
            echo "OTP Code: " . $otpRecord['otp'] . "\n";
            echo "Expires at: " . $otpRecord['expires_at'] . "\n";
        }
        
    } else {
        echo "❌ OTP email failed\n";
        echo "Error: " . ($responseData['message'] ?? 'Unknown error') . "\n";
    }

    echo "\n🎉 Test completed! You can now test the OTP functionality in the admin panel.\n";
    echo "Remember to disable OTP when done testing if you don't want it enabled.\n";

} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
}
?>
