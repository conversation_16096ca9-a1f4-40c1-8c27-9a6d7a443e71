<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $user_id = $_GET['user_id'] ?? null;
        
        if (!$user_id) {
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            exit;
        }

        // Get user's bets with challenge details
        $query = "SELECT 
                    b.*,
                    c.team1 as team_a,
                    c.team2 as team_b,
                    c.match_time,
                    c.match_type,
                    CASE 
                        WHEN b.user1_id = :user_id THEN b.amount_user1
                        ELSE b.amount_user2
                    END as bet_amount,
                    CASE 
                        WHEN b.user1_id = :user_id THEN b.team_user1
                        ELSE b.team_user2
                    END as selected_team
                  FROM bets b
                  LEFT JOIN challenges c ON b.challenge_id = c.challenge_id
                  WHERE b.user1_id = :user_id OR b.user2_id = :user_id
                  ORDER BY b.created_at DESC";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        
        $bets = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format the bets data
        $formatted_bets = [];
        foreach ($bets as $bet) {
            $formatted_bets[] = [
                'bet_id' => $bet['bet_id'],
                'team_a' => $bet['team_a'],
                'team_b' => $bet['team_b'],
                'selected_team' => $bet['selected_team'],
                'amount_user1' => $bet['bet_amount'],
                'bet_status' => $bet['bet_status'],
                'match_time' => $bet['match_time'],
                'match_type' => $bet['match_type'],
                'created_at' => $bet['created_at']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'bets' => $formatted_bets
        ]);
        
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
