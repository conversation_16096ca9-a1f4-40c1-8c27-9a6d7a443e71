"use strict";(self.webpackChunkfanbet247_admin=self.webpackChunkfanbet247_admin||[]).push([[488],{107:(e,t,n)=>{n.r(t),n.d(t,{CLSThresholds:()=>E,FCPThresholds:()=>L,INPThresholds:()=>R,LCPThresholds:()=>_,TTFBThresholds:()=>O,onCLS:()=>S,onFCP:()=>P,onINP:()=>q,onLCP:()=>D,onTTFB:()=>W});let i=-1;const r=e=>{addEventListener("pageshow",t=>{t.persisted&&(i=t.timeStamp,e(t))},!0)},s=(e,t,n,i)=>{let r,s;return o=>{t.value>=0&&(o||i)&&(s=t.value-(r??0),(s||void 0===r)&&(r=t.value,t.delta=s,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,n),e(t)))}},o=e=>{requestAnimationFrame(()=>requestAnimationFrame(()=>e()))},a=()=>{const e=performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},l=()=>{const e=a();return e?.activationStart??0},d=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;const n=a();let r="navigate";return i>=0?r="back-forward-cache":n&&(document.prerendering||l()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:t,rating:"good",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},c=new WeakMap;function h(e,t){return c.get(e)||c.set(e,new t),c.get(e)}class u{t;i=0;o=[];h(e){if(e.hadRecentInput)return;const t=this.o[0],n=this.o.at(-1);this.i&&t&&n&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),this.t?.(e)}}const m=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const i=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return i.observe({type:e,buffered:!0,...n}),i}}catch{}},p=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let v=-1;const f=()=>"hidden"!==document.visibilityState||document.prerendering?1/0:0,g=e=>{"hidden"===document.visibilityState&&v>-1&&(v="visibilitychange"===e.type?e.timeStamp:0,y())},T=()=>{addEventListener("visibilitychange",g,!0),addEventListener("prerenderingchange",g,!0)},y=()=>{removeEventListener("visibilitychange",g,!0),removeEventListener("prerenderingchange",g,!0)},b=()=>{if(v<0){const e=l(),t=document.prerendering?void 0:globalThis.performance.getEntriesByType("visibility-state").filter(t=>"hidden"===t.name&&t.startTime>e)[0]?.startTime;v=t??f(),T(),r(()=>{setTimeout(()=>{v=f(),T()})})}return{get firstHiddenTime(){return v}}},C=e=>{document.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},L=[1800,3e3],P=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};C(()=>{const n=b();let i,a=d("FCP");const c=m("paint",e=>{for(const t of e)"first-contentful-paint"===t.name&&(c.disconnect(),t.startTime<n.firstHiddenTime&&(a.value=Math.max(t.startTime-l(),0),a.entries.push(t),i(!0)))});c&&(i=s(e,a,L,t.reportAllChanges),r(n=>{a=d("FCP"),i=s(e,a,L,t.reportAllChanges),o(()=>{a.value=performance.now()-n.timeStamp,i(!0)})}))})},E=[.1,.25],S=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};P(p(()=>{let n,i=d("CLS",0);const a=h(t,u),l=e=>{for(const t of e)a.h(t);a.i>i.value&&(i.value=a.i,i.entries=a.o,n())},c=m("layout-shift",l);c&&(n=s(e,i,E,t.reportAllChanges),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(l(c.takeRecords()),n(!0))}),r(()=>{a.i=0,i=d("CLS",0),n=s(e,i,E,t.reportAllChanges),o(()=>n())}),setTimeout(n))}))};let w=0,A=1/0,I=0;const k=e=>{for(const t of e)t.interactionId&&(A=Math.min(A,t.interactionId),I=Math.max(I,t.interactionId),w=I?(I-A)/7+1:0)};let M;const F=()=>M?w:performance.interactionCount??0;let B=0;class x{u=[];l=(()=>new Map)();m;v;p(){B=F(),this.u.length=0,this.l.clear()}P(){const e=Math.min(this.u.length-1,Math.floor((F()-B)/50));return this.u[e]}h(e){if(this.m?.(e),!e.interactionId&&"first-input"!==e.entryType)return;const t=this.u.at(-1);let n=this.l.get(e.interactionId);if(n||this.u.length<10||e.duration>t.T){if(n?e.duration>n.T?(n.entries=[e],n.T=e.duration):e.duration===n.T&&e.startTime===n.entries[0].startTime&&n.entries.push(e):(n={id:e.interactionId,entries:[e],T:e.duration},this.l.set(n.id,n),this.u.push(n)),this.u.sort((e,t)=>t.T-e.T),this.u.length>10){const e=this.u.splice(10);for(const t of e)this.l.delete(t.id)}this.v?.(n)}}}const N=e=>{const t=globalThis.requestIdleCallback||setTimeout;"hidden"===document.visibilityState?e():(e=p(e),document.addEventListener("visibilitychange",e,{once:!0}),t(()=>{e(),document.removeEventListener("visibilitychange",e)}))},R=[200,500],q=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&C(()=>{"interactionCount"in performance||M||(M=m("event",k,{type:"event",buffered:!0,durationThreshold:0}));let n,i=d("INP");const o=h(t,x),a=e=>{N(()=>{for(const n of e)o.h(n);const t=o.P();t&&t.T!==i.value&&(i.value=t.T,i.entries=t.entries,n())})},l=m("event",a,{durationThreshold:t.durationThreshold??40});n=s(e,i,R,t.reportAllChanges),l&&(l.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(a(l.takeRecords()),n(!0))}),r(()=>{o.p(),i=d("INP"),n=s(e,i,R,t.reportAllChanges)}))})};class H{m;h(e){this.m?.(e)}}const _=[2500,4e3],D=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};C(()=>{const n=b();let i,a=d("LCP");const c=h(t,H),u=e=>{t.reportAllChanges||(e=e.slice(-1));for(const t of e)c.h(t),t.startTime<n.firstHiddenTime&&(a.value=Math.max(t.startTime-l(),0),a.entries=[t],i())},v=m("largest-contentful-paint",u);if(v){i=s(e,a,_,t.reportAllChanges);const n=p(()=>{u(v.takeRecords()),v.disconnect(),i(!0)});for(const e of["keydown","click","visibilitychange"])addEventListener(e,()=>N(n),{capture:!0,once:!0});r(n=>{a=d("LCP"),i=s(e,a,_,t.reportAllChanges),o(()=>{a.value=performance.now()-n.timeStamp,i(!0)})})}})},O=[800,1800],$=e=>{document.prerendering?C(()=>$(e)):"complete"!==document.readyState?addEventListener("load",()=>$(e),!0):setTimeout(e)},W=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=d("TTFB"),i=s(e,n,O,t.reportAllChanges);$(()=>{const o=a();o&&(n.value=Math.max(o.responseStart-l(),0),n.entries=[o],i(!0),r(()=>{n=d("TTFB",0),i=s(e,n,O,t.reportAllChanges),i(!0)}))})}}}]);
//# sourceMappingURL=488.8a63ce2b.chunk.js.map