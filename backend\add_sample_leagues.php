<?php
/**
 * Add sample league data for testing the homepage layout
 */

include_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Sample leagues data
    $sampleLeagues = [
        [
            'name' => 'Premier League Champions',
            'description' => 'Compete with the best in our most prestigious league. High stakes, high rewards!',
            'theme_color' => '#1a365d',
            'status' => 'active',
            'min_bet_amount' => 1000,
            'max_bet_amount' => 50000,
            'icon_path' => null
        ],
        [
            'name' => 'Weekend Warriors',
            'description' => 'Perfect for casual bettors who love weekend matches. Join the fun every Saturday and Sunday!',
            'theme_color' => '#38a169',
            'status' => 'active',
            'min_bet_amount' => 500,
            'max_bet_amount' => 25000,
            'icon_path' => null
        ],
        [
            'name' => 'Champions League Elite',
            'description' => 'For the elite bettors only. Experience the thrill of European football betting.',
            'theme_color' => '#ed8936',
            'status' => 'active',
            'min_bet_amount' => 2000,
            'max_bet_amount' => 100000,
            'icon_path' => null
        ],
        [
            'name' => 'Rookie League',
            'description' => 'New to betting? Start here! Learn the ropes with lower stakes and friendly competition.',
            'theme_color' => '#3182ce',
            'status' => 'active',
            'min_bet_amount' => 100,
            'max_bet_amount' => 5000,
            'icon_path' => null
        ],
        [
            'name' => 'La Liga Masters',
            'description' => 'Spanish football at its finest. Join fellow La Liga enthusiasts in this exciting league.',
            'theme_color' => '#e53e3e',
            'status' => 'upcoming',
            'min_bet_amount' => 1500,
            'max_bet_amount' => 75000,
            'icon_path' => null
        ],
        [
            'name' => 'Serie A Specialists',
            'description' => 'Italian football betting league for true connoisseurs of the beautiful game.',
            'theme_color' => '#805ad5',
            'status' => 'upcoming',
            'min_bet_amount' => 1200,
            'max_bet_amount' => 60000,
            'icon_path' => null
        ]
    ];

    // Insert sample leagues
    $stmt = $conn->prepare("
        INSERT INTO leagues (name, description, theme_color, status, min_bet_amount, max_bet_amount, icon_path, created_at)
        VALUES (:name, :description, :theme_color, :status, :min_bet_amount, :max_bet_amount, :icon_path, NOW())
    ");

    $insertedCount = 0;
    foreach ($sampleLeagues as $league) {
        try {
            $stmt->execute([
                ':name' => $league['name'],
                ':description' => $league['description'],
                ':theme_color' => $league['theme_color'],
                ':status' => $league['status'],
                ':min_bet_amount' => $league['min_bet_amount'],
                ':max_bet_amount' => $league['max_bet_amount'],
                ':icon_path' => $league['icon_path']
            ]);
            $insertedCount++;
            echo "✅ Added league: " . $league['name'] . "\n";
        } catch (Exception $e) {
            echo "❌ Failed to add league: " . $league['name'] . " - " . $e->getMessage() . "\n";
        }
    }

    // Add some sample league memberships for statistics
    $leagueIds = $conn->query("SELECT league_id FROM leagues ORDER BY league_id DESC LIMIT 6")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($leagueIds)) {
        $membershipStmt = $conn->prepare("
            INSERT INTO league_memberships (league_id, user_id, deposit_amount, status, joined_at)
            VALUES (:league_id, :user_id, :deposit_amount, 'active', NOW())
        ");

        // Get some user IDs
        $userIds = $conn->query("SELECT user_id FROM users LIMIT 10")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($userIds)) {
            foreach ($leagueIds as $index => $leagueId) {
                // Add random number of members to each league
                $memberCount = rand(5, 15);
                for ($i = 0; $i < $memberCount && $i < count($userIds); $i++) {
                    $userId = $userIds[$i % count($userIds)];
                    $depositAmount = rand(1000, 10000);
                    
                    try {
                        $membershipStmt->execute([
                            ':league_id' => $leagueId,
                            ':user_id' => $userId,
                            ':deposit_amount' => $depositAmount
                        ]);
                    } catch (Exception $e) {
                        // Ignore duplicate entries
                    }
                }
                echo "✅ Added members to league ID: $leagueId\n";
            }
        }
    }

    echo "\n🎉 Sample data creation completed!\n";
    echo "📊 Inserted $insertedCount leagues\n";
    echo "🔗 You can now test the homepage at: http://localhost:3000\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>