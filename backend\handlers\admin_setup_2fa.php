<?php
/**
 * Setup Admin 2FA (Google Authenticator)
 * Generates secret key and QR code for admin 2FA setup
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';
require_once '../vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    $adminId = $_GET['adminId'] ?? null;
    if (!$adminId) {
        throw new Exception("Admin ID required");
    }
    
    // Verify admin exists
    $stmt = $conn->prepare("SELECT admin_id, username, email, role FROM admins WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Admin not found");
    }
    
    // Check if 2FA is enabled globally
    $stmt = $conn->prepare("SELECT setting_value FROM admin_auth_settings WHERE setting_name = 'admin_2fa_enabled'");
    $stmt->execute();
    $tfaEnabled = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tfaEnabled || $tfaEnabled['setting_value'] !== 'true') {
        throw new Exception("2FA authentication is not enabled globally");
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Generate new 2FA setup
        
        $google2fa = new Google2FA();
        
        // Generate secret key
        $secretKey = $google2fa->generateSecretKey();
        
        // Get backup codes count setting
        $stmt = $conn->prepare("SELECT setting_value FROM admin_auth_settings WHERE setting_name = 'admin_backup_codes_count'");
        $stmt->execute();
        $backupCodesCount = $stmt->fetch(PDO::FETCH_ASSOC)['setting_value'] ?? 10;
        
        // Generate backup codes
        $backupCodes = [];
        for ($i = 0; $i < $backupCodesCount; $i++) {
            $backupCodes[] = strtoupper(substr(bin2hex(random_bytes(4)), 0, 8));
        }
        
        $conn->beginTransaction();
        
        // Store secret key and backup codes (not enabled yet)
        $stmt = $conn->prepare("
            INSERT INTO admin_2fa (admin_id, secret_key, auth_type, backup_codes, is_enabled, setup_completed) 
            VALUES (?, ?, 'google_auth', ?, 0, 0)
            ON DUPLICATE KEY UPDATE 
            secret_key = VALUES(secret_key), 
            backup_codes = VALUES(backup_codes), 
            is_enabled = 0,
            setup_completed = 0,
            updated_at = NOW()
        ");
        
        $backupCodesJson = json_encode($backupCodes);
        $stmt->execute([$adminId, $secretKey, $backupCodesJson]);
        
        // Generate QR code URL
        $qrCodeUrl = $google2fa->getQRCodeUrl(
            'FanBet247 Admin',
            $admin['email'],
            $secretKey
        );
        
        // Log 2FA setup initiation
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, '2fa', '2fa_setup', ?, ?, ?)
        ");
        $stmt->execute([
            $adminId,
            json_encode(['setup_initiated' => true, 'auth_type' => 'google_auth']),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'secret_key' => $secretKey,
            'qr_code_url' => $qrCodeUrl,
            'backup_codes' => $backupCodes,
            'manual_entry_key' => $secretKey,
            'issuer' => 'FanBet247 Admin',
            'account_name' => $admin['email'],
            'setup_complete' => false,
            'message' => '2FA setup initiated. Please scan the QR code with Google Authenticator and verify with a test code.'
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Verify and complete 2FA setup
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['verification_code'])) {
            throw new Exception("Verification code required");
        }
        
        $verificationCode = $input['verification_code'];
        
        // Get the pending 2FA setup
        $stmt = $conn->prepare("
            SELECT secret_key, backup_codes 
            FROM admin_2fa 
            WHERE admin_id = ? AND setup_completed = 0
        ");
        $stmt->execute([$adminId]);
        $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$tfaSetup) {
            throw new Exception("No pending 2FA setup found. Please initiate setup first.");
        }
        
        $google2fa = new Google2FA();
        
        // Verify the code
        $isValid = $google2fa->verifyKey($tfaSetup['secret_key'], $verificationCode);
        
        if (!$isValid) {
            // Log failed verification
            $stmt = $conn->prepare("
                INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
                VALUES (?, '2fa', 'login_failed', ?, ?, ?)
            ");
            $stmt->execute([
                $adminId,
                json_encode(['reason' => 'invalid_2fa_setup_code']),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            throw new Exception("Invalid verification code. Please check your Google Authenticator app and try again.");
        }
        
        $conn->beginTransaction();
        
        // Complete 2FA setup
        $stmt = $conn->prepare("
            UPDATE admin_2fa 
            SET is_enabled = 1, setup_completed = 1, updated_at = NOW() 
            WHERE admin_id = ?
        ");
        $stmt->execute([$adminId]);
        
        // Update admin record
        $stmt = $conn->prepare("
            UPDATE admins 
            SET two_factor_enabled = 1, auth_method = '2fa', last_2fa_setup = NOW() 
            WHERE admin_id = ?
        ");
        $stmt->execute([$adminId]);
        
        // Log successful 2FA setup completion
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, '2fa', '2fa_setup', ?, ?, ?)
        ");
        $stmt->execute([
            $adminId,
            json_encode(['setup_completed' => true, 'auth_type' => 'google_auth']),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => '2FA setup completed successfully',
            'backup_codes' => json_decode($tfaSetup['backup_codes'], true),
            'setup_complete' => true,
            'auth_method' => '2fa',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
