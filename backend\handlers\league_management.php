<?php
header("Access-Control-Allow-Origin: *"); 
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';
$conn = getDBConnection();

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
    exit();
}

// Verify admin authentication
function isAdmin() {
    // For now, return admin ID 1. In production, implement proper admin authentication
    return 1;
}

$adminId = isAdmin();
if (!$adminId) {
    jsonResponse(401, 'Unauthorized access');
    exit;
}

// Handle file uploads
function handleFileUpload($file, $type) {
    if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
        return null;
    }

    $uploadDir = "../uploads/leagues/" . $type . "s/";
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $fileInfo = pathinfo($file['name']);
    $extension = strtolower($fileInfo['extension']);
    
    // Validate file type
    $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
    if (!in_array($extension, $allowedTypes)) {
        throw new Exception("Invalid file type. Allowed types: " . implode(', ', $allowedTypes));
    }

    // Generate unique filename
    $filename = uniqid('league_' . $type . '_') . '.' . $extension;
    $targetPath = $uploadDir . $filename;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
        throw new Exception("Failed to upload " . $type);
    }

    return $filename;
}

// GET: Retrieve leagues
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        if (isset($_GET['league_id'])) {
            // Fetch specific league
            $query = "SELECT * FROM leagues WHERE league_id = ?";
            $stmt = $conn->prepare($query);
            $stmt->execute([$_GET['league_id']]);
            $league = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($league) {
                // Add full URLs for media files
                if ($league['icon_path']) {
                    $league['icon_url'] = '/backend/uploads/leagues/icons/' . $league['icon_path'];
                }
                if ($league['banner_path']) {
                    $league['banner_url'] = '/backend/uploads/leagues/banners/' . $league['banner_path'];
                }
                jsonResponse(200, 'League fetched successfully', $league);
            } else {
                jsonResponse(404, 'League not found');
            }
        } else {
            // Fetch all leagues
            $query = "SELECT 
                l.*,
                COALESCE(m.member_count, 0) as member_count
            FROM leagues l
            LEFT JOIN (
                SELECT league_id, COUNT(*) as member_count 
                FROM league_memberships 
                WHERE status = 'active'
                GROUP BY league_id
            ) m ON m.league_id = l.league_id
            ORDER BY l.created_at DESC";
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $leagues = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Add full URLs for media files
            foreach ($leagues as &$league) {
                if ($league['icon_path']) {
                    $league['icon_url'] = '/backend/uploads/leagues/icons/' . $league['icon_path'];
                }
                if ($league['banner_path']) {
                    $league['banner_url'] = '/backend/uploads/leagues/banners/' . $league['banner_path'];
                }
            }
            
            jsonResponse(200, 'Leagues fetched successfully', $leagues);
        }
    } catch (PDOException $e) {
        error_log("Database error in league_management.php: " . $e->getMessage());
        jsonResponse(500, 'Database error occurred');
    }
}

// POST: Create or update league
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Check if it's an update operation
        $isUpdate = isset($_POST['action']) && $_POST['action'] === 'update' && isset($_POST['league_id']);

        // Validate required fields
        $requiredFields = ['name', 'min_bet_amount', 'max_bet_amount', 'description'];
        foreach ($requiredFields as $field) {
            if (!isset($_POST[$field]) || empty($_POST[$field])) {
                jsonResponse(400, "Missing required field: $field");
            }
        }

        $name = $_POST['name'];
        $min_bet_amount = $_POST['min_bet_amount'];
        $max_bet_amount = $_POST['max_bet_amount'];
        $description = $_POST['description'];
        $season_duration = $_POST['season_duration'] ?? 90;
        $league_rules = $_POST['league_rules'] ?? '';
        $reward_description = $_POST['reward_description'] ?? '';
        $status = $_POST['status'] ?? 'upcoming';
        $theme_color = $_POST['theme_color'] ?? '#007bff';

        // Process file uploads if files are present
        $iconPath = null;
        $bannerPath = null;
        if (isset($_FILES['icon']) && $_FILES['icon']['size'] > 0) {
            $iconPath = handleFileUpload($_FILES['icon'], 'icon');
        }
        if (isset($_FILES['banner']) && $_FILES['banner']['size'] > 0) {
            $bannerPath = handleFileUpload($_FILES['banner'], 'banner');
        }

        if ($isUpdate) {
            // Update existing league
            $query = "UPDATE leagues SET 
                     name = :name, 
                     min_bet_amount = :min_bet_amount, 
                     max_bet_amount = :max_bet_amount, 
                     description = :description, 
                     season_duration = :season_duration, 
                     league_rules = :league_rules,
                     reward_description = :reward_description, 
                     status = :status, 
                     theme_color = :theme_color, 
                     updated_at = NOW()";

            $params = [
                ':name' => $name,
                ':min_bet_amount' => $min_bet_amount,
                ':max_bet_amount' => $max_bet_amount,
                ':description' => $description,
                ':season_duration' => $season_duration,
                ':league_rules' => $league_rules,
                ':reward_description' => $reward_description,
                ':status' => $status,
                ':theme_color' => $theme_color
            ];

            if ($iconPath) {
                $query .= ", icon_path = :icon_path";
                $params[':icon_path'] = $iconPath;
            }
            if ($bannerPath) {
                $query .= ", banner_path = :banner_path";
                $params[':banner_path'] = $bannerPath;
            }

            $query .= " WHERE league_id = :league_id";
            $params[':league_id'] = $_POST['league_id'];

            $stmt = $conn->prepare($query);
            $result = $stmt->execute($params);

            if ($result) {
                jsonResponse(200, 'League updated successfully', [
                    'league_id' => $_POST['league_id'],
                    'name' => $name,
                    'description' => $description,
                    'min_bet_amount' => $min_bet_amount,
                    'max_bet_amount' => $max_bet_amount,
                    'season_duration' => $season_duration,
                    'league_rules' => $league_rules,
                    'reward_description' => $reward_description,
                    'status' => $status,
                    'theme_color' => $theme_color,
                    'icon_path' => $iconPath,
                    'banner_path' => $bannerPath
                ]);
            } else {
                jsonResponse(500, 'Failed to update league');
            }
        } else {
            // Create new league
            $query = "INSERT INTO leagues (
                name, min_bet_amount, max_bet_amount, description, 
                season_duration, league_rules, reward_description, 
                status, icon_path, banner_path, theme_color, 
                created_by, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
                NOW(), NOW()
            )";
            
            $stmt = $conn->prepare($query);
            $stmt->execute([
                $name, $min_bet_amount, $max_bet_amount, $description, 
                $season_duration, $league_rules, $reward_description, 
                $status, $iconPath, $bannerPath, $theme_color,
                $adminId // Add created_by from admin ID
            ]);

            $league_id = $conn->lastInsertId();
            jsonResponse(201, 'League created successfully', ['league_id' => $league_id]);
        }
    } catch (Exception $e) {
        error_log("Error in league_management.php: " . $e->getMessage());
        jsonResponse(500, 'Failed to process league data: ' . $e->getMessage());
    }
}

// DELETE: Remove a league
if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    try {
        $data = json_decode(file_get_contents("php://input"));
        if (!isset($data->league_id)) {
            jsonResponse(400, 'League ID is required');
        }

        // First get the league to delete its media files
        $query = "SELECT icon_path, banner_path FROM leagues WHERE league_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$data->league_id]);
        $league = $stmt->fetch(PDO::FETCH_ASSOC);

        // Delete the league
        $query = "DELETE FROM leagues WHERE league_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$data->league_id]);

        if ($stmt->rowCount() > 0) {
            // Delete media files if they exist
            if ($league) {
                if ($league['icon_path']) {
                    @unlink("../uploads/leagues/icons/" . $league['icon_path']);
                }
                if ($league['banner_path']) {
                    @unlink("../uploads/leagues/banners/" . $league['banner_path']);
                }
            }
            jsonResponse(200, 'League deleted successfully');
        } else {
            jsonResponse(404, 'League not found');
        }
    } catch (PDOException $e) {
        error_log("Database error in league_management.php: " . $e->getMessage());
        jsonResponse(500, 'Failed to delete league');
    }
}

jsonResponse(405, 'Method not allowed');
