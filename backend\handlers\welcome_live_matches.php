<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get limit from query parameter
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 6;
    $limit = max(1, min($limit, 20)); // Ensure limit is between 1 and 20
    
    // Query to get active challenges with team logos
    $query = "SELECT 
        c.challenge_id,
        c.team_a,
        c.team_b,
        c.odds_team_a,
        c.odds_team_b,
        c.odds_draw,
        c.match_date,
        c.start_time,
        c.end_time,
        c.status,
        t1.logo as team_a_logo,
        t2.logo as team_b_logo,
        TIMESTAMPDIFF(SECOND, NOW(), c.end_time) as seconds_remaining,
        COUNT(b.bet_id) as total_bets
    FROM challenges c
    LEFT JOIN teams t1 ON c.team_a = t1.name
    LEFT JOIN teams t2 ON c.team_b = t2.name
    LEFT JOIN bets b ON c.challenge_id = b.challenge_id
    WHERE c.status = 'Open' 
    AND c.end_time > NOW()
    GROUP BY c.challenge_id
    ORDER BY c.match_date ASC, c.challenge_date DESC
    LIMIT ?";
    
    $stmt = $conn->prepare($query);
    $stmt->execute([$limit]);
    $challenges = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process the challenges to add live match simulation
    $liveMatches = [];
    foreach ($challenges as $challenge) {
        // Simulate live match data
        $isLive = rand(0, 1) === 1; // 50% chance of being live
        $matchTime = $isLive ? rand(1, 90) : 0;
        
        // Generate realistic scores for live matches
        $teamAScore = $isLive ? rand(0, 3) : 0;
        $teamBScore = $isLive ? rand(0, 3) : 0;
        
        // Add some variation to odds for live matches
        $oddsVariation = $isLive ? (rand(-15, 15) / 100) : 0;
        
        $match = [
            'challenge_id' => $challenge['challenge_id'],
            'team_a' => $challenge['team_a'],
            'team_b' => $challenge['team_b'],
            'team_a_logo' => $challenge['team_a_logo'],
            'team_b_logo' => $challenge['team_b_logo'],
            'odds_team_a' => number_format(max(1.01, floatval($challenge['odds_team_a']) + $oddsVariation), 2),
            'odds_team_b' => number_format(max(1.01, floatval($challenge['odds_team_b']) + $oddsVariation), 2),
            'odds_draw' => number_format(max(1.01, floatval($challenge['odds_draw']) + $oddsVariation), 2),
            'match_date' => $challenge['match_date'],
            'start_time' => $challenge['start_time'],
            'end_time' => $challenge['end_time'],
            'status' => $challenge['status'],
            'seconds_remaining' => $challenge['seconds_remaining'],
            'total_bets' => $challenge['total_bets'],
            'is_live' => $isLive,
            'match_time' => $matchTime,
            'team_a_score' => $teamAScore,
            'team_b_score' => $teamBScore,
            'league' => 'Premier League', // Default league for demo
            'last_updated' => date('Y-m-d H:i:s')
        ];
        
        $liveMatches[] = $match;
    }
    
    // If no real challenges, create some demo data
    if (empty($liveMatches)) {
        $demoMatches = [
            [
                'challenge_id' => 'demo_1',
                'team_a' => 'Manchester City',
                'team_b' => 'Arsenal',
                'team_a_logo' => null,
                'team_b_logo' => null,
                'odds_team_a' => '1.75',
                'odds_team_b' => '4.20',
                'odds_draw' => '3.40',
                'is_live' => true,
                'match_time' => 65,
                'team_a_score' => 2,
                'team_b_score' => 1,
                'league' => 'Premier League',
                'total_bets' => 156,
                'seconds_remaining' => 1500,
                'last_updated' => date('Y-m-d H:i:s')
            ],
            [
                'challenge_id' => 'demo_2',
                'team_a' => 'Real Madrid',
                'team_b' => 'Barcelona',
                'team_a_logo' => null,
                'team_b_logo' => null,
                'odds_team_a' => '2.10',
                'odds_team_b' => '3.80',
                'odds_draw' => '3.25',
                'is_live' => true,
                'match_time' => 72,
                'team_a_score' => 1,
                'team_b_score' => 1,
                'league' => 'La Liga',
                'total_bets' => 243,
                'seconds_remaining' => 1080,
                'last_updated' => date('Y-m-d H:i:s')
            ],
            [
                'challenge_id' => 'demo_3',
                'team_a' => 'Bayern Munich',
                'team_b' => 'Borussia Dortmund',
                'team_a_logo' => null,
                'team_b_logo' => null,
                'odds_team_a' => '1.95',
                'odds_team_b' => '4.10',
                'odds_draw' => '3.50',
                'is_live' => true,
                'match_time' => 53,
                'team_a_score' => 0,
                'team_b_score' => 0,
                'league' => 'Bundesliga',
                'total_bets' => 189,
                'seconds_remaining' => 2220,
                'last_updated' => date('Y-m-d H:i:s')
            ]
        ];
        
        $liveMatches = array_slice($demoMatches, 0, $limit);
    }
    
    echo json_encode([
        'success' => true,
        'matches' => $liveMatches,
        'count' => count($liveMatches),
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => 'Live matches fetched successfully'
    ]);

} catch (Exception $e) {
    error_log("Error in welcome_live_matches.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'matches' => [],
        'count' => 0,
        'message' => 'Failed to fetch live matches: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
