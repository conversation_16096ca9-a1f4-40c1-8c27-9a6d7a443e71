<?php
// Save as php_check.php
header("Content-Type: text/html; charset=UTF-8");

function checkPhpEnvironment() {
    echo "<html><head><title>PHP Environment Check</title>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        code { background: #f4f4f4; padding: 2px 5px; }
    </style></head><body>";
    
    echo "<h1>PHP Environment Check</h1>";
    
    // PHP Version
    echo "<div class='section'>";
    echo "<h2>PHP Version</h2>";
    echo "Current PHP version: " . PHP_VERSION;
    if (version_compare(PHP_VERSION, '7.0.0', '>=')) {
        echo " <span class='success'>(✓ Good)</span>";
    } else {
        echo " <span class='error'>(✗ Needs update)</span>";
    }
    echo "</div>";
    
    // PDO Check
    echo "<div class='section'>";
    echo "<h2>PDO Status</h2>";
    if (extension_loaded('pdo')) {
        echo "<p class='success'>✓ PDO is installed</p>";
        
        echo "<h3>Available PDO Drivers:</h3>";
        $drivers = PDO::getAvailableDrivers();
        foreach ($drivers as $driver) {
            echo "- $driver<br>";
        }
        
        if (in_array('mysql', $drivers)) {
            echo "<p class='success'>✓ PDO MySQL driver is installed</p>";
        } else {
            echo "<p class='error'>✗ PDO MySQL driver is NOT installed</p>";
        }
    } else {
        echo "<p class='error'>✗ PDO is NOT installed</p>";
    }
    echo "</div>";
    
    // MySQL/MariaDB Check
    /*
    echo "<div class='section'>";
    echo "<h2>MySQL/MariaDB Support</h2>";
    if (function_exists('mysqli_connect')) {
        echo "<p class='success'>✓ MySQLi is installed</p>";
    } else {
        echo "<p class='error'>✗ MySQLi is NOT installed</p>";
    }
    echo "</div>";
    */
    
    // PHP Info Summary
    echo "<div class='section'>";
    echo "<h2>Installation Instructions</h2>";
    echo "<h3>For Ubuntu/Debian:</h3>";
    echo "<code>sudo apt-get update</code><br>";
    echo "<code>sudo apt-get install php-mysql php-pdo</code><br>";
    echo "<code>sudo systemctl restart apache2</code><br><br>";
    
    echo "<h3>For CentOS/RHEL:</h3>";
    echo "<code>sudo yum install php-mysql php-pdo</code><br>";
    echo "<code>sudo systemctl restart httpd</code><br><br>";
    
    echo "<h3>For Windows (XAMPP):</h3>";
    echo "PDO should be pre-installed with XAMPP. If not, check your php.ini file and ensure the following lines are uncommented:<br>";
    echo "<code>extension=pdo</code><br>";
    echo "<code>extension=pdo_mysql</code><br>";
    echo "</div>";
    
    // Server Information
    echo "<div class='section'>";
    echo "<h2>Server Information</h2>";
    echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
    echo "PHP SAPI: " . php_sapi_name() . "<br>";
    echo "Operating System: " . PHP_OS . "<br>";
    
    // Check important PHP settings
    echo "<h3>PHP Settings:</h3>";
    $settings = array(
        'display_errors',
        'error_reporting',
        'max_execution_time',
        'memory_limit',
        'post_max_size',
        'upload_max_filesize'
    );
    
    foreach ($settings as $setting) {
        echo "$setting: " . ini_get($setting) . "<br>";
    }
    echo "</div>";
    
    echo "</body></html>";
}

checkPhpEnvironment();
?>