<?php
/**
 * Authentication Check Functions
 * Provides authentication verification for admin and user sessions
 */

function checkAuth() {
    try {
        // Check for session-based authentication first
        session_start();
        
        if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
            return [
                'success' => true,
                'user_id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'auth_method' => 'session'
            ];
        }
        
        // Check for token-based authentication
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;
        
        if ($authHeader && strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            return validateToken($token);
        }
        
        // Check for admin authentication in localStorage (for admin pages)
        if (isset($_SERVER['HTTP_X_ADMIN_ID']) && isset($_SERVER['HTTP_X_ADMIN_TOKEN'])) {
            $adminId = $_SERVER['HTTP_X_ADMIN_ID'];
            $adminToken = $_SERVER['HTTP_X_ADMIN_TOKEN'];
            return validateAdminToken($adminId, $adminToken);
        }
        
        return [
            'success' => false,
            'message' => 'No valid authentication found'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Authentication check failed: ' . $e->getMessage()
        ];
    }
}

function validateToken($token) {
    try {
        require_once __DIR__ . '/db_connect.php';
        $conn = getDBConnection();
        
        // Simple token validation - in production, use JWT or similar
        $stmt = $conn->prepare("
            SELECT u.user_id, u.username, u.role 
            FROM users u 
            JOIN user_sessions s ON u.user_id = s.user_id 
            WHERE s.session_token = ? AND s.expires_at > NOW()
        ");
        
        $stmt->execute([$token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            return [
                'success' => true,
                'user_id' => $user['user_id'],
                'username' => $user['username'],
                'role' => $user['role'],
                'auth_method' => 'token'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Invalid or expired token'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Token validation failed: ' . $e->getMessage()
        ];
    }
}

function validateAdminToken($adminId, $adminToken) {
    try {
        require_once __DIR__ . '/db_connect.php';
        $conn = getDBConnection();
        
        // Validate admin session
        $stmt = $conn->prepare("
            SELECT u.user_id, u.username, u.role 
            FROM users u 
            WHERE u.user_id = ? AND u.role = 'admin'
        ");
        
        $stmt->execute([$adminId]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            // In a real implementation, validate the admin token properly
            // For now, we'll accept any admin ID with role 'admin'
            return [
                'success' => true,
                'user_id' => $admin['user_id'],
                'username' => $admin['username'],
                'role' => $admin['role'],
                'auth_method' => 'admin_token'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Invalid admin credentials'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Admin validation failed: ' . $e->getMessage()
        ];
    }
}

function requireAuth() {
    $authResult = checkAuth();
    
    if (!$authResult['success']) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Authentication required'
        ]);
        exit;
    }
    
    return $authResult;
}

function requireAdminAuth() {
    $authResult = checkAuth();
    
    if (!$authResult['success']) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Authentication required'
        ]);
        exit;
    }
    
    if ($authResult['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'message' => 'Admin privileges required'
        ]);
        exit;
    }
    
    return $authResult;
}

function getCurrentUser() {
    $authResult = checkAuth();
    
    if ($authResult['success']) {
        try {
            require_once __DIR__ . '/db_connect.php';
            $conn = getDBConnection();
            
            $stmt = $conn->prepare("
                SELECT user_id, username, email, role, status, created_at, last_login 
                FROM users 
                WHERE user_id = ?
            ");
            
            $stmt->execute([$authResult['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                return [
                    'success' => true,
                    'user' => $user
                ];
            }
        } catch (Exception $e) {
            // Fall through to return auth result
        }
    }
    
    return $authResult;
}
?>
