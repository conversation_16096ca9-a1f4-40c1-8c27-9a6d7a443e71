<?php
/**
 * Check Admin Authentication Setup Status
 * This endpoint checks if all required tables and settings exist for admin authentication
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    $status = [
        'database_connected' => true,
        'tables' => [],
        'settings' => [],
        'admins_table_updated' => false,
        'ready_for_auth' => false
    ];
    
    // Check required tables
    $requiredTables = [
        'admin_auth_settings' => 'Admin authentication settings',
        'admin_2fa' => 'Admin 2FA configuration',
        'admin_otp' => 'Admin OTP tokens',
        'admin_auth_logs' => 'Admin authentication logs',
        'admin_login_attempts' => 'Admin login attempt tracking'
    ];
    
    foreach ($requiredTables as $table => $description) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->rowCount() > 0;
            
            if ($exists) {
                // Get table info
                $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                
                $status['tables'][$table] = [
                    'exists' => true,
                    'description' => $description,
                    'record_count' => $count
                ];
            } else {
                $status['tables'][$table] = [
                    'exists' => false,
                    'description' => $description,
                    'record_count' => 0
                ];
            }
        } catch (PDOException $e) {
            $status['tables'][$table] = [
                'exists' => false,
                'description' => $description,
                'error' => $e->getMessage()
            ];
        }
    }
    
    // Check if admin_auth_settings has required settings
    if ($status['tables']['admin_auth_settings']['exists']) {
        try {
            $stmt = $conn->query("SELECT setting_name, setting_value FROM admin_auth_settings");
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            $status['settings'] = $settings;
        } catch (PDOException $e) {
            $status['settings_error'] = $e->getMessage();
        }
    }
    
    // Check if admins table has been updated with new columns
    try {
        $stmt = $conn->query("SHOW COLUMNS FROM admins LIKE 'auth_method'");
        $status['admins_table_updated'] = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $status['admins_table_error'] = $e->getMessage();
    }
    
    // Check if SMTP settings exist (required for OTP)
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'smtp_settings'");
        $smtpExists = $stmt->rowCount() > 0;
        
        if ($smtpExists) {
            $stmt = $conn->query("SELECT is_active FROM smtp_settings LIMIT 1");
            $smtpActive = $stmt->fetch(PDO::FETCH_ASSOC);
            $status['smtp_configured'] = $smtpActive ? $smtpActive['is_active'] : false;
        } else {
            $status['smtp_configured'] = false;
        }
    } catch (PDOException $e) {
        $status['smtp_error'] = $e->getMessage();
    }
    
    // Determine if system is ready for enhanced authentication
    $allTablesExist = array_reduce($status['tables'], function($carry, $table) {
        return $carry && $table['exists'];
    }, true);
    
    $status['ready_for_auth'] = $allTablesExist && 
                               $status['admins_table_updated'] && 
                               !empty($status['settings']);
    
    // Get current admin count
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM admins");
        $status['admin_count'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    } catch (PDOException $e) {
        $status['admin_count_error'] = $e->getMessage();
    }
    
    echo json_encode([
        'success' => true,
        'status' => $status,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to check admin authentication setup',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
