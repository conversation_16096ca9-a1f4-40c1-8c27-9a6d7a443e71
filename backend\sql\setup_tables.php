<?php
// Setup script to create missing database tables
try {
    $conn = new PDO(
        "mysql:host=localhost;dbname=fanbet247",
        'root',
        'root',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    if (!$conn) {
        die("Database connection failed");
    }
    
    echo "Setting up database tables...\n";
    
    // Execute general_settings.sql
    $generalSettingsSQL = file_get_contents('create_general_settings.sql');
    if ($generalSettingsSQL) {
        $conn->exec($generalSettingsSQL);
        echo "✓ General settings table created\n";
    }
    
    // Execute security_settings.sql
    $securitySettingsSQL = file_get_contents('create_security_settings.sql');
    if ($securitySettingsSQL) {
        $conn->exec($securitySettingsSQL);
        echo "✓ Security settings table created\n";
    }
    
    // Execute smtp_settings.sql
    $smtpSettingsSQL = file_get_contents('create_smtp_settings.sql');
    if ($smtpSettingsSQL) {
        $conn->exec($smtpSettingsSQL);
        echo "✓ SMTP settings table created\n";
    }
    
    // Execute notification_settings.sql
    $notificationSettingsSQL = file_get_contents('create_notification_settings.sql');
    if ($notificationSettingsSQL) {
        $conn->exec($notificationSettingsSQL);
        echo "✓ Notification settings table created\n";
    }
    
    echo "\nAll tables created successfully!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
