<?php
/**
 * Test Admin Login Handler
 */

echo "🔧 Testing Admin Login Handler...\n\n";

// Test database connection first
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    echo "✅ Database connection successful!\n\n";
    
    // Check if admin exists
    echo "📋 Checking admin accounts...\n";
    $stmt = $conn->prepare("SELECT admin_id, username, email, role, auth_method, two_factor_enabled FROM admins");
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    foreach ($admins as $admin) {
        echo "- Admin ID: {$admin['admin_id']}\n";
        echo "  Username: {$admin['username']}\n";
        echo "  Email: {$admin['email']}\n";
        echo "  Role: {$admin['role']}\n";
        echo "  Auth Method: {$admin['auth_method']}\n";
        echo "  2FA Enabled: " . ($admin['two_factor_enabled'] ? 'Yes' : 'No') . "\n\n";
    }
    
    // Test login with superadmin
    echo "🔐 Testing login with superadmin...\n";
    $testUsername = 'superadmin';
    $testPassword = 'admin123'; // Default password
    
    $stmt = $conn->prepare("SELECT admin_id, username, password_hash, role FROM admins WHERE username = ? OR email = ?");
    $stmt->execute([$testUsername, $testUsername]);
    
    if ($stmt->rowCount() > 0) {
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Found admin: {$admin['username']}\n";
        
        // Test password verification
        if (password_verify($testPassword, $admin['password_hash'])) {
            echo "✅ Password verification successful!\n";
        } else {
            echo "❌ Password verification failed!\n";
            echo "Trying alternative passwords...\n";
            
            $altPasswords = ['password', '123456', 'admin', 'superadmin'];
            foreach ($altPasswords as $altPass) {
                if (password_verify($altPass, $admin['password_hash'])) {
                    echo "✅ Password '$altPass' works!\n";
                    break;
                }
            }
        }
    } else {
        echo "❌ Admin not found!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>