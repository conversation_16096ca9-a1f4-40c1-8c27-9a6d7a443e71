<?php
/**
 * Run User Authentication Schema Fix
 * This script applies the database schema fixes for user authentication
 */

require_once 'includes/db_connect.php';

echo "🔧 Starting User Authentication Schema Fix...\n\n";

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Failed to connect to database");
    }
    
    echo "✅ Database connection established\n";
    
    // Read and execute the SQL file
    $sqlFile = __DIR__ . '/sql/fix_user_auth_schema.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "📋 Executing " . count($statements) . " SQL statements...\n\n";
    
    $conn->beginTransaction();
    
    foreach ($statements as $index => $statement) {
        if (trim($statement)) {
            try {
                echo "Executing statement " . ($index + 1) . "...\n";
                $conn->exec($statement);
                echo "✅ Success\n";
            } catch (PDOException $e) {
                // Ignore "column already exists" errors
                if (strpos($e->getMessage(), 'Duplicate column name') !== false ||
                    strpos($e->getMessage(), 'already exists') !== false) {
                    echo "⚠️  Column already exists (skipping)\n";
                } else {
                    throw $e;
                }
            }
        }
    }
    
    $conn->commit();
    
    echo "\n🎉 Schema fix completed successfully!\n\n";
    
    // Verify the changes
    echo "📋 Verifying users table structure:\n";
    
    $stmt = $conn->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $authColumns = ['otp_enabled', 'tfa_enabled', 'auth_method'];
    
    foreach ($authColumns as $column) {
        $found = false;
        foreach ($columns as $col) {
            if ($col['Field'] === $column) {
                echo "✅ Column '$column' exists: {$col['Type']}\n";
                $found = true;
                break;
            }
        }
        if (!$found) {
            echo "❌ Column '$column' missing\n";
        }
    }
    
    // Check if required tables exist
    echo "\n📋 Verifying authentication tables:\n";
    
    $tables = ['user_otp', 'user_auth_logs', 'user_login_attempts'];
    
    foreach ($tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists\n";
        } else {
            echo "❌ Table '$table' missing\n";
        }
    }
    
    echo "\n✨ User authentication system is now ready!\n";
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
