# Design Document

## Overview

The FanBet247 modern home page will serve as the primary entry point for "The Ultimate Soccer Betting Experience". The design emphasizes a clean, modern aesthetic with strategic use of space, engaging visual elements, and seamless data integration from the existing database. The page will showcase live betting opportunities, community engagement, and expert insights while maintaining optimal performance and accessibility.

## Architecture

### Component Structure
```
ModernHomePage
├── Header (Navigation & Branding)
├── HeroSection (Tagline & CTA)
├── TopLeaguesSection (Database-driven)
├── LiveChallengesSection (Real-time data)
├── RecentBetsSection (Community activity)
├── BlogSection (Content management)
└── Footer (Links & Information)
```

### Data Flow Architecture
- **Frontend**: React components with hooks for state management
- **Backend**: PHP API endpoints serving JSON data
- **Database**: MySQL queries optimized for home page performance
- **Caching**: Client-side caching for improved performance
- **Real-time Updates**: Periodic data refresh without full page reload

## Components and Interfaces

### 1. Header Component
**Purpose**: Navigation, branding, and user authentication access

**Design Elements**:
- Logo positioned left with "FanBet247" branding
- Horizontal navigation menu (Home, Leagues, Challenges, About)
- User authentication buttons (Login/Register) or user menu if authenticated
- Responsive hamburger menu for mobile devices
- Sticky header with subtle shadow on scroll

**Technical Specifications**:
- Height: 70px desktop, 60px mobile
- Background: White with 0.95 opacity
- Typography: Inter font family, 16px navigation links
- Hover effects: Smooth 0.3s transitions

### 2. Hero Section
**Purpose**: Immediate impact with tagline and primary call-to-action

**Design Elements**:
- Large, bold typography for "The Ultimate Soccer Betting Experience"
- Subtitle explaining the platform's value proposition
- Primary CTA button "Start Betting Now" or "Join a League"
- Background: Gradient overlay on soccer-themed imagery
- Animated elements: Subtle fade-in animations

**Technical Specifications**:
- Height: 500px desktop, 400px mobile
- Typography: 48px heading (32px mobile), 18px subtitle
- CTA button: 16px padding, rounded corners, hover animations
- Background: Linear gradient from #1a365d to #2d3748

### 3. Top Leagues Section
**Purpose**: Showcase most active and popular betting leagues

**Design Elements**:
- Section heading "Top Performing Leagues"
- Grid layout with league cards (3 columns desktop, 1 column mobile)
- Each card displays: league name, participant count, active bets, prize pool
- Visual indicators for league status (active, upcoming)
- "View All Leagues" button at bottom

**Data Integration**:
```sql
SELECT l.name, l.theme_color, ls.total_participants, 
       COUNT(lm.membership_id) as active_members,
       SUM(lm.deposit_amount) as prize_pool,
       l.status
FROM leagues l 
LEFT JOIN league_seasons ls ON l.league_id = ls.league_id
LEFT JOIN league_memberships lm ON ls.season_id = lm.season_id
WHERE l.status IN ('active', 'upcoming')
GROUP BY l.league_id
ORDER BY active_members DESC, prize_pool DESC
LIMIT 6
```

**Technical Specifications**:
- Card dimensions: 320px width, 200px height
- Spacing: 24px gap between cards
- Border radius: 12px with subtle shadow
- Hover effects: Scale 1.02 transform, shadow increase

### 4. Live Challenges Section
**Purpose**: Display active betting challenges with urgency indicators

**Design Elements**:
- Section heading "Live Challenges"
- Horizontal scrollable cards on mobile, grid on desktop
- Each card shows: team matchup, odds, time remaining, participants
- Countdown timers for challenges ending soon
- Team logos and match information
- "Join Challenge" buttons with different states

**Data Integration**:
```sql
SELECT c.challenge_id, c.team_a, c.team_b, c.logo1, c.logo2,
       c.odds_team_a, c.odds_team_b, c.odds_draw,
       c.match_date, c.status,
       COUNT(b.bet_id) as total_bets,
       COUNT(DISTINCT CASE WHEN b.user2_id IS NOT NULL THEN b.bet_id END) as active_bets
FROM challenges c
LEFT JOIN bets b ON c.challenge_id = b.challenge_id
WHERE c.status = 'Open' AND c.match_date > NOW()
GROUP BY c.challenge_id
ORDER BY c.match_date ASC
LIMIT 8
```

**Technical Specifications**:
- Card dimensions: 280px width, 180px height
- Team logos: 40px diameter, circular crop
- Countdown timer: Real-time JavaScript updates
- Status indicators: Color-coded badges (green=active, orange=ending soon)

### 5. Recent Bets Section
**Purpose**: Show community betting activity to encourage engagement

**Design Elements**:
- Section heading "Recent Betting Activity"
- List/card layout showing recent bet placements
- Anonymized user information (username only)
- Bet details: amount, choice, timestamp, status
- Win/loss indicators with appropriate styling
- "View All Bets" link

**Data Integration**:
```sql
SELECT b.bet_id, b.amount_user1, b.bet_choice_user1, b.created_at,
       b.bet_status, b.user1_outcome,
       u.username as user1_name,
       c.team_a, c.team_b, c.match_date
FROM bets b
JOIN users u ON b.user1_id = u.user_id
JOIN challenges c ON b.challenge_id = c.challenge_id
WHERE b.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY b.created_at DESC
LIMIT 10
```

**Technical Specifications**:
- List item height: 80px
- Avatar placeholders: 32px diameter
- Status indicators: Icons with color coding
- Timestamp: Relative time display (e.g., "2 hours ago")

### 6. Blog Section
**Purpose**: Provide expert insights and platform updates

**Design Elements**:
- Section heading "Latest Insights"
- Featured article with large image and excerpt
- Secondary articles in smaller card format
- Author information and publication dates
- Category tags for content organization
- "Read More" links and "View All Articles" button

**Data Structure** (New table needed):
```sql
CREATE TABLE blog_posts (
    post_id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    excerpt TEXT,
    content LONGTEXT,
    featured_image VARCHAR(255),
    author_name VARCHAR(100),
    category VARCHAR(50),
    published_at TIMESTAMP,
    status ENUM('draft', 'published') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Technical Specifications**:
- Featured article: 400px height, full width
- Secondary articles: 250px width, 200px height
- Image aspect ratio: 16:9 for consistency
- Typography: 24px headings, 16px body text

### 7. Footer Component
**Purpose**: Site navigation, legal information, and social links

**Design Elements**:
- Multi-column layout with organized link sections
- Company information and contact details
- Social media icons with hover effects
- Newsletter signup form
- Copyright and legal links
- Responsive stacking on mobile

**Technical Specifications**:
- Background: Dark theme (#1a202c)
- Text color: Light gray (#a0aec0)
- Link hover: White color transition
- Padding: 60px top/bottom, 40px sides

## Data Models

### Homepage Data API Response
```typescript
interface HomepageData {
  topLeagues: League[];
  liveChallenges: Challenge[];
  recentBets: RecentBet[];
  blogPosts: BlogPost[];
  siteStats: SiteStatistics;
}

interface League {
  league_id: number;
  name: string;
  theme_color: string;
  total_participants: number;
  active_members: number;
  prize_pool: number;
  status: 'active' | 'upcoming' | 'inactive';
}

interface Challenge {
  challenge_id: number;
  team_a: string;
  team_b: string;
  logo1: string;
  logo2: string;
  odds_team_a: number;
  odds_team_b: number;
  odds_draw: number;
  match_date: string;
  time_remaining: number;
  total_bets: number;
  status: string;
}

interface RecentBet {
  bet_id: number;
  username: string;
  amount: number;
  bet_choice: string;
  created_at: string;
  status: 'pending' | 'won' | 'lost';
  team_a: string;
  team_b: string;
}

interface BlogPost {
  post_id: number;
  title: string;
  excerpt: string;
  featured_image: string;
  author_name: string;
  category: string;
  published_at: string;
}
```

## Error Handling

### API Error Scenarios
1. **Database Connection Failure**: Display cached data with offline indicator
2. **Slow Query Response**: Show loading skeletons, timeout after 5 seconds
3. **Empty Data Sets**: Display encouraging messages and CTAs
4. **Image Loading Failures**: Fallback to default team logos and placeholders
5. **Network Connectivity**: Retry mechanism with exponential backoff

### User Experience Considerations
- Graceful degradation when JavaScript is disabled
- Progressive loading with skeleton screens
- Error boundaries to prevent complete page crashes
- Accessibility compliance (WCAG 2.1 AA)
- SEO optimization with proper meta tags and structured data

## Testing Strategy

### Unit Testing
- Component rendering tests for all sections
- Data transformation and formatting functions
- API response handling and error scenarios
- Responsive design breakpoint testing

### Integration Testing
- End-to-end user flows from home page to registration/login
- Database query performance under load
- Cross-browser compatibility (Chrome, Firefox, Safari, Edge)
- Mobile device testing on various screen sizes

### Performance Testing
- Lighthouse performance audits (target: 90+ score)
- Core Web Vitals optimization
- Image optimization and lazy loading
- Bundle size analysis and code splitting

### Accessibility Testing
- Screen reader compatibility
- Keyboard navigation support
- Color contrast compliance
- Focus management and ARIA labels

## Visual Design System

### Color Palette
- **Primary**: #1a365d (Deep Blue)
- **Secondary**: #38a169 (Success Green)
- **Accent**: #ed8936 (Warning Orange)
- **Background**: #f7fafc (Light Gray)
- **Text**: #2d3748 (Dark Gray)
- **Borders**: #e2e8f0 (Light Border)

### Typography
- **Primary Font**: Inter (Google Fonts)
- **Headings**: 700 weight, varied sizes
- **Body Text**: 400 weight, 16px base size
- **Captions**: 400 weight, 14px size

### Spacing System
- **Base Unit**: 8px
- **Component Padding**: 16px, 24px, 32px
- **Section Margins**: 48px, 64px, 80px
- **Grid Gaps**: 16px, 24px, 32px

### Animation Guidelines
- **Transition Duration**: 0.2s for micro-interactions, 0.3s for larger changes
- **Easing**: ease-out for entrances, ease-in for exits
- **Hover Effects**: Subtle scale (1.02), shadow, or color changes
- **Loading States**: Smooth skeleton animations

This design provides a comprehensive foundation for building a modern, engaging home page that effectively showcases FanBet247's betting platform while maintaining excellent user experience and performance standards.