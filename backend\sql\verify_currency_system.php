<?php
/**
 * FanBet247 Currency System Verification Script
 * This script verifies that the multi-currency system is properly installed
 * 
 * Usage: php verify_currency_system.php
 * Or access via browser: http://your-domain/backend/sql/verify_currency_system.php
 */

header("Content-Type: application/json; charset=UTF-8");

// Include database connection
require_once __DIR__ . '/../includes/db_connect.php';

try {
    $conn = getDBConnection();
    $checks = [];
    $errors = [];
    $warnings = [];
    
    // Check 1: Verify currencies table exists and has data
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM currencies");
        $stmt->execute();
        $currencyCount = $stmt->fetchColumn();
        
        if ($currencyCount > 0) {
            $checks[] = "✓ Currencies table exists with {$currencyCount} currencies";
            
            // Check for required currencies
            $stmt = $conn->prepare("SELECT currency_code FROM currencies WHERE currency_code IN ('USD', 'ZAR') AND is_active = 1");
            $stmt->execute();
            $requiredCurrencies = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (in_array('USD', $requiredCurrencies) && in_array('ZAR', $requiredCurrencies)) {
                $checks[] = "✓ Required currencies (USD, ZAR) are present and active";
            } else {
                $errors[] = "✗ Missing required currencies (USD and/or ZAR)";
            }
        } else {
            $errors[] = "✗ Currencies table is empty";
        }
    } catch (PDOException $e) {
        $errors[] = "✗ Currencies table does not exist or is inaccessible";
    }
    
    // Check 2: Verify exchange_rates table exists and has data
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM exchange_rates");
        $stmt->execute();
        $rateCount = $stmt->fetchColumn();
        
        if ($rateCount > 0) {
            $checks[] = "✓ Exchange rates table exists with {$rateCount} rates";
            
            // Check for USD base rate
            $stmt = $conn->prepare("
                SELECT er.rate_to_fancoin 
                FROM exchange_rates er 
                JOIN currencies c ON er.currency_id = c.id 
                WHERE c.currency_code = 'USD'
                ORDER BY er.updated_at DESC 
                LIMIT 1
            ");
            $stmt->execute();
            $usdRate = $stmt->fetchColumn();
            
            if ($usdRate == 1.0000) {
                $checks[] = "✓ USD base rate is correctly set to 1.0000";
            } else {
                $warnings[] = "⚠ USD rate is {$usdRate}, expected 1.0000";
            }
        } else {
            $errors[] = "✗ Exchange rates table is empty";
        }
    } catch (PDOException $e) {
        $errors[] = "✗ Exchange rates table does not exist or is inaccessible";
    }
    
    // Check 3: Verify users table has preferred_currency_id column
    try {
        $stmt = $conn->prepare("SHOW COLUMNS FROM users LIKE 'preferred_currency_id'");
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $checks[] = "✓ Users table has preferred_currency_id column";
            
            // Check how many users have currency preferences set
            $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE preferred_currency_id IS NOT NULL");
            $stmt->execute();
            $usersWithCurrency = $stmt->fetchColumn();
            
            $stmt = $conn->prepare("SELECT COUNT(*) FROM users");
            $stmt->execute();
            $totalUsers = $stmt->fetchColumn();
            
            if ($usersWithCurrency == $totalUsers) {
                $checks[] = "✓ All {$totalUsers} users have currency preferences set";
            } else {
                $warnings[] = "⚠ {$usersWithCurrency} of {$totalUsers} users have currency preferences set";
            }
        } else {
            $errors[] = "✗ Users table missing preferred_currency_id column";
        }
    } catch (PDOException $e) {
        $errors[] = "✗ Cannot verify users table structure";
    }
    
    // Check 4: Verify foreign key relationships
    try {
        $stmt = $conn->prepare("
            SELECT 
                CONSTRAINT_NAME,
                TABLE_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
            AND (
                (TABLE_NAME = 'exchange_rates' AND COLUMN_NAME = 'currency_id')
                OR (TABLE_NAME = 'users' AND COLUMN_NAME = 'preferred_currency_id')
                OR (TABLE_NAME = 'exchange_rates' AND COLUMN_NAME = 'updated_by_admin_id')
            )
        ");
        $stmt->execute();
        $foreignKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $expectedKeys = [
            'exchange_rates.currency_id -> currencies.id',
            'exchange_rates.updated_by_admin_id -> admins.admin_id',
            'users.preferred_currency_id -> currencies.id'
        ];
        
        $foundKeys = [];
        foreach ($foreignKeys as $key) {
            $foundKeys[] = $key['TABLE_NAME'] . '.' . $key['COLUMN_NAME'] . ' -> ' . $key['REFERENCED_TABLE_NAME'] . '.' . $key['REFERENCED_COLUMN_NAME'];
        }
        
        foreach ($expectedKeys as $expectedKey) {
            if (in_array($expectedKey, $foundKeys)) {
                $checks[] = "✓ Foreign key constraint exists: {$expectedKey}";
            } else {
                $warnings[] = "⚠ Missing foreign key constraint: {$expectedKey}";
            }
        }
    } catch (PDOException $e) {
        $warnings[] = "⚠ Cannot verify foreign key constraints";
    }
    
    // Check 5: Test currency conversion functionality
    try {
        $stmt = $conn->prepare("
            SELECT 
                c.currency_code,
                c.currency_symbol,
                er.rate_to_fancoin,
                (100 * er.rate_to_fancoin) as converted_amount
            FROM currencies c
            JOIN exchange_rates er ON c.id = er.currency_id
            WHERE c.is_active = 1
            ORDER BY c.currency_code
        ");
        $stmt->execute();
        $conversions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($conversions) > 0) {
            $checks[] = "✓ Currency conversion test successful";
            $conversionExamples = [];
            foreach ($conversions as $conv) {
                $conversionExamples[] = "100 FanCoin = {$conv['currency_symbol']}" . number_format($conv['converted_amount'], 2) . " {$conv['currency_code']}";
            }
        } else {
            $errors[] = "✗ Currency conversion test failed - no active currencies with rates";
        }
    } catch (PDOException $e) {
        $errors[] = "✗ Currency conversion test failed: " . $e->getMessage();
    }
    
    // Determine overall status
    $overallStatus = 'success';
    if (!empty($errors)) {
        $overallStatus = 'error';
    } elseif (!empty($warnings)) {
        $overallStatus = 'warning';
    }
    
    // Prepare response
    $response = [
        'success' => $overallStatus !== 'error',
        'status' => $overallStatus,
        'message' => $overallStatus === 'success' ? 'Currency system verification passed!' : 
                    ($overallStatus === 'warning' ? 'Currency system verification passed with warnings' : 'Currency system verification failed'),
        'checks_passed' => $checks,
        'warnings' => $warnings,
        'errors' => $errors,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    // Add conversion examples if available
    if (isset($conversionExamples)) {
        $response['conversion_examples'] = $conversionExamples;
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'status' => 'error',
        'message' => 'Verification failed: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}
?>
