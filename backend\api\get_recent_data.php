<?php
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

require_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();

    // Get limits from query parameters
    $betsLimit = isset($_GET['bets_limit']) ? (int)$_GET['bets_limit'] : 5;
    $usersLimit = isset($_GET['users_limit']) ? (int)$_GET['users_limit'] : 5;
    $challengesLimit = isset($_GET['challenges_limit']) ? (int)$_GET['challenges_limit'] : 5;

    // Get recent bets with comprehensive data
    $betsQuery = "SELECT
        b.*,
        u1.username as user1_name,
        u2.username as user2_name,
        c.team_a,
        c.team_b,
        'Premier League' as league_name,
        c.logo1 as team_a_logo,
        c.logo2 as team_b_logo
    FROM bets b
    LEFT JOIN users u1 ON b.user1_id = u1.user_id
    LEFT JOIN users u2 ON b.user2_id = u2.user_id
    LEFT JOIN challenges c ON b.challenge_id = c.challenge_id
    ORDER BY b.created_at DESC
    LIMIT :bets_limit";
    $betsStmt = $conn->prepare($betsQuery);
    $betsStmt->bindParam(':bets_limit', $betsLimit, PDO::PARAM_INT);
    $betsStmt->execute();
    $recentBets = $betsStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get recent users with additional stats
    $usersQuery = "SELECT
        u.user_id,
        u.username,
        u.created_at,
        u.balance,
        COUNT(b.bet_id) as total_bets,
        SUM(CASE WHEN b.bet_status = 'completed' AND b.winner_id = u.user_id THEN 1 ELSE 0 END) as wins
    FROM users u
    LEFT JOIN bets b ON (u.user_id = b.user1_id OR u.user_id = b.user2_id)
    GROUP BY u.user_id
    ORDER BY u.created_at DESC
    LIMIT :users_limit";
    $usersStmt = $conn->prepare($usersQuery);
    $usersStmt->bindParam(':users_limit', $usersLimit, PDO::PARAM_INT);
    $usersStmt->execute();
    $recentUsers = $usersStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get recent challenges with enhanced data
    $challengesQuery = "SELECT
        c.*,
        u1.username as creator_name,
        c.logo1 as team_a_logo,
        c.logo2 as team_b_logo,
        COUNT(DISTINCT b.bet_id) as participant_count,
        SUM(b.amount_user1 + COALESCE(b.amount_user2, 0)) as total_prize_pool,
        TIMESTAMPDIFF(SECOND, NOW(), c.end_time) as seconds_remaining,
        CASE
            WHEN NOW() >= c.end_time THEN 'expired'
            WHEN TIMESTAMPDIFF(HOUR, NOW(), c.end_time) < 1 THEN 'urgent'
            ELSE 'active'
        END as status_display
    FROM challenges c
    LEFT JOIN bets b ON c.challenge_id = b.challenge_id
    LEFT JOIN users u1 ON b.user1_id = u1.user_id
    WHERE c.status = 'Open'
    GROUP BY c.challenge_id
    ORDER BY c.created_at DESC
    LIMIT :challenges_limit";
    $challengesStmt = $conn->prepare($challengesQuery);
    $challengesStmt->bindParam(':challenges_limit', $challengesLimit, PDO::PARAM_INT);
    $challengesStmt->execute();
    $recentChallenges = $challengesStmt->fetchAll(PDO::FETCH_ASSOC);

    // Format the data
    foreach ($recentBets as &$bet) {
        $bet['amount_user1'] = floatval($bet['amount_user1']);
        $bet['amount_user2'] = floatval($bet['amount_user2']);
        $bet['odds_user1'] = floatval($bet['odds_user1']);
        $bet['odds_user2'] = floatval($bet['odds_user2']);
    }

    foreach ($recentUsers as &$user) {
        $user['balance'] = floatval($user['balance']);
        $user['total_bets'] = (int)$user['total_bets'];
        $user['wins'] = (int)$user['wins'];
        $user['win_rate'] = $user['total_bets'] > 0 ? round(($user['wins'] / $user['total_bets']) * 100, 1) : 0;
    }

    foreach ($recentChallenges as &$challenge) {
        $challenge['participant_count'] = (int)$challenge['participant_count'];
        $challenge['total_prize_pool'] = floatval($challenge['total_prize_pool']);
        $challenge['seconds_remaining'] = (int)$challenge['seconds_remaining'];

        // Format time remaining
        $hours = floor($challenge['seconds_remaining'] / 3600);
        $minutes = floor(($challenge['seconds_remaining'] % 3600) / 60);
        if ($hours > 0) {
            $challenge['time_display'] = $hours . 'h ' . $minutes . 'm';
        } else if ($minutes > 0) {
            $challenge['time_display'] = $minutes . 'm';
        } else {
            $challenge['time_display'] = $challenge['seconds_remaining'] . 's';
        }
    }

    $response = [
        'status' => 'success',
        'data' => [
            'recentBets' => $recentBets,
            'recentUsers' => $recentUsers,
            'recentChallenges' => $recentChallenges
        ],
        'meta' => [
            'bets_count' => count($recentBets),
            'users_count' => count($recentUsers),
            'challenges_count' => count($recentChallenges),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];

    echo json_encode($response);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
