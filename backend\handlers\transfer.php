<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    $conn->beginTransaction();

    // Get and validate input - handle both POST and JSON data
    $input = json_decode(file_get_contents('php://input'), true);
    if ($input) {
        // JSON data
        $fromUserId = $input['from_user_id'] ?? null;
        $toUserId = $input['to_user_id'] ?? null;
        $amount = floatval($input['amount'] ?? 0);
    } else {
        // Form data
        $fromUserId = $_POST['from_user_id'] ?? null;
        $toUserId = $_POST['to_user_id'] ?? null;
        $amount = floatval($_POST['amount'] ?? 0);
    }

    if (!$fromUserId || !$toUserId || $amount <= 0) {
        throw new Exception('Invalid input parameters');
    }

    // Check if users exist and are friends
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM user_friends 
        WHERE ((user_id = :from_id AND friend_id = :to_id) OR 
               (user_id = :to_id AND friend_id = :from_id))
        AND status = 'accepted'
    ");
    $stmt->bindParam(':from_id', $fromUserId);
    $stmt->bindParam(':to_id', $toUserId);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result['count'] == 0) {
        throw new Exception('Invalid friend relationship');
    }

    // Check sender's balance
    $stmt = $conn->prepare("SELECT balance FROM users WHERE user_id = :user_id FOR UPDATE");
    $stmt->bindParam(':user_id', $fromUserId);
    $stmt->execute();
    $sender = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$sender || $sender['balance'] < $amount) {
        throw new Exception('Insufficient balance');
    }

    // Update sender's balance
    $stmt = $conn->prepare("
        UPDATE users 
        SET balance = balance - :amount 
        WHERE user_id = :user_id
    ");
    $stmt->bindParam(':amount', $amount);
    $stmt->bindParam(':user_id', $fromUserId);
    $stmt->execute();

    // Update recipient's balance
    $stmt = $conn->prepare("
        UPDATE users 
        SET balance = balance + :amount 
        WHERE user_id = :user_id
    ");
    $stmt->bindParam(':amount', $amount);
    $stmt->bindParam(':user_id', $toUserId);
    $stmt->execute();

    // Record the transactions
    $stmt = $conn->prepare("
        INSERT INTO transactions (
            user_id, 
            type, 
            amount, 
            status,
            description,
            related_user_id
        ) VALUES 
        (:from_id, 'transfer_sent', :negative_amount, 'completed', :desc1, :to_id),
        (:to_id, 'transfer_received', :positive_amount, 'completed', :desc2, :from_id)
    ");

    $negativeAmount = -$amount;
    $description = "Transfer between users";
    
    $stmt->bindParam(':from_id', $fromUserId);
    $stmt->bindParam(':to_id', $toUserId);
    $stmt->bindParam(':negative_amount', $negativeAmount);
    $stmt->bindParam(':positive_amount', $amount);
    $stmt->bindParam(':desc1', $description);
    $stmt->bindParam(':desc2', $description);
    $stmt->execute();

    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Transfer completed successfully'
    ]);

} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    error_log("Error in transfer.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 