/**
 * C<PERSON>rency Amount Component Styles
 * 
 * Provides consistent styling for all currency display components
 * with support for different sizes, states, and themes.
 */

/* Base currency amount styles */
.currency-amount {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 500;
    line-height: 1.2;
    color: var(--text-primary, #1a1a1a);
}

/* Size variants */
.currency-amount.small {
    font-size: 0.875rem;
    gap: 0.125rem;
}

.currency-amount.medium {
    font-size: 1rem;
    gap: 0.25rem;
}

.currency-amount.large {
    font-size: 1.25rem;
    gap: 0.375rem;
    font-weight: 600;
}

/* Primary amount display */
.currency-amount .primary-amount {
    display: inline-flex;
    align-items: center;
    gap: 0.125rem;
    font-weight: inherit;
}

.currency-amount .currency-symbol {
    color: var(--text-secondary, #666);
    font-weight: 500;
}

.currency-amount .amount-value {
    font-weight: 600;
    color: var(--text-primary, #1a1a1a);
}

.currency-amount .currency-code {
    font-size: 0.85em;
    color: var(--text-secondary, #666);
    font-weight: 400;
    margin-left: 0.125rem;
}

/* Secondary amount (FanCoin) display */
.currency-amount .secondary-amount {
    margin-left: 0.375rem;
    font-size: 0.85em;
    color: var(--text-muted, #888);
    font-weight: 400;
}

.currency-amount .fancoin-amount {
    font-style: italic;
}

/* FanCoin only display */
.currency-amount.fancoin-only {
    color: var(--primary-color, #007bff);
}

.currency-amount.fancoin-only .currency-label {
    font-size: 0.85em;
    color: var(--text-secondary, #666);
    margin-left: 0.25rem;
}

/* Loading state */
.currency-amount.loading {
    opacity: 0.6;
    pointer-events: none;
}

.currency-amount .loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    border-radius: 4px;
    height: 1em;
    width: 4em;
    display: inline-block;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Error state */
.currency-amount.error {
    color: var(--error-color, #dc3545);
}

.currency-amount .error-text {
    font-style: italic;
    font-size: 0.9em;
}

/* Specialized component styles */

/* Currency Balance */
.currency-balance {
    font-weight: 700;
    color: var(--success-color, #28a745);
}

.currency-balance.large {
    font-size: 1.5rem;
}

/* Currency Bet Amount */
.currency-bet-amount {
    background: var(--bg-light, #f8f9fa);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    border: 1px solid var(--border-color, #e9ecef);
}

/* Currency Compact */
.currency-compact {
    font-size: 0.8rem;
    gap: 0.125rem;
}

.currency-compact .secondary-amount {
    display: none; /* Hide FanCoin amount in compact view */
}

/* Currency Input */
.currency-input-wrapper {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.currency-input-wrapper .input-group {
    display: flex;
    align-items: center;
    position: relative;
}

.currency-input-wrapper .currency-input {
    flex: 1;
    padding: 0.75rem 1rem;
    padding-right: 5rem; /* Space for suffix */
    border: 2px solid var(--border-color, #e9ecef);
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    background: var(--bg-white, #ffffff);
    color: var(--text-primary, #1a1a1a);
    transition: border-color 0.2s ease;
}

.currency-input-wrapper .currency-input:focus {
    outline: none;
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.currency-input-wrapper .input-suffix {
    position: absolute;
    right: 1rem;
    color: var(--text-secondary, #666);
    font-weight: 500;
    font-size: 0.9rem;
    pointer-events: none;
}

.currency-input-wrapper .conversion-preview {
    padding: 0.5rem 0.75rem;
    background: var(--bg-light, #f8f9fa);
    border: 1px solid var(--border-color, #e9ecef);
    border-radius: 6px;
    font-size: 0.9rem;
    color: var(--text-secondary, #666);
}

.currency-input-wrapper .conversion-text {
    font-weight: 500;
}

/* Currency Comparison */
.currency-comparison {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--bg-light, #f8f9fa);
    border-radius: 8px;
    border: 1px solid var(--border-color, #e9ecef);
}

.currency-comparison .amount-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.currency-comparison .label {
    font-weight: 500;
    color: var(--text-secondary, #666);
    font-size: 0.9rem;
}

.currency-comparison .change.positive {
    color: var(--success-color, #28a745);
}

.currency-comparison .change.negative {
    color: var(--error-color, #dc3545);
}

.currency-comparison .change-indicator {
    font-weight: 600;
}

/* Currency Tooltip */
.currency-tooltip-wrapper {
    cursor: help;
    border-bottom: 1px dotted var(--text-secondary, #666);
}

/* Responsive design */
@media (max-width: 768px) {
    .currency-amount.large {
        font-size: 1.125rem;
    }
    
    .currency-amount .secondary-amount {
        display: none; /* Hide FanCoin amount on mobile for space */
    }
    
    .currency-input-wrapper .currency-input {
        padding: 0.625rem 0.875rem;
        padding-right: 4.5rem;
    }
    
    .currency-comparison {
        padding: 0.75rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .currency-amount {
        color: var(--text-primary-dark, #ffffff);
    }
    
    .currency-amount .currency-symbol,
    .currency-amount .currency-code {
        color: var(--text-secondary-dark, #b3b3b3);
    }
    
    .currency-amount .secondary-amount {
        color: var(--text-muted-dark, #888);
    }
    
    .currency-input-wrapper .currency-input {
        background: var(--bg-dark, #2a2a2a);
        border-color: var(--border-color-dark, #404040);
        color: var(--text-primary-dark, #ffffff);
    }
    
    .currency-input-wrapper .currency-input:focus {
        border-color: var(--primary-color-dark, #4dabf7);
        box-shadow: 0 0 0 3px rgba(77, 171, 247, 0.1);
    }
    
    .currency-input-wrapper .conversion-preview,
    .currency-comparison {
        background: var(--bg-light-dark, #333);
        border-color: var(--border-color-dark, #404040);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .currency-amount {
        font-weight: 600;
    }
    
    .currency-input-wrapper .currency-input {
        border-width: 3px;
    }
    
    .currency-input-wrapper .currency-input:focus {
        box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.3);
    }
}
