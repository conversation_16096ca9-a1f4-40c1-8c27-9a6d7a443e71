<?php
/**
 * Update Admin Authentication Settings
 * Updates admin authentication configuration
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, PUT');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';
require_once '../includes/error_logger.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception("Invalid JSON input");
    }
    
    // Validate admin authentication (basic check)
    $adminId = $_GET['adminId'] ?? $input['adminId'] ?? null;
    if (!$adminId) {
        throw new Exception("Admin authentication required");
    }
    
    // Verify admin exists
    $stmt = $conn->prepare("SELECT admin_id, role FROM admins WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Invalid admin credentials");
    }
    
    // Check if admin_auth_settings table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'admin_auth_settings'");
    if ($stmt->rowCount() === 0) {
        throw new Exception("Admin authentication tables not initialized. Please run setup first.");
    }
    
    // Define allowed settings and their validation rules
    $allowedSettings = [
        'admin_auth_method' => ['password_only', 'otp', '2fa'],
        'admin_otp_enabled' => ['true', 'false'],
        'admin_2fa_enabled' => ['true', 'false'],
        'admin_otp_expiry_time' => 'numeric',
        'admin_max_otp_attempts' => 'numeric',
        'admin_max_login_attempts' => 'numeric',
        'admin_lockout_time' => 'numeric',
        'admin_require_2fa_for' => 'string',
        'admin_backup_codes_count' => 'numeric',
        'admin_session_timeout' => 'numeric'
    ];
    
    $conn->beginTransaction();
    
    $updatedSettings = [];
    $errors = [];
    
    foreach ($input as $settingName => $settingValue) {
        if (!array_key_exists($settingName, $allowedSettings)) {
            $errors[] = "Unknown setting: $settingName";
            continue;
        }
        
        // Validate setting value
        $validation = $allowedSettings[$settingName];
        
        if (is_array($validation)) {
            // Enum validation
            if (!in_array($settingValue, $validation)) {
                $errors[] = "Invalid value for $settingName. Allowed: " . implode(', ', $validation);
                continue;
            }
        } elseif ($validation === 'numeric') {
            // Numeric validation
            if (!is_numeric($settingValue) || $settingValue < 0) {
                $errors[] = "Invalid numeric value for $settingName";
                continue;
            }
        } elseif ($validation === 'string') {
            // String validation
            if (!is_string($settingValue)) {
                $errors[] = "Invalid string value for $settingName";
                continue;
            }
        }
        
        // Additional specific validations
        if ($settingName === 'admin_otp_expiry_time' && $settingValue < 60) {
            $errors[] = "OTP expiry time must be at least 60 seconds";
            continue;
        }
        
        if ($settingName === 'admin_max_otp_attempts' && ($settingValue < 1 || $settingValue > 10)) {
            $errors[] = "Max OTP attempts must be between 1 and 10";
            continue;
        }
        
        if ($settingName === 'admin_lockout_time' && $settingValue < 300) {
            $errors[] = "Lockout time must be at least 300 seconds (5 minutes)";
            continue;
        }
        
        // Update or insert setting
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_settings (setting_name, setting_value, updated_at) 
            VALUES (?, ?, NOW())
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value), 
            updated_at = NOW()
        ");
        
        if ($stmt->execute([$settingName, $settingValue])) {
            $updatedSettings[$settingName] = $settingValue;
        } else {
            $errors[] = "Failed to update $settingName";
        }
    }
    
    if (!empty($errors)) {
        $conn->rollBack();
        throw new Exception("Validation errors: " . implode(', ', $errors));
    }
    
    // Log the admin action
    try {
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, 'password', 'settings_updated', ?, ?, ?)
        ");
        $stmt->execute([
            $adminId,
            json_encode(['updated_settings' => array_keys($updatedSettings)]),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (PDOException $e) {
        // Log error but don't fail the main operation
        error_log("Failed to log admin action: " . $e->getMessage());
    }
    
    $conn->commit();
    
    // Check if SMTP is configured (needed for OTP)
    $smtpWarning = null;
    if (in_array('admin_otp_enabled', array_keys($updatedSettings)) && $updatedSettings['admin_otp_enabled'] === 'true') {
        try {
            $stmt = $conn->query("SELECT is_active FROM smtp_settings WHERE is_active = 1 LIMIT 1");
            if ($stmt->rowCount() === 0) {
                $smtpWarning = "OTP enabled but SMTP is not configured. Please configure SMTP settings first.";
            }
        } catch (PDOException $e) {
            $smtpWarning = "Cannot verify SMTP configuration. Please ensure SMTP is configured for OTP functionality.";
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Admin authentication settings updated successfully',
        'updated_settings' => $updatedSettings,
        'smtp_warning' => $smtpWarning,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to update admin authentication settings',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
