<?php
// Fix SMTP configuration for OTP email functionality
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Check current table structure
    $stmt = $conn->prepare("DESCRIBE smtp_settings");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📊 Current smtp_settings columns:\n";
    foreach ($columns as $column) {
        echo "   - $column\n";
    }
    
    // Add missing columns if they don't exist
    $requiredColumns = [
        'smtp_host' => 'VARCHAR(255) DEFAULT NULL',
        'smtp_port' => 'INT DEFAULT 587',
        'smtp_username' => 'VARCHAR(255) DEFAULT NULL',
        'smtp_password' => 'VARCHAR(255) DEFAULT NULL',
        'smtp_encryption' => 'VARCHAR(10) DEFAULT "tls"'
    ];
    
    foreach ($requiredColumns as $columnName => $columnDef) {
        if (!in_array($columnName, $columns)) {
            $alterSql = "ALTER TABLE smtp_settings ADD COLUMN $columnName $columnDef";
            $conn->exec($alterSql);
            echo "✅ Added column: $columnName\n";
        }
    }
    
    // Update the existing record with proper SMTP settings
    $stmt = $conn->prepare("
        UPDATE smtp_settings 
        SET 
            smtp_host = 'smtp.gmail.com',
            smtp_port = 587,
            smtp_username = '<EMAIL>',
            smtp_password = 'demo-password-change-me',
            smtp_encryption = 'tls'
        WHERE is_active = 1
    ");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "✅ Updated SMTP settings\n";
    }
    
    // Display updated settings
    $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $smtp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($smtp) {
        echo "\n📊 Updated SMTP Configuration:\n";
        echo "   Host: " . ($smtp['smtp_host'] ?? 'Not set') . "\n";
        echo "   Port: " . ($smtp['smtp_port'] ?? 'Not set') . "\n";
        echo "   Username: " . ($smtp['smtp_username'] ?? 'Not set') . "\n";
        echo "   Password: " . (isset($smtp['smtp_password']) ? str_repeat('*', strlen($smtp['smtp_password'])) : 'Not set') . "\n";
        echo "   Encryption: " . ($smtp['smtp_encryption'] ?? 'Not set') . "\n";
        echo "   From Email: " . ($smtp['from_email'] ?? 'Not set') . "\n";
        echo "   From Name: " . ($smtp['from_name'] ?? 'Not set') . "\n";
        echo "   Status: " . ($smtp['is_active'] ? 'Active' : 'Inactive') . "\n";
    }
    
    echo "\n⚠️  IMPORTANT NOTES:\n";
    echo "1. The SMTP password is set to 'demo-password-change-me'\n";
    echo "2. You need to configure with real email credentials for OTP to work\n";
    echo "3. For Gmail, you need to use an 'App Password' not your regular password\n";
    echo "4. For testing, you can use services like Mailtrap or MailHog\n";
    
    echo "\n🧪 For testing OTP without real email:\n";
    echo "1. The OTP codes are stored in the user_otp table\n";
    echo "2. You can manually check the database for generated OTP codes\n";
    echo "3. Or implement a test mode that displays OTP codes in the browser\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
