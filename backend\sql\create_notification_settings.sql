-- Create notification_settings table
CREATE TABLE IF NOT EXISTS notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_name VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default notification settings
INSERT INTO notification_settings (setting_name, setting_value, description) VALUES
('email_notifications_enabled', 'true', 'Master switch for all email notifications'),
('bet_notifications', 'true', 'Send notifications for bet activities'),
('challenge_notifications', 'true', 'Send notifications for challenge activities'),
('league_notifications', 'true', 'Send notifications for league activities'),
('admin_notifications', 'true', 'Send notifications for admin activities'),
('notification_frequency', 'immediate', 'Frequency of notifications (immediate, hourly, daily)')
ON <PERSON>UP<PERSON><PERSON>ATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    description = VALUES(description);
