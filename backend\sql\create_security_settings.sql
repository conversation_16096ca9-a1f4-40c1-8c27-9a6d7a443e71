-- Create Security Settings Table
CREATE TABLE IF NOT EXISTS security_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_name VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default security settings
INSERT INTO security_settings (setting_name, setting_value, description) VALUES
('enable_2fa', 'false', 'Enable two-factor authentication for users'),
('allowed_auth_methods', 'email_otp,google_auth', 'Allowed authentication methods (comma-separated)'),
('otp_expiry_time', '300', 'OTP expiry time in seconds'),
('max_otp_attempts', '3', 'Maximum number of OTP verification attempts before lockout'),
('lockout_time', '1800', 'Account lockout time in seconds after max failed attempts'),
('password_min_length', '8', 'Minimum password length requirement'),
('require_special_chars', 'true', 'Require special characters in passwords'),
('session_timeout', '3600', 'Session timeout in seconds'),
('max_login_attempts', '5', 'Maximum login attempts before account lockout')
ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    description = VALUES(description);
