<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!isset($_GET['userId'])) {
        throw new Exception('User ID is required');
    }
    
    $userId = $_GET['userId'];
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $offset = ($page - 1) * $limit;
    
    // Get total count for pagination
    $countQuery = "SELECT COUNT(*) as total FROM bets b WHERE b.user2_id = :userId";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->execute(['userId' => $userId]);
    $totalCount = $countStmt->fetch()['total'];
    
    // Main query with pagination
    $query = "SELECT b.*, 
            u1.username as username, 
            u2.username as username2,
            c.team_a, c.team_b, 
            c.odds_team_a, c.odds_team_b,
            c.match_date,
            c.start_time,
            c.end_time,
            c.challenge_date,
            c.match_type,
            b.potential_return_win_user2,
            b.potential_return_draw_user2,
            b.potential_return_loss_user2
            FROM bets b
            JOIN users u1 ON b.user1_id = u1.user_id
            LEFT JOIN users u2 ON b.user2_id = u2.user_id
            JOIN challenges c ON b.challenge_id = c.challenge_id
            WHERE b.user2_id = :userId
            ORDER BY b.created_at DESC
            LIMIT :offset, :limit";
            
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':userId', $userId, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    $bets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Update status for display
    foreach ($bets as &$bet) {
        if ($bet['bet_status'] === 'open' && $bet['user2_id'] !== null) {
            $bet['bet_status'] = 'joined';
        }
        
        // Format match_date for display
        if (isset($bet['match_date'])) {
            $bet['match_date'] = date('Y-m-d H:i:s', strtotime($bet['match_date']));
        }
    }

    echo json_encode([
        'success' => true,
        'bets' => $bets,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => ceil($totalCount / $limit),
            'totalItems' => $totalCount,
            'itemsPerPage' => $limit
        ]
    ]);
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
