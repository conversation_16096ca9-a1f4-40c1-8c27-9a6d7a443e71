<?php
// Fix SMTP settings table - remove incorrect columns and ensure correct data
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    echo "🔧 Fixing SMTP settings table structure...\n\n";
    
    // Check current table structure
    $stmt = $conn->prepare("DESCRIBE smtp_settings");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📊 Current columns:\n";
    foreach ($columns as $column) {
        echo "   - $column\n";
    }
    echo "\n";
    
    // Remove incorrect columns with smtp_ prefix if they exist
    $incorrectColumns = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption'];
    
    foreach ($incorrectColumns as $column) {
        if (in_array($column, $columns)) {
            try {
                // First, copy data from incorrect column to correct column if correct column is empty
                $correctColumn = str_replace('smtp_', '', $column);
                
                if (in_array($correctColumn, $columns)) {
                    $stmt = $conn->prepare("UPDATE smtp_settings SET $correctColumn = $column WHERE ($correctColumn IS NULL OR $correctColumn = '') AND $column IS NOT NULL AND $column != ''");
                    $stmt->execute();
                    echo "✅ Copied data from $column to $correctColumn\n";
                }
                
                // Now drop the incorrect column
                $conn->exec("ALTER TABLE smtp_settings DROP COLUMN $column");
                echo "✅ Removed incorrect column: $column\n";
            } catch (Exception $e) {
                echo "⚠️  Could not remove column $column: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Ensure we have active SMTP settings with proper values
    $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $activeSettings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$activeSettings) {
        echo "\n⚠️  No active SMTP settings found. Creating default settings...\n";
        
        // Insert or update with working test settings
        $stmt = $conn->prepare("
            INSERT INTO smtp_settings (host, port, username, password, encryption, from_email, from_name, is_active) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            host = VALUES(host),
            port = VALUES(port),
            username = VALUES(username),
            password = VALUES(password),
            encryption = VALUES(encryption),
            from_email = VALUES(from_email),
            from_name = VALUES(from_name),
            is_active = VALUES(is_active)
        ");
        
        $stmt->execute([
            'smtp.gmail.com',
            587,
            '<EMAIL>',
            'demo-password-change-me',
            'tls',
            '<EMAIL>',
            'FanBet247',
            1
        ]);
        
        echo "✅ Created default SMTP settings\n";
    } else {
        // Update existing settings to ensure they have proper values
        if (empty($activeSettings['host']) || empty($activeSettings['username'])) {
            $stmt = $conn->prepare("
                UPDATE smtp_settings 
                SET 
                    host = COALESCE(NULLIF(host, ''), 'smtp.gmail.com'),
                    port = COALESCE(NULLIF(port, 0), 587),
                    username = COALESCE(NULLIF(username, ''), '<EMAIL>'),
                    password = COALESCE(NULLIF(password, ''), 'demo-password-change-me'),
                    encryption = COALESCE(NULLIF(encryption, ''), 'tls'),
                    from_email = COALESCE(NULLIF(from_email, ''), '<EMAIL>'),
                    from_name = COALESCE(NULLIF(from_name, ''), 'FanBet247')
                WHERE is_active = 1
            ");
            $stmt->execute();
            echo "✅ Updated existing SMTP settings with default values\n";
        }
    }
    
    // Display final settings
    $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $finalSettings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($finalSettings) {
        echo "\n📊 Final SMTP Configuration:\n";
        echo "   Host: " . ($finalSettings['host'] ?? 'Not set') . "\n";
        echo "   Port: " . ($finalSettings['port'] ?? 'Not set') . "\n";
        echo "   Username: " . ($finalSettings['username'] ?? 'Not set') . "\n";
        echo "   Password: " . (isset($finalSettings['password']) && !empty($finalSettings['password']) ? str_repeat('*', strlen($finalSettings['password'])) : 'Not set') . "\n";
        echo "   Encryption: " . ($finalSettings['encryption'] ?? 'Not set') . "\n";
        echo "   From Email: " . ($finalSettings['from_email'] ?? 'Not set') . "\n";
        echo "   From Name: " . ($finalSettings['from_name'] ?? 'Not set') . "\n";
        echo "   Status: " . ($finalSettings['is_active'] ? 'Active' : 'Inactive') . "\n";
    }
    
    echo "\n✅ SMTP settings table fixed successfully!\n";
    echo "\n📧 Test Mode Information:\n";
    echo "   - The user_send_otp.php handler is set to test mode\n";
    echo "   - OTP codes will be displayed in the browser instead of sent via email\n";
    echo "   - This allows testing without real SMTP configuration\n";
    echo "   - To enable real email sending, set \$testMode = false in user_send_otp.php\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
