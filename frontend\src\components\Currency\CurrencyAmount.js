import React from 'react';
import { useCurrency } from '../../contexts/CurrencyContext';
import './CurrencyAmount.css';

/**
 * CurrencyAmount Component
 * Displays FanCoin amounts in user's preferred currency with optional FanCoin display
 *
 * Props:
 * - amount: FanCoin amount to display
 * - showBoth: Whether to show both converted amount and FanCoin (default: true)
 * - showFanCoinOnly: Force display of only FanCoin amount (default: false)
 * - className: Additional CSS classes
 * - size: Display size ('small', 'medium', 'large') (default: 'medium')
 * - loading: Show loading state (default: false)
 */
const CurrencyAmount = ({
    amount,
    showBoth = true,
    showFanCoinOnly = false,
    className = '',
    size = 'medium',
    loading = false
}) => {
    const {
        convertToUserCurrency,
        userCurrency,
        loading: currencyLoading,
        initialized
    } = useCurrency();

    // Handle loading state
    if (loading || currencyLoading || !initialized) {
        return (
            <span className={`currency-amount loading ${size} ${className}`}>
                <span className="loading-placeholder">Loading...</span>
            </span>
        );
    }

    // Handle invalid amount
    if (amount === null || amount === undefined || isNaN(amount)) {
        return (
            <span className={`currency-amount error ${size} ${className}`}>
                <span className="error-text">Invalid amount</span>
            </span>
        );
    }

    // Force FanCoin only display
    if (showFanCoinOnly) {
        return (
            <span className={`currency-amount fancoin-only ${size} ${className}`}>
                <span className="amount-value">{Number(amount).toFixed(2)}</span>
                <span className="currency-label">FanCoin</span>
            </span>
        );
    }

    // Convert to user's preferred currency
    const conversion = convertToUserCurrency(amount);
    const isFanCoinOnly = !userCurrency || conversion.currency === 'FC';

    return (
        <span className={`currency-amount ${size} ${className}`}>
            <span className="primary-amount">
                <span className="currency-symbol">{conversion.symbol}</span>
                <span className="amount-value">
                    {conversion.amount.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })}
                </span>
                <span className="currency-code">{conversion.currency}</span>
            </span>

            {showBoth && !isFanCoinOnly && (
                <span className="secondary-amount">
                    <span className="fancoin-amount">
                        ({Number(amount).toFixed(2)} FC)
                    </span>
                </span>
            )}
        </span>
    );
};

/**
 * CurrencyBalance Component
 * Specialized component for displaying user balance with emphasis
 */
export const CurrencyBalance = ({ amount, className = '', size = 'large' }) => {
    return (
        <CurrencyAmount 
            amount={amount}
            showBoth={true}
            className={`currency-balance ${className}`}
            size={size}
        />
    );
};

/**
 * CurrencyBetAmount Component
 * Specialized component for displaying bet amounts
 */
export const CurrencyBetAmount = ({ amount, className = '', size = 'medium' }) => {
    return (
        <CurrencyAmount 
            amount={amount}
            showBoth={true}
            className={`currency-bet-amount ${className}`}
            size={size}
        />
    );
};

/**
 * CurrencyCompact Component
 * Compact display for tables and lists
 */
export const CurrencyCompact = ({ amount, className = '', showBoth = false }) => {
    return (
        <CurrencyAmount 
            amount={amount}
            showBoth={showBoth}
            className={`currency-compact ${className}`}
            size="small"
        />
    );
};

/**
 * CurrencyInput Component
 * Input field with currency conversion preview
 */
export const CurrencyInput = ({
    value,
    onChange,
    placeholder = "Enter amount",
    className = '',
    showConversion = true
}) => {
    const { convertToUserCurrency, userCurrency } = useCurrency();

    const conversion = value ? convertToUserCurrency(parseFloat(value) || 0) : null;

    return (
        <div className={`currency-input-wrapper ${className}`}>
            <div className="input-group">
                <input
                    type="number"
                    value={value}
                    onChange={onChange}
                    placeholder={placeholder}
                    className="currency-input"
                    min="0"
                    step="0.01"
                />
                <span className="input-suffix">FanCoin</span>
            </div>

            {showConversion && conversion && value && parseFloat(value) > 0 && conversion.currency !== 'FC' && (
                <div className="conversion-preview">
                    <span className="conversion-text">
                        ≈ {conversion.symbol}
                        {conversion.amount.toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        })} {conversion.currency}
                    </span>
                </div>
            )}
        </div>
    );
};

/**
 * CurrencyComparison Component
 * Shows before/after amounts for transactions
 */
export const CurrencyComparison = ({ 
    beforeAmount, 
    afterAmount, 
    type = 'transaction', // 'transaction', 'bet', 'transfer'
    className = '' 
}) => {
    const difference = afterAmount - beforeAmount;
    const isPositive = difference > 0;
    
    return (
        <div className={`currency-comparison ${type} ${className}`}>
            <div className="amount-row before">
                <span className="label">Before:</span>
                <CurrencyAmount amount={beforeAmount} showBoth={false} size="small" />
            </div>
            
            <div className={`amount-row change ${isPositive ? 'positive' : 'negative'}`}>
                <span className="label">Change:</span>
                <span className="change-indicator">
                    {isPositive ? '+' : ''}
                    <CurrencyAmount amount={difference} showBoth={false} size="small" />
                </span>
            </div>
            
            <div className="amount-row after">
                <span className="label">After:</span>
                <CurrencyAmount amount={afterAmount} showBoth={false} size="small" />
            </div>
        </div>
    );
};

/**
 * CurrencyTooltip Component
 * Shows detailed conversion information on hover
 */
export const CurrencyTooltip = ({ amount, children, className = '' }) => {
    const { convertToUserCurrency, userCurrency, exchangeRates } = useCurrency();

    if (!userCurrency) return children;

    const conversion = convertToUserCurrency(amount);
    const rate = exchangeRates.find(r => r.currency_id === userCurrency.id);

    return (
        <div className={`currency-tooltip-wrapper ${className}`} title={
            `${amount} FanCoin = ${conversion.formatted}\n` +
            `Exchange Rate: 1 FanCoin = ${userCurrency.currency_symbol}${rate?.rate_to_fancoin?.toFixed(4) || '1.0000'} ${userCurrency.currency_code}`
        }>
            {children}
        </div>
    );
};

export default CurrencyAmount;
