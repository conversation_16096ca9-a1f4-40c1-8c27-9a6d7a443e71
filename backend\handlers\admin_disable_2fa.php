<?php
/**
 * Disable Admin 2FA
 * Allows admin to disable their 2FA setup (with proper verification)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_connect.php';
require_once '../vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['admin_id'])) {
        throw new Exception("Admin ID required");
    }
    
    $adminId = $input['admin_id'];
    $currentPassword = $input['current_password'] ?? null;
    $verificationCode = $input['verification_code'] ?? null;
    $backupCode = $input['backup_code'] ?? null;
    
    // Verify admin exists
    $stmt = $conn->prepare("SELECT admin_id, username, email, password_hash FROM admins WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("Admin not found");
    }
    
    // Verify current password (required for security)
    if (!$currentPassword || !password_verify($currentPassword, $admin['password_hash'])) {
        throw new Exception("Current password is required and must be correct to disable 2FA");
    }
    
    // Get admin's 2FA setup
    $stmt = $conn->prepare("
        SELECT secret_key, backup_codes, is_enabled, setup_completed 
        FROM admin_2fa 
        WHERE admin_id = ?
    ");
    $stmt->execute([$adminId]);
    $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tfaSetup || !$tfaSetup['is_enabled'] || !$tfaSetup['setup_completed']) {
        throw new Exception("2FA is not currently enabled for this admin account");
    }
    
    // Verify 2FA code or backup code (additional security)
    $verificationPassed = false;
    $usedBackupCode = null;
    
    if ($verificationCode) {
        $google2fa = new Google2FA();
        $verificationPassed = $google2fa->verifyKey($tfaSetup['secret_key'], $verificationCode);
    } elseif ($backupCode) {
        $backupCodes = json_decode($tfaSetup['backup_codes'], true);
        if (in_array(strtoupper($backupCode), $backupCodes)) {
            $verificationPassed = true;
            $usedBackupCode = strtoupper($backupCode);
        }
    }
    
    if (!$verificationPassed) {
        // Log failed attempt
        $stmt = $conn->prepare("
            INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, '2fa', 'disable_2fa_failed', ?, ?, ?)
        ");
        $stmt->execute([
            $adminId,
            json_encode(['reason' => 'invalid_verification_code']),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        throw new Exception("Valid 2FA verification code or backup code is required to disable 2FA");
    }
    
    $conn->beginTransaction();
    
    // Disable 2FA
    $stmt = $conn->prepare("
        UPDATE admin_2fa 
        SET is_enabled = 0, setup_completed = 0, updated_at = NOW() 
        WHERE admin_id = ?
    ");
    $stmt->execute([$adminId]);
    
    // Update admin record
    $stmt = $conn->prepare("
        UPDATE admins 
        SET two_factor_enabled = 0, auth_method = 'password_only' 
        WHERE admin_id = ?
    ");
    $stmt->execute([$adminId]);
    
    // If a backup code was used, remove it from the list
    if ($usedBackupCode) {
        $backupCodes = json_decode($tfaSetup['backup_codes'], true);
        $backupCodes = array_diff($backupCodes, [$usedBackupCode]);
        $updatedBackupCodes = json_encode(array_values($backupCodes));
        
        $stmt = $conn->prepare("UPDATE admin_2fa SET backup_codes = ? WHERE admin_id = ?");
        $stmt->execute([$updatedBackupCodes, $adminId]);
    }
    
    // Clear any failed login attempts
    $stmt = $conn->prepare("DELETE FROM admin_login_attempts WHERE admin_id = ?");
    $stmt->execute([$adminId]);
    
    // Log successful 2FA disable
    $stmt = $conn->prepare("
        INSERT INTO admin_auth_logs (admin_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, '2fa', 'disable_2fa_success', ?, ?, ?)
    ");
    $stmt->execute([
        $adminId,
        json_encode(['used_backup_code' => $usedBackupCode !== null]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => '2FA has been successfully disabled for your account',
        'auth_method' => 'password_only',
        'warning' => 'Your account is now using password-only authentication. Consider enabling 2FA again for better security.',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
