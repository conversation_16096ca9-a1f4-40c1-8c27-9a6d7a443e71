<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$query = "SELECT
            c.challenge_id,
            c.team_a,
            c.team_b,
            c.odds_team_a,
            c.odds_team_b,
            c.odds_draw,
            c.team_a_goal_advantage,
            c.team_b_goal_advantage,
            c.match_date,
            c.status,
            c.start_time,
            c.end_time,
            c.match_type,
            t1.logo as team_a_logo,
            t2.logo as team_b_logo
          FROM challenges c
          LEFT JOIN teams t1 ON c.team_a = t1.name
          LEFT JOIN teams t2 ON c.team_b = t2.name
          WHERE c.status = 'Open'
          ORDER BY c.start_time ASC
          LIMIT 6";

$stmt = $conn->prepare($query);
$stmt->execute();

if ($stmt->rowCount() > 0) {
    $challenges = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode(array("success" => true, "challenges" => $challenges));
} else {
    echo json_encode(array("success" => true, "challenges" => []));
}
