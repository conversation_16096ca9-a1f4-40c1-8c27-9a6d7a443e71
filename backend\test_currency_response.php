<?php
// Test the currency API response format
$_SERVER['REQUEST_METHOD'] = 'GET';

// Capture output
ob_start();
include 'handlers/get_currencies.php';
$response = ob_get_clean();

echo "Currency API Response:\n";
echo $response . "\n\n";

// Parse and check structure
$data = json_decode($response, true);
if ($data) {
    echo "Response structure:\n";
    echo "- success: " . ($data['success'] ? 'true' : 'false') . "\n";
    echo "- status: " . $data['status'] . "\n";
    echo "- message: " . $data['message'] . "\n";
    echo "- data structure: " . print_r(array_keys($data['data']), true) . "\n";
    if (isset($data['data']['currencies'])) {
        echo "- currencies count: " . count($data['data']['currencies']) . "\n";
        if (!empty($data['data']['currencies'])) {
            echo "- first currency: " . print_r($data['data']['currencies'][0], true) . "\n";
        }
    }
} else {
    echo "Failed to parse JSON response\n";
}
?>
