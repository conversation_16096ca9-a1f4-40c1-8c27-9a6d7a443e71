<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$team1 = $_POST['team1'] ?? '';
$creditType1 = $_POST['creditType1'] ?? '';
$team2 = $_POST['team2'] ?? '';
$creditType2 = $_POST['creditType2'] ?? '';
$startDate = $_POST['startDate'] ?? '';
$endDate = $_POST['endDate'] ?? '';

try {
    // Get team IDs
    $teamQuery = "SELECT id FROM teams WHERE name IN (?, ?)";
    $teamStmt = $conn->prepare($teamQuery);
    $teamStmt->execute([$team1, $team2]);
    $teamIds = $teamStmt->fetchAll(PDO::FETCH_COLUMN);

    if (count($teamIds) !== 2) {
        echo json_encode(['success' => false, 'message' => 'Teams not found']);
        exit;
    }

    $conn->beginTransaction();

    // Process credits for both teams
    $processTeamCredits = function($teamId, $creditType) use ($conn, $startDate, $endDate) {
        $sql = "SELECT b.*, 
                u.user_id, u.username, u.balance
                FROM bets b
                JOIN users u ON (b.user1_id = u.user_id OR b.user2_id = u.user_id)
                JOIN challenges c ON b.challenge_id = c.challenge_id
                WHERE b.bet_status = 'joined' 
                AND b.outcome = 'pending'
                AND c.status = 'Closed'
                AND ((b.team1_id = ? AND b.user1_id = u.user_id) 
                    OR (b.team2_id = ? AND b.user2_id = u.user_id))";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$teamId, $teamId, $startDate, $endDate, $startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    };

    $creditsProcessed = 0;
    $usersUpdated = [];

    // Process first team
    foreach ($processTeamCredits($teamIds[0], $creditType1) as $bet) {
        $creditAmount = processCredit($bet, $teamIds[0], $creditType1);
        if ($creditAmount > 0) {
            updateUserAndBet($conn, $bet, $creditAmount, $creditType1);
            $creditsProcessed++;
            $usersUpdated[] = $bet['username'];
        }
    }

    // Process second team
    foreach ($processTeamCredits($teamIds[1], $creditType2) as $bet) {
        $creditAmount = processCredit($bet, $teamIds[1], $creditType2);
        if ($creditAmount > 0) {
            updateUserAndBet($conn, $bet, $creditAmount, $creditType2);
            $creditsProcessed++;
            $usersUpdated[] = $bet['username'];
        }
    }

    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => "Credited {$creditsProcessed} users: " . implode(', ', array_unique($usersUpdated)),
        'credits_processed' => $creditsProcessed
    ]);

} catch (Exception $e) {
    $conn->rollBack();
    echo json_encode([
        'success' => false,
        'message' => 'Error processing credits: ' . $e->getMessage()
    ]);
}

function processCredit($bet, $teamId, $creditType) {
    if ($bet['team1_id'] == $teamId && $bet['user1_id'] == $bet['user_id']) {
        switch($creditType) {
            case 'win': return $bet['potential_return_win_user1'];
            case 'draw': return $bet['potential_return_draw_user1'];
            case 'loss': return $bet['potential_return_loss_user1'];
        }
    } elseif ($bet['team2_id'] == $teamId && $bet['user2_id'] == $bet['user_id']) {
        switch($creditType) {
            case 'win': return $bet['potential_return_win_user2'];
            case 'draw': return $bet['potential_return_draw_user2'];
            case 'loss': return $bet['potential_return_loss_user2'];
        }
    }
    return 0;
}

function updateUserAndBet($conn, $bet, $creditAmount, $creditType) {
    // Update user balance
    $updateBalance = "UPDATE users SET balance = balance + ? WHERE user_id = ?";
    $balanceStmt = $conn->prepare($updateBalance);
    $balanceStmt->execute([$creditAmount, $bet['user_id']]);

    // Record transaction
    $transactionSql = "INSERT INTO transactions (user_id, amount, type, status, related_bet_id) 
                     VALUES (?, ?, ?, 'completed', ?)";
    $transStmt = $conn->prepare($transactionSql);
    $transStmt->execute([$bet['user_id'], $creditAmount, $creditType, $bet['bet_id']]);

    // Update bet status
    $updateBet = "UPDATE bets SET bet_status = 'completed', outcome = ? WHERE bet_id = ?";
    $betStmt = $conn->prepare($updateBet);
    $betStmt->execute([$creditType, $bet['bet_id']]);
}
