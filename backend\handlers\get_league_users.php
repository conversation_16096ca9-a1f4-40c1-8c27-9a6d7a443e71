<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'db_connect.php';
require_once 'auth_middleware.php';

// Check if the request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['status' => 405, 'message' => 'Method not allowed']);
    exit;
}

// Get league ID from query parameters
if (!isset($_GET['league_id'])) {
    http_response_code(400);
    echo json_encode(['status' => 400, 'message' => 'League ID is required']);
    exit;
}

$leagueId = $_GET['league_id'];

try {
    // Fetch users and their stats for the specified league
    $stmt = $pdo->prepare("
        SELECT 
            u.user_id,
            u.username,
            u.balance,
            lm.current_points as points,
            lm.wins,
            lm.draws,
            lm.losses,
            lm.current_streak,
            lm.highest_streak,
            lm.total_bets,
            lm.status as membership_status,
            lm.deposit_amount
        FROM league_memberships lm
        JOIN users u ON lm.user_id = u.user_id
        WHERE lm.league_id = ? AND lm.status = 'active'
        ORDER BY lm.current_points DESC, lm.wins DESC, lm.current_streak DESC
    ");

    $stmt->execute([$leagueId]);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format the response
    foreach ($users as &$user) {
        // Convert numeric values
        $user['points'] = (int)$user['points'];
        $user['wins'] = (int)$user['wins'];
        $user['draws'] = (int)$user['draws'];
        $user['losses'] = (int)$user['losses'];
        $user['current_streak'] = (int)$user['current_streak'];
        $user['highest_streak'] = (int)$user['highest_streak'];
        $user['total_bets'] = (int)$user['total_bets'];
        $user['balance'] = (float)$user['balance'];
        $user['deposit_amount'] = (float)$user['deposit_amount'];
    }

    echo json_encode([
        'status' => 200,
        'message' => 'League users retrieved successfully',
        'data' => $users
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 500,
        'message' => 'Failed to fetch league users: ' . $e->getMessage()
    ]);
}
?> 