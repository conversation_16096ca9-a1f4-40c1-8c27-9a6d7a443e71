<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

// Simple admin authentication check
function getAdminId() {
    $headers = getallheaders();
    if (isset($headers['Admin-ID'])) {
        return (int)$headers['Admin-ID'];
    }
    
    $input = json_decode(file_get_contents("php://input"), true);
    if (isset($input['admin_id'])) {
        return (int)$input['admin_id'];
    }
    
    return null;
}

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            // Get all currencies with their exchange rates (admin view)
            try {
                $stmt = $conn->prepare("
                    SELECT 
                        c.id,
                        c.currency_code,
                        c.currency_name,
                        c.currency_symbol,
                        c.is_active,
                        c.created_at,
                        c.updated_at,
                        er.id as rate_id,
                        er.rate_to_fancoin,
                        er.notes,
                        er.created_at as rate_created_at,
                        er.updated_at as rate_updated_at,
                        er.updated_by_admin_id,
                        a.username as updated_by_admin_username,
                        (SELECT COUNT(*) FROM users WHERE preferred_currency_id = c.id) as user_count
                    FROM currencies c
                    LEFT JOIN exchange_rates er ON c.id = er.currency_id
                    LEFT JOIN admins a ON er.updated_by_admin_id = a.admin_id
                    ORDER BY c.currency_code ASC, er.updated_at DESC
                ");
                
                $stmt->execute();
                $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $currencies = [];
                $processedIds = [];
                
                foreach ($results as $row) {
                    if (in_array($row['id'], $processedIds)) {
                        continue; // Skip duplicates, keep latest rate
                    }
                    
                    $currencies[] = [
                        'id' => (int)$row['id'],
                        'currency_code' => $row['currency_code'],
                        'currency_name' => $row['currency_name'],
                        'currency_symbol' => $row['currency_symbol'],
                        'is_active' => (bool)$row['is_active'],
                        'created_at' => $row['created_at'],
                        'updated_at' => $row['updated_at'],
                        'rate_id' => $row['rate_id'] ? (int)$row['rate_id'] : null,
                        'rate_to_fancoin' => $row['rate_to_fancoin'] ? (float)$row['rate_to_fancoin'] : null,
                        'formatted_rate' => $row['rate_to_fancoin'] ? 
                            $row['currency_symbol'] . number_format($row['rate_to_fancoin'], 4) : 
                            'No rate set',
                        'notes' => $row['notes'],
                        'rate_created_at' => $row['rate_created_at'],
                        'rate_updated_at' => $row['rate_updated_at'],
                        'updated_by_admin_id' => $row['updated_by_admin_id'] ? (int)$row['updated_by_admin_id'] : null,
                        'updated_by_admin_username' => $row['updated_by_admin_username'],
                        'user_count' => (int)$row['user_count'],
                        'has_rate' => !is_null($row['rate_to_fancoin']),
                        'can_delete' => (int)$row['user_count'] === 0 && $row['currency_code'] !== 'USD' // Can't delete USD or currencies with users
                    ];
                    
                    $processedIds[] = $row['id'];
                }
                
                jsonResponse(200, "Currencies fetched successfully", [
                    'currencies' => $currencies,
                    'total_count' => count($currencies)
                ]);
            } catch (Exception $e) {
                jsonResponse(500, "Error fetching currencies: " . $e->getMessage());
            }
            break;
            
        case 'POST':
            // Add new currency
            $adminId = getAdminId();
            if (!$adminId) {
                jsonResponse(401, "Admin authentication required");
            }
            
            $input = json_decode(file_get_contents("php://input"), true);
            
            $currencyCode = isset($input['currency_code']) ? strtoupper(trim($input['currency_code'])) : null;
            $currencyName = isset($input['currency_name']) ? trim($input['currency_name']) : null;
            $currencySymbol = isset($input['currency_symbol']) ? trim($input['currency_symbol']) : null;
            $rateToFanCoin = isset($input['rate_to_fancoin']) ? (float)$input['rate_to_fancoin'] : null;
            $notes = isset($input['notes']) ? trim($input['notes']) : '';
            
            // Validate required fields
            if (!$currencyCode || strlen($currencyCode) !== 3) {
                jsonResponse(400, "Valid 3-letter currency code is required");
            }
            
            if (!$currencyName) {
                jsonResponse(400, "Currency name is required");
            }
            
            if (!$currencySymbol) {
                jsonResponse(400, "Currency symbol is required");
            }
            
            if (!$rateToFanCoin || $rateToFanCoin <= 0) {
                jsonResponse(400, "Valid exchange rate is required");
            }
            
            $conn->beginTransaction();
            
            try {
                // Check if currency code already exists
                $stmt = $conn->prepare("SELECT id FROM currencies WHERE currency_code = :currency_code");
                $stmt->execute(['currency_code' => $currencyCode]);
                
                if ($stmt->rowCount() > 0) {
                    jsonResponse(400, "Currency code already exists");
                }
                
                // Insert new currency
                $stmt = $conn->prepare("
                    INSERT INTO currencies (currency_code, currency_name, currency_symbol, is_active)
                    VALUES (:currency_code, :currency_name, :currency_symbol, 1)
                ");
                
                $stmt->execute([
                    'currency_code' => $currencyCode,
                    'currency_name' => $currencyName,
                    'currency_symbol' => $currencySymbol
                ]);
                
                $currencyId = $conn->lastInsertId();
                
                // Insert exchange rate
                $stmt = $conn->prepare("
                    INSERT INTO exchange_rates (currency_id, rate_to_fancoin, notes, updated_by_admin_id)
                    VALUES (:currency_id, :rate_to_fancoin, :notes, :admin_id)
                ");
                
                $stmt->execute([
                    'currency_id' => $currencyId,
                    'rate_to_fancoin' => $rateToFanCoin,
                    'notes' => $notes,
                    'admin_id' => $adminId
                ]);
                
                $conn->commit();
                
                jsonResponse(201, "Currency added successfully", [
                    'currency_id' => (int)$currencyId,
                    'currency_code' => $currencyCode,
                    'currency_name' => $currencyName,
                    'currency_symbol' => $currencySymbol,
                    'rate_to_fancoin' => (float)$rateToFanCoin,
                    'notes' => $notes
                ]);
                
            } catch (Exception $e) {
                $conn->rollback();
                throw $e;
            }
            break;
            
        case 'PUT':
            // Update currency (activate/deactivate)
            $adminId = getAdminId();
            if (!$adminId) {
                jsonResponse(401, "Admin authentication required");
            }
            
            $currencyId = isset($_GET['id']) ? (int)$_GET['id'] : null;
            if (!$currencyId) {
                jsonResponse(400, "Currency ID is required");
            }
            
            $input = json_decode(file_get_contents("php://input"), true);
            $isActive = isset($input['is_active']) ? (bool)$input['is_active'] : null;
            
            if ($isActive === null) {
                jsonResponse(400, "is_active field is required");
            }
            
            // Prevent deactivating USD
            $stmt = $conn->prepare("SELECT currency_code FROM currencies WHERE id = :id");
            $stmt->execute(['id' => $currencyId]);
            $currency = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$currency) {
                jsonResponse(404, "Currency not found");
            }
            
            if ($currency['currency_code'] === 'USD' && !$isActive) {
                jsonResponse(400, "Cannot deactivate USD currency");
            }
            
            $stmt = $conn->prepare("UPDATE currencies SET is_active = :is_active WHERE id = :id");
            $stmt->execute(['is_active' => $isActive ? 1 : 0, 'id' => $currencyId]);
            
            jsonResponse(200, "Currency status updated successfully", [
                'currency_id' => $currencyId,
                'is_active' => $isActive
            ]);
            break;
            
        default:
            jsonResponse(405, "Method not allowed");
    }
    
} catch (PDOException $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    error_log("Database error in manage_currencies.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    error_log("General error in manage_currencies.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred while managing currencies");
}
?>
