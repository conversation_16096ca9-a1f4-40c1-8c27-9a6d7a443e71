<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

$conn = getDBConnection();

try {
    $data = json_decode(file_get_contents("php://input"));

    // Verify user exists and get their data
    $stmt = $conn->prepare("SELECT user_id, username, balance FROM users WHERE user_id = ?");
    $stmt->execute([$data->userId]);
    $user = $stmt->fetch();

    if (!$user) {
        echo json_encode(["success" => false, "message" => "User not found"]);
        exit;
    }

    if ($user['balance'] < $data->amount) {
        echo json_encode([
            "success" => false,
            "message" => "Insufficient balance. Your balance: " . $user['balance'] . " FanCoins"
        ]);
        exit;
    }

    $conn->beginTransaction();

    // Insert bet with user and team data
    $stmt = $conn->prepare("
        INSERT INTO bets (
            user1_id, 
            challenge_id, 
            amount_user1, 
            bet_choice_user1, 
            odds_user1,
            potential_return_user1,
            potential_return_win_user1,
            potential_return_draw_user1,
            potential_return_loss_user1,
            bet_status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'open')
    ");
    $stmt->execute([
        $user['user_id'],
        $data->challengeId,
        $data->amount,
        $data->outcome,
        $data->odds,
        $data->potential_return_win_user1, // Set this same as win amount
        $data->potential_return_win_user1,
        $data->potential_return_draw_user1,
        $data->potential_return_loss_user1
    ]);

    $betId = $conn->lastInsertId();

    // Update user balance
    $stmt = $conn->prepare("UPDATE users SET balance = balance - ? WHERE user_id = ?");
    $stmt->execute([$data->amount, $user['user_id']]);

    $conn->commit();

    echo json_encode([
        "success" => true,
        "betId" => $betId,
        "message" => "Bet placed successfully"
    ]);

} catch (Exception $e) {
    $conn->rollBack();
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
}

// Generate unique reference number
$reference = $betId . substr(uniqid(), -6);

// Update the bet with the reference
$stmt = $conn->prepare("UPDATE bets SET unique_code = ? WHERE bet_id = ?");
$stmt->execute([$reference, $betId]);
