<?php
/**
 * Test script for homepage API endpoint
 * Run this script to verify the homepage_data.php handler works correctly
 */

echo "<h1>FanBet247 Homepage API Test</h1>\n";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; } .success { background-color: #d4edda; } .error { background-color: #f8d7da; } pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }</style>\n";

// Test different API endpoints
$tests = [
    'All Data' => 'handlers/homepage_data.php',
    'Top Leagues' => 'handlers/homepage_data.php?section=leagues&limit=3',
    'Live Challenges' => 'handlers/homepage_data.php?section=challenges&limit=5',
    'Recent Bets' => 'handlers/homepage_data.php?section=bets&limit=5',
    'Blog Posts' => 'handlers/homepage_data.php?section=blog&limit=2',
    'Site Statistics' => 'handlers/homepage_data.php?section=stats'
];

foreach ($tests as $testName => $endpoint) {
    echo "<div class='section'>";
    echo "<h2>Testing: $testName</h2>";
    
    try {
        // Make request to the endpoint
        $url = "http://localhost/FanBet247/backend/$endpoint";
        $response = file_get_contents($url);
        
        if ($response === false) {
            throw new Exception("Failed to fetch data from endpoint");
        }
        
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response: " . json_last_error_msg());
        }
        
        if ($data['status'] === 'success') {
            echo "<div class='success'>";
            echo "<strong>✅ Success!</strong><br>";
            echo "Message: " . $data['message'] . "<br>";
            
            // Show data structure
            if (isset($data['data'])) {
                echo "<strong>Data Structure:</strong><br>";
                echo "<pre>" . json_encode($data['data'], JSON_PRETTY_PRINT) . "</pre>";
            }
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<strong>❌ API Error!</strong><br>";
            echo "Message: " . ($data['message'] ?? 'Unknown error') . "<br>";
            if (isset($data['error'])) {
                echo "Error: " . $data['error'] . "<br>";
            }
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<strong>❌ Test Failed!</strong><br>";
        echo "Error: " . $e->getMessage() . "<br>";
        echo "Endpoint: $url<br>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Test database connection
echo "<div class='section'>";
echo "<h2>Database Connection Test</h2>";

try {
    include_once 'includes/db_connect.php';
    $conn = getDBConnection();
    
    if ($conn) {
        echo "<div class='success'>";
        echo "<strong>✅ Database Connected!</strong><br>";
        
        // Test basic queries
        $tables = ['users', 'leagues', 'challenges', 'bets'];
        foreach ($tables as $table) {
            try {
                $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                echo "$table: $count records<br>";
            } catch (Exception $e) {
                echo "$table: Error - " . $e->getMessage() . "<br>";
            }
        }
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<strong>❌ Database Connection Failed!</strong><br>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>❌ Database Test Failed!</strong><br>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "</div>";
}

echo "</div>";

echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li>If all tests pass, the API is ready for frontend integration</li>";
echo "<li>If there are errors, check the database connection and table structure</li>";
echo "<li>You can test individual endpoints by visiting them directly in your browser</li>";
echo "<li>The blog section will show sample data until you create the blog_posts table</li>";
echo "</ul>";
?>