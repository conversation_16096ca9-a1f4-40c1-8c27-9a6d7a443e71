<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

$conn = getDBConnection();

// Assuming you have a 'Teams' table with 'id' and 'logo' columns
function getTeamLogoPath($conn, $teamName) {
    try {
        $stmt = $conn->prepare("SELECT logo FROM Teams WHERE name = :teamName");
        $stmt->bindParam(':teamName', $teamName);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['logo'] : null; 
    } catch (PDOException $e) {
        error_log("Error fetching team logo: " . $e->getMessage());
        return null;
    }
}

try {
    $logo1 = $_POST['logo1']; 
    $logo2 = $_POST['logo2'];
    $stmt = $conn->prepare("INSERT INTO challenges (
        admin_id, team_a, logo1, team_b, logo2, 
        odds_team_a, odds_team_b, odds_draw, odds_lost,
        team_a_goal_advantage, team_b_goal_advantage, 
        challenge_date, start_time, end_time, match_date,
        match_type
    ) VALUES (
        :admin_id, :team_a, :logo1, :team_b, :logo2,
        :odds_team_a, :odds_team_b, :odds_draw, :odds_lost,
        :team_a_goal_advantage, :team_b_goal_advantage,
        :challenge_date, :start_time, :end_time, :match_date,
        :match_type
    )");

    // Make sure to bind all parameters
    $stmt->bindParam(':admin_id', $_POST['admin_id']); 
    $stmt->bindParam(':team_a', $_POST['team1']);
    $stmt->bindParam(':logo1', $logo1); 
    $stmt->bindParam(':team_b', $_POST['team2']);
    $stmt->bindParam(':logo2', $logo2); 
    $stmt->bindParam(':odds_team_a', $_POST['odds1']);
    $stmt->bindParam(':odds_team_b', $_POST['odds2']);
    $stmt->bindParam(':odds_draw', $_POST['oddsDraw']);
    $stmt->bindParam(':odds_lost', $_POST['oddsLost']);
    $stmt->bindParam(':team_a_goal_advantage', $_POST['goalAdvantage1']);
    $stmt->bindParam(':team_b_goal_advantage', $_POST['goalAdvantage2']);
    $stmt->bindParam(':challenge_date', $_POST['startTime']); // Assuming you are using startTime for challenge_date
    $stmt->bindParam(':start_time', $_POST['startTime']);
    $stmt->bindParam(':end_time', $_POST['endTime']);
    $stmt->bindParam(':match_date', $_POST['matchTime']);
    $stmt->bindParam(':match_type', $_POST['matchType']);

    if ($stmt->execute()) {
        echo json_encode(array("success" => true, "message" => "Challenge created successfully."));
    } else {
        echo json_encode(array("success" => false, "message" => "Unable to create challenge."));
    }
} catch(PDOException $e) {
    echo json_encode(array("success" => false, "message" => "Error: " . $e->getMessage()));
}
?>
