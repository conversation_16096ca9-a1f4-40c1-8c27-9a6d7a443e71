<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Use provided settings or default to the specified credentials
$host = $input['host'] ?? 'mail.hostinger.com';
$port = $input['port'] ?? 465;
$username = $input['username'] ?? '<EMAIL>';
$password = $input['password'] ?? 'Money2025@Demo#';
$encryption = $input['encryption'] ?? 'ssl';
$from_email = $input['from_email'] ?? '<EMAIL>';
$from_name = $input['from_name'] ?? 'FanBet247';

try {
    
    // Test SMTP connection using fsockopen (simple connection test)
    $timeout = 10;

    if ($encryption === 'ssl') {
        $host_with_ssl = 'ssl://' . $host;
    } else {
        $host_with_ssl = $host;
    }

    $connection = @fsockopen($host_with_ssl, $port, $errno, $errstr, $timeout);

    if (!$connection) {
        throw new Exception("Cannot connect to SMTP server: $errstr ($errno)");
    }

    // Read server response
    $response = fgets($connection, 515);
    if (substr($response, 0, 3) !== '220') {
        fclose($connection);
        throw new Exception("SMTP server did not respond correctly: $response");
    }

    // Send EHLO command
    fwrite($connection, "EHLO " . ($_SERVER['SERVER_NAME'] ?? 'localhost') . "\r\n");
    $response = fgets($connection, 515);

    if (substr($response, 0, 3) !== '250') {
        fclose($connection);
        throw new Exception("EHLO command failed: $response");
    }

    // Close connection
    fwrite($connection, "QUIT\r\n");
    fclose($connection);

    echo json_encode([
        'success' => true,
        'message' => 'SMTP connection test successful! Server is reachable at ' . $host . ':' . $port
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'SMTP test failed: ' . $e->getMessage()
    ]);
}
?>
