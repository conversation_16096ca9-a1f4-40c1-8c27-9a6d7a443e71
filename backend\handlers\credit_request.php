<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Validate input
    if (!isset($_POST['user_id']) || !isset($_POST['amount']) || !isset($_POST['payment_method_id']) || !isset($_FILES['proof_image'])) {
        throw new Exception('Missing required fields');
    }

    $userId = $_POST['user_id'];
    $amount = floatval($_POST['amount']);
    $paymentMethodId = $_POST['payment_method_id'];
    $file = $_FILES['proof_image'];

    // Validate amount
    if ($amount <= 0) {
        throw new Exception('Invalid amount');
    }

    // Validate file
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Error uploading file');
    }

    $allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    $fileType = mime_content_type($file['tmp_name']);
    if (!in_array($fileType, $allowedTypes)) {
        throw new Exception('Invalid file type. Only JPG, JPEG, and PNG files are allowed.');
    }

    if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
        throw new Exception('File size too large. Maximum size is 5MB.');
    }

    // Create uploads directory if it doesn't exist
    $uploadDir = '../../uploads/payment_proofs/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $targetPath = $uploadDir . $filename;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
        throw new Exception('Failed to save file');
    }

    $conn->beginTransaction();

    // Insert credit request
    $stmt = $conn->prepare("
        INSERT INTO credit_requests (
            user_id,
            amount,
            payment_method_id,
            proof_image,
            status,
            created_at,
            expires_at
        ) VALUES (
            :user_id,
            :amount,
            :payment_method_id,
            :proof_image,
            'pending',
            NOW(),
            DATE_ADD(NOW(), INTERVAL 24 HOUR)
        )
    ");

    $proofImagePath = 'uploads/payment_proofs/' . $filename;
    $stmt->bindParam(':user_id', $userId);
    $stmt->bindParam(':amount', $amount);
    $stmt->bindParam(':payment_method_id', $paymentMethodId);
    $stmt->bindParam(':proof_image', $proofImagePath);
    $stmt->execute();

    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Credit request submitted successfully'
    ]);

} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }

    // Delete uploaded file if it exists
    if (isset($targetPath) && file_exists($targetPath)) {
        unlink($targetPath);
    }

    error_log("Error in credit_request.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 