<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

include_once '../includes/db_connect.php';

// Function to generate OTP
function generateOTP($length = 6) {
    $characters = '0123456789';
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $otp .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $otp;
}

// Function to send email
function sendEmail($to, $subject, $message, $conn) {
    // Get SMTP settings
    $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        return [
            'success' => false,
            'message' => 'SMTP settings not configured'
        ];
    }
    
    $smtp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Set up PHPMailer
    require '../vendor/autoload.php';
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtp['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtp['username'];
        $mail->Password = $smtp['password'];
        
        if ($smtp['encryption'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        } elseif ($smtp['encryption'] === 'tls') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        }
        
        $mail->Port = $smtp['port'];
        
        // Recipients
        $mail->setFrom($smtp['from_email'], $smtp['from_name']);
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $message;
        
        $mail->send();
        return [
            'success' => true,
            'message' => 'Email sent successfully'
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Email could not be sent. Error: ' . $mail->ErrorInfo
        ];
    }
}

try {
    $conn = getDBConnection();
    
    // Get request data
    $data = json_decode(file_get_contents("php://input"));
    
    // Validate required fields
    if (!isset($data->email)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Email is required"
        ]);
        exit;
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT user_id, email FROM users WHERE email = :email");
    $stmt->bindParam(':email', $data->email);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode([
            "success" => false,
            "message" => "User not found"
        ]);
        exit;
    }
    
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get security settings
    $stmt = $conn->prepare("SELECT setting_value FROM security_settings WHERE setting_name = 'enable_2fa'");
    $stmt->execute();
    $enable2fa = $stmt->fetch(PDO::FETCH_ASSOC)['setting_value'] === 'true';
    
    if (!$enable2fa) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Two-factor authentication is not enabled"
        ]);
        exit;
    }
    
    // Get OTP expiry time
    $stmt = $conn->prepare("SELECT setting_value FROM security_settings WHERE setting_name = 'otp_expiry_time'");
    $stmt->execute();
    $expiryTime = (int)$stmt->fetch(PDO::FETCH_ASSOC)['setting_value'];
    
    // Generate OTP
    $otp = generateOTP();
    $expiryTimestamp = date('Y-m-d H:i:s', time() + $expiryTime);
    
    // Store OTP in database
    $stmt = $conn->prepare("
        INSERT INTO user_otp (user_id, otp, expires_at, attempts) 
        VALUES (:user_id, :otp, :expires_at, 0)
        ON DUPLICATE KEY UPDATE 
        otp = :otp, 
        expires_at = :expires_at, 
        attempts = 0
    ");
    
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->bindParam(':otp', $otp);
    $stmt->bindParam(':expires_at', $expiryTimestamp);
    
    if (!$stmt->execute()) {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Failed to generate OTP"
        ]);
        exit;
    }
    
    // Send OTP via email
    $subject = "Your FanBet247 OTP Code";
    $message = "
        <html>
        <head>
            <title>Your OTP Code</title>
        </head>
        <body>
            <h2>FanBet247 Authentication</h2>
            <p>Your one-time password (OTP) is: <strong>{$otp}</strong></p>
            <p>This code will expire in " . ($expiryTime / 60) . " minutes.</p>
            <p>If you did not request this code, please ignore this email.</p>
        </body>
        </html>
    ";
    
    $emailResult = sendEmail($user['email'], $subject, $message, $conn);
    
    if (!$emailResult['success']) {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Failed to send OTP: " . $emailResult['message']
        ]);
        exit;
    }
    
    http_response_code(200);
    echo json_encode([
        "success" => true,
        "message" => "OTP sent successfully",
        "expiresIn" => $expiryTime
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
}
?>
