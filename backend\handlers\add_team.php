<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header("Content-Type: application/json");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $logo = $_FILES['logo'];

    $target_dir = "../../uploads/team_logos/";
    $file_extension = pathinfo($logo['name'], PATHINFO_EXTENSION);
    $file_name = uniqid() . '.' . $file_extension;
    $target_file = $target_dir . $file_name;

    if (move_uploaded_file($logo["tmp_name"], $target_file)) {
        $logo_path = "uploads/team_logos/" . $file_name;
        $query = "INSERT INTO teams (name, logo) VALUES ('$name', '$logo_path')";
        
        if (mysqli_query($conn, $query)) {
            echo json_encode(["success" => true, "message" => "Team added successfully"]);
        } else {
            echo json_encode(["success" => false, "message" => "Error adding team: " . mysqli_error($conn)]);
        }
    } else {
        echo json_encode(["success" => false, "message" => "Error uploading logo"]);
    }
}
