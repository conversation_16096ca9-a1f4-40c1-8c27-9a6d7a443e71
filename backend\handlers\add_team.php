<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header("Content-Type: application/json; charset=UTF-8");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../includes/db_connect.php';
$conn = getDBConnection();

if (!$conn) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'] ?? null;
    $logo = $_FILES['logo'] ?? null;

    if (!$name || !$logo) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Team name and logo are required.']);
        exit;
    }

    $target_dir = "../../uploads/team_logos/";
    if (!is_dir($target_dir)) {
        mkdir($target_dir, 0777, true);
    }
    
    $file_extension = pathinfo($logo['name'], PATHINFO_EXTENSION);
    $file_name = uniqid('team_logo_', true) . '.' . $file_extension;
    $target_file = $target_dir . $file_name;

    if (move_uploaded_file($logo["tmp_name"], $target_file)) {
        $logo_path = "uploads/team_logos/" . $file_name;
        
        try {
            $query = "INSERT INTO teams (name, logo) VALUES (:name, :logo)";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':logo', $logo_path);

            if ($stmt->execute()) {
                echo json_encode(['success' => true, 'message' => 'Team added successfully.']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to add team to the database.']);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'A database error occurred.', 'error' => $e->getMessage()]);
        }
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error uploading logo.']);
    }
}
