<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    // Find users missing password_hash but have a plain password
    $stmt = $conn->prepare("SELECT user_id, password FROM users WHERE (password_hash IS NULL OR password_hash = '') AND password IS NOT NULL AND password != ''");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $updated = 0;
    foreach ($users as $user) {
        $hash = password_hash($user['password'], PASSWORD_DEFAULT);
        $updateStmt = $conn->prepare("UPDATE users SET password_hash = :hash WHERE user_id = :user_id");
        $updateStmt->bindParam(':hash', $hash);
        $updateStmt->bindParam(':user_id', $user['user_id']);
        if ($updateStmt->execute()) {
            $updated++;
        }
    }
    echo json_encode([
        'success' => true,
        'message' => "Migrated $updated users to password_hash."
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
