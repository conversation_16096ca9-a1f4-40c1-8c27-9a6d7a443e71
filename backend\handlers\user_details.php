<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $user_id = $_GET['id'] ?? null;
        
        if (!$user_id) {
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            exit;
        }

        // Get user details with additional statistics
        $query = "SELECT 
                    u.*,
                    COUNT(DISTINCT b.bet_id) as total_bets,
                    COUNT(DISTINCT CASE WHEN b.bet_status = 'completed' AND b.winner_user_id = u.user_id THEN b.bet_id END) as wins,
                    COUNT(DISTINCT CASE WHEN b.bet_status = 'completed' AND b.winner_user_id != u.user_id THEN b.bet_id END) as losses,
                    COALESCE(SUM(CASE WHEN b.bet_status = 'completed' AND b.winner_user_id = u.user_id THEN b.amount_user1 ELSE 0 END), 0) as total_winnings,
                    COALESCE(SUM(CASE WHEN b.bet_status = 'completed' AND b.winner_user_id != u.user_id THEN b.amount_user1 ELSE 0 END), 0) as total_losses,
                    0 as current_streak,
                    COALESCE(u.balance, 0) as total_points
                  FROM users u
                  LEFT JOIN bets b ON (b.user1_id = u.user_id OR b.user2_id = u.user_id)
                  WHERE u.user_id = :user_id
                  GROUP BY u.user_id";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            // Calculate current streak
            $streak_query = "SELECT 
                                b.bet_status,
                                b.winner_user_id,
                                b.created_at
                             FROM bets b 
                             WHERE (b.user1_id = :user_id OR b.user2_id = :user_id) 
                             AND b.bet_status = 'completed'
                             ORDER BY b.created_at DESC 
                             LIMIT 10";
            
            $streak_stmt = $db->prepare($streak_query);
            $streak_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $streak_stmt->execute();
            
            $recent_bets = $streak_stmt->fetchAll(PDO::FETCH_ASSOC);
            $current_streak = 0;
            
            foreach ($recent_bets as $bet) {
                if ($bet['winner_user_id'] == $user_id) {
                    $current_streak++;
                } else {
                    break;
                }
            }
            
            $user['current_streak'] = $current_streak;
            
            echo json_encode([
                'success' => true,
                'user' => $user
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'User not found'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
