<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    error_log("Connecting to database in welcome_recent_bets.php");

    // Get limit from query parameter, default to 6
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 6;
    $limit = max(1, min(20, $limit)); // Ensure limit is between 1 and 20

    // Enhanced query to get recent bets with comprehensive data
    $query = "SELECT
        b.bet_id,
        b.amount_user1,
        b.amount_user2,
        b.bet_status,
        b.created_at,
        b.unique_code,
        COALESCE(u1.username, 'User 1') as user1_name,
        COALESCE(u2.username, 'User 2') as user2_name,
        b.bet_choice_user1,
        b.bet_choice_user2,
        b.odds_user1,
        b.odds_user2,
        b.potential_return_user1,
        b.potential_return_user2,
        c.team_a,
        c.team_b,
        c.match_date,
        c.status as challenge_status,
        c.logo1 as team_a_logo,
        c.logo2 as team_b_logo,
        c.odds_team_a,
        c.odds_team_b,
        c.odds_draw,
        CASE
            WHEN b.bet_status = 'completed' AND b.user1_outcome = 'win' THEN 'won'
            WHEN b.bet_status = 'completed' AND b.user1_outcome = 'loss' THEN 'lost'
            WHEN b.bet_status = 'completed' AND b.outcome = 'draw' THEN 'draw'
            WHEN b.bet_status = 'joined' THEN 'active'
            ELSE 'pending'
        END as display_status
    FROM bets b
    LEFT JOIN users u1 ON b.user1_id = u1.user_id
    LEFT JOIN users u2 ON b.user2_id = u2.user_id
    LEFT JOIN challenges c ON b.challenge_id = c.challenge_id
    WHERE b.bet_status IN ('open', 'joined', 'completed')
    ORDER BY b.created_at DESC
    LIMIT :limit";

    error_log("Executing query: " . $query . " with limit: " . $limit);

    $stmt = $conn->prepare($query);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();

    $bets = array();
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $bet = array(
            'bet_id' => $row['bet_id'],
            'amount_user1' => floatval($row['amount_user1']),
            'amount_user2' => floatval($row['amount_user2']),
            'bet_status' => $row['bet_status'],
            'display_status' => $row['display_status'],
            'created_at' => $row['created_at'],
            'unique_code' => $row['unique_code'],
            'user1_name' => $row['user1_name'],
            'user2_name' => $row['user2_name'],
            'bet_choice_user1' => $row['bet_choice_user1'],
            'bet_choice_user2' => $row['bet_choice_user2'],
            'odds_user1' => floatval($row['odds_user1']),
            'odds_user2' => floatval($row['odds_user2']),
            'potential_return_user1' => floatval($row['potential_return_user1']),
            'potential_return_user2' => floatval($row['potential_return_user2']),
            'team_a' => $row['team_a'],
            'team_b' => $row['team_b'],
            'league_name' => 'Premier League', // Default league for now
            'match_date' => $row['match_date'],
            'challenge_status' => $row['challenge_status'],
            'team_a_logo' => $row['team_a_logo'],
            'team_b_logo' => $row['team_b_logo'],
            'odds_team_a' => floatval($row['odds_team_a']),
            'odds_team_b' => floatval($row['odds_team_b']),
            'odds_draw' => floatval($row['odds_draw'])
        );
        array_push($bets, $bet);
    }

    error_log("Found " . count($bets) . " bets");

    echo json_encode(array(
        'success' => true,
        'bets' => $bets,
        'total_count' => count($bets),
        'limit' => $limit,
        'timestamp' => date('Y-m-d H:i:s')
    ));

} catch(PDOException $e) {
    error_log("Database Error in welcome_recent_bets.php: " . $e->getMessage());
    echo json_encode(array(
        'success' => false,
        'message' => 'Database Error: ' . $e->getMessage()
    ));
} 