@echo off
echo ========================================
echo FanBet247 VPS Deployment Script
echo ========================================

echo.
echo Checking local files...
if not exist "index.html" (
    echo ERROR: index.html not found. Please run 'npm run build' first.
    pause
    exit /b 1
)

if not exist ".htaccess" (
    echo ERROR: .htaccess not found.
    pause
    exit /b 1
)

if not exist "backend" (
    echo ERROR: backend directory not found.
    pause
    exit /b 1
)

echo ✓ Local files found

echo.
echo Testing VPS connection...
ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@************** "echo 'Connection successful'"
if %errorlevel% neq 0 (
    echo ERROR: Cannot connect to VPS
    pause
    exit /b 1
)
echo ✓ VPS connection successful

echo.
echo Creating backup on VPS...
ssh root@************** "cd /var/www && tar -czf fanbet247_backup_%date:~-4,4%-%date:~-10,2%-%date:~-7,2%_%time:~0,2%-%time:~3,2%.tar.gz fanbet247.xyz 2>/dev/null || echo 'No existing files to backup'"

echo.
echo Ensuring directory structure...
ssh root@************** "mkdir -p /var/www/fanbet247.xyz/{backend/{handlers,includes},uploads,static}"

echo.
echo Uploading frontend files...
scp -o StrictHostKeyChecking=no index.html root@**************:/var/www/fanbet247.xyz/
scp -o StrictHostKeyChecking=no manifest.json root@**************:/var/www/fanbet247.xyz/
scp -o StrictHostKeyChecking=no favicon.ico root@**************:/var/www/fanbet247.xyz/
scp -o StrictHostKeyChecking=no -r static root@**************:/var/www/fanbet247.xyz/

echo.
echo Uploading backend files...
scp -o StrictHostKeyChecking=no -r backend root@**************:/var/www/fanbet247.xyz/

echo.
echo Uploading configuration files...
scp -o StrictHostKeyChecking=no .htaccess root@**************:/var/www/fanbet247.xyz/
scp -o StrictHostKeyChecking=no debug.php root@**************:/var/www/fanbet247.xyz/
scp -o StrictHostKeyChecking=no vps_structure_checker.php root@**************:/var/www/fanbet247.xyz/

echo.
echo Setting permissions...
ssh root@************** "chown -R www-data:www-data /var/www/fanbet247.xyz"
ssh root@************** "find /var/www/fanbet247.xyz -type d -exec chmod 755 {} \;"
ssh root@************** "find /var/www/fanbet247.xyz -type f -exec chmod 644 {} \;"
ssh root@************** "chmod 777 /var/www/fanbet247.xyz/uploads"

echo.
echo Testing Apache configuration...
ssh root@************** "apache2ctl configtest"

echo.
echo Restarting Apache...
ssh root@************** "systemctl restart apache2"

echo.
echo Verifying deployment...
ssh root@************** "ls -la /var/www/fanbet247.xyz/ | head -10"

echo.
echo ========================================
echo Deployment completed!
echo ========================================
echo.
echo Next steps:
echo 1. Visit https://fanbet247.xyz/vps_structure_checker.php
echo 2. Visit https://fanbet247.xyz/debug.php
echo 3. Test your main site at https://fanbet247.xyz
echo.
pause
