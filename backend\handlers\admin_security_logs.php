<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once __DIR__ . '/../includes/db_connect.php';
$conn = getDBConnection();

function sendResponse($status, $message, $data = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status === 200,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

// Simple admin check (matching existing pattern)
function isAdmin() {
    // For now, return admin ID 1. In production, implement proper admin authentication
    return 1;
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendResponse(405, "Only GET method is allowed");
    }

    // Verify admin (using same pattern as other admin endpoints)
    $adminId = isAdmin();
    if (!$adminId) {
        sendResponse(403, "Admin privileges required");
    }

    $targetUserId = $_GET['userId'] ?? null;
    
    if (!$targetUserId) {
        throw new Exception("User ID is required");
    }

    // Verify target user exists
    $stmt = $conn->prepare("SELECT username, email FROM users WHERE user_id = ?");
    $stmt->execute([$targetUserId]);
    $targetUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$targetUser) {
        throw new Exception("User not found");
    }

    // Get security logs for the user (simplified for now)
    $logs = [
        [
            'id' => 'demo_1',
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => 'system',
            'action' => 'Security logs feature initialized',
            'details' => 'Security management system is now active for this user',
            'admin_user' => 'system'
        ]
    ];

    // Log admin access (simplified)
    error_log("Admin $adminId viewed security logs for user {$targetUser['username']}");

    echo json_encode([
        'success' => true,
        'message' => 'Security logs retrieved successfully',
        'logs' => $logs,
        'user' => $targetUser
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch security logs: ' . $e->getMessage()
    ]);
}
?>
