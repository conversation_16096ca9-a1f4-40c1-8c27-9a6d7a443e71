<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../includes/db_connect.php';

// Function to send test email using P<PERSON>'s built-in mail function with SMTP
function sendTestEmail($to, $smtpSettings) {
    // Check if PHPMailer is available
    if (file_exists('../vendor/autoload.php')) {
        return sendTestEmailWithPHPMailer($to, $smtpSettings);
    } else {
        return sendTestEmailWithBuiltIn($to, $smtpSettings);
    }
}

// Function to send test email with PHPMailer (if available)
function sendTestEmailWithPHPMailer($to, $smtpSettings) {
    require '../vendor/autoload.php';
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtpSettings['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtpSettings['username'];
        $mail->Password = $smtpSettings['password'];
        
        if ($smtpSettings['encryption'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        } elseif ($smtpSettings['encryption'] === 'tls') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        }
        
        $mail->Port = $smtpSettings['port'];
        
        // Recipients
        $mail->setFrom($smtpSettings['from_email'], $smtpSettings['from_name']);
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = "FanBet247 SMTP Test Email";
        $mail->Body = "
            <html>
            <head>
                <title>SMTP Test Email</title>
            </head>
            <body>
                <h2>FanBet247 SMTP Configuration Test</h2>
                <p>This is a test email to verify your SMTP configuration is working correctly.</p>
                <p><strong>Test Details:</strong></p>
                <ul>
                    <li>SMTP Host: {$smtpSettings['host']}</li>
                    <li>Port: {$smtpSettings['port']}</li>
                    <li>Encryption: {$smtpSettings['encryption']}</li>
                    <li>From Email: {$smtpSettings['from_email']}</li>
                    <li>From Name: {$smtpSettings['from_name']}</li>
                </ul>
                <p>If you received this email, your SMTP configuration is working properly!</p>
                <hr>
                <p><small>This email was sent from FanBet247 SMTP Test System</small></p>
            </body>
            </html>
        ";
        
        $mail->send();
        return [
            'success' => true,
            'message' => 'Test email sent successfully'
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Test email could not be sent. Error: ' . $mail->ErrorInfo
        ];
    }
}

// Function to send test email with built-in PHP mail (fallback)
function sendTestEmailWithBuiltIn($to, $smtpSettings) {
    try {
        $subject = "FanBet247 SMTP Test Email";
        $message = "
            <html>
            <head>
                <title>SMTP Test Email</title>
            </head>
            <body>
                <h2>FanBet247 SMTP Configuration Test</h2>
                <p>This is a test email to verify your SMTP configuration is working correctly.</p>
                <p><strong>Test Details:</strong></p>
                <ul>
                    <li>SMTP Host: {$smtpSettings['host']}</li>
                    <li>Port: {$smtpSettings['port']}</li>
                    <li>Encryption: {$smtpSettings['encryption']}</li>
                    <li>From Email: {$smtpSettings['from_email']}</li>
                    <li>From Name: {$smtpSettings['from_name']}</li>
                </ul>
                <p>If you received this email, your SMTP configuration is working properly!</p>
                <p><strong>Note:</strong> This email was sent using PHP's built-in mail function as PHPMailer is not installed.</p>
                <hr>
                <p><small>This email was sent from FanBet247 SMTP Test System</small></p>
            </body>
            </html>
        ";

        // Set headers
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: {$smtpSettings['from_name']} <{$smtpSettings['from_email']}>" . "\r\n";

        // Send email
        if (mail($to, $subject, $message, $headers)) {
            return [
                'success' => true,
                'message' => 'Test email sent successfully using built-in PHP mail function'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to send email using built-in PHP mail function'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Test email could not be sent. Error: ' . $e->getMessage()
        ];
    }
}

try {
    $conn = getDBConnection();
    
    // Get request data
    $data = json_decode(file_get_contents("php://input"));
    
    // Validate required fields
    if (!isset($data->email)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Email address is required"
        ]);
        exit;
    }
    
    // Check if SMTP settings are provided or get from database
    if (isset($data->smtp_settings)) {
        // Use provided SMTP settings
        $smtpSettings = [
            'host' => $data->smtp_settings->host,
            'port' => $data->smtp_settings->port,
            'username' => $data->smtp_settings->username,
            'password' => $data->smtp_settings->password,
            'encryption' => $data->smtp_settings->encryption,
            'from_email' => $data->smtp_settings->from_email,
            'from_name' => $data->smtp_settings->from_name
        ];
    } else {
        // Get SMTP settings from database
        $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            http_response_code(400);
            echo json_encode([
                "success" => false,
                "message" => "No active SMTP settings found. Please configure SMTP settings first."
            ]);
            exit;
        }
        
        $smtpSettings = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // Validate SMTP settings
    if (empty($smtpSettings['host']) || empty($smtpSettings['username']) || empty($smtpSettings['password'])) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "SMTP settings are incomplete. Please configure all required fields."
        ]);
        exit;
    }
    
    // Send test email
    $result = sendTestEmail($data->email, $smtpSettings);
    
    if ($result['success']) {
        http_response_code(200);
        echo json_encode([
            "success" => true,
            "message" => "Test email sent successfully to " . $data->email
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => $result['message']
        ]);
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Error: " . $e->getMessage()
    ]);
}
?>
