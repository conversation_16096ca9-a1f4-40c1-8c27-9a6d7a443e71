<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
    exit();
}

try {
    // Get database connection
    $conn = getDBConnection();

    // Get user from token or session
    session_start();
    $userId = null;
    
    if (isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
        $stmt = $conn->prepare("SELECT user_id FROM user_sessions WHERE token = ? AND expires_at > NOW()");
        $stmt->execute([$token]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result) {
            $userId = $result['user_id'];
        }
    }

    if (!$userId) {
        jsonResponse(401, 'User not authenticated');
    }

    // Get POST data
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['league_id']) || !isset($data['amount'])) {
        jsonResponse(400, 'Missing required fields');
    }

    $leagueId = $data['league_id'];
    $amount = floatval($data['amount']);

    // Start transaction
    $conn->beginTransaction();

    // Check if league exists and is active
    $stmt = $conn->prepare("SELECT * FROM leagues WHERE league_id = ? AND status = 'active'");
    $stmt->execute([$leagueId]);
    $league = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$league) {
        jsonResponse(404, 'League not found or inactive');
    }

    // Validate amount
    if ($amount < $league['min_bet_amount'] || $amount > $league['max_bet_amount']) {
        jsonResponse(400, "Amount must be between {$league['min_bet_amount']} and {$league['max_bet_amount']} FC");
    }

    // Check if user is already a member
    $stmt = $conn->prepare("SELECT * FROM league_memberships WHERE user_id = ? AND league_id = ? AND status = 'active'");
    $stmt->execute([$userId, $leagueId]);
    if ($stmt->fetch()) {
        jsonResponse(400, 'You are already a member of this league');
    }

    // Get current season or create new one
    $stmt = $conn->prepare("SELECT * FROM seasons WHERE league_id = ? AND status = 'active'");
    $stmt->execute([$leagueId]);
    $season = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$season) {
        // Get admin user ID
        $stmt = $conn->prepare("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        $adminId = $admin ? $admin['user_id'] : $userId;

        // Create new season
        $seasonName = "Season " . date('Y-m');
        $startDate = date('Y-m-d');
        $endDate = date('Y-m-d', strtotime('+' . $league['season_duration'] . ' days'));

        $stmt = $conn->prepare("INSERT INTO seasons (league_id, season_name, created_by, start_date, end_date, status) VALUES (?, ?, ?, ?, ?, 'active')");
        $stmt->execute([$leagueId, $seasonName, $adminId, $startDate, $endDate]);
        $seasonId = $conn->lastInsertId();
    } else {
        $seasonId = $season['season_id'];
    }

    // Create league membership
    $stmt = $conn->prepare("INSERT INTO league_memberships (user_id, league_id, season_id, amount, status) VALUES (?, ?, ?, ?, 'active')");
    $stmt->execute([$userId, $leagueId, $seasonId, $amount]);
    $membershipId = $conn->lastInsertId();

    // Create transaction
    $stmt = $conn->prepare("INSERT INTO transactions (user_id, amount, type, status, description) VALUES (?, ?, 'admin_debit', 'completed', ?)");
    $description = "Joined league: {$league['name']} with {$amount} FC";
    $stmt->execute([$userId, $amount, $description]);
    $transactionId = $conn->lastInsertId();

    // Update user balance
    $stmt = $conn->prepare("UPDATE users SET balance = balance - ? WHERE user_id = ?");
    $stmt->execute([$amount, $userId]);

    // Log user activity
    $activityDetails = json_encode([
        'league_id' => $leagueId,
        'league_name' => $league['name'],
        'amount' => $amount,
        'min_bet_amount' => $league['min_bet_amount']
    ]);
    
    $stmt = $conn->prepare("INSERT INTO user_activity_log (user_id, activity_type, details) VALUES (:user_id, 'league_join', :details)");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':details', $activityDetails, PDO::PARAM_STR);
    $stmt->execute();

    // Get updated league data
    $leagueQuery = "SELECT 
        l.*,
        COALESCE(m.member_count, 0) as member_count
    FROM leagues l
    LEFT JOIN (
        SELECT league_id, COUNT(*) as member_count 
        FROM league_memberships 
        WHERE status = 'active'
        GROUP BY league_id
    ) m ON m.league_id = l.league_id
    WHERE l.league_id = ?";
    
    $leagueStmt = $conn->prepare($leagueQuery);
    $leagueStmt->execute([$leagueId]);
    $updatedLeague = $leagueStmt->fetch(PDO::FETCH_ASSOC);

    $conn->commit();
    jsonResponse(200, 'Successfully joined the league', [
        'league' => $updatedLeague
    ]);

} catch (Exception $e) {
    $conn->rollBack();
    jsonResponse(500, 'Error joining league: ' . $e->getMessage());
}
