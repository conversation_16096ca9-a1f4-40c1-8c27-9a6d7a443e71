<?php
// Debug OTP records for testuser123
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Get testuser123's user_id
    $stmt = $conn->prepare('SELECT user_id, username, email, otp_enabled FROM users WHERE username = ?');
    $stmt->execute(['testuser123']);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "User found: {$user['username']} (ID: {$user['user_id']}, Email: {$user['email']}, OTP Enabled: " . ($user['otp_enabled'] ? 'YES' : 'NO') . ")\n\n";
        
        // Get recent OTP records
        $stmt = $conn->prepare('SELECT id, otp, expiry, attempts, used, created_at FROM user_otp WHERE user_id = ? ORDER BY created_at DESC LIMIT 5');
        $stmt->execute([$user['user_id']]);
        $otps = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Recent OTP records:\n";
        foreach ($otps as $otp) {
            $status = $otp['used'] ? 'USED' : 'ACTIVE';
            $expired = strtotime($otp['expiry']) < time() ? 'EXPIRED' : 'VALID';
            $timeLeft = strtotime($otp['expiry']) - time();
            $timeLeftStr = $timeLeft > 0 ? "({$timeLeft}s left)" : "(expired)";
            
            echo "  ID: {$otp['id']}\n";
            echo "  OTP: {$otp['otp']}\n";
            echo "  Status: $status\n";
            echo "  Expiry: $expired $timeLeftStr\n";
            echo "  Expiry Time: {$otp['expiry']}\n";
            echo "  Attempts: {$otp['attempts']}\n";
            echo "  Created: {$otp['created_at']}\n";
            echo "  ---\n";
        }
        
        // Check current time
        echo "\nCurrent server time: " . date('Y-m-d H:i:s') . "\n";
        
        // Get the most recent valid OTP
        $currentTime = date('Y-m-d H:i:s');
        $stmt = $conn->prepare("
            SELECT id, otp, expiry, attempts, used
            FROM user_otp
            WHERE user_id = ? AND used = 0 AND expiry > ?
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$user['user_id'], $currentTime]);
        $validOtp = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($validOtp) {
            echo "\nMost recent VALID OTP:\n";
            echo "  ID: {$validOtp['id']}\n";
            echo "  OTP: {$validOtp['otp']}\n";
            echo "  Expiry: {$validOtp['expiry']}\n";
            echo "  Attempts: {$validOtp['attempts']}\n";
        } else {
            echo "\nNo valid OTP found!\n";
        }
        
    } else {
        echo "User testuser123 not found\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
