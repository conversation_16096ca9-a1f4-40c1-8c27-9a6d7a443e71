<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

include_once '../includes/db_connect.php';
require '../vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

try {
    $conn = getDBConnection();
    
    // Get request data
    $data = json_decode(file_get_contents("php://input"));
    
    // Validate required fields
    if (!isset($data->user_id) || !isset($data->code)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "User ID and verification code are required"
        ]);
        exit;
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT user_id FROM users WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $data->user_id);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode([
            "success" => false,
            "message" => "User not found"
        ]);
        exit;
    }
    
    // Get user's Google Auth secret
    $stmt = $conn->prepare("
        SELECT secret_key FROM user_2fa 
        WHERE user_id = :user_id AND auth_type = 'google_auth' AND is_enabled = 0
    ");
    $stmt->bindParam(':user_id', $data->user_id);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Google Authenticator setup not found or already enabled"
        ]);
        exit;
    }
    
    $authData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Verify Google Auth code
    $google2fa = new Google2FA();
    $valid = $google2fa->verifyKey($authData['secret_key'], $data->code);
    
    if (!$valid) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Invalid verification code"
        ]);
        exit;
    }
    
    // Enable Google Auth
    $stmt = $conn->prepare("
        UPDATE user_2fa 
        SET is_enabled = 1 
        WHERE user_id = :user_id AND auth_type = 'google_auth'
    ");
    $stmt->bindParam(':user_id', $data->user_id);
    
    if (!$stmt->execute()) {
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Failed to enable Google Authenticator"
        ]);
        exit;
    }
    
    http_response_code(200);
    echo json_encode([
        "success" => true,
        "message" => "Google Authenticator enabled successfully"
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Error: " . $e->getMessage()
    ]);
}
?>
