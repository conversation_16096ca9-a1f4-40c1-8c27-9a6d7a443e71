<?php
require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    $stmt = $conn->prepare('SELECT backup_codes FROM user_2fa WHERE user_id = 4');
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        $backupCodes = json_decode($result['backup_codes'], true);
        echo "Backup codes for user 4:\n";
        foreach ($backupCodes as $index => $code) {
            echo "  " . ($index + 1) . ": $code\n";
        }
    } else {
        echo "No backup codes found\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
