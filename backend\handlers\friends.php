<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

// Handle GET requests for fetching friends
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $userId = $_GET['user_id'] ?? null;

    if (!$userId) {
        echo json_encode(['success' => false, 'message' => 'User ID required']);
        exit;
    }

    try {
        // Get accepted friends
        $query = "SELECT 
                    u.user_id as id,
                    u.username,
                    u.favorite_team,
                    COUNT(DISTINCT b.bet_id) as total_bets,
                    COALESCE(
                        ROUND(
                            SUM(CASE WHEN b.outcome = 'win' THEN 1 ELSE 0 END) * 100.0 / 
                            NULLIF(COUNT(DISTINCT b.bet_id), 0), 
                            2
                        ),
                        0
                    ) as win_rate
                FROM users u 
                INNER JOIN user_friends uf ON (u.user_id = uf.friend_id OR u.user_id = uf.user_id)
                LEFT JOIN bets b ON u.user_id = b.user1_id OR u.user_id = b.user2_id
                WHERE ((uf.user_id = :user_id AND u.user_id = uf.friend_id) 
                   OR (uf.friend_id = :user_id AND u.user_id = uf.user_id))
                AND uf.status = 'accepted'
                AND u.user_id != :user_id
                GROUP BY 
                    u.user_id,
                    u.username,
                    u.favorite_team";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $friends = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Add debug information
        error_log("Friends for user $userId: " . json_encode($friends));

        // Get pending friend requests
        $pendingQuery = "SELECT 
                            u.user_id as id,
                            u.username,
                            u.favorite_team,
                            COUNT(DISTINCT b.bet_id) as total_bets,
                            COALESCE(
                                ROUND(
                                    SUM(CASE WHEN b.outcome = 'win' THEN 1 ELSE 0 END) * 100.0 / 
                                    NULLIF(COUNT(DISTINCT b.bet_id), 0), 
                                    2
                                ),
                                0
                            ) as win_rate,
                            MAX(uf.created_at) as request_date,
                            CASE 
                                WHEN uf.user_id = :user_id THEN 'sent'
                                ELSE 'received'
                            END as request_type
                        FROM user_friends uf
                        JOIN users u ON 
                            CASE 
                                WHEN uf.user_id = :user_id THEN u.user_id = uf.friend_id
                                ELSE u.user_id = uf.user_id
                            END
                        LEFT JOIN bets b ON u.user_id = b.user1_id OR u.user_id = b.user2_id
                        WHERE (uf.friend_id = :user_id OR uf.user_id = :user_id)
                        AND uf.status = 'pending'
                        GROUP BY 
                            u.user_id,
                            u.username,
                            u.favorite_team,
                            CASE 
                                WHEN uf.user_id = :user_id THEN 'sent'
                                ELSE 'received'
                            END";

        $pendingStmt = $conn->prepare($pendingQuery);
        $pendingStmt->bindParam(':user_id', $userId);
        $pendingStmt->execute();
        $pendingRequests = $pendingStmt->fetchAll(PDO::FETCH_ASSOC);

        // Add debug information
        error_log("Pending requests for user $userId: " . json_encode($pendingRequests));

        echo json_encode([
            'success' => true, 
            'friends' => $friends,
            'pendingRequests' => $pendingRequests
        ]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

// Handle POST requests for friend actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents("php://input"));
    
    if (!isset($data->action) || !isset($data->user_id) || !isset($data->friend_id)) {
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
        exit;
    }

    try {
        switch ($data->action) {
            case 'unfriend':
                $query = "DELETE FROM user_friends 
                         WHERE (user_id = :user_id AND friend_id = :friend_id)
                         OR (user_id = :friend_id AND friend_id = :user_id)";
                break;

            case 'request':
                // Check if request already exists
                $checkQuery = "SELECT * FROM user_friends 
                             WHERE (user_id = :user_id AND friend_id = :friend_id)
                             OR (user_id = :friend_id AND friend_id = :user_id)";
                $checkStmt = $conn->prepare($checkQuery);
                $checkStmt->bindParam(':user_id', $data->user_id);
                $checkStmt->bindParam(':friend_id', $data->friend_id);
                $checkStmt->execute();
                
                if ($checkStmt->rowCount() > 0) {
                    echo json_encode(['success' => false, 'message' => 'Friend request already exists']);
                    exit;
                }

                $query = "INSERT INTO user_friends (user_id, friend_id, status) 
                         VALUES (:user_id, :friend_id, 'pending')";
                break;

            case 'accept':
                $query = "UPDATE user_friends 
                         SET status = 'accepted' 
                         WHERE user_id = :friend_id AND friend_id = :user_id 
                         AND status = 'pending'";
                break;

            case 'reject':
                $query = "DELETE FROM user_friends 
                         WHERE user_id = :friend_id AND friend_id = :user_id 
                         AND status = 'pending'";
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
                exit;
        }
            
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $data->user_id);
        $stmt->bindParam(':friend_id', $data->friend_id);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Action completed successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to complete action']);
        }
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}
